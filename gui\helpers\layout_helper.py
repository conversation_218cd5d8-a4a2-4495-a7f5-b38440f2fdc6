#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布局辅助模块

该模块提供布局创建和管理的辅助功能。
"""

from typing import Tuple

from PyQt6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QGridLayout, QFrame
)
from PyQt6.QtCore import Qt


class LayoutHelper:
    """布局辅助类"""
    
    @staticmethod
    def create_vbox_layout(spacing: int = 6, 
                          margins: Tuple[int, int, int, int] = None) -> QVBoxLayout:
        """创建垂直布局
        
        Args:
            spacing: 间距
            margins: 边距 (left, top, right, bottom)
            
        Returns:
            QVBoxLayout: 垂直布局
        """
        layout = QVBoxLayout()
        layout.setSpacing(spacing)
        
        if margins:
            layout.setContentsMargins(*margins)
        
        return layout
    
    @staticmethod
    def create_hbox_layout(spacing: int = 6, 
                          margins: Tuple[int, int, int, int] = None) -> QHBoxLayout:
        """创建水平布局
        
        Args:
            spacing: 间距
            margins: 边距 (left, top, right, bottom)
            
        Returns:
            QHBoxLayout: 水平布局
        """
        layout = QHBoxLayout()
        layout.setSpacing(spacing)
        
        if margins:
            layout.setContentsMargins(*margins)
        
        return layout
    
    @staticmethod
    def create_grid_layout(spacing: int = 6, 
                          margins: Tuple[int, int, int, int] = None) -> QGridLayout:
        """创建网格布局
        
        Args:
            spacing: 间距
            margins: 边距 (left, top, right, bottom)
            
        Returns:
            QGridLayout: 网格布局
        """
        layout = QGridLayout()
        layout.setSpacing(spacing)
        
        if margins:
            layout.setContentsMargins(*margins)
        
        return layout
    
    @staticmethod
    def add_stretch_to_layout(layout, stretch: int = 1):
        """向布局添加弹性空间
        
        Args:
            layout: 布局对象
            stretch: 拉伸因子
        """
        if isinstance(layout, (QVBoxLayout, QHBoxLayout)):
            layout.addStretch(stretch)
    
    @staticmethod
    def clear_layout(layout):
        """清空布局
        
        Args:
            layout: 布局对象
        """
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
            elif child.layout():
                LayoutHelper.clear_layout(child.layout())