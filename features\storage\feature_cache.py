"""特征缓存管理模块

提供特征数据的内存缓存功能，包括线程安全的缓存操作。
"""

import threading
from typing import Dict, Any, Optional, List, Union
import numpy as np
from pathlib import Path
import pickle
import logging
import lz4.frame
import time
from collections import OrderedDict
import psutil
import gc


class FeatureCache:
    """特征缓存管理器
    
    提供线程安全的特征缓存功能，支持：
    - 特征的存储和检索
    - 缓存大小限制
    - LRU淘汰策略
    - 缓存统计信息
    """
    
    def __init__(self, max_size: int = 50000, max_memory_mb: int = 512, enable_compression: bool = True):
        """初始化特征缓存

        Args:
            max_size: 最大缓存条目数
            max_memory_mb: 最大内存使用量(MB)
            enable_compression: 是否启用压缩
        """
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.enable_compression = enable_compression

        # 使用OrderedDict实现更高效的LRU
        self._cache: OrderedDict[Any, Union[np.ndarray, bytes]] = OrderedDict()
        self._cache_sizes: Dict[Any, int] = {}  # 记录每个条目的内存大小
        self._current_memory = 0

        self._lock = threading.RLock()
        self.logger = logging.getLogger(__name__)

        # 统计信息
        self._hits = 0
        self._misses = 0
        self._evictions = 0
        self._compression_ratio = 0.0
        self._last_cleanup = time.time()
        
    def get(self, key: Any) -> Optional[np.ndarray]:
        """获取缓存的特征

        Args:
            key: 缓存键

        Returns:
            Optional[np.ndarray]: 特征数组，如果不存在返回None
        """
        with self._lock:
            if key in self._cache:
                # 移动到末尾(最近使用)
                value = self._cache.pop(key)
                self._cache[key] = value
                self._hits += 1

                # 解压缩并返回
                if self.enable_compression and isinstance(value, bytes):
                    try:
                        decompressed = lz4.frame.decompress(value)
                        features = np.frombuffer(decompressed, dtype=np.float32)
                        return features.copy()
                    except Exception as e:
                        self.logger.error(f"解压缩特征失败: {e}")
                        return None
                else:
                    return value.copy() if isinstance(value, np.ndarray) else None
            else:
                self._misses += 1
                return None
                
    def put(self, key: Any, features: np.ndarray) -> bool:
        """存储特征到缓存

        Args:
            key: 缓存键
            features: 特征数组

        Returns:
            bool: 是否成功存储
        """
        try:
            if not isinstance(features, np.ndarray):
                self.logger.warning(f"特征类型无效: {type(features)}")
                return False

            if len(features) == 0:
                self.logger.warning("特征数组为空")
                return False

            # 检查特征是否包含NaN或无穷大值
            if np.isnan(features).any() or np.isinf(features).any():
                self.logger.warning("特征包含NaN或无穷大值")
                return False

            with self._lock:
                # 准备存储的数据
                if self.enable_compression:
                    # 压缩特征数据
                    features_bytes = features.astype(np.float32).tobytes()
                    compressed = lz4.frame.compress(features_bytes)
                    store_value = compressed
                    data_size = len(compressed)

                    # 更新压缩比统计
                    original_size = len(features_bytes)
                    if original_size > 0:
                        ratio = len(compressed) / original_size
                        self._compression_ratio = (self._compression_ratio + ratio) / 2
                else:
                    store_value = features.copy()
                    data_size = features.nbytes

                # 如果键已存在，先移除旧数据
                if key in self._cache:
                    old_size = self._cache_sizes.get(key, 0)
                    self._current_memory -= old_size
                    del self._cache[key]
                    del self._cache_sizes[key]

                # 检查内存限制
                while (self._current_memory + data_size > self.max_memory_bytes or
                       len(self._cache) >= self.max_size) and self._cache:
                    self._evict_lru()

                # 添加新条目
                self._cache[key] = store_value
                self._cache_sizes[key] = data_size
                self._current_memory += data_size

                # 定期清理
                self._periodic_cleanup()

                return True

        except Exception as e:
            self.logger.error(f"存储特征到缓存失败: {e}")
            return False
            
    def remove(self, key: Any) -> bool:
        """从缓存中移除特征

        Args:
            key: 缓存键

        Returns:
            bool: 是否成功移除
        """
        with self._lock:
            if key in self._cache:
                size = self._cache_sizes.get(key, 0)
                self._current_memory -= size
                del self._cache[key]
                del self._cache_sizes[key]
                return True
            return False
            
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._cache_sizes.clear()
            self._current_memory = 0
            self._hits = 0
            self._misses = 0
            self._evictions = 0

    def _periodic_cleanup(self) -> None:
        """定期清理和优化"""
        current_time = time.time()
        if current_time - self._last_cleanup > 300:  # 5分钟清理一次
            self._last_cleanup = current_time

            # 强制垃圾回收
            gc.collect()

            # 检查内存使用情况
            try:
                process = psutil.Process()
                memory_info = process.memory_info()
                self.logger.debug(f"缓存内存使用: {self._current_memory / 1024 / 1024:.1f}MB, "
                                f"进程内存: {memory_info.rss / 1024 / 1024:.1f}MB")
            except:
                pass  # 忽略psutil错误

    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用统计

        Returns:
            Dict[str, Any]: 内存使用信息
        """
        with self._lock:
            return {
                'current_memory_mb': self._current_memory / 1024 / 1024,
                'max_memory_mb': self.max_memory_bytes / 1024 / 1024,
                'memory_usage_percent': (self._current_memory / self.max_memory_bytes) * 100,
                'cache_size': len(self._cache),
                'max_size': self.max_size,
                'compression_ratio': self._compression_ratio,
                'hits': self._hits,
                'misses': self._misses,
                'hit_rate': self._hits / (self._hits + self._misses) if (self._hits + self._misses) > 0 else 0,
                'evictions': self._evictions
            }

    def optimize_memory(self) -> None:
        """优化内存使用"""
        with self._lock:
            # 如果内存使用超过80%，主动清理
            if self._current_memory > self.max_memory_bytes * 0.8:
                target_size = int(len(self._cache) * 0.7)  # 清理到70%
                while len(self._cache) > target_size:
                    self._evict_lru()

                # 强制垃圾回收
                gc.collect()

                self.logger.info(f"内存优化完成，缓存大小: {len(self._cache)}, "
                               f"内存使用: {self._current_memory / 1024 / 1024:.1f}MB")
            
    def _evict_lru(self) -> None:
        """淘汰最近最少使用的条目"""
        if self._cache:
            # OrderedDict的第一个元素是最旧的
            lru_key, _ = self._cache.popitem(last=False)
            size = self._cache_sizes.pop(lru_key, 0)
            self._current_memory -= size
            self._evictions += 1
            
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)
        
    def hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self._hits + self._misses
        return self._hits / total if total > 0 else 0.0
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hits': self._hits,
                'misses': self._misses,
                'hit_rate': self.hit_rate(),
                'memory_usage_mb': self._estimate_memory_usage()
            }
            
    def _estimate_memory_usage(self) -> float:
        """估算内存使用量（MB）"""
        total_bytes = 0
        for features in self._cache.values():
            total_bytes += features.nbytes
        return total_bytes / (1024 * 1024)
        
    def save_to_disk(self, file_path: Path) -> bool:
        """将缓存保存到磁盘
        
        Args:
            file_path: 保存路径
            
        Returns:
            bool: 是否成功保存
        """
        try:
            with self._lock:
                cache_data = {
                    'cache': dict(self._cache),
                    'access_order': list(self._access_order),
                    'hits': self._hits,
                    'misses': self._misses
                }
                
            file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(file_path, 'wb') as f:
                pickle.dump(cache_data, f)
                
            self.logger.info(f"缓存保存成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
            return False
            
    def load_from_disk(self, file_path: Path) -> bool:
        """从磁盘加载缓存
        
        Args:
            file_path: 缓存文件路径
            
        Returns:
            bool: 是否成功加载
        """
        try:
            if not file_path.exists():
                self.logger.warning(f"缓存文件不存在: {file_path}")
                return False
                
            with open(file_path, 'rb') as f:
                cache_data = pickle.load(f)
                
            with self._lock:
                self._cache = cache_data.get('cache', {})
                self._access_order = cache_data.get('access_order', [])
                self._hits = cache_data.get('hits', 0)
                self._misses = cache_data.get('misses', 0)
                
            self.logger.info(f"缓存加载成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载缓存失败: {e}")
            return False
            
    def contains(self, key: Any) -> bool:
        """检查缓存中是否包含指定键
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否包含
        """
        with self._lock:
            return key in self._cache
            
    def keys(self) -> List[Any]:
        """获取所有缓存键
        
        Returns:
            List[Any]: 缓存键列表
        """
        with self._lock:
            return list(self._cache.keys())