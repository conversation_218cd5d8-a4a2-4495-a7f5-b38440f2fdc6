#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像对比面板 (重构版)

该模块提供图像对比功能的用户界面，使用模块化组件。
"""

from typing import Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QSplitter, QFrame
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QWheelEvent

from utils.log_utils import LoggerMixin
from gui.helpers.message_helper import MessageHelper
from gui.image_viewer import ImageViewer
from .compare_config import CompareConfig
from .compare_controller import CompareController
from .compare_toolbar import CompareToolbar
from .image_info_widget import ImageInfoWidget


class ImageComparePanel(QDialog, LoggerMixin):
    """图像对比面板 (重构版)"""
    
    def __init__(self, parent=None, config: Optional[CompareConfig] = None):
        super().__init__(parent)
        
        # 配置
        self.config = config or CompareConfig()
        
        # 控制器
        self.controller = CompareController(self.config)
        
        # 图像路径
        self.image1_path = None
        self.image2_path = None
        
        self.setup_ui()
        self.setup_connections()
        self.setup_window()
    
    def setup_ui(self):
        """设置界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 工具栏
        self.toolbar = CompareToolbar(self.config)
        main_layout.addWidget(self.toolbar)
        
        # 图像信息显示
        self.info_widget = ImageInfoWidget()
        main_layout.addWidget(self.info_widget)
        
        # 图像查看区域
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 图像1查看器
        self.image1_viewer = ImageViewer()
        self.image1_viewer.setMinimumSize(400, 400)
        self.splitter.addWidget(self.image1_viewer)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.VLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        self.splitter.addWidget(line)
        
        # 图像2查看器
        self.image2_viewer = ImageViewer()
        self.image2_viewer.setMinimumSize(400, 400)
        self.splitter.addWidget(self.image2_viewer)
        
        # 设置分割比例
        self.splitter.setSizes([500, 10, 500])
        
        main_layout.addWidget(self.splitter)
    
    def setup_connections(self):
        """设置信号连接"""
        # 工具栏信号
        self.toolbar.zoomInRequested.connect(self.zoom_in)
        self.toolbar.zoomOutRequested.connect(self.zoom_out)
        self.toolbar.fitToWindowRequested.connect(self.fit_to_window)
        self.toolbar.actualSizeRequested.connect(self.actual_size)
        self.toolbar.resetViewRequested.connect(self.reset_views)
        self.toolbar.syncZoomToggled.connect(self.controller.set_sync_zoom)
        self.toolbar.syncPanToggled.connect(self.controller.set_sync_pan)
        self.toolbar.closeRequested.connect(self.close)
        
        # 控制器信号
        self.controller.imageInfoUpdated.connect(self.info_widget.update_image_info)
        self.controller.zoomSyncRequested.connect(self.sync_zoom)
        
        # 图像查看器信号
        self.image1_viewer.zoomChanged.connect(
            lambda zoom: self.controller.on_zoom_changed(0, zoom)
        )
        self.image2_viewer.zoomChanged.connect(
            lambda zoom: self.controller.on_zoom_changed(1, zoom)
        )
        
        # 注册缩放回调
        self.controller.register_zoom_callback(0, self.image1_viewer.set_zoom_factor)
        self.controller.register_zoom_callback(1, self.image2_viewer.set_zoom_factor)
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("图像对比")
        self.resize(self.config.window_width, self.config.window_height)
        
        if self.config.maximized:
            self.showMaximized()
    
    def set_images(self, image1_path: str, image2_path: str):
        """设置要对比的图像
        
        Args:
            image1_path: 第一个图像路径
            image2_path: 第二个图像路径
        """
        try:
            self.image1_path = image1_path
            self.image2_path = image2_path
            
            # 通过控制器设置图像
            if not self.controller.set_images(image1_path, image2_path):
                MessageHelper.show_warning(self, "警告", "部分图像加载失败，请检查图像路径是否正确")
                return
            
            # 加载图像到查看器
            success1 = self.image1_viewer.load_image(image1_path)
            success2 = self.image2_viewer.load_image(image2_path)
            
            if not success1 or not success2:
                self.logger.warning(f"加载图像失败: {image1_path} 或 {image2_path}")
                MessageHelper.show_warning(self, "警告", "部分图像加载失败，请检查图像路径是否正确")
            
            # 自动适应窗口
            if self.config.auto_fit:
                QTimer.singleShot(100, self.fit_to_window)
            
            self.logger.info(f"加载对比图像: {image1_path} vs {image2_path}")
            
        except Exception as e:
            self.logger.error(f"设置对比图像失败: {e}")
            MessageHelper.show_error(self, "错误", f"设置对比图像失败: {e}")
    
    def sync_zoom(self, target_index: int, zoom_factor: float):
        """同步缩放
        
        Args:
            target_index: 目标查看器索引
            zoom_factor: 缩放因子
        """
        try:
            if target_index == 0:
                self.image1_viewer.set_zoom_factor(zoom_factor)
            elif target_index == 1:
                self.image2_viewer.set_zoom_factor(zoom_factor)
        except Exception as e:
            self.logger.error(f"同步缩放失败: {e}")
    
    def zoom_in(self):
        """放大图像"""
        try:
            self.image1_viewer.zoom_widget.zoom_in()
            if not self.toolbar.is_sync_zoom_enabled():
                self.image2_viewer.zoom_widget.zoom_in()
            self.logger.info("放大图像")
        except Exception as e:
            self.logger.error(f"放大图像失败: {e}")
    
    def zoom_out(self):
        """缩小图像"""
        try:
            self.image1_viewer.zoom_widget.zoom_out()
            if not self.toolbar.is_sync_zoom_enabled():
                self.image2_viewer.zoom_widget.zoom_out()
            self.logger.info("缩小图像")
        except Exception as e:
            self.logger.error(f"缩小图像失败: {e}")
    
    def fit_to_window(self):
        """适应窗口"""
        try:
            self.image1_viewer.fit_to_window()
            self.image2_viewer.fit_to_window()
            self.logger.info("图像适应窗口")
        except Exception as e:
            self.logger.error(f"设置图像适应窗口失败: {e}")
    
    def actual_size(self):
        """实际大小"""
        try:
            self.image1_viewer.actual_size()
            self.image2_viewer.actual_size()
            self.logger.info("图像实际大小")
        except Exception as e:
            self.logger.error(f"设置图像实际大小失败: {e}")
    
    def reset_views(self):
        """重置视图"""
        try:
            if self.image1_path and self.image2_path:
                # 重新加载图像
                self.image1_viewer.load_image(self.image1_path)
                self.image2_viewer.load_image(self.image2_path)
                # 适应窗口
                self.fit_to_window()
            else:
                self.image1_viewer.reset_viewer()
                self.image2_viewer.reset_viewer()
            self.logger.info("重置图像视图")
            
        except Exception as e:
            self.logger.error(f"重置视图失败: {e}")
    
    def wheelEvent(self, event: QWheelEvent):
        """鼠标滚轮事件
        
        Args:
            event: 滚轮事件
        """
        try:
            # Ctrl + 滚轮缩放
            if event.modifiers() & Qt.KeyboardModifier.ControlModifier:
                delta = event.angleDelta().y()
                if delta > 0:
                    self.zoom_in()
                else:
                    self.zoom_out()
                event.accept()
            else:
                super().wheelEvent(event)
        except Exception as e:
            self.logger.error(f"处理滚轮事件失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.logger.info("关闭图像对比面板")
        super().closeEvent(event)