{"name": "custom", "display_name": "自定义主题", "theme_type": "custom", "color_scheme": {"primary": "#3498db", "secondary": "#2ecc71", "success": "#27ae60", "warning": "#f39c12", "error": "#e74c3c", "info": "#3498db", "background": "#ffffff", "surface": "#F5F5F5", "card": "#FFFFFF", "text_primary": "#212121", "text_secondary": "#757575", "text_disabled": "#BDBDBD", "border": "#dddddd", "divider": "#EEEEEE", "hover": "#F0F0F0", "selected": "#E3F2FD", "focus": "#BBDEFB"}, "font_scheme": {"family": "<PERSON><PERSON>", "monospace_family": "Consolas, Monaco, 'Courier New', monospace", "size_small": 8, "size_normal": 12, "size_medium": 10, "size_large": 12, "size_xlarge": 14, "size_title": 16, "weight_normal": 400, "weight_medium": 500, "weight_bold": 700}, "stylesheet": "\n/* 全局样式 */\nQWidget {\n    font-family: Arial;\n    font-size: 12px;\n    color: #212121;\n    background-color: #ffffff;\n}\n\n/* 主窗口 */\nQMainWindow {\n    background-color: #ffffff;\n}\n\n/* 按钮 */\nQPushButton {\n    background-color: #3498db;\n    color: white;\n    border: none;\n    padding: 6px 12px;\n    border-radius: 4px;\n    font-weight: 500;\n}\n\nQPushButton:hover {\n    background-color: #2e88c5;\n}\n\nQPushButton:pressed {\n    background-color: #2979af;\n}\n\nQPushButton:disabled {\n    background-color: #BDBDBD;\n    color: #757575;\n}\n\n/* 输入框 */\nQLineEdit, QTextEdit, QPlainTextEdit {\n    background-color: #FFFFFF;\n    border: 1px solid #dddddd;\n    border-radius: 4px;\n    padding: 6px;\n    selection-background-color: #E3F2FD;\n}\n\nQLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {\n    border-color: #3498db;\n}\n\n/* 列表和树 */\nQListWidget, QTreeWidget, QTableWidget {\n    background-color: #FFFFFF;\n    border: 1px solid #dddddd;\n    selection-background-color: #E3F2FD;\n    alternate-background-color: #F5F5F5;\n}\n\nQListWidget::item, QTreeWidget::item, QTableWidget::item {\n    padding: 4px;\n    border-bottom: 1px solid #EEEEEE;\n}\n\nQListWidget::item:hover, QTreeWidget::item:hover, QTableWidget::item:hover {\n    background-color: #F0F0F0;\n}\n\nQListWidget::item:selected, QTreeWidget::item:selected, QTableWidget::item:selected {\n    background-color: #E3F2FD;\n}\n\n/* 标签页 */\nQTabWidget::pane {\n    border: 1px solid #dddddd;\n    background-color: #FFFFFF;\n}\n\nQTabBar::tab {\n    background-color: #F5F5F5;\n    border: 1px solid #dddddd;\n    padding: 6px 12px;\n    margin-right: 2px;\n}\n\nQTabBar::tab:selected {\n    background-color: #FFFFFF;\n    border-bottom-color: #FFFFFF;\n}\n\nQTabBar::tab:hover {\n    background-color: #F0F0F0;\n}\n\n/* 滚动条 */\nQScrollBar:vertical {\n    background-color: #F5F5F5;\n    width: 12px;\n    border-radius: 6px;\n}\n\nQScrollBar::handle:vertical {\n    background-color: #BDBDBD;\n    border-radius: 6px;\n    min-height: 20px;\n}\n\nQScrollBar::handle:vertical:hover {\n    background-color: #757575;\n}\n\nQScrollBar:horizontal {\n    background-color: #F5F5F5;\n    height: 12px;\n    border-radius: 6px;\n}\n\nQScrollBar::handle:horizontal {\n    background-color: #BDBDBD;\n    border-radius: 6px;\n    min-width: 20px;\n}\n\nQScrollBar::handle:horizontal:hover {\n    background-color: #757575;\n}\n\n/* 菜单 */\nQMenuBar {\n    background-color: #F5F5F5;\n    border-bottom: 1px solid #dddddd;\n}\n\nQMenuBar::item {\n    padding: 6px 12px;\n}\n\nQMenuBar::item:selected {\n    background-color: #F0F0F0;\n}\n\nQMenu {\n    background-color: #FFFFFF;\n    border: 1px solid #dddddd;\n}\n\nQMenu::item {\n    padding: 6px 12px;\n}\n\nQMenu::item:selected {\n    background-color: #E3F2FD;\n}\n\n/* 工具栏 */\nQToolBar {\n    background-color: #F5F5F5;\n    border: 1px solid #dddddd;\n    spacing: 2px;\n}\n\nQToolButton {\n    background-color: transparent;\n    border: none;\n    padding: 4px;\n    border-radius: 4px;\n}\n\nQToolButton:hover {\n    background-color: #F0F0F0;\n}\n\nQToolButton:pressed {\n    background-color: #E3F2FD;\n}\n\n/* 状态栏 */\nQStatusBar {\n    background-color: #F5F5F5;\n    border-top: 1px solid #dddddd;\n}\n\n/* 分组框 */\nQGroupBox {\n    font-weight: 500;\n    border: 1px solid #dddddd;\n    border-radius: 4px;\n    margin-top: 8px;\n    padding-top: 8px;\n}\n\nQGroupBox::title {\n    subcontrol-origin: margin;\n    left: 8px;\n    padding: 0 4px 0 4px;\n}\n\n/* 进度条 */\nQProgressBar {\n    border: 1px solid #dddddd;\n    border-radius: 4px;\n    text-align: center;\n    background-color: #F5F5F5;\n}\n\nQProgressBar::chunk {\n    background-color: #3498db;\n    border-radius: 3px;\n}\n\n/* 滑块 */\nQSlider::groove:horizontal {\n    border: 1px solid #dddddd;\n    height: 6px;\n    background-color: #F5F5F5;\n    border-radius: 3px;\n}\n\nQSlider::handle:horizontal {\n    background-color: #3498db;\n    border: 1px solid #dddddd;\n    width: 16px;\n    margin: -6px 0;\n    border-radius: 8px;\n}\n\n/* 复选框和单选框 */\nQCheckBox, QRadioButton {\n    spacing: 6px;\n}\n\nQCheckBox::indicator, QRadioButton::indicator {\n    width: 16px;\n    height: 16px;\n}\n\nQCheckBox::indicator:unchecked {\n    border: 1px solid #dddddd;\n    background-color: #FFFFFF;\n    border-radius: 2px;\n}\n\nQCheckBox::indicator:checked {\n    border: 1px solid #3498db;\n    background-color: #3498db;\n    border-radius: 2px;\n}\n\nQRadioButton::indicator:unchecked {\n    border: 1px solid #dddddd;\n    background-color: #FFFFFF;\n    border-radius: 8px;\n}\n\nQRadioButton::indicator:checked {\n    border: 1px solid #3498db;\n    background-color: #3498db;\n    border-radius: 8px;\n}\n\n/* 组合框 */\nQComboBox {\n    background-color: #FFFFFF;\n    border: 1px solid #dddddd;\n    border-radius: 4px;\n    padding: 6px;\n    min-width: 100px;\n}\n\nQComboBox:hover {\n    border-color: #3498db;\n}\n\nQComboBox::drop-down {\n    border: none;\n    width: 20px;\n}\n\nQComboBox::down-arrow {\n    width: 12px;\n    height: 12px;\n}\n\nQComboBox QAbstractItemView {\n    background-color: #FFFFFF;\n    border: 1px solid #dddddd;\n    selection-background-color: #E3F2FD;\n}\n\n/* 分隔线 */\nQFrame[frameShape=\"4\"], QFrame[frameShape=\"5\"] {\n    color: #EEEEEE;\n}\n", "custom_properties": {}}