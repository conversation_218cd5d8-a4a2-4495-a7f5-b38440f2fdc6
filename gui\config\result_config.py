#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果配置模块

该模块定义了结果面板的配置类和枚举。
"""

from enum import Enum
from dataclasses import dataclass


class ViewMode(Enum):
    """视图模式"""
    GRID = "grid"
    LIST = "list"


class SortMode(Enum):
    """排序模式"""
    RELEVANCE = "relevance"
    DATE_DESC = "date_desc"
    DATE_ASC = "date_asc"
    SIZE_DESC = "size_desc"
    SIZE_ASC = "size_asc"
    NAME_ASC = "name_asc"
    NAME_DESC = "name_desc"


@dataclass
class ResultConfig:
    """结果配置"""
    view_mode: ViewMode = ViewMode.GRID
    sort_mode: SortMode = SortMode.RELEVANCE
    thumbnail_size: int = 150
    items_per_page: int = 25
    show_details: bool = True
    show_similarity_scores: bool = True
    
    def __post_init__(self):
        """初始化后检查参数有效性"""
        # 确保items_per_page不为0或负数
        if self.items_per_page <= 0:
            self.items_per_page = 25