#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型选择器组件

该模块提供模型选择的UI组件。
"""

from typing import Dict, List, Optional, Callable
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QRadioButton, QButtonGroup, QFrame, QLabel
)
from PyQt6.QtCore import Qt, pyqtSignal, QObject

from utils.log_utils import LoggerMixin
from config.model_config import ModelConfigManager
from gui.widgets.widget_factory import WidgetFactory
from .model_config import ModelType, ModelDisplayInfo, ModelSelectorConfig


class ModelSelector(QWidget, LoggerMixin):
    """模型选择器组件"""
    
    # 信号
    modelChanged = pyqtSignal(str)  # 模型名称
    
    def __init__(self, config: ModelSelectorConfig = None, parent=None):
        super().__init__(parent)
        
        self.config = config or ModelSelectorConfig()
        self.widget_factory = WidgetFactory()
        self.model_config_manager = ModelConfigManager()
        
        # 当前选择的模型
        self.current_model = self.config.default_model
        
        # UI组件
        self.model_buttons = None
        self.model_grid = None
        
        self.setup_ui()
        self.load_models()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # 模型选择组
        model_group = self.widget_factory.create_group_box("可用模型", "grid")
        self.model_grid = model_group.layout()
        layout.addWidget(model_group)
    
    def load_models(self):
        """加载模型列表"""
        try:
            # 获取可用模型
            available_models = self.model_config_manager.get_available_models()
            
            # 创建模型选择按钮
            self.model_buttons = QButtonGroup(self)
            self.model_buttons.setExclusive(True)  # 单选
            
            row = 0
            for model_type, models in self.config.model_groups.items():
                # 添加类型标签
                type_label = self.widget_factory.create_label(model_type.value + ":", bold=True)
                self.model_grid.addWidget(type_label, row, 0, 1, self.config.models_per_row)
                row += 1
                
                # 添加该类型的模型
                col = 0
                for model_name in models:
                    if model_name in available_models:
                        model_config = self.model_config_manager.get_config(model_name)
                        if model_config:
                            radio = QRadioButton(model_config.name)
                            radio.setProperty("model_name", model_name)
                            radio.setChecked(model_name == self.current_model)
                            radio.toggled.connect(self.on_model_selected)
                            
                            self.model_buttons.addButton(radio)
                            self.model_grid.addWidget(radio, row, col)
                            
                            col += 1
                            if col >= self.config.models_per_row:
                                col = 0
                                row += 1
                
                if col > 0:  # 如果该行有模型，移到下一行
                    row += 1
                
                # 添加分隔行
                separator = QFrame()
                separator.setFrameShape(QFrame.Shape.HLine)
                separator.setFrameShadow(QFrame.Shadow.Sunken)
                self.model_grid.addWidget(separator, row, 0, 1, self.config.models_per_row)
                row += 1
            
        except Exception as e:
            self.logger.error(f"加载模型列表失败: {e}")
    
    def on_model_selected(self, checked):
        """模型选择变更"""
        if checked:
            sender = self.sender()
            model_name = sender.property("model_name")
            if model_name:
                self.current_model = model_name
                self.modelChanged.emit(model_name)
    
    def get_selected_model(self) -> str:
        """获取选择的模型名称"""
        return self.current_model
    
    def set_selected_model(self, model_name: str):
        """设置选择的模型"""
        if self.model_buttons:
            for button in self.model_buttons.buttons():
                if button.property("model_name") == model_name:
                    button.setChecked(True)
                    self.current_model = model_name
                    break