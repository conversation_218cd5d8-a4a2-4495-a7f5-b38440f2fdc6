#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相似度计算调试脚本

用于调试和验证相似度计算、权重应用和结果排序的正确性
"""

import numpy as np
import logging
from typing import Dict, List
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from search.search_strategies import WeightedSearchStrategy, FeatureWeights
from database.models import FabricImage

# 简单的相似度计算器
class SimpleSimilarityCalculator:
    def __init__(self):
        pass

    def calculate_similarity(self, query_features, database_features, top_k=10):
        """简单的相似度计算"""
        from collections import namedtuple

        # 计算余弦相似度
        query_norm = np.linalg.norm(query_features)
        similarities = []

        for i, db_feature in enumerate(database_features):
            db_norm = np.linalg.norm(db_feature)
            if query_norm > 0 and db_norm > 0:
                similarity = np.dot(query_features.flatten(), db_feature.flatten()) / (query_norm * db_norm)
            else:
                similarity = 0.0
            similarities.append((i, similarity))

        # 排序并返回top_k
        similarities.sort(key=lambda x: x[1], reverse=True)

        # 创建简单的响应对象
        Response = namedtuple('Response', ['similar_items'])
        return Response(similar_items=similarities[:top_k])

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_mock_data():
    """创建模拟数据用于测试"""
    
    # 创建模拟的查询特征
    query_features = {
        'color': np.array([0.8, 0.2, 0.1, 0.3]),  # 查询图像的颜色特征
        'texture': np.array([0.5, 0.7, 0.3, 0.9]),  # 查询图像的纹理特征
        'shape': np.array([0.2, 0.8, 0.6, 0.4]),  # 查询图像的形状特征
        'deep_learning': np.array([0.9, 0.1, 0.5, 0.7])  # 查询图像的深度学习特征
    }
    
    # 创建模拟的数据库特征（10张图像）
    database_features = {
        'color': [
            np.array([0.9, 0.1, 0.2, 0.4]),  # 图像0 - 颜色很相似
            np.array([0.1, 0.9, 0.8, 0.2]),  # 图像1 - 颜色不相似
            np.array([0.7, 0.3, 0.1, 0.2]),  # 图像2 - 颜色较相似
            np.array([0.2, 0.8, 0.9, 0.1]),  # 图像3 - 颜色不相似
            np.array([0.8, 0.2, 0.0, 0.3]),  # 图像4 - 颜色很相似
            np.array([0.3, 0.7, 0.5, 0.8]),  # 图像5 - 颜色一般
            np.array([0.9, 0.1, 0.1, 0.4]),  # 图像6 - 颜色很相似
            np.array([0.0, 0.9, 0.7, 0.1]),  # 图像7 - 颜色不相似
            np.array([0.8, 0.3, 0.2, 0.3]),  # 图像8 - 颜色较相似
            np.array([0.1, 0.1, 0.9, 0.8]),  # 图像9 - 颜色不相似
        ],
        'texture': [
            np.array([0.4, 0.6, 0.2, 0.8]),  # 图像0
            np.array([0.6, 0.8, 0.4, 0.9]),  # 图像1 - 纹理相似
            np.array([0.3, 0.5, 0.1, 0.7]),  # 图像2
            np.array([0.5, 0.7, 0.3, 0.8]),  # 图像3 - 纹理很相似
            np.array([0.2, 0.4, 0.6, 0.5]),  # 图像4
            np.array([0.7, 0.9, 0.5, 0.8]),  # 图像5 - 纹理相似
            np.array([0.1, 0.3, 0.8, 0.2]),  # 图像6
            np.array([0.4, 0.6, 0.2, 0.9]),  # 图像7 - 纹理相似
            np.array([0.8, 0.2, 0.9, 0.1]),  # 图像8
            np.array([0.5, 0.7, 0.3, 0.9]),  # 图像9 - 纹理很相似
        ],
        'shape': [
            np.array([0.1, 0.7, 0.5, 0.3]),  # 图像0
            np.array([0.3, 0.9, 0.7, 0.5]),  # 图像1 - 形状相似
            np.array([0.0, 0.6, 0.4, 0.2]),  # 图像2
            np.array([0.2, 0.8, 0.6, 0.4]),  # 图像3 - 形状很相似
            np.array([0.4, 0.5, 0.9, 0.7]),  # 图像4
            np.array([0.1, 0.9, 0.5, 0.3]),  # 图像5 - 形状相似
            np.array([0.5, 0.3, 0.8, 0.9]),  # 图像6
            np.array([0.2, 0.7, 0.6, 0.5]),  # 图像7 - 形状相似
            np.array([0.6, 0.2, 0.1, 0.8]),  # 图像8
            np.array([0.3, 0.8, 0.7, 0.4]),  # 图像9 - 形状相似
        ],
        'deep_learning': [
            np.array([0.8, 0.2, 0.4, 0.6]),  # 图像0 - 深度特征相似
            np.array([0.1, 0.9, 0.3, 0.2]),  # 图像1
            np.array([0.9, 0.1, 0.5, 0.7]),  # 图像2 - 深度特征很相似
            np.array([0.2, 0.8, 0.1, 0.9]),  # 图像3
            np.array([0.7, 0.3, 0.6, 0.8]),  # 图像4 - 深度特征相似
            np.array([0.3, 0.7, 0.2, 0.4]),  # 图像5
            np.array([0.9, 0.0, 0.5, 0.6]),  # 图像6 - 深度特征很相似
            np.array([0.1, 0.8, 0.9, 0.3]),  # 图像7
            np.array([0.8, 0.2, 0.4, 0.7]),  # 图像8 - 深度特征相似
            np.array([0.4, 0.6, 0.8, 0.2]),  # 图像9
        ]
    }
    
    # 创建模拟的FabricImage对象
    fabric_images = []
    for i in range(10):
        fabric_image = FabricImage(
            id=i,
            file_name=f"test_image_{i}.jpg",
            file_path=f"/test/images/test_image_{i}.jpg"
        )
        fabric_images.append(fabric_image)
    
    return query_features, database_features, fabric_images

def test_single_feature_search(feature_type: str):
    """测试单特征搜索"""
    logger.info(f"\n{'='*50}")
    logger.info(f"测试单特征搜索: {feature_type}")
    logger.info(f"{'='*50}")
    
    # 创建模拟数据
    query_features, database_features, fabric_images = create_mock_data()
    
    # 创建只有一个特征权重为1的权重配置
    weights = FeatureWeights()
    if feature_type == 'color':
        weights.color_weight = 1.0
        weights.texture_weight = 0.0
        weights.shape_weight = 0.0
        weights.deep_learning_weight = 0.0
    elif feature_type == 'texture':
        weights.color_weight = 0.0
        weights.texture_weight = 1.0
        weights.shape_weight = 0.0
        weights.deep_learning_weight = 0.0
    elif feature_type == 'shape':
        weights.color_weight = 0.0
        weights.texture_weight = 0.0
        weights.shape_weight = 1.0
        weights.deep_learning_weight = 0.0
    elif feature_type == 'deep_learning':
        weights.color_weight = 0.0
        weights.texture_weight = 0.0
        weights.shape_weight = 0.0
        weights.deep_learning_weight = 1.0
    
    # 创建相似度计算器和搜索策略
    similarity_calculator = SimpleSimilarityCalculator()
    strategy = WeightedSearchStrategy(similarity_calculator, weights)
    
    # 执行搜索
    results = strategy.search(
        query_features=query_features,
        database_features=database_features,
        fabric_images=fabric_images,
        top_k=10
    )
    
    # 输出结果
    logger.info(f"搜索结果 (按{feature_type}特征排序):")
    for i, result in enumerate(results):
        feature_score = result.feature_scores.get(feature_type, 0.0)
        logger.info(f"  #{i+1}: 图像{result.fabric_image.id}, "
                   f"总相似度={result.similarity_score:.3f}, "
                   f"{feature_type}相似度={feature_score:.3f}")
    
    return results

def test_weight_changes():
    """测试权重变化对结果的影响"""
    logger.info(f"\n{'='*50}")
    logger.info("测试权重变化对结果的影响")
    logger.info(f"{'='*50}")
    
    # 创建模拟数据
    query_features, database_features, fabric_images = create_mock_data()
    similarity_calculator = SimpleSimilarityCalculator()
    
    # 测试不同的权重配置
    weight_configs = [
        ("均匀权重", FeatureWeights(0.25, 0.25, 0.25, 0.25)),
        ("颜色主导", FeatureWeights(0.7, 0.1, 0.1, 0.1)),
        ("纹理主导", FeatureWeights(0.1, 0.7, 0.1, 0.1)),
        ("形状主导", FeatureWeights(0.1, 0.1, 0.7, 0.1)),
        ("深度学习主导", FeatureWeights(0.1, 0.1, 0.1, 0.7)),
    ]
    
    for config_name, weights in weight_configs:
        logger.info(f"\n--- {config_name} ---")
        logger.info(f"权重配置: {weights.to_dict()}")
        
        strategy = WeightedSearchStrategy(similarity_calculator, weights)
        results = strategy.search(
            query_features=query_features,
            database_features=database_features,
            fabric_images=fabric_images,
            top_k=5
        )
        
        logger.info("前5个结果:")
        for i, result in enumerate(results):
            logger.info(f"  #{i+1}: 图像{result.fabric_image.id}, 相似度={result.similarity_score:.3f}")

if __name__ == "__main__":
    # 测试各个单特征搜索
    for feature_type in ['color', 'texture', 'shape', 'deep_learning']:
        test_single_feature_search(feature_type)
    
    # 测试权重变化
    test_weight_changes()
    
    logger.info(f"\n{'='*50}")
    logger.info("调试测试完成")
    logger.info(f"{'='*50}")
