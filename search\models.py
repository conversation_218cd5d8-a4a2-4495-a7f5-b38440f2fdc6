#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索模型模块

该模块定义搜索引擎使用的数据模型。
"""

from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field

from database.models import FabricImage
from .search_history import SearchType


@dataclass
class SearchQuery:
    """搜索查询"""
    # 基本查询参数
    query_type: SearchType
    user_id: str = "default"
    session_id: Optional[str] = None
    
    # 图片相似度搜索
    query_image_path: Optional[str] = None
    query_image_id: Optional[int] = None
    
    # 文本搜索
    text_query: Optional[str] = None
    search_fields: Optional[List[str]] = None
    
    # 类别和标签搜索
    categories: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    
    # 搜索参数
    top_k: int = 10
    similarity_threshold: float = 0.0
    use_filters: bool = True
    active_filters: Optional[List[str]] = None
    
    # 特征权重参数
    search_strategy: str = "default"  # default, weighted, adaptive
    feature_weights: Dict[str, float] = field(default_factory=dict)  # 特征权重，如 {"deep": 0.4, "color": 0.3, "texture": 0.2, "shape": 0.1}
    
    # 传统特征提取参数
    feature_extraction_params: Optional[Dict[str, Any]] = None
    
    # 排序参数
    sort_by: str = "similarity"  # similarity, date, name, size
    sort_order: str = "desc"  # asc, desc
    
    # 分页参数
    page: int = 1
    page_size: int = 20
    
    # 其他参数
    include_metadata: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SearchResult:
    """搜索结果"""
    # 基本信息
    query: SearchQuery
    success: bool = True
    error_message: Optional[str] = None
    
    # 结果数据
    results: List[Tuple[FabricImage, float]] = field(default_factory=list)  # (image, score)
    total_results: int = 0
    filtered_results: int = 0
    
    # 分页信息
    current_page: int = 1
    total_pages: int = 1
    has_next_page: bool = False
    has_prev_page: bool = False
    
    # 性能信息
    search_time: float = 0.0
    filter_time: float = 0.0
    total_time: float = 0.0
    
    # 统计信息
    statistics: Dict[str, Any] = field(default_factory=dict)
    
    def get_images(self) -> List[FabricImage]:
        """获取图片列表
        
        Returns:
            List[FabricImage]: 图片列表
        """
        return [result[0] for result in self.results]
    
    def get_scores(self) -> List[float]:
        """获取分数列表
        
        Returns:
            List[float]: 分数列表
        """
        return [result[1] for result in self.results]
    
    def get_result_by_id(self, image_id: int) -> Optional[Tuple[FabricImage, float]]:
        """根据ID获取结果
        
        Args:
            image_id: 图片ID
            
        Returns:
            Optional[Tuple[FabricImage, float]]: 结果元组
        """
        for result in self.results:
            if result[0].id == image_id:
                return result
        return None