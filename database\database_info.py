#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库信息查看模块

该模块提供数据库信息查看和统计功能。
"""

import os
import json
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime
import sqlite3

from utils.log_utils import LoggerMixin
from utils.file_utils import FileManager, FileUtils
from database.feature_database import FeatureDatabase
from database.database_compatibility import DatabaseCompatibilityChecker


@dataclass
class DatabaseInfo:
    """数据库基本信息"""
    db_path: str
    db_type: str
    db_size: int
    db_size_formatted: str
    image_count: int
    feature_count: int
    created_time: Optional[str]
    modified_time: Optional[str]
    last_sync_time: Optional[str]
    version: Optional[str]
    model_name: Optional[str]
    model_version: Optional[str]
    feature_dimension: Optional[int]
    feature_type: Optional[str]
    is_healthy: bool
    health_issues: List[str]


@dataclass
class DatabaseStatistics:
    """数据库统计信息"""
    total_images: int
    indexed_images: int
    unindexed_images: int
    feature_types: Dict[str, int]
    file_formats: Dict[str, int]
    size_distribution: Dict[str, int]
    date_distribution: Dict[str, int]
    largest_files: List[Tuple[str, int]]
    recent_files: List[Tuple[str, str]]
    orphaned_features: int
    duplicate_files: int


class DatabaseInfoManager(LoggerMixin):
    """数据库信息管理器"""
    
    def __init__(self, database: FeatureDatabase):
        super().__init__()
        self.database = database
        self.compatibility_checker = DatabaseCompatibilityChecker()
        self.file_manager = FileManager()
    
    def get_database_info(self) -> DatabaseInfo:
        """获取数据库基本信息
        
        Returns:
            DatabaseInfo: 数据库信息
        """
        try:
            db_info = self.database.get_database_info()
            db_path = Path(db_info['db_path'])
            
            # 获取文件信息
            db_size = 0
            created_time = None
            modified_time = None
            
            if db_path.exists():
                if db_path.is_file():
                    file_info = self.file_manager.get_file_info(db_path)
                    db_size = file_info['size']
                    created_time = datetime.fromtimestamp(file_info['created_time']).isoformat()
                    modified_time = datetime.fromtimestamp(file_info['modified_time']).isoformat()
                elif db_path.is_dir():
                    # 计算目录大小
                    db_size = sum(f.stat().st_size for f in db_path.rglob('*') if f.is_file())
                    # 获取最早和最晚的文件时间
                    times = [f.stat().st_mtime for f in db_path.rglob('*') if f.is_file()]
                    if times:
                        created_time = datetime.fromtimestamp(min(times)).isoformat()
                        modified_time = datetime.fromtimestamp(max(times)).isoformat()
            
            # 获取版本信息
            version_info = self._get_version_info(db_path)
            
            # 获取特征统计
            feature_stats = self._get_feature_statistics()
            
            # 健康检查
            health_check = self._perform_health_check()
            
            # 获取最后同步时间
            last_sync_time = self._get_last_sync_time()
            
            return DatabaseInfo(
                db_path=str(db_path),
                db_type=db_info['db_type'],
                db_size=db_size,
                db_size_formatted=FileUtils.format_file_size(db_size),
                image_count=db_info['image_count'],
                feature_count=feature_stats['total_features'],
                created_time=created_time,
                modified_time=modified_time,
                last_sync_time=last_sync_time,
                version=version_info.get('version'),
                model_name=version_info.get('model_name'),
                model_version=version_info.get('model_version'),
                feature_dimension=version_info.get('feature_dimension'),
                feature_type=version_info.get('feature_type'),
                is_healthy=health_check['is_healthy'],
                health_issues=health_check['issues']
            )
            
        except Exception as e:
            self.logger.error(f"获取数据库信息失败: {e}")
            return DatabaseInfo(
                db_path="", db_type="unknown", db_size=0, db_size_formatted="0 B",
                image_count=0, feature_count=0, created_time=None, modified_time=None,
                last_sync_time=None, version=None, model_name=None, model_version=None,
                feature_dimension=None, feature_type=None, is_healthy=False,
                health_issues=[f"获取数据库信息失败: {e}"]
            )
    
    def get_database_statistics(self) -> DatabaseStatistics:
        """获取数据库统计信息
        
        Returns:
            DatabaseStatistics: 统计信息
        """
        try:
            image_paths = self.database.get_image_paths()
            total_images = len(image_paths)
            
            # 统计已索引和未索引的图像
            indexed_images = 0
            unindexed_images = 0
            feature_types = {}
            file_formats = {}
            size_distribution = {'<1MB': 0, '1-10MB': 0, '10-100MB': 0, '>100MB': 0}
            date_distribution = {}
            largest_files = []
            recent_files = []
            
            for image_path in image_paths:
                try:
                    # 检查是否有特征
                    features = self.database.get_features(image_path)
                    if features:
                        indexed_images += 1
                        
                        # 统计特征类型
                        for feature_name in features.keys():
                            feature_types[feature_name] = feature_types.get(feature_name, 0) + 1
                    else:
                        unindexed_images += 1
                    
                    # 文件信息统计
                    file_path = Path(image_path)
                    if file_path.exists():
                        # 文件格式统计
                        file_ext = file_path.suffix.lower()
                        file_formats[file_ext] = file_formats.get(file_ext, 0) + 1
                        
                        # 文件大小统计
                        file_size = file_path.stat().st_size
                        if file_size < 1024 * 1024:  # <1MB
                            size_distribution['<1MB'] += 1
                        elif file_size < 10 * 1024 * 1024:  # 1-10MB
                            size_distribution['1-10MB'] += 1
                        elif file_size < 100 * 1024 * 1024:  # 10-100MB
                            size_distribution['10-100MB'] += 1
                        else:  # >100MB
                            size_distribution['>100MB'] += 1
                        
                        # 记录大文件
                        largest_files.append((image_path, file_size))
                        
                        # 文件日期统计
                        file_date = datetime.fromtimestamp(file_path.stat().st_mtime).strftime('%Y-%m')
                        date_distribution[file_date] = date_distribution.get(file_date, 0) + 1
                        
                        # 记录最近文件
                        recent_files.append((image_path, file_date))
                
                except Exception as e:
                    self.logger.warning(f"处理文件统计失败 {image_path}: {e}")
            
            # 排序和限制结果
            largest_files.sort(key=lambda x: x[1], reverse=True)
            largest_files = largest_files[:10]  # 只保留前10个最大文件
            
            recent_files.sort(key=lambda x: x[1], reverse=True)
            recent_files = recent_files[:10]  # 只保留前10个最新文件
            
            # 检查孤立特征和重复文件
            orphaned_features = self._count_orphaned_features()
            duplicate_files = self._count_duplicate_files()
            
            return DatabaseStatistics(
                total_images=total_images,
                indexed_images=indexed_images,
                unindexed_images=unindexed_images,
                feature_types=feature_types,
                file_formats=file_formats,
                size_distribution=size_distribution,
                date_distribution=date_distribution,
                largest_files=largest_files,
                recent_files=recent_files,
                orphaned_features=orphaned_features,
                duplicate_files=duplicate_files
            )
            
        except Exception as e:
            self.logger.error(f"获取数据库统计信息失败: {e}")
            return DatabaseStatistics(
                total_images=0, indexed_images=0, unindexed_images=0,
                feature_types={}, file_formats={}, size_distribution={},
                date_distribution={}, largest_files=[], recent_files=[],
                orphaned_features=0, duplicate_files=0
            )
    
    def export_database_report(self, output_path: Path) -> bool:
        """导出数据库报告
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            bool: 导出是否成功
        """
        try:
            # 获取数据库信息和统计
            db_info = self.get_database_info()
            db_stats = self.get_database_statistics()
            
            # 生成报告
            report = {
                'generated_at': datetime.now().isoformat(),
                'database_info': asdict(db_info),
                'statistics': asdict(db_stats)
            }
            
            # 保存报告
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if output_path.suffix.lower() == '.json':
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2)
            else:
                # 生成文本报告
                self._generate_text_report(report, output_path)
            
            self.logger.info(f"数据库报告导出成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出数据库报告失败: {e}")
            return False
    
    def _get_version_info(self, db_path: Path) -> Dict[str, Any]:
        """获取版本信息"""
        try:
            version_path = self.compatibility_checker._get_version_file_path(db_path)
            
            if version_path.exists():
                with open(version_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
        except Exception as e:
            self.logger.warning(f"获取版本信息失败: {e}")
        
        return {}
    
    def _get_feature_statistics(self) -> Dict[str, Any]:
        """获取特征统计信息"""
        try:
            all_features = self.database.get_all_features()
            
            total_features = 0
            feature_types = set()
            
            for image_features in all_features.values():
                if image_features:
                    total_features += len(image_features)
                    feature_types.update(image_features.keys())
            
            return {
                'total_features': total_features,
                'feature_types': list(feature_types)
            }
            
        except Exception as e:
            self.logger.warning(f"获取特征统计失败: {e}")
            return {'total_features': 0, 'feature_types': []}
    
    def _perform_health_check(self) -> Dict[str, Any]:
        """执行健康检查"""
        issues = []
        
        try:
            # 检查数据库文件是否存在
            db_info = self.database.get_database_info()
            db_path = Path(db_info['db_path'])
            
            if not db_path.exists():
                issues.append("数据库文件不存在")
            
            # 检查数据一致性
            image_paths = self.database.get_image_paths()
            missing_files = []
            
            for image_path in image_paths[:100]:  # 只检查前100个文件
                if not Path(image_path).exists():
                    missing_files.append(image_path)
            
            if missing_files:
                issues.append(f"发现 {len(missing_files)} 个缺失文件")
            
            # 检查特征完整性
            incomplete_features = 0
            for image_path in image_paths[:100]:  # 只检查前100个文件
                features = self.database.get_features(image_path)
                if not features:
                    incomplete_features += 1
            
            if incomplete_features > 0:
                issues.append(f"发现 {incomplete_features} 个文件缺少特征")
            
        except Exception as e:
            issues.append(f"健康检查失败: {e}")
        
        return {
            'is_healthy': len(issues) == 0,
            'issues': issues
        }
    
    def _get_last_sync_time(self) -> Optional[str]:
        """获取最后同步时间"""
        try:
            # 尝试从同步状态文件获取
            db_info = self.database.get_database_info()
            db_path = Path(db_info['db_path'])
            
            sync_state_file = db_path.parent / 'sync_state.json'
            if sync_state_file.exists():
                with open(sync_state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    last_update = data.get('last_update')
                    if last_update:
                        return datetime.fromtimestamp(last_update).isoformat()
            
        except Exception as e:
            self.logger.warning(f"获取最后同步时间失败: {e}")
        
        return None
    
    def _count_orphaned_features(self) -> int:
        """统计孤立特征数量"""
        try:
            orphaned_count = 0
            image_paths = self.database.get_image_paths()
            
            for image_path in image_paths:
                if not Path(image_path).exists():
                    orphaned_count += 1
            
            return orphaned_count
            
        except Exception as e:
            self.logger.warning(f"统计孤立特征失败: {e}")
            return 0
    
    def _count_duplicate_files(self) -> int:
        """统计重复文件数量"""
        try:
            # 这里可以实现基于文件哈希的重复检测
            # 暂时返回0，实际实现需要计算文件哈希
            return 0
            
        except Exception as e:
            self.logger.warning(f"统计重复文件失败: {e}")
            return 0
    
    def _generate_text_report(self, report: Dict[str, Any], output_path: Path):
        """生成文本格式报告"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("数据库信息报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 基本信息
            db_info = report['database_info']
            f.write("基本信息:\n")
            f.write(f"  数据库路径: {db_info['db_path']}\n")
            f.write(f"  数据库类型: {db_info['db_type']}\n")
            f.write(f"  数据库大小: {db_info['db_size_formatted']}\n")
            f.write(f"  图像数量: {db_info['image_count']}\n")
            f.write(f"  特征数量: {db_info['feature_count']}\n")
            f.write(f"  版本: {db_info['version'] or 'N/A'}\n")
            f.write(f"  模型: {db_info['model_name'] or 'N/A'}\n")
            f.write(f"  健康状态: {'正常' if db_info['is_healthy'] else '异常'}\n\n")
            
            # 统计信息
            stats = report['statistics']
            f.write("统计信息:\n")
            f.write(f"  总图像数: {stats['total_images']}\n")
            f.write(f"  已索引: {stats['indexed_images']}\n")
            f.write(f"  未索引: {stats['unindexed_images']}\n")
            f.write(f"  孤立特征: {stats['orphaned_features']}\n")
            f.write(f"  重复文件: {stats['duplicate_files']}\n\n")
            
            # 文件格式分布
            if stats['file_formats']:
                f.write("文件格式分布:\n")
                for fmt, count in stats['file_formats'].items():
                    f.write(f"  {fmt}: {count}\n")
                f.write("\n")
            
            # 健康问题
            if db_info['health_issues']:
                f.write("健康问题:\n")
                for issue in db_info['health_issues']:
                    f.write(f"  - {issue}\n")
                f.write("\n")
            
            f.write(f"报告生成时间: {report['generated_at']}\n")