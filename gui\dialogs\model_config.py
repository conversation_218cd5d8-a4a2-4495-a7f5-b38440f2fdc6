#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型选择对话框配置

该模块定义了模型选择对话框的配置类和数据结构。
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
from enum import Enum


class ModelType(Enum):
    """模型类型枚举"""
    CNN = "CNN"
    TRANSFORMER = "Transformer"


@dataclass
class ModelDisplayInfo:
    """模型显示信息"""
    name: str
    model_name: str
    type: ModelType
    description: str
    architecture: str
    input_size: tuple
    feature_dim: int
    is_available: bool = True


@dataclass
class ModelSelectorConfig:
    """模型选择器配置"""
    default_model: str = "resnet50"
    use_gpu_default: bool = True
    models_per_row: int = 3
    
    # 模型分组配置
    model_groups: Dict[ModelType, List[str]] = None
    
    def __post_init__(self):
        if self.model_groups is None:
            self.model_groups = {
                ModelType.CNN: ["resnet50", "vgg16", "efficientnet", "efficientnetv2", "convnext", "convnextv2"],
                ModelType.TRANSFORMER: ["vit", "swin"]
            }