import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication, QFileDialog, QMessageBox
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtTest import QTest
from PyQt5.QtGui import QPixmap

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from ui.main_window import MainWindow
from models.feature_extractor import FeatureExtractor
from database.feature_database import FeatureDatabase

class TestMainWindow:
    """主窗口功能测试"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def main_window(self, app):
        """创建主窗口实例"""
        window = MainWindow()
        yield window
        window.close()
    
    def test_window_initialization(self, main_window):
        """测试窗口初始化"""
        assert main_window.windowTitle() == "布料图片相似度搜索系统"
        assert main_window.isVisible() == False
        assert hasattr(main_window, 'search_panel')
        assert hasattr(main_window, 'result_display')
        
    def test_window_show_hide(self, main_window):
        """测试窗口显示隐藏"""
        main_window.show()
        assert main_window.isVisible() == True
        main_window.hide()
        assert main_window.isVisible() == False
        
    @patch('PyQt5.QtWidgets.QFileDialog.getOpenFileName')
    def test_load_query_image(self, mock_dialog, main_window):
        """测试加载查询图像"""
        # 模拟文件选择对话框
        mock_dialog.return_value = ('test_image.jpg', 'Image Files (*.png *.jpg *.jpeg)')
        
        # 模拟点击加载图像按钮
        main_window.search_panel.load_image_btn.click()
        
        # 验证图像路径设置
        assert main_window.search_panel.query_image_path == 'test_image.jpg'
        
    def test_search_configuration(self, main_window):
        """测试搜索配置"""
        search_panel = main_window.search_panel
        
        # 测试搜索模式设置
        search_panel.search_mode_combo.setCurrentText("混合搜索")
        assert search_panel.search_mode_combo.currentText() == "混合搜索"
        
        # 测试结果数量设置
        search_panel.top_n_spin.setValue(20)
        assert search_panel.top_n_spin.value() == 20
        
        # 测试特征权重设置
        search_panel.deep_weight_spin.setValue(0.5)
        assert search_panel.deep_weight_spin.value() == 0.5