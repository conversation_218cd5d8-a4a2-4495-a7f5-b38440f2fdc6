#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索相关的数据模型和枚举

该模块定义了搜索功能中使用的数据模型、枚举类型和配置类。
"""

from typing import Optional, Dict, Any
from enum import Enum
from dataclasses import dataclass


class SearchMode(Enum):
    """搜索模式"""
    SIMILARITY = "similarity"  # 相似度搜索
    TEXT = "text"  # 文本搜索
    CATEGORY = "category"  # 类别搜索
    TAG = "tag"  # 标签搜索
    ADVANCED = "advanced"  # 高级搜索


class SortOrder(Enum):
    """排序方式"""
    RELEVANCE = "relevance"  # 相关性
    DATE_DESC = "date_desc"  # 日期降序
    DATE_ASC = "date_asc"  # 日期升序
    SIZE_DESC = "size_desc"  # 大小降序
    SIZE_ASC = "size_asc"  # 大小升序
    NAME_ASC = "name_asc"  # 名称升序
    NAME_DESC = "name_desc"  # 名称降序


@dataclass
class SearchConfig:
    """搜索配置"""
    mode: SearchMode = SearchMode.SIMILARITY
    max_results: int = 100
    similarity_threshold: float = 0.7
    sort_order: SortOrder = SortOrder.RELEVANCE
    enable_filters: bool = True
    save_history: bool = True
    
    # 相似度搜索特有参数
    feature_weights: Optional[Dict[str, float]] = None
    feature_extraction_params: Optional[Dict[str, Any]] = None
    search_strategy: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "mode": self.mode.value,
            "max_results": self.max_results,
            "similarity_threshold": self.similarity_threshold,
            "sort_order": self.sort_order.value,
            "enable_filters": self.enable_filters,
            "save_history": self.save_history
        }
        
        # 添加相似度搜索特有参数
        if self.feature_weights is not None:
            result["feature_weights"] = self.feature_weights
        if self.feature_extraction_params is not None:
            result["feature_extraction_params"] = self.feature_extraction_params
        if self.search_strategy is not None:
            result["search_strategy"] = self.search_strategy
            
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchConfig':
        """从字典创建"""
        return cls(
            mode=SearchMode(data.get("mode", SearchMode.SIMILARITY.value)),
            max_results=data.get("max_results", 100),
            similarity_threshold=data.get("similarity_threshold", 0.7),
            sort_order=SortOrder(data.get("sort_order", SortOrder.RELEVANCE.value)),
            enable_filters=data.get("enable_filters", True),
            save_history=data.get("save_history", True),
            feature_weights=data.get("feature_weights"),
            feature_extraction_params=data.get("feature_extraction_params"),
            search_strategy=data.get("search_strategy")
        )