#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模块

该模块提供数据库连接、操作和管理功能。
重构后的模块结构：
- config: 数据库配置
- core: 核心功能（连接、事务、架构、查询）
- repositories: 数据仓库
- utils: 工具函数
- exceptions: 异常定义
"""

__version__ = "2.0.0"
__author__ = "Fabric Search System"

# 核心数据库管理
from .database_manager import DatabaseManager, get_database_manager, close_database_manager

# 数据模型
from .models import (
    FabricImage, 
    SearchHistory, 
    UserPreference,
    create_tables,
    drop_tables
)

# 布料仓库
from .fabric_repository import FabricRepository
from .fabric_repository import get_fabric_repository, close_fabric_repository

# 搜索仓库
from .repositories.search_history_repository import SearchHistoryRepository

# 仓库模块
try:
    from .repositories import (
        BaseRepository,
        FabricCrud,
        FabricSearchRepository,
        FabricStatisticsRepository,
        ImageFeatureRepository,
        SearchHistoryRepository,
        UserPreferencesRepository
    )
except ImportError:
    # 如果新模块不可用，设置为None
    BaseRepository = None
    FabricCrud = None
    FabricSearchRepository = None
    FabricStatisticsRepository = None
    ImageFeatureRepository = None
    SearchHistoryRepository = None
    UserPreferencesRepository = None

# 配置模块
try:
    from .config import DatabaseConfig
except ImportError:
    DatabaseConfig = None

# 核心模块
try:
    from .core import (
        ConnectionManager,
        TransactionManager,
        SchemaManager,
        QueryExecutor
    )
except ImportError:
    ConnectionManager = None
    TransactionManager = None
    SchemaManager = None
    QueryExecutor = None

# 工具模块
try:
    from .utils import database_utils
except ImportError:
    database_utils = None

# 异常模块
try:
    from .exceptions import database_exceptions
except ImportError:
    database_exceptions = None

# 导出列表
__all__ = [
    # 版本信息
    '__version__',
    '__author__',
    
    # 核心数据库管理
    'DatabaseManager',
    'get_database_manager',
    'close_database_manager',
    
    # 数据库管理器
    
    # 数据模型
    'FabricImage',
    'SearchHistory', 
    'UserPreference',
    'create_tables',
    'drop_tables',
    
    # 布料仓库
    'FabricRepository',
    'get_fabric_repository',
    'close_fabric_repository',
    
    # 搜索历史仓库
    'SearchHistoryRepository',
    
    # 仓库模块
    'BaseRepository',
    'FabricCrud',
    'FabricSearchRepository',
    'FabricStatisticsRepository',
    'ImageFeatureRepository',
    'SearchHistoryRepository',
    'UserPreferencesRepository',
    
    # 配置模块
    'DatabaseConfig',
    
    # 核心模块
    'ConnectionManager',
    'TransactionManager',
    'SchemaManager',
    'QueryExecutor',
    
    # 工具和异常模块
    'database_utils',
    'database_exceptions'
]

# 过滤掉None值
__all__ = [name for name in __all__ if globals().get(name) is not None]