#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建最终的PDF报告

该脚本创建一个包含所有流程图和文档的完整PDF报告
"""

from reportlab.lib.pagesizes import A4, letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.lib import colors
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY
from pathlib import Path
import os

def create_final_pdf_report():
    """创建最终的PDF报告"""
    
    # 输出路径
    output_dir = Path('docs/diagrams')
    output_dir.mkdir(exist_ok=True)
    pdf_path = output_dir / 'Fabric_Search_完整技术文档.pdf'
    
    # 创建PDF文档
    doc = SimpleDocTemplate(str(pdf_path), pagesize=A4,
                           rightMargin=72, leftMargin=72,
                           topMargin=72, bottomMargin=18)
    
    # 获取样式
    styles = getSampleStyleSheet()
    
    # 自定义样式
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=12,
        textColor=colors.darkblue
    )
    
    subheading_style = ParagraphStyle(
        'CustomSubHeading',
        parent=styles['Heading3'],
        fontSize=14,
        spaceAfter=10,
        textColor=colors.darkgreen
    )
    
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=6,
        alignment=TA_JUSTIFY
    )
    
    code_style = ParagraphStyle(
        'Code',
        parent=styles['Normal'],
        fontSize=9,
        fontName='Courier',
        leftIndent=20,
        spaceAfter=6,
        backColor=colors.lightgrey
    )
    
    # 构建文档内容
    story = []
    
    # 标题页
    story.append(Spacer(1, 2*inch))
    story.append(Paragraph("Fabric Search 项目", title_style))
    story.append(Paragraph("GUI参数传递与搜索策略详细技术文档", heading_style))
    story.append(Spacer(1, 0.5*inch))
    story.append(Paragraph("版本: v2.0", normal_style))
    story.append(Paragraph("日期: 2025年8月", normal_style))
    story.append(Spacer(1, 1*inch))
    
    # 目录
    toc_data = [
        ['章节', '内容', '页码'],
        ['第1章', '项目概述', '2'],
        ['第2章', 'GUI参数传递流程', '3'],
        ['第3章', '搜索策略工作机制', '5'],
        ['第4章', '系统架构设计', '7'],
        ['第5章', '性能优化机制', '8'],
        ['第6章', '代码示例', '9'],
        ['附录A', 'GUI参数传递流程图', '10'],
        ['附录B', '搜索策略详细流程图', '11'],
    ]
    
    toc_table = Table(toc_data, colWidths=[1.5*inch, 3*inch, 1*inch])
    toc_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    story.append(Paragraph("目录", heading_style))
    story.append(toc_table)
    story.append(PageBreak())
    
    # 第1章：项目概述
    story.append(Paragraph("第1章 项目概述", heading_style))
    story.append(Paragraph("1.1 项目简介", subheading_style))
    story.append(Paragraph(
        "Fabric Search是一个基于深度学习和传统计算机视觉技术的布料图像搜索系统。"
        "该系统支持多种搜索策略，提供灵活的参数配置，能够根据用户需求进行精确的相似度搜索。",
        normal_style
    ))
    
    story.append(Paragraph("1.2 核心功能", subheading_style))
    features = [
        "• 多特征融合：支持深度学习特征、颜色、纹理、形状等多维度特征",
        "• 多种搜索策略：加权搜索、自适应搜索、查询扩展、混合搜索等",
        "• 灵活的参数配置：用户可自定义特征权重和搜索参数",
        "• 高性能索引：基于FAISS的向量索引，支持大规模数据搜索",
        "• 完善的GUI界面：直观的参数设置和结果展示"
    ]
    
    for feature in features:
        story.append(Paragraph(feature, normal_style))
    
    story.append(Paragraph("1.3 技术架构", subheading_style))
    story.append(Paragraph(
        "系统采用分层架构设计，从GUI界面到搜索引擎形成清晰的数据流转链路。"
        "使用策略模式实现多种搜索算法，工厂模式支持动态策略创建，"
        "观察者模式处理GUI事件，确保系统的可维护性和可扩展性。",
        normal_style
    ))
    
    story.append(PageBreak())
    
    # 第2章：GUI参数传递流程
    story.append(Paragraph("第2章 GUI参数传递流程", heading_style))
    
    story.append(Paragraph("2.1 参数收集层", subheading_style))
    story.append(Paragraph(
        "GUI参数收集层包含三个主要组件，负责收集用户的搜索配置：",
        normal_style
    ))
    
    param_components = [
        "• FeatureWeightsWidget：特征权重设置组件，允许用户调整不同特征类型的重要性",
        "• FeatureParamsWidget：传统特征参数设置，配置颜色直方图、LBP、傅里叶描述符等参数",
        "• AdvancedSearchWidget：高级搜索选项，包括文件过滤、日期范围、排序方式等"
    ]
    
    for component in param_components:
        story.append(Paragraph(component, normal_style))
    
    story.append(Paragraph("2.2 参数标准化", subheading_style))
    story.append(Paragraph(
        "每个GUI组件都提供标准化的参数获取方法：",
        normal_style
    ))
    
    story.append(Paragraph("get_weights()方法示例：", code_style))
    story.append(Paragraph("""
def get_weights(self):
    weights = {}
    total_weight = 0
    
    if self.deep_feature_cb.isChecked():
        deep_weight = self.deep_weight_slider.value()
        weights['deep_learning'] = deep_weight
        total_weight += deep_weight
    
    # 归一化权重
    if total_weight > 0:
        for key in weights:
            weights[key] = weights[key] / total_weight
    
    return weights
    """, code_style))
    
    story.append(Paragraph("2.3 配置对象创建", subheading_style))
    story.append(Paragraph(
        "SearchPanel负责聚合各组件的参数，创建统一的SearchConfig对象：",
        normal_style
    ))
    
    story.append(Paragraph("""
config = SearchConfig(
    mode=SearchMode.SIMILARITY,
    feature_weights=feature_weights,
    feature_extraction_params=feature_params,
    max_results=50,
    similarity_threshold=0.7
)
    """, code_style))
    
    story.append(Paragraph("2.4 查询对象转换", subheading_style))
    story.append(Paragraph(
        "SearchHandler将GUI配置转换为搜索引擎可识别的SearchQuery对象，"
        "完成参数映射和格式转换。",
        normal_style
    ))
    
    story.append(PageBreak())
    
    # 第3章：搜索策略工作机制
    story.append(Paragraph("第3章 搜索策略工作机制", heading_style))
    
    story.append(Paragraph("3.1 策略模式设计", subheading_style))
    story.append(Paragraph(
        "系统使用策略模式实现多种搜索算法，所有策略都继承自SearchStrategy基类，"
        "提供统一的search()接口。",
        normal_style
    ))
    
    strategies = [
        "• WeightedSearchStrategy：加权搜索策略，根据用户设置的权重进行特征融合",
        "• AdaptiveSearchStrategy：自适应搜索策略，根据查询图像特征自动调整权重",
        "• QueryExpansionStrategy：查询扩展策略，通过两轮搜索提高准确性",
        "• HybridSearchStrategy：混合搜索策略，综合多种策略的结果",
        "• SingleFeatureSearchStrategy：单特征搜索策略，仅使用指定的特征类型"
    ]
    
    for strategy in strategies:
        story.append(Paragraph(strategy, normal_style))
    
    story.append(Paragraph("3.2 加权搜索策略", subheading_style))
    story.append(Paragraph(
        "加权搜索策略的核心是加权融合算法：",
        normal_style
    ))
    
    story.append(Paragraph("""
def _weighted_fusion(self, feature_similarities):
    weights = self.feature_weights.to_dict()
    final_similarities = np.zeros(num_images)
    
    # 加权求和: final_score = Σ(weight_i × similarity_i)
    for feature_type, similarities in feature_similarities.items():
        weight = weights.get(feature_type, 0.0)
        final_similarities += weight * similarities
    
    return final_similarities
    """, code_style))
    
    story.append(Paragraph("3.3 自适应搜索策略", subheading_style))
    story.append(Paragraph(
        "自适应策略通过分析查询图像的特征分布，自动调整各特征的权重：",
        normal_style
    ))
    
    story.append(Paragraph("""
def _analyze_query_features(self, query_features):
    feature_variances = {}
    
    # 计算各特征的方差
    for feature_type, features in query_features.items():
        if len(features) > 1:
            feature_variances[feature_type] = np.var(features)
    
    # 根据方差调整权重（方差大的特征更重要）
    total_variance = sum(feature_variances.values())
    if total_variance > 0:
        for feature_type, variance in feature_variances.items():
            weight = variance / total_variance
    """, code_style))
    
    story.append(PageBreak())
    
    # 添加流程图
    story.append(Paragraph("附录A GUI参数传递流程图", heading_style))
    
    # 检查流程图文件是否存在
    simple_flowchart = output_dir / 'GUI参数传递流程图_简化版.png'
    if simple_flowchart.exists():
        story.append(Image(str(simple_flowchart), width=7*inch, height=9*inch))
    else:
        story.append(Paragraph("流程图文件未找到", normal_style))
    
    story.append(PageBreak())
    
    story.append(Paragraph("附录B 搜索策略详细流程图", heading_style))
    
    strategy_flowchart = output_dir / '搜索策略详细工作机制流程图.png'
    if strategy_flowchart.exists():
        story.append(Image(str(strategy_flowchart), width=7*inch, height=9*inch))
    else:
        story.append(Paragraph("策略流程图文件未找到", normal_style))
    
    # 构建PDF
    doc.build(story)
    print(f"最终PDF报告已生成: {pdf_path}")

if __name__ == "__main__":
    try:
        create_final_pdf_report()
    except ImportError as e:
        print(f"缺少reportlab库，请安装: pip install reportlab")
        print(f"错误详情: {e}")
    except Exception as e:
        print(f"生成PDF时出错: {e}")
        # 备用方案：创建简单的文本文档
        output_dir = Path('docs/diagrams')
        txt_path = output_dir / 'Fabric_Search_技术文档.txt'
        
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write("Fabric Search 项目 - GUI参数传递与搜索策略技术文档\n")
            f.write("=" * 60 + "\n\n")
            f.write("本文档详细描述了Fabric Search项目的技术架构和实现细节。\n\n")
            f.write("主要内容包括：\n")
            f.write("1. GUI参数传递流程\n")
            f.write("2. 搜索策略工作机制\n")
            f.write("3. 系统架构设计\n")
            f.write("4. 性能优化机制\n\n")
            f.write("详细的流程图已生成为PNG格式，请查看docs/diagrams目录。\n")
        
        print(f"备用文本文档已生成: {txt_path}")
