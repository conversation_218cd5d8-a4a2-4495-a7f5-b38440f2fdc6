"""
列表视图模块

包含列表视图的实现
"""

from typing import List
from PyQt6.QtWidgets import (
    QScrollArea, QWidget, QVBoxLayout
)
from PyQt6.QtCore import Qt, pyqtSignal

from database.models import FabricImage
from utils.logger_mixin import LoggerMixin
from gui.panels.list_item import ListItemWidget


class ListView(QScrollArea, LoggerMixin):
    """列表视图"""
    
    # 信号
    itemClicked = pyqtSignal(FabricImage)  # 项目点击
    itemDoubleClicked = pyqtSignal(FabricImage)  # 项目双击
    selectionChanged = pyqtSignal(list)  # 选择变更
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.fabric_images = []
        self.selected_items = []
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        # 设置滚动区域属性
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 创建内容容器
        self.container = QWidget()
        self.container_layout = QVBoxLayout(self.container)
        self.container_layout.setContentsMargins(0, 0, 0, 0)
        self.container_layout.setSpacing(0)
        self.container_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        # 设置视图内容
        self.setWidget(self.container)
    
    def set_results(self, results: List[FabricImage]):
        """设置结果
        
        Args:
            results: 结果列表
        """
        try:
            # 清除现有项目
            self.clear()
            self.fabric_images = results
            
            # 添加新项目
            for fabric_image in results:
                # 获取特征分数
                feature_scores = {}
                
                # 如果是SearchResult对象，获取特征分数
                if hasattr(fabric_image, 'feature_scores') and fabric_image.feature_scores:
                    feature_scores = fabric_image.feature_scores
                    self.logger.info(f"列表模式找到特征分数: {feature_scores}")
                elif hasattr(fabric_image, 'similarity_score') and fabric_image.similarity_score is not None:
                    # 如果只有总相似度分数，创建包含总分的feature_scores
                    feature_scores = {"总分": fabric_image.similarity_score}
                    self.logger.info(f"列表模式只有相似度分数: {fabric_image.similarity_score}")
                else:
                    self.logger.warning(f"列表模式未找到分数数据: {fabric_image.file_path}")
                    feature_scores = {}
                
                # 创建列表项组件
                item_widget = ListItemWidget(fabric_image, feature_scores)
                item_widget.itemClicked.connect(self.on_item_clicked)
                item_widget.itemDoubleClicked.connect(self.on_item_double_clicked)
                
                # 添加到容器
                self.container_layout.addWidget(item_widget)
            
            self.logger.info(f"列表视图显示 {len(results)} 个结果")
            
        except Exception as e:
            self.logger.error(f"设置结果失败: {e}")
    
    def clear(self):
        """清除所有项目"""
        # 清除容器中的所有组件
        while self.container_layout.count():
            item = self.container_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        self.selected_items = []
    
    def on_item_clicked(self, fabric_image: FabricImage):
        """项目点击事件"""
        try:
            self.itemClicked.emit(fabric_image)
            
            # 更新选中状态
            self.selected_items = [fabric_image]
            self.selectionChanged.emit(self.selected_items)
            
        except Exception as e:
            self.logger.error(f"项目点击事件处理失败: {e}")
    
    def on_item_double_clicked(self, fabric_image: FabricImage):
        """项目双击事件"""
        try:
            self.itemDoubleClicked.emit(fabric_image)
        except Exception as e:
            self.logger.error(f"项目双击事件处理失败: {e}")
    
    def get_selected_items(self) -> List[FabricImage]:
        """获取选中的项目
        
        Returns:
            List[FabricImage]: 选中的项目列表
        """
        return self.selected_items