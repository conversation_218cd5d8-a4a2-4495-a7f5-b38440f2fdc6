import pytest
from unittest.mock import Mock, patch
from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

from ui.search_panel import SearchPanel

class TestSearchPanel:
    """搜索面板功能测试"""
    
    @pytest.fixture
    def app(self):
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def search_panel(self, app):
        panel = SearchPanel()
        yield panel
        
    def test_panel_initialization(self, search_panel):
        """测试面板初始化"""
        assert hasattr(search_panel, 'load_image_btn')
        assert hasattr(search_panel, 'search_btn')
        assert hasattr(search_panel, 'search_mode_combo')
        assert hasattr(search_panel, 'top_n_spin')
        
    def test_search_mode_options(self, search_panel):
        """测试搜索模式选项"""
        combo = search_panel.search_mode_combo
        expected_modes = ["混合搜索", "深度学习特征", "颜色特征", "纹理特征", "形状特征", "自适应搜索"]
        
        actual_modes = [combo.itemText(i) for i in range(combo.count())]
        for mode in expected_modes:
            assert mode in actual_modes
            
    def test_weight_controls(self, search_panel):
        """测试权重控制"""
        # 测试深度学习权重
        search_panel.deep_weight_spin.setValue(0.6)
        assert search_panel.deep_weight_spin.value() == 0.6
        
        # 测试颜色权重
        search_panel.color_weight_spin.setValue(0.2)
        assert search_panel.color_weight_spin.value() == 0.2
        
        # 测试纹理权重
        search_panel.texture_weight_spin.setValue(0.1)
        assert search_panel.texture_weight_spin.value() == 0.1
        
        # 测试形状权重
        search_panel.shape_weight_spin.setValue(0.1)
        assert search_panel.shape_weight_spin.value() == 0.1
        
    @patch('PyQt5.QtWidgets.QFileDialog.getOpenFileName')
    def test_image_loading(self, mock_dialog, search_panel):
        """测试图像加载功能"""
        mock_dialog.return_value = ('test.jpg', 'Image Files (*.png *.jpg *.jpeg)')
        
        # 模拟点击加载按钮
        QTest.mouseClick(search_panel.load_image_btn, Qt.LeftButton)
        
        assert search_panel.query_image_path == 'test.jpg'
        
    def test_search_button_state(self, search_panel):
        """测试搜索按钮状态"""
        # 初始状态应该禁用
        assert not search_panel.search_btn.isEnabled()
        
        # 设置查询图像后应该启用
        search_panel.query_image_path = 'test.jpg'
        search_panel.update_search_button_state()
        assert search_panel.search_btn.isEnabled()