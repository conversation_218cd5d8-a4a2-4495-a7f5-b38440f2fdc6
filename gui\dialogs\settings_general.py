#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
常规设置页面

该模块提供应用程序常规设置的用户界面。
"""

import platform
import os
import winreg
from typing import Dict, Any

from PyQt6.QtWidgets import <PERSON>VBoxLayout, QHBoxLayout
from PyQt6.QtCore import Qt

from .settings_base import BaseSettingsWidget


class GeneralSettingsWidget(BaseSettingsWidget):
    """常规设置组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # 语言设置
        language_group = self.widget_factory.create_group_box(title="语言设置", layout_type="form")
        language_layout = language_group.layout()
        
        self.language_combo = self.widget_factory.create_combo_box(
            items=["简体中文", "English", "日本語"]
        )
        language_layout.addRow("界面语言:", self.language_combo)
        
        layout.addWidget(language_group)
        
        # 自动保存设置
        autosave_group = self.widget_factory.create_group_box(title="自动保存", layout_type="vbox")
        autosave_layout = autosave_group.layout()
        
        self.auto_save_cb = self.widget_factory.create_checkbox(
            "启用自动保存", checked=True
        )
        autosave_layout.addWidget(self.auto_save_cb)
        
        # 备份设置
        backup_layout = QHBoxLayout()
        self.backup_enabled_cb = self.widget_factory.create_checkbox(
            "启用自动备份", checked=True
        )
        backup_layout.addWidget(self.backup_enabled_cb)
        
        backup_layout.addWidget(self.widget_factory.create_label("间隔:"))
        self.backup_interval_spin = self.widget_factory.create_spin_box(
            minimum=5, maximum=1440, value=30, suffix=" 分钟"
        )
        backup_layout.addWidget(self.backup_interval_spin)
        
        backup_layout.addStretch()
        autosave_layout.addLayout(backup_layout)
        
        layout.addWidget(autosave_group)
        
        layout.addStretch()
    
    def get_cpu_info(self) -> str:
        """获取CPU信息"""
        try:
            # 获取CPU信息
            cpu_name = platform.processor()
            if not cpu_name or cpu_name.strip() == "":
                # 在某些系统上processor()可能返回空字符串，尝试其他方法
                try:
                    # Windows系统尝试从注册表获取
                    if platform.system() == "Windows":
                        key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                           r"HARDWARE\DESCRIPTION\System\CentralProcessor\0")
                        cpu_name = winreg.QueryValueEx(key, "ProcessorNameString")[0]
                        winreg.CloseKey(key)
                    else:
                        # Linux/Mac系统尝试从/proc/cpuinfo获取
                        if os.path.exists('/proc/cpuinfo'):
                            try:
                                with open('/proc/cpuinfo', 'r') as f:
                                    for line in f:
                                        if 'model name' in line:
                                            cpu_name = line.split(':')[1].strip()
                                            break
                            except (OSError, IOError, PermissionError) as e:
                                self.logger.debug(f"无法读取/proc/cpuinfo: {e}")
                                cpu_name = platform.machine()
                except Exception as e:
                    self.logger.debug(f"获取详细CPU信息失败: {e}")
                    # 如果获取详细信息失败，使用基本信息
            
            cpu_count = os.cpu_count() or 1
            
            if cpu_name and cpu_name.strip():
                return f"{cpu_name.strip()} ({cpu_count} 核心)"
            else:
                return f"未知CPU ({cpu_count} 核心)"
                
        except Exception as e:
            self.logger.warning(f"获取CPU信息失败: {e}")
            return "CPU信息获取失败"
    
    def get_gpu_info(self) -> str:
        """获取GPU信息"""
        try:
            # 尝试检测GPU
            gpu_info = "未检测到GPU"
            
            # 检查是否有CUDA可用
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_name = torch.cuda.get_device_name(0)
                    gpu_info = f"{gpu_name} (CUDA)"
                else:
                    gpu_info = "无CUDA支持的GPU"
            except ImportError:
                # 如果没有PyTorch，尝试其他方法
                try:
                    import subprocess
                    result = subprocess.run(['nvidia-smi', '--query-gpu=name', '--format=csv,noheader,nounits'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0 and result.stdout.strip():
                        gpu_info = result.stdout.strip().split('\n')[0]
                except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
                    pass
            
            return gpu_info
            
        except Exception as e:
            self.logger.warning(f"获取GPU信息失败: {e}")
            return "GPU信息获取失败"
    
    def get_settings(self) -> Dict[str, Any]:
        """获取设置"""
        language_map = {
            "简体中文": "zh_CN",
            "English": "en_US",
            "日本語": "ja_JP"
        }
        
        return {
            "language": language_map.get(self.language_combo.currentText(), "zh_CN"),
            "auto_save": self.auto_save_cb.isChecked(),
            "backup_enabled": self.backup_enabled_cb.isChecked(),
            "backup_interval": self.backup_interval_spin.value()
        }
    
    def set_settings(self, settings: Dict[str, Any]):
        """设置设置"""
        language_map = {
            "zh_CN": "简体中文",
            "en_US": "English",
            "ja_JP": "日本語"
        }
        
        language_text = language_map.get(settings.get("language", "zh_CN"), "简体中文")
        self.language_combo.setCurrentText(language_text)
        
        self.auto_save_cb.setChecked(settings.get("auto_save", True))
        self.backup_enabled_cb.setChecked(settings.get("backup_enabled", True))
        self.backup_interval_spin.setValue(settings.get("backup_interval", 30))