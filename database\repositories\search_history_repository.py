#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索历史管理模块

该模块提供搜索历史的管理功能，包括搜索记录的增删改查操作。
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from .base_repository import BaseRepository
from ..database_manager import DatabaseManager
from ..exceptions.database_exceptions import RepositoryError
from ..utils.database_utils import serialize_json, deserialize_json


class SearchHistoryRepository(BaseRepository):
    """搜索历史仓库类"""
    
    @property
    def table_name(self) -> str:
        """表名"""
        return "search_history"
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化搜索历史仓库
        
        Args:
            db_manager: 数据库管理器
        """
        super().__init__(db_manager)
    
    def _row_to_dict(self, row) -> Dict[str, Any]:
        """将数据库行转换为字典
        
        Args:
            row: 数据库行
            
        Returns:
            Dict[str, Any]: 搜索历史字典
        """
        try:
            # 处理时间字段
            search_time = None
            if row['search_time']:
                try:
                    search_time = datetime.fromisoformat(row['search_time'].replace('Z', '+00:00'))
                except ValueError:
                    search_time = datetime.strptime(row['search_time'], '%Y-%m-%d %H:%M:%S')
            
            # 处理JSON字段
            search_params = deserialize_json(row.get('search_params'))
            search_results = deserialize_json(row.get('search_results'))
            
            return {
                'id': row['id'],
                'search_query': row['query_text'],
                'search_type': row['search_type'],
                'search_params': search_params,
                'search_results': search_results,
                'result_count': row.get('result_count', 0),
                'search_time': search_time,
                'execution_time': row.get('execution_time', 0.0)
            }
            
        except Exception as e:
            self.logger.error(f"转换数据库行为字典失败: {e}")
            raise RepositoryError(f"转换数据库行为字典失败: {e}")
    
    def add_search_record(self, search_query: str, search_type: str,
                         search_params: Dict[str, Any] = None,
                         search_results: List[Dict[str, Any]] = None,
                         result_count: int = 0,
                         execution_time: float = 0.0,
                         user_id: str = "default_user") -> Optional[int]:
        """添加搜索记录
        
        Args:
            search_query: 搜索查询
            search_type: 搜索类型
            search_params: 搜索参数
            search_results: 搜索结果
            result_count: 结果数量
            execution_time: 执行时间（秒）
            user_id: 用户ID，默认为"default_user"
            
        Returns:
            Optional[int]: 记录ID，如果失败则返回None
        """
        try:
            sql = """
                INSERT INTO search_history (
                    user_id, query_text, search_type, search_params, filters,
                    result_count, execution_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                user_id,
                search_query,
                search_type,
                serialize_json(search_params) if search_params else None,
                serialize_json(search_results) if search_results else None,
                result_count,
                execution_time
            )
            
            record_id = self.db_manager.execute_insert(sql, params)
            self.logger.debug(f"搜索记录添加成功: {record_id}")
            return record_id
            
        except Exception as e:
            self.logger.error(f"添加搜索记录失败: {e}")
            raise RepositoryError(f"添加搜索记录失败: {e}")
    
    def get_search_record(self, record_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取搜索记录
        
        Args:
            record_id: 记录ID
            
        Returns:
            Optional[Dict[str, Any]]: 搜索记录，如果不存在则返回None
        """
        try:
            sql = "SELECT * FROM search_history WHERE id = ?"
            rows = self.db_manager.execute_query(sql, (record_id,))
            
            if rows:
                return self._row_to_dict(rows[0])
            return None
            
        except Exception as e:
            self.logger.error(f"根据ID获取搜索记录失败: {e}")
            raise RepositoryError(f"根据ID获取搜索记录失败: {e}")
    
    def get_recent_searches(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取最近的搜索记录
        
        Args:
            limit: 限制数量
            
        Returns:
            List[Dict[str, Any]]: 搜索记录列表
        """
        try:
            sql = """
                SELECT * FROM search_history 
                ORDER BY search_time DESC 
                LIMIT ?
            """
            rows = self.db_manager.execute_query(sql, (limit,))
            
            return [self._row_to_dict(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"获取最近搜索记录失败: {e}")
            raise RepositoryError(f"获取最近搜索记录失败: {e}")
    
    def get_searches_by_type(self, search_type: str, 
                           limit: int = 50) -> List[Dict[str, Any]]:
        """根据搜索类型获取搜索记录
        
        Args:
            search_type: 搜索类型
            limit: 限制数量
            
        Returns:
            List[Dict[str, Any]]: 搜索记录列表
        """
        try:
            sql = """
                SELECT * FROM search_history 
                WHERE search_type = ?
                ORDER BY search_time DESC 
                LIMIT ?
            """
            rows = self.db_manager.execute_query(sql, (search_type, limit))
            
            return [self._row_to_dict(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"根据搜索类型获取搜索记录失败: {e}")
            raise RepositoryError(f"根据搜索类型获取搜索记录失败: {e}")
    
    def search_history(self, query: str = None, search_type: str = None,
                      start_date: datetime = None, end_date: datetime = None,
                      limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """搜索历史记录
        
        Args:
            query: 查询关键词
            search_type: 搜索类型
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 搜索记录列表
        """
        try:
            sql = "SELECT * FROM search_history WHERE 1=1"
            params = []
            
            if query:
                sql += " AND query_text LIKE ?"
                params.append(f"%{query}%")
            
            if search_type:
                sql += " AND search_type = ?"
                params.append(search_type)
            
            if start_date:
                sql += " AND created_at >= ?"
                params.append(start_date.isoformat())
            
            if end_date:
                sql += " AND created_at <= ?"
                params.append(end_date.isoformat())
            
            sql += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            rows = self.db_manager.execute_query(sql, tuple(params))
            
            return [self._row_to_dict(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"搜索历史记录失败: {e}")
            raise RepositoryError(f"搜索历史记录失败: {e}")
    
    def get_popular_searches(self, limit: int = 20, 
                           days: int = 30) -> List[Dict[str, Any]]:
        """获取热门搜索
        
        Args:
            limit: 限制数量
            days: 统计天数
            
        Returns:
            List[Dict[str, Any]]: 热门搜索列表
        """
        try:
            start_date = datetime.now() - timedelta(days=days)
            
            sql = """
                SELECT query_text, search_type, COUNT(*) as search_count,
                       AVG(result_count) as avg_results,
                       AVG(execution_time) as avg_time,
                       MAX(created_at) as last_search
                FROM search_history 
                WHERE created_at >= ?
                GROUP BY query_text, search_type
                ORDER BY search_count DESC, last_search DESC
                LIMIT ?
            """
            
            rows = self.db_manager.execute_query(sql, (start_date.isoformat(), limit))
            
            result = []
            for row in rows:
                last_search = None
                if row['last_search']:
                    try:
                        last_search = datetime.fromisoformat(row['last_search'].replace('Z', '+00:00'))
                    except ValueError:
                        last_search = datetime.strptime(row['last_search'], '%Y-%m-%d %H:%M:%S')
                
                result.append({
                    'search_query': row['query_text'],
                    'search_type': row['search_type'],
                    'search_count': row['search_count'],
                    'avg_results': row['avg_results'],
                    'avg_time': row['avg_time'],
                    'last_search': last_search
                })
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取热门搜索失败: {e}")
            raise RepositoryError(f"获取热门搜索失败: {e}")
    
    def get_search_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取搜索统计信息
        
        Args:
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 搜索统计信息
        """
        try:
            start_date = datetime.now() - timedelta(days=days)
            stats = {}
            
            # 总搜索次数
            sql = "SELECT COUNT(*) as total FROM search_history WHERE created_at >= ?"
            result = self.db_manager.execute_query(sql, (start_date.isoformat(),))
            stats['total_searches'] = result[0]['total'] if result else 0
            
            # 按类型统计
            sql = """
                SELECT search_type, COUNT(*) as count 
                FROM search_history 
                WHERE created_at >= ?
                GROUP BY search_type
                ORDER BY count DESC
            """
            result = self.db_manager.execute_query(sql, (start_date.isoformat(),))
            stats['by_type'] = {row['search_type']: row['count'] for row in result}
            
            # 平均执行时间
            sql = """
                SELECT AVG(execution_time) as avg_time 
                FROM search_history 
                WHERE created_at >= ? AND execution_time > 0
            """
            result = self.db_manager.execute_query(sql, (start_date.isoformat(),))
            stats['avg_execution_time'] = result[0]['avg_time'] if result and result[0]['avg_time'] else 0.0
            
            # 平均结果数量
            sql = """
                SELECT AVG(result_count) as avg_results 
                FROM search_history 
                WHERE created_at >= ?
            """
            result = self.db_manager.execute_query(sql, (start_date.isoformat(),))
            stats['avg_result_count'] = result[0]['avg_results'] if result and result[0]['avg_results'] else 0.0
            
            # 按日期统计
            sql = """
                SELECT DATE(created_at) as search_date, COUNT(*) as count
                FROM search_history 
                WHERE created_at >= ?
                GROUP BY DATE(created_at)
                ORDER BY search_date DESC
            """
            result = self.db_manager.execute_query(sql, (start_date.isoformat(),))
            stats['by_date'] = {row['search_date']: row['count'] for row in result}
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取搜索统计信息失败: {e}")
            raise RepositoryError(f"获取搜索统计信息失败: {e}")
    
    def delete_old_searches(self, days: int = 90) -> int:
        """删除旧的搜索记录
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的记录数量
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            sql = "DELETE FROM search_history WHERE created_at < ?"
            affected_rows = self.db_manager.execute_update(sql, (cutoff_date.isoformat(),))
            
            self.logger.info(f"删除旧搜索记录成功: {affected_rows} 条记录")
            return affected_rows
            
        except Exception as e:
            self.logger.error(f"删除旧搜索记录失败: {e}")
            raise RepositoryError(f"删除旧搜索记录失败: {e}")
    
    def clear_all_history(self) -> int:
        """清空所有搜索历史
        
        Returns:
            int: 删除的记录数量
        """
        try:
            sql = "DELETE FROM search_history"
            affected_rows = self.db_manager.execute_update(sql)
            
            self.logger.info(f"清空搜索历史成功: {affected_rows} 条记录")
            return affected_rows
            
        except Exception as e:
            self.logger.error(f"清空搜索历史失败: {e}")
            raise RepositoryError(f"清空搜索历史失败: {e}")
    
    def get_search_trends(self, days: int = 30) -> Dict[str, Any]:
        """获取搜索趋势
        
        Args:
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 搜索趋势数据
        """
        try:
            start_date = datetime.now() - timedelta(days=days)
            
            # 每日搜索量趋势
            sql = """
                SELECT DATE(created_at) as date, COUNT(*) as count
                FROM search_history 
                WHERE created_at >= ?
                GROUP BY DATE(created_at)
                ORDER BY date ASC
            """
            result = self.db_manager.execute_query(sql, (start_date.isoformat(),))
            daily_trends = [{'date': row['date'], 'count': row['count']} for row in result]
            
            # 每小时搜索量趋势
            sql = """
                SELECT strftime('%H', created_at) as hour, COUNT(*) as count
                FROM search_history 
                WHERE created_at >= ?
                GROUP BY strftime('%H', created_at)
                ORDER BY hour ASC
            """
            result = self.db_manager.execute_query(sql, (start_date.isoformat(),))
            hourly_trends = [{'hour': int(row['hour']), 'count': row['count']} for row in result]
            
            # 搜索类型趋势
            sql = """
                SELECT search_type, DATE(created_at) as date, COUNT(*) as count
                FROM search_history 
                WHERE created_at >= ?
                GROUP BY search_type, DATE(created_at)
                ORDER BY date ASC, search_type ASC
            """
            result = self.db_manager.execute_query(sql, (start_date.isoformat(),))
            type_trends = {}
            for row in result:
                if row['search_type'] not in type_trends:
                    type_trends[row['search_type']] = []
                type_trends[row['search_type']].append({
                    'date': row['date'],
                    'count': row['count']
                })
            
            return {
                'daily_trends': daily_trends,
                'hourly_trends': hourly_trends,
                'type_trends': type_trends
            }
            
        except Exception as e:
            self.logger.error(f"获取搜索趋势失败: {e}")
            raise RepositoryError(f"获取搜索趋势失败: {e}")
    
    def get_search_types(self) -> List[str]:
        """获取所有搜索类型
        
        Returns:
            List[str]: 搜索类型列表
        """
        try:
            sql = """
                SELECT DISTINCT search_type 
                FROM search_history 
                WHERE search_type IS NOT NULL 
                ORDER BY search_type
            """
            result = self.db_manager.execute_query(sql)
            return [row['search_type'] for row in result]
            
        except Exception as e:
            self.logger.error(f"获取搜索类型列表失败: {e}")
            raise RepositoryError(f"获取搜索类型列表失败: {e}")
    
    def update_search_record(self, record_id: int, 
                           search_results: List[Dict[str, Any]] = None,
                           result_count: int = None,
                           execution_time: float = None) -> bool:
        """更新搜索记录
        
        Args:
            record_id: 记录ID
            search_results: 搜索结果
            result_count: 结果数量
            execution_time: 执行时间
            
        Returns:
            bool: 更新是否成功
        """
        try:
            updates = []
            params = []
            
            if search_results is not None:
                updates.append("search_results = ?")
                params.append(serialize_json(search_results))
            
            if result_count is not None:
                updates.append("result_count = ?")
                params.append(result_count)
            
            if execution_time is not None:
                updates.append("execution_time = ?")
                params.append(execution_time)
            
            if not updates:
                return False
            
            sql = f"UPDATE search_history SET {', '.join(updates)} WHERE id = ?"
            params.append(record_id)
            
            affected_rows = self.db_manager.execute_update(sql, tuple(params))
            success = affected_rows > 0
            
            if success:
                self.logger.debug(f"搜索记录更新成功: {record_id}")
            else:
                self.logger.warning(f"搜索记录更新失败，未找到记录: {record_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"更新搜索记录失败: {e}")
            raise RepositoryError(f"更新搜索记录失败: {e}")
    
    def export_search_history(self, start_date: datetime = None,
                             end_date: datetime = None) -> List[Dict[str, Any]]:
        """导出搜索历史
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict[str, Any]]: 搜索历史列表
        """
        try:
            sql = "SELECT * FROM search_history WHERE 1=1"
            params = []
            
            if start_date:
                sql += " AND search_time >= ?"
                params.append(start_date.isoformat())
            
            if end_date:
                sql += " AND search_time <= ?"
                params.append(end_date.isoformat())
            
            sql += " ORDER BY search_time ASC"
            
            rows = self.db_manager.execute_query(sql, tuple(params))
            
            return [self._row_to_dict(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"导出搜索历史失败: {e}")
            raise RepositoryError(f"导出搜索历史失败: {e}")