#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索模块

版本: 1.0.0
作者: AI Fabric Team
"""

# 核心组件
from .search_engine import SearchEngine
from .image_search import ImageSearchEngine, SearchConfig

# 数据模型
from .models import SearchQuery, SearchResult

# 搜索方法
from .search_methods import SearchMethods

# 结果处理
from .result_processor import ResultProcessor

# 缓存管理
from .cache_manager import CacheManager

# 统计管理
from .statistics_manager import StatisticsManager

# 过滤器
from .search_filters import SearchFilter, FilterType, FilterEngine

# 搜索历史
from .search_history import SearchHistoryManager, SearchType

# 配置管理
from .config import SearchConfig as BaseSearchConfig, ConfigManager

# 工具类
from .utils import SearchUtils

__all__ = [
    # 核心组件
    'SearchEngine',
    'ImageSearchEngine',
    
    # 数据模型
    'SearchQuery', 'SearchResult',
    
    # 搜索方法
    'SearchMethods',
    
    # 结果处理
    'ResultProcessor',
    
    # 缓存管理
    'CacheManager',
    
    # 统计管理
    'StatisticsManager',
    
    # 过滤器
    'SearchFilter', 'FilterType', 'FilterEngine',
    
    # 搜索历史
    'SearchHistoryManager', 'SearchType',
    
    # 配置管理
    'SearchConfig', 'BaseSearchConfig', 'ConfigManager',
    
    # 工具类
    'SearchUtils'
]