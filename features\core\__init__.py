"""特征管理核心模块

该模块包含重构后的特征管理器核心功能，按功能拆分为多个子模块：
- manager: 核心管理器类
- search: 搜索相关功能
- processing: 特征处理功能
- statistics: 统计信息功能
"""

from .manager import FeatureManager
from .search import SearchRequest, SearchResponse, SearchHandler
from .processing import FeatureProcessor
from .statistics import StatisticsCollector
from .data_models import FeatureIndexInfo

__all__ = [
    'FeatureManager',
    'SearchRequest',
    'SearchResponse', 
    'SearchHandler',
    'FeatureProcessor',
    'StatisticsCollector',
    'FeatureIndexInfo'
]