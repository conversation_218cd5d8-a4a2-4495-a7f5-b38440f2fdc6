#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题管理器模块

该模块负责管理应用程序的主题和样式。
"""

import os
from typing import Dict, Any, Optional, List, Callable, Union

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QFont

from utils.log_utils import LoggerMixin
from config.ui_config import UIConfig
from gui.themes.schemes import Theme, ThemeType
from gui.themes.loader import ThemeLoader
from gui.themes.builder import StyleBuilder


class ThemeManager(QObject, LoggerMixin):
    """主题管理器
    
    负责管理应用程序的主题和样式。
    """
    
    # 信号
    theme_changed = pyqtSignal(str)  # 主题改变信号
    
    def __init__(self, ui_config: UIConfig, themes_dir: str = None):
        """初始化主题管理器
        
        Args:
            ui_config: UI配置
            themes_dir: 主题目录
        """
        super().__init__()
        LoggerMixin.__init__(self)
        
        self.ui_config = ui_config
        self.themes_dir = themes_dir or "themes"
        
        # 初始化组件
        self.theme_loader = ThemeLoader(self.themes_dir)
        self.style_builder = StyleBuilder()
        
        # 主题存储
        self.current_theme: Optional[Theme] = None
        
        # 主题变更回调
        self.theme_callbacks: List[Callable[[Theme], None]] = []
        
        self.logger.info("主题管理器初始化完成")
    
    def get_available_themes(self) -> List[str]:
        """获取可用主题列表
        
        Returns:
            List[str]: 主题名称列表
        """
        return self.theme_loader.get_available_themes()
    
    def get_theme(self, theme_name: str) -> Optional[Theme]:
        """获取主题
        
        Args:
            theme_name: 主题名称
            
        Returns:
            Optional[Theme]: 主题对象
        """
        return self.theme_loader.get_theme(theme_name)
    
    def get_current_theme(self) -> Optional[Theme]:
        """获取当前主题
        
        Returns:
            Optional[Theme]: 当前主题
        """
        return self.current_theme
    
    def set_theme(self, theme_name: str) -> bool:
        """设置主题
        
        Args:
            theme_name: 主题名称
            
        Returns:
            bool: 是否设置成功
        """
        try:
            theme = self.theme_loader.get_theme(theme_name)
            if not theme:
                self.logger.warning(f"主题不存在: {theme_name}")
                return False
            
            # 生成样式表（如果没有）
            if not theme.stylesheet:
                theme.stylesheet = self.style_builder.generate_stylesheet(
                    theme.color_scheme, theme.font_scheme
                )
            
            # 应用主题
            self._apply_theme(theme)
            
            # 更新当前主题
            self.current_theme = theme
            
            # 保存到配置
            self.ui_config.theme.current_theme = theme_name
            
            # 发送信号
            self.theme_changed.emit(theme_name)
            
            # 调用回调函数
            for callback in self.theme_callbacks:
                try:
                    callback(theme)
                except Exception as e:
                    self.logger.error(f"主题回调函数执行失败: {e}")
            
            self.logger.info(f"主题已切换: {theme.display_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置主题失败: {e}")
            return False
    
    def _apply_theme(self, theme: Theme):
        """应用主题
        
        Args:
            theme: 主题对象
        """
        try:
            app = QApplication.instance()
            if app:
                # 应用样式表
                app.setStyleSheet(theme.stylesheet)
                
                # 设置调色板
                palette = self.style_builder.create_palette(theme.color_scheme)
                app.setPalette(palette)
                
                # 设置字体
                font = QFont(theme.font_scheme.family, theme.font_scheme.size_normal)
                app.setFont(font)
                
        except Exception as e:
            self.logger.error(f"应用主题失败: {e}")
    
    def apply_theme(self, theme: Union[str, Theme]) -> bool:
        """直接应用主题
        
        Args:
            theme: 主题名称或主题对象
            
        Returns:
            bool: 是否应用成功
        """
        try:
            # 如果传入的是主题名称，先获取主题对象
            if isinstance(theme, str):
                theme_obj = self.theme_loader.get_theme(theme)
                if not theme_obj:
                    self.logger.warning(f"主题不存在: {theme}")
                    return False
            else:
                theme_obj = theme
            
            # 生成样式表（如果没有）
            if not theme_obj.stylesheet:
                theme_obj.stylesheet = self.style_builder.generate_stylesheet(
                    theme_obj.color_scheme, theme_obj.font_scheme
                )
            
            # 应用主题
            self._apply_theme(theme_obj)
            
            # 更新当前主题
            self.current_theme = theme_obj
            
            # 发送信号
            self.theme_changed.emit(theme_obj.name)
            
            # 调用回调函数
            for callback in self.theme_callbacks:
                try:
                    callback(theme_obj)
                except Exception as e:
                    self.logger.error(f"主题回调函数执行失败: {e}")
            
            self.logger.info(f"主题已应用: {theme_obj.display_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"应用主题失败: {e}")
            return False
    
    def create_custom_theme(self, name: str, display_name: str,
                          color_scheme: Any, font_scheme: Any = None) -> bool:
        """创建自定义主题
        
        Args:
            name: 主题名称
            display_name: 显示名称
            color_scheme: 颜色方案
            font_scheme: 字体方案
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 生成样式表
            stylesheet = self.style_builder.generate_stylesheet(color_scheme, font_scheme)
            
            # 创建主题
            theme = Theme(
                name=name,
                display_name=display_name,
                theme_type=ThemeType.CUSTOM,
                color_scheme=color_scheme,
                font_scheme=font_scheme,
                stylesheet=stylesheet
            )
            
            # 保存主题到文件
            if self.theme_loader.save_theme_to_file(theme):
                # 添加到主题列表
                self.theme_loader.themes[name] = theme
                self.logger.info(f"自定义主题创建成功: {display_name}")
                return True
            else:
                self.logger.error(f"保存自定义主题失败: {name}")
                return False
            
        except Exception as e:
            self.logger.error(f"创建自定义主题失败: {e}")
            return False
    
    def delete_custom_theme(self, theme_name: str) -> bool:
        """删除自定义主题
        
        Args:
            theme_name: 主题名称
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 如果是当前主题，切换到默认主题
            if self.current_theme and self.current_theme.name == theme_name:
                self.set_theme('light')
            
            # 删除主题
            return self.theme_loader.delete_theme(theme_name)
            
        except Exception as e:
            self.logger.error(f"删除自定义主题失败: {e}")
            return False
    
    def export_theme(self, theme_name: str, export_path: str) -> bool:
        """导出主题
        
        Args:
            theme_name: 主题名称
            export_path: 导出路径
            
        Returns:
            bool: 是否导出成功
        """
        try:
            theme = self.theme_loader.get_theme(theme_name)
            if not theme:
                self.logger.warning(f"主题不存在: {theme_name}")
                return False
            
            # 保存到指定路径
            with open(export_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(theme.to_dict(), f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"主题导出成功: {theme_name} -> {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出主题失败: {e}")
            return False
    
    def import_theme(self, import_path: str) -> bool:
        """导入主题
        
        Args:
            import_path: 导入路径
            
        Returns:
            bool: 是否导入成功
        """
        try:
            theme = self.theme_loader.import_theme(import_path)
            return theme is not None
            
        except Exception as e:
            self.logger.error(f"导入主题失败: {e}")
            return False
    
    def add_theme_callback(self, callback: Callable[[Theme], None]):
        """添加主题变更回调
        
        Args:
            callback: 回调函数
        """
        if callback not in self.theme_callbacks:
            self.theme_callbacks.append(callback)
    
    def remove_theme_callback(self, callback: Callable[[Theme], None]):
        """移除主题变更回调
        
        Args:
            callback: 回调函数
        """
        if callback in self.theme_callbacks:
            self.theme_callbacks.remove(callback)
    
    def get_theme_info(self) -> Dict[str, Any]:
        """获取主题信息
        
        Returns:
            Dict[str, Any]: 主题信息
        """
        try:
            themes = self.theme_loader.themes
            
            info = {
                'total_themes': len(themes),
                'builtin_themes': len([t for t in themes.values() 
                                     if t.theme_type in [ThemeType.LIGHT, ThemeType.DARK]]),
                'custom_themes': len([t for t in themes.values() 
                                    if t.theme_type == ThemeType.CUSTOM]),
                'current_theme': self.current_theme.name if self.current_theme else None,
                'available_themes': [
                    {
                        'name': theme.name,
                        'display_name': theme.display_name,
                        'type': theme.theme_type.value
                    }
                    for theme in themes.values()
                ]
            }
            
            return info
            
        except Exception as e:
            self.logger.error(f"获取主题信息失败: {e}")
            return {}
    
    def initialize_theme(self):
        """初始化主题"""
        try:
            # 获取配置的主题
            theme_name = self.ui_config.theme.current_theme
            
            # 如果主题不存在，使用默认主题
            if not self.theme_loader.get_theme(theme_name):
                theme_name = 'light'
            
            # 设置主题
            self.set_theme(theme_name)
            
        except Exception as e:
            self.logger.error(f"初始化主题失败: {e}")
            # 使用默认主题
            self.set_theme('light')
    
    def cleanup(self):
        """清理资源"""
        try:
            self.theme_callbacks.clear()
            self.logger.debug("主题管理器资源清理完成")
        except Exception as e:
            self.logger.error(f"主题管理器资源清理失败: {e}")