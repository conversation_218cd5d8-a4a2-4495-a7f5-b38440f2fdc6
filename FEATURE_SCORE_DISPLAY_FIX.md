# 特征分数显示修复报告

## 问题描述

用户报告在GUI中更改特征权重时，搜索结果排序和相似度分数保持不变，即使选择不同的单一特征进行搜索，结果也没有变化，特征分数显示错误。

## 问题分析

通过日志分析发现两个关键问题：

### 1. 权重传递问题
- **现象**：搜索时使用的权重是正确的，但结果面板获取的权重却是默认的均匀权重
- **原因**：`_get_current_search_weights()`方法从`search_config`获取权重，但该配置可能不是最新的GUI状态

### 2. 特征分数计算错误
- **现象**：显示的特征分数变成了权重值，而不是实际的相似度分数
- **原因**：`_update_display_feature_scores()`方法错误地将权重值直接作为显示分数

## 修复方案

### 1. 修复权重获取逻辑

**文件**: `gui/panels/result_panel.py`

**修改**: `_get_current_search_weights()`方法

```python
def _get_current_search_weights(self):
    """获取当前搜索的特征权重"""
    try:
        # 直接从相似度搜索组件获取当前权重
        if hasattr(self, 'parent') and self.parent:
            main_window = self.parent
            if hasattr(main_window, 'search_panel') and main_window.search_panel:
                similarity_widget = main_window.search_panel.similarity_widget
                if hasattr(similarity_widget, 'feature_weights_widget'):
                    current_weights = similarity_widget.feature_weights_widget.get_weights()
                    self.logger.info(f"从GUI获取的当前权重: {current_weights}")
                    return current_weights
        
        # 如果无法获取，返回默认权重
        self.logger.warning("无法获取当前搜索权重，使用默认权重")
        return {'deep_learning': 0.25, 'color': 0.25, 'texture': 0.25, 'shape': 0.25}
    except Exception as e:
        self.logger.error(f"获取当前搜索权重失败: {e}")
        return {'deep_learning': 0.25, 'color': 0.25, 'texture': 0.25, 'shape': 0.25}
```

### 2. 修复特征分数计算逻辑

**文件**: `gui/panels/result_panel.py`

**修改**: `_update_display_feature_scores()`方法

```python
def _update_display_feature_scores(self, fabric_image, current_weights):
    """根据当前权重更新显示的特征分数"""
    try:
        if not current_weights:
            return
        
        # 获取原始特征分数（这些是实际的相似度分数）
        original_scores = {}
        if hasattr(fabric_image, 'feature_scores') and fabric_image.feature_scores:
            original_scores = fabric_image.feature_scores.copy()
            self.logger.info(f"原始特征分数: {original_scores}")
        
        # 如果没有原始特征分数，保持不变
        if not original_scores:
            self.logger.warning("没有找到原始特征分数，跳过更新")
            return
        
        # 根据当前权重决定显示哪些特征分数
        display_scores = {}
        
        # 为每个特征设置显示分数
        for feature_type in ['deep_learning', 'color', 'texture', 'shape']:
            score_key = feature_type if feature_type != 'deep_learning' else 'deep'
            
            if feature_type in current_weights:
                weight = current_weights[feature_type]
                
                if weight > 0:
                    # 如果权重大于0，显示原始特征分数
                    if score_key in original_scores:
                        display_scores[score_key] = original_scores[score_key]
                    else:
                        display_scores[score_key] = getattr(fabric_image, 'similarity_score', 0.0)
                else:
                    # 如果权重为0，显示0分数（表示该特征未参与搜索）
                    display_scores[score_key] = 0.0
            else:
                # 如果权重字典中没有该特征，使用原始分数
                if score_key in original_scores:
                    display_scores[score_key] = original_scores[score_key]
                else:
                    display_scores[score_key] = 0.0
        
        # 更新fabric_image的feature_scores用于显示
        fabric_image.feature_scores = display_scores
        
        self.logger.info(f"更新显示特征分数: {display_scores}")
        
    except Exception as e:
        self.logger.error(f"更新显示特征分数失败: {e}")
```

### 3. 在结果处理中调用更新方法

**文件**: `gui/panels/result_panel.py`

**修改**: 在`set_results()`方法的所有FabricImage处理位置添加特征分数更新调用

```python
# 重新计算显示的特征分数
self._update_display_feature_scores(fabric_image, current_weights)
```

## 修复效果

### 修复前
- 特征分数显示为权重值：`{'deep': 1.0, 'color': 0.0, 'texture': 0.0, 'shape': 0.0}`
- 无论选择哪个特征，显示的分数都不变

### 修复后
- 特征分数显示为实际相似度分数：`{'deep': 0.08860599, 'color': 0.0, 'texture': 0.0, 'shape': 0.0}`
- 权重为0的特征显示为0.0，表示未参与搜索
- 权重大于0的特征显示实际的相似度分数

## 验证结果

通过日志验证修复效果：

```
2025-08-01 13:34:15,632 - GridView - INFO - 网格模式找到特征分数: {'deep': 0.08860599249601364, 'color': 0.0, 'texture': 0.0, 'shape': 0.0}
2025-08-01 13:34:15,668 - ResultItem - INFO - 特征分数: {'deep': 0.03340371698141098, 'color': 0.0, 'texture': 0.0, 'shape': 0.0}
```

现在特征分数正确显示：
- **激活特征**（权重>0）：显示实际相似度分数
- **未激活特征**（权重=0）：显示0.0

## 总结

此次修复解决了特征权重变化时结果显示不正确的问题，确保：

1. **权重传递正确**：从GUI组件直接获取最新的特征权重
2. **分数显示正确**：显示实际的特征相似度分数，而不是权重值
3. **用户体验改善**：用户可以清楚地看到哪些特征参与了搜索，以及各特征的实际相似度
