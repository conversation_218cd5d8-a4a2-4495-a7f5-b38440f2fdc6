#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多特征提取和存储功能
"""

import sys
import os
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_multi_feature_extraction():
    """测试多特征提取功能"""
    print("=== 多特征提取测试 ===\n")
    
    try:
        # 1. 检查测试图像
        test_images_dir = project_root / "test_images"
        if not test_images_dir.exists():
            print("❌ 测试图像目录不存在")
            return False
        
        test_images = list(test_images_dir.glob("*.jpg")) + list(test_images_dir.glob("*.png"))
        if not test_images:
            print("❌ 没有找到测试图像")
            return False
        
        print(f"✓ 找到 {len(test_images)} 个测试图像")
        
        # 2. 初始化组件
        print("\n2. 初始化组件...")
        
        try:
            from features.config.config_manager import ConfigManager
            from features.core.manager import FeatureManager
            from database.fabric_repository import FabricRepository
            from database.database_manager import DatabaseManager

            # 初始化配置
            config_manager = ConfigManager()

            # 初始化数据库
            db_manager = DatabaseManager()
            fabric_repository = FabricRepository(db_manager)

            # 初始化特征管理器
            feature_manager = FeatureManager(config_manager, fabric_repository)
            
            print("✓ 组件初始化成功")
            
        except Exception as e:
            print(f"❌ 组件初始化失败: {e}")
            return False
        
        # 3. 测试单个图像的特征提取
        print("\n3. 测试单个图像的特征提取...")
        
        test_image = test_images[0]
        print(f"测试图像: {test_image}")
        
        try:
            # 提取特征
            result = feature_manager.feature_extractor.extract_features(str(test_image), extract_traditional=True)
            
            if result and result.success:
                print("✓ 特征提取成功")
                print(f"  深度特征维度: {len(result.features) if result.features is not None else 0}")
                
                if hasattr(result, 'feature_vectors') and result.feature_vectors:
                    print("  提取的特征类型:")
                    for feature_type, features in result.feature_vectors.items():
                        print(f"    {feature_type}: {len(features)} 维")
                else:
                    print("  ⚠️ 没有提取到多种特征类型")
            else:
                error_msg = getattr(result, 'error_message', '未知错误') if result else "结果为空"
                print(f"❌ 特征提取失败: {error_msg}")
                return False
                
        except Exception as e:
            print(f"❌ 特征提取异常: {e}")
            return False
        
        # 4. 测试特征存储
        print("\n4. 测试特征存储...")
        
        try:
            # 创建图像记录
            fabric_image = fabric_repository.create_fabric_image(str(test_image))
            if not fabric_image:
                print("❌ 创建图像记录失败")
                return False
            
            print(f"✓ 创建图像记录成功，ID: {fabric_image.id}")
            
            # 存储特征
            if hasattr(result, 'feature_vectors') and result.feature_vectors:
                success = feature_manager.feature_storage.store_multiple_features(
                    fabric_image.id, result.feature_vectors
                )
                if success:
                    print("✓ 多特征存储成功")
                else:
                    print("❌ 多特征存储失败")
                    return False
            else:
                print("⚠️ 没有多特征可存储")
                
        except Exception as e:
            print(f"❌ 特征存储异常: {e}")
            return False
        
        # 5. 验证数据库中的特征
        print("\n5. 验证数据库中的特征...")
        
        try:
            db_path = 'data/fabric_search.db'
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 查询特征类型
                cursor.execute('SELECT DISTINCT feature_type FROM image_features ORDER BY feature_type;')
                feature_types = cursor.fetchall()
                
                print("数据库中的特征类型:")
                for feature_type in feature_types:
                    cursor.execute('SELECT COUNT(*) FROM image_features WHERE feature_type = ?;', feature_type)
                    count = cursor.fetchone()[0]
                    print(f"  {feature_type[0]}: {count} 条记录")
                
                # 查询最新添加的特征
                cursor.execute('''
                    SELECT feature_type, LENGTH(feature_data) as data_size 
                    FROM image_features 
                    WHERE image_id = ? 
                    ORDER BY created_at DESC
                ''', (fabric_image.id,))
                
                new_features = cursor.fetchall()
                if new_features:
                    print(f"\n图像 {fabric_image.id} 的特征:")
                    for feature_type, data_size in new_features:
                        print(f"  {feature_type}: {data_size} 字节")
                else:
                    print(f"⚠️ 图像 {fabric_image.id} 没有找到特征记录")
                
                conn.close()
            else:
                print("❌ 数据库文件不存在")
                return False
                
        except Exception as e:
            print(f"❌ 数据库验证失败: {e}")
            return False
        
        # 6. 清理资源
        print("\n6. 清理资源...")
        try:
            feature_manager.cleanup()
            print("✓ 资源清理完成")
        except Exception as e:
            print(f"⚠️ 资源清理异常: {e}")
        
        print("\n🎉 多特征提取测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = test_multi_feature_extraction()
    sys.exit(0 if success else 1)
