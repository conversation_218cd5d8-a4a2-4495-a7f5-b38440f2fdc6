#!/usr/bin/env python3
"""
测试特征分数显示修复
验证当用户在GUI中更改特征权重时，显示的特征分数是否正确反映当前权重
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.models import FabricImage
from gui.panels.result_panel import ResultPanel
from PyQt6.QtWidgets import QApplication, QMainWindow

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_feature_score_update():
    """测试特征分数更新功能"""
    print("=== 测试特征分数显示修复 ===")
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    
    # 创建结果面板
    result_panel = ResultPanel(main_window)
    
    # 创建测试用的FabricImage对象
    test_fabric = FabricImage(
        id="test_001",
        file_path="test_images/fabric1.jpg",
        file_name="fabric1.jpg",
        width=512,
        height=512,
        format="JPEG",
        category="cotton"
    )
    
    # 设置原始特征分数
    test_fabric.feature_scores = {
        'deep': 0.8,
        'color': 0.6,
        'texture': 0.7,
        'shape': 0.5
    }
    test_fabric.similarity_score = 0.75
    
    print(f"原始特征分数: {test_fabric.feature_scores}")
    
    # 测试不同的权重配置
    test_weights = [
        {'deep_learning': 1.0, 'color': 0.0, 'texture': 0.0, 'shape': 0.0},  # 只选择深度学习
        {'deep_learning': 0.0, 'color': 1.0, 'texture': 0.0, 'shape': 0.0},  # 只选择颜色
        {'deep_learning': 0.0, 'color': 0.0, 'texture': 1.0, 'shape': 0.0},  # 只选择纹理
        {'deep_learning': 0.0, 'color': 0.0, 'texture': 0.0, 'shape': 1.0},  # 只选择形状
        {'deep_learning': 0.25, 'color': 0.25, 'texture': 0.25, 'shape': 0.25}  # 均匀权重
    ]
    
    for i, weights in enumerate(test_weights):
        print(f"\n--- 测试权重配置 {i+1}: {weights} ---")
        
        # 重置特征分数
        test_fabric.feature_scores = {
            'deep': 0.8,
            'color': 0.6,
            'texture': 0.7,
            'shape': 0.5
        }
        
        # 调用更新方法
        result_panel._update_display_feature_scores(test_fabric, weights)
        
        print(f"更新后的显示分数: {test_fabric.feature_scores}")
        
        # 验证权重为1.0的特征分数应该为1.0，权重为0.0的应该为0.0
        for feature_type, weight in weights.items():
            score_key = feature_type if feature_type != 'deep_learning' else 'deep'
            expected_score = weight
            actual_score = test_fabric.feature_scores.get(score_key, 0.0)
            
            if abs(actual_score - expected_score) < 0.001:
                print(f"✓ {score_key}: 期望={expected_score}, 实际={actual_score}")
            else:
                print(f"✗ {score_key}: 期望={expected_score}, 实际={actual_score}")
    
    print("\n=== 测试完成 ===")
    
    # 清理
    app.quit()

if __name__ == "__main__":
    test_feature_score_update()
