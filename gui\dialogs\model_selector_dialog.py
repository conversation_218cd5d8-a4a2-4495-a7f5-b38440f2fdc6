#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型选择对话框

该模块提供用户选择特征提取模型的界面。
"""

from typing import Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QCheckBox, QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QSettings

from utils.log_utils import LoggerMixin
from gui.widgets.widget_factory import WidgetFactory
from gui.common.message_helper import MessageHelper
from .model_config import ModelSelectorConfig
from .model_selector import ModelSelector
from .model_details import ModelDetailsWidget


class ModelSelectorDialog(QDialog, LoggerMixin):
    """模型选择对话框"""
    
    # 信号
    modelSelected = pyqtSignal(str)  # 模型名称
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setWindowTitle("选择特征提取模型")
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)
        
        # 组件
        self.widget_factory = WidgetFactory()
        self.settings = QSettings("FabricSearch", "ModelSelector")
        self.config = ModelSelectorConfig()
        
        # 当前选择的模型
        self.current_model = self.settings.value("current_model", self.config.default_model)
        self.config.default_model = self.current_model
        
        # UI组件
        self.model_selector = None
        self.model_details = None
        self.use_gpu_cb = None
        
        self.setup_ui()
        self.setup_connections()
        
        # 初始化显示
        self.model_details.update_model_details(self.current_model)
    
    def setup_ui(self):
        """设置界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 说明标签
        description_label = self.widget_factory.create_label(
            "请选择用于特征提取的深度学习模型。不同模型在特征提取能力、速度和资源消耗上各有优劣。"
        )
        description_label.setWordWrap(True)
        main_layout.addWidget(description_label)
        
        # 模型选择器
        self.model_selector = ModelSelector(self.config)
        main_layout.addWidget(self.model_selector)
        
        # 模型详情
        self.model_details = ModelDetailsWidget()
        main_layout.addWidget(self.model_details)
        
        # 高级选项
        advanced_group = self.widget_factory.create_group_box("高级选项", "vbox")
        advanced_layout = advanced_group.layout()
        
        self.use_gpu_cb = self.widget_factory.create_checkbox(
            "使用GPU加速（如果可用）", 
            checked=self.settings.value("use_gpu", self.config.use_gpu_default, type=bool)
        )
        advanced_layout.addWidget(self.use_gpu_cb)
        
        main_layout.addWidget(advanced_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        button_layout.addStretch()
        
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept_model)
        self.ok_button.setDefault(True)
        button_layout.addWidget(self.ok_button)
        
        main_layout.addLayout(button_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        # 模型选择变更
        self.model_selector.modelChanged.connect(self.on_model_changed)
    
    def on_model_changed(self, model_name: str):
        """模型选择变更处理
        
        Args:
            model_name: 选择的模型名称
        """
        self.current_model = model_name
        self.model_details.update_model_details(model_name)
    
    def accept_model(self):
        """接受选择的模型"""
        try:
            # 保存设置
            self.settings.setValue("current_model", self.current_model)
            self.settings.setValue("use_gpu", self.use_gpu_cb.isChecked())
            
            # 发送信号
            self.modelSelected.emit(self.current_model)
            
            # 关闭对话框
            self.accept()
            
        except Exception as e:
            self.logger.error(f"接受模型选择失败: {e}")
            MessageHelper.show_error(self, "错误", f"保存模型选择失败: {e}")
    
    def get_selected_model(self) -> str:
        """获取选择的模型名称"""
        return self.current_model
    
    def get_use_gpu(self) -> bool:
        """获取是否使用GPU"""
        return self.use_gpu_cb.isChecked() if self.use_gpu_cb else False