#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
颜色特征提取器模块

该模块实现颜色相关的传统图像特征提取，包括：
- 颜色直方图
- 主要颜色
- 颜色矩
"""

import logging
from typing import Optional
import numpy as np
from PIL import Image
import cv2
from sklearn.cluster import KMeans
from ..config.feature_config import TraditionalFeatureConfig

logger = logging.getLogger(__name__)


class ColorFeatureExtractor:
    """颜色特征提取器"""
    
    def __init__(self, config: TraditionalFeatureConfig):
        """初始化颜色特征提取器
        
        Args:
            config: 传统特征配置
        """
        if config is None:
            raise ValueError("Config cannot be None")
        
        self.config = config
        
        # 验证配置参数
        if not hasattr(config, 'hist_bins') or config.hist_bins <= 0:
            logger.warning("Invalid hist_bins, using default value 256")
            self.config.hist_bins = 256
            
        if not hasattr(config, 'n_dominant_colors') or config.n_dominant_colors <= 0:
            logger.warning("Invalid n_dominant_colors, using default value 5")
            self.config.n_dominant_colors = 5
    
    def extract_color_histogram(self, image: Image.Image) -> np.ndarray:
        """提取颜色直方图特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 颜色直方图特征
        """
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return np.zeros(self.config.hist_bins * 3, dtype=np.float32)
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return np.zeros(self.config.hist_bins * 3, dtype=np.float32)
            
            # 检查图像尺寸
            if image.size[0] == 0 or image.size[1] == 0:
                logger.error("Image has zero dimensions")
                return np.zeros(self.config.hist_bins * 3, dtype=np.float32)
            
            # 转换为numpy数组
            try:
                img_array = np.array(image)
            except Exception as e:
                logger.error(f"Failed to convert image to numpy array: {str(e)}")
                return np.zeros(self.config.hist_bins * 3, dtype=np.float32)
            
            # 验证数组
            if img_array.size == 0:
                logger.error("Image array is empty")
                return np.zeros(self.config.hist_bins * 3, dtype=np.float32)
            
            if len(img_array.shape) == 3:
                # RGB图像
                if img_array.shape[2] < 3:
                    logger.error(f"Invalid number of channels: {img_array.shape[2]}")
                    return np.zeros(self.config.hist_bins * 3, dtype=np.float32)
                
                try:
                    hist_r = cv2.calcHist([img_array], [0], None, [self.config.hist_bins], [0, 256])
                    hist_g = cv2.calcHist([img_array], [1], None, [self.config.hist_bins], [0, 256])
                    hist_b = cv2.calcHist([img_array], [2], None, [self.config.hist_bins], [0, 256])
                    
                    # 验证直方图
                    if hist_r is None or hist_g is None or hist_b is None:
                        logger.error("Failed to compute color histograms")
                        return np.zeros(self.config.hist_bins * 3, dtype=np.float32)
                    
                    # 归一化
                    hist_r = hist_r.flatten()
                    hist_g = hist_g.flatten()
                    hist_b = hist_b.flatten()
                    
                    # 检查并处理异常值
                    hist_r = np.nan_to_num(hist_r, nan=0.0, posinf=0.0, neginf=0.0)
                    hist_g = np.nan_to_num(hist_g, nan=0.0, posinf=0.0, neginf=0.0)
                    hist_b = np.nan_to_num(hist_b, nan=0.0, posinf=0.0, neginf=0.0)
                    
                    # 安全归一化
                    sum_r = np.sum(hist_r)
                    sum_g = np.sum(hist_g)
                    sum_b = np.sum(hist_b)
                    
                    hist_r = hist_r / sum_r if sum_r > 0 else hist_r
                    hist_g = hist_g / sum_g if sum_g > 0 else hist_g
                    hist_b = hist_b / sum_b if sum_b > 0 else hist_b
                    
                    result = np.concatenate([hist_r, hist_g, hist_b]).astype(np.float32)
                    
                    # 最终验证
                    if result.size != self.config.hist_bins * 3:
                        logger.error(f"Unexpected result size: {result.size}")
                        return np.zeros(self.config.hist_bins * 3, dtype=np.float32)
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"Error computing RGB histograms: {str(e)}")
                    return np.zeros(self.config.hist_bins * 3, dtype=np.float32)
                    
            else:
                # 灰度图像
                try:
                    hist = cv2.calcHist([img_array], [0], None, [self.config.hist_bins], [0, 256])
                    
                    if hist is None:
                        logger.error("Failed to compute grayscale histogram")
                        return np.zeros(self.config.hist_bins, dtype=np.float32)
                    
                    hist = hist.flatten()
                    
                    # 检查并处理异常值
                    hist = np.nan_to_num(hist, nan=0.0, posinf=0.0, neginf=0.0)
                    
                    # 安全归一化
                    sum_hist = np.sum(hist)
                    hist = hist / sum_hist if sum_hist > 0 else hist
                    
                    result = hist.astype(np.float32)
                    
                    # 最终验证
                    if result.size != self.config.hist_bins:
                        logger.error(f"Unexpected result size: {result.size}")
                        return np.zeros(self.config.hist_bins, dtype=np.float32)
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"Error computing grayscale histogram: {str(e)}")
                    return np.zeros(self.config.hist_bins, dtype=np.float32)
                
        except Exception as e:
            logger.error(f"Error extracting color histogram: {str(e)}")
            return np.zeros(self.config.hist_bins * 3, dtype=np.float32)
    
    def extract_dominant_colors(self, image: Image.Image) -> np.ndarray:
        """提取主要颜色特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 主要颜色特征
        """
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
            
            # 检查图像尺寸
            if image.size[0] == 0 or image.size[1] == 0:
                logger.error("Image has zero dimensions")
                return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
            
            # 转换为numpy数组并重塑
            try:
                img_array = np.array(image)
            except Exception as e:
                logger.error(f"Failed to convert image to numpy array: {str(e)}")
                return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
            
            # 验证数组
            if img_array.size == 0:
                logger.error("Image array is empty")
                return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
            
            # 重塑像素数据
            try:
                if len(img_array.shape) == 3:
                    if img_array.shape[2] < 3:
                        logger.error(f"Invalid number of channels: {img_array.shape[2]}")
                        return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
                    pixels = img_array.reshape(-1, 3)
                    is_rgb = True
                else:
                    pixels = img_array.reshape(-1, 1)
                    is_rgb = False
            except Exception as e:
                logger.error(f"Failed to reshape image array: {str(e)}")
                return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
            
            # 检查数据量，避免聚类数量大于数据点数量
            n_pixels = len(pixels)
            if n_pixels == 0:
                logger.error("No pixels found in image")
                return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
            
            n_clusters = min(self.config.n_dominant_colors, n_pixels)
            
            if n_clusters <= 0:
                logger.error("Invalid number of clusters")
                return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
            
            # 使用K-means聚类找到主要颜色
            try:
                import warnings
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore", category=UserWarning, module="sklearn")
                    
                    kmeans = KMeans(
                        n_clusters=n_clusters, 
                        random_state=42, 
                        n_init=10,
                        max_iter=300,
                        tol=1e-4
                    )
                    kmeans.fit(pixels)
                
                # 获取聚类中心（主要颜色）
                dominant_colors = kmeans.cluster_centers_
                
                # 验证聚类结果
                if dominant_colors is None or len(dominant_colors) == 0:
                    logger.error("K-means clustering failed")
                    return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
                
                # 计算每个颜色的权重（像素数量比例）
                labels = kmeans.labels_
                if labels is None or len(labels) == 0:
                    logger.error("K-means labels are invalid")
                    return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
                
                weights = np.bincount(labels) / len(labels)
                
                # 检查并处理异常值
                dominant_colors = np.nan_to_num(dominant_colors, nan=0.0, posinf=255.0, neginf=0.0)
                weights = np.nan_to_num(weights, nan=0.0, posinf=1.0, neginf=0.0)
                
                # 按权重排序
                sorted_indices = np.argsort(weights)[::-1]
                dominant_colors = dominant_colors[sorted_indices]
                weights = weights[sorted_indices]
                
                # 展平为特征向量
                features = []
                for i in range(self.config.n_dominant_colors):
                    if i < len(dominant_colors):
                        color = dominant_colors[i]
                        weight = weights[i]
                        
                        # 确保颜色值在有效范围内
                        color = np.clip(color, 0, 255)
                        weight = np.clip(weight, 0, 1)
                        
                        if is_rgb:
                            features.extend([color[0], color[1], color[2], weight])
                        else:
                            features.extend([color[0], 0, 0, weight])  # 灰度转RGB格式
                    else:
                        # 填充零值
                        features.extend([0, 0, 0, 0])  # RGB + weight
                
                result = np.array(features, dtype=np.float32)
                
                # 最终验证
                expected_size = self.config.n_dominant_colors * 4
                if result.size != expected_size:
                    logger.error(f"Unexpected result size: {result.size}, expected: {expected_size}")
                    return np.zeros(expected_size, dtype=np.float32)
                
                return result
                
            except Exception as e:
                logger.error(f"Error in K-means clustering: {str(e)}")
                return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error extracting dominant colors: {str(e)}")
            return np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32)
    
    def extract_color_moments(self, image: Image.Image) -> np.ndarray:
        """提取颜色矩特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 颜色矩特征
        """
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return np.zeros(9, dtype=np.float32)
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return np.zeros(9, dtype=np.float32)
            
            # 检查图像尺寸
            if image.size[0] == 0 or image.size[1] == 0:
                logger.error("Image has zero dimensions")
                return np.zeros(9, dtype=np.float32)
            
            # 转换为numpy数组
            try:
                img_array = np.array(image)
            except Exception as e:
                logger.error(f"Failed to convert image to numpy array: {str(e)}")
                return np.zeros(9, dtype=np.float32)
            
            # 验证数组
            if img_array.size == 0:
                logger.error("Image array is empty")
                return np.zeros(9, dtype=np.float32)
            
            if len(img_array.shape) == 3:
                # RGB图像，计算每个通道的前三阶矩
                if img_array.shape[2] < 3:
                    logger.error(f"Invalid number of channels: {img_array.shape[2]}")
                    return np.zeros(9, dtype=np.float32)
                
                moments = []
                try:
                    for channel in range(3):
                        channel_data = img_array[:, :, channel].flatten()
                        
                        # 验证通道数据
                        if len(channel_data) == 0:
                            logger.warning(f"Channel {channel} is empty")
                            moments.extend([0.0, 0.0, 0.0])
                            continue
                        
                        # 检查并处理异常值
                        channel_data = np.nan_to_num(channel_data, nan=0.0, posinf=255.0, neginf=0.0)
                        channel_data = channel_data.astype(np.float64)  # 使用更高精度
                        
                        # 一阶矩（均值）
                        mean = np.mean(channel_data)
                        mean = np.nan_to_num(mean, nan=0.0)
                        
                        # 二阶矩（标准差）
                        std = np.std(channel_data)
                        std = np.nan_to_num(std, nan=0.0)
                        
                        # 三阶矩（偏度）
                        if std > 1e-10:  # 避免除零
                            try:
                                normalized_data = (channel_data - mean) / std
                                skewness = np.mean(normalized_data ** 3)
                                skewness = np.nan_to_num(skewness, nan=0.0)
                            except Exception as e:
                                logger.warning(f"Error computing skewness for channel {channel}: {str(e)}")
                                skewness = 0.0
                        else:
                            skewness = 0.0
                        
                        # 确保值在合理范围内
                        mean = np.clip(mean, 0, 255)
                        std = np.clip(std, 0, 255)
                        skewness = np.clip(skewness, -10, 10)  # 偏度通常在这个范围内
                        
                        moments.extend([mean, std, skewness])
                    
                    result = np.array(moments, dtype=np.float32)
                    
                    # 最终验证
                    if result.size != 9:
                        logger.error(f"Unexpected result size: {result.size}")
                        return np.zeros(9, dtype=np.float32)
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"Error computing RGB color moments: {str(e)}")
                    return np.zeros(9, dtype=np.float32)
                
            else:
                # 灰度图像
                try:
                    channel_data = img_array.flatten()
                    
                    # 验证数据
                    if len(channel_data) == 0:
                        logger.error("Grayscale image data is empty")
                        return np.zeros(3, dtype=np.float32)
                    
                    # 检查并处理异常值
                    channel_data = np.nan_to_num(channel_data, nan=0.0, posinf=255.0, neginf=0.0)
                    channel_data = channel_data.astype(np.float64)
                    
                    # 一阶矩（均值）
                    mean = np.mean(channel_data)
                    mean = np.nan_to_num(mean, nan=0.0)
                    
                    # 二阶矩（标准差）
                    std = np.std(channel_data)
                    std = np.nan_to_num(std, nan=0.0)
                    
                    # 三阶矩（偏度）
                    if std > 1e-10:  # 避免除零
                        try:
                            normalized_data = (channel_data - mean) / std
                            skewness = np.mean(normalized_data ** 3)
                            skewness = np.nan_to_num(skewness, nan=0.0)
                        except Exception as e:
                            logger.warning(f"Error computing skewness for grayscale: {str(e)}")
                            skewness = 0.0
                    else:
                        skewness = 0.0
                    
                    # 确保值在合理范围内
                    mean = np.clip(mean, 0, 255)
                    std = np.clip(std, 0, 255)
                    skewness = np.clip(skewness, -10, 10)
                    
                    result = np.array([mean, std, skewness], dtype=np.float32)
                    
                    # 最终验证
                    if result.size != 3:
                        logger.error(f"Unexpected result size: {result.size}")
                        return np.zeros(3, dtype=np.float32)
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"Error computing grayscale color moments: {str(e)}")
                    return np.zeros(3, dtype=np.float32)
                
        except Exception as e:
            logger.error(f"Error extracting color moments: {str(e)}")
            return np.zeros(9, dtype=np.float32)  # 默认返回RGB格式大小
    
    def extract_features(self, image: Image.Image) -> np.ndarray:
        """提取所有颜色特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 颜色特征向量
        """
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return np.zeros(1, dtype=np.float32)
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return np.zeros(1, dtype=np.float32)
            
            # 提取各种颜色特征
            features = []
            
            # 颜色直方图
            try:
                hist_features = self.extract_color_histogram(image)
                features.append(hist_features)
            except Exception as e:
                logger.error(f"Error extracting color histogram: {str(e)}")
                features.append(np.zeros(self.config.hist_bins * 3, dtype=np.float32))
            
            # 主要颜色
            try:
                dominant_features = self.extract_dominant_colors(image)
                features.append(dominant_features)
            except Exception as e:
                logger.error(f"Error extracting dominant colors: {str(e)}")
                features.append(np.zeros(self.config.n_dominant_colors * 4, dtype=np.float32))
            
            # 颜色矩
            try:
                moment_features = self.extract_color_moments(image)
                features.append(moment_features)
            except Exception as e:
                logger.error(f"Error extracting color moments: {str(e)}")
                features.append(np.zeros(9, dtype=np.float32))
            
            # 合并所有特征
            try:
                combined_features = np.concatenate(features)
                
                # 验证结果
                if combined_features.size == 0:
                    logger.error("Combined features are empty")
                    return np.zeros(1, dtype=np.float32)
                
                # 检查并处理异常值
                combined_features = np.nan_to_num(combined_features, nan=0.0, posinf=1.0, neginf=-1.0)
                
                return combined_features.astype(np.float32)
                
            except Exception as e:
                logger.error(f"Error combining features: {str(e)}")
                return np.zeros(1, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error extracting color features: {str(e)}")
            return np.zeros(1, dtype=np.float32)