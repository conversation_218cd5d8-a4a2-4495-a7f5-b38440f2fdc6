#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题方案模块

该模块定义了应用程序的主题类型、颜色方案、字体方案和主题类。
"""

from enum import Enum
from typing import Dict, Any, Optional
from dataclasses import dataclass, field


class ThemeType(Enum):
    """主题类型"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"  # 跟随系统
    CUSTOM = "custom"


@dataclass
class ColorScheme:
    """颜色方案"""
    # 基础颜色
    primary: str = "#2196F3"
    secondary: str = "#FFC107"
    success: str = "#4CAF50"
    warning: str = "#FF9800"
    error: str = "#F44336"
    info: str = "#00BCD4"
    
    # 背景颜色
    background: str = "#FFFFFF"
    surface: str = "#F5F5F5"
    card: str = "#FFFFFF"
    
    # 文本颜色
    text: str = "#212121"  # 兼容旧版本测试
    text_primary: str = "#212121"
    text_secondary: str = "#757575"
    text_disabled: str = "#BDBDBD"
    
    # 边框颜色
    border: str = "#E0E0E0"
    divider: str = "#EEEEEE"
    
    # 状态颜色
    hover: str = "#F0F0F0"
    selected: str = "#E3F2FD"
    focus: str = "#BBDEFB"
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典"""
        return {
            'primary': self.primary,
            'secondary': self.secondary,
            'success': self.success,
            'warning': self.warning,
            'error': self.error,
            'info': self.info,
            'background': self.background,
            'surface': self.surface,
            'card': self.card,
            'text_primary': self.text_primary,
            'text_secondary': self.text_secondary,
            'text_disabled': self.text_disabled,
            'border': self.border,
            'divider': self.divider,
            'hover': self.hover,
            'selected': self.selected,
            'focus': self.focus
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, str]) -> 'ColorScheme':
        """从字典创建"""
        return cls(**data)


@dataclass
class FontScheme:
    """字体方案"""
    # 字体族
    family: str = "Microsoft YaHei, Arial, sans-serif"
    monospace_family: str = "Consolas, Monaco, 'Courier New', monospace"
    
    # 字体大小
    size_small: int = 8
    size_normal: int = 9
    size_medium: int = 10
    size_large: int = 12
    size_xlarge: int = 14
    size_title: int = 16
    
    # 字体权重
    weight_normal: int = 400
    weight_medium: int = 500
    weight_bold: int = 700
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'family': self.family,
            'monospace_family': self.monospace_family,
            'size_small': self.size_small,
            'size_normal': self.size_normal,
            'size_medium': self.size_medium,
            'size_large': self.size_large,
            'size_xlarge': self.size_xlarge,
            'size_title': self.size_title,
            'weight_normal': self.weight_normal,
            'weight_medium': self.weight_medium,
            'weight_bold': self.weight_bold
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FontScheme':
        """从字典创建"""
        return cls(**data)


@dataclass
class Theme:
    """主题"""
    name: str
    display_name: str
    theme_type: ThemeType
    color_scheme: ColorScheme
    font_scheme: FontScheme
    stylesheet: str = ""
    custom_properties: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'display_name': self.display_name,
            'theme_type': self.theme_type.value,
            'color_scheme': self.color_scheme.to_dict(),
            'font_scheme': self.font_scheme.to_dict(),
            'stylesheet': self.stylesheet,
            'custom_properties': self.custom_properties
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Theme':
        """从字典创建"""
        return cls(
            name=data['name'],
            display_name=data['display_name'],
            theme_type=ThemeType(data['theme_type']),
            color_scheme=ColorScheme.from_dict(data['color_scheme']),
            font_scheme=FontScheme.from_dict(data['font_scheme']),
            stylesheet=data.get('stylesheet', ''),
            custom_properties=data.get('custom_properties', {})
        )