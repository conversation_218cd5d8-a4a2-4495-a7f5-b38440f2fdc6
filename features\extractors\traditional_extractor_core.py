"""
传统特征提取器核心模块

该模块整合了颜色、纹理和形状特征提取器，提供统一的传统特征提取接口。
"""

import numpy as np
from PIL import Image
from typing import Dict
import logging

from ..config import TraditionalFeatureConfig
from .color_extractor import ColorFeatureExtractor
from .texture_extractor import TextureFeatureExtractor
from .shape_extractor import ShapeFeatureExtractor

logger = logging.getLogger(__name__)


class TraditionalFeatureExtractor:
    """传统特征提取器主类"""
    
    def __init__(self, config: TraditionalFeatureConfig):
        """初始化传统特征提取器
        
        Args:
            config: 传统特征配置
        """
        try:
            # 输入验证
            if config is None:
                logger.error("Config is None")
                raise ValueError("Config cannot be None")
                
            if not isinstance(config, TraditionalFeatureConfig):
                logger.error(f"Invalid config type: {type(config)}")
                raise TypeError("Config must be TraditionalFeatureConfig instance")
            
            self.config = config
            
            # 初始化子提取器
            try:
                self.color_extractor = ColorFeatureExtractor(config) if config.extract_color else None
            except Exception as e:
                logger.error(f"Error initializing color extractor: {str(e)}")
                self.color_extractor = None
                
            try:
                self.texture_extractor = TextureFeatureExtractor(config) if config.extract_texture else None
            except Exception as e:
                logger.error(f"Error initializing texture extractor: {str(e)}")
                self.texture_extractor = None
                
            try:
                self.shape_extractor = ShapeFeatureExtractor(config) if config.extract_shape else None
            except Exception as e:
                logger.error(f"Error initializing shape extractor: {str(e)}")
                self.shape_extractor = None
                
        except Exception as e:
            logger.error(f"Error initializing TraditionalFeatureExtractor: {str(e)}")
            raise
    
    def extract_features(self, image: Image.Image) -> Dict[str, np.ndarray]:
        """提取所有传统特征
        
        Args:
            image: 输入图像
            
        Returns:
            Dict[str, np.ndarray]: 特征字典
        """
        features = {}
        
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return features
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return features
            
            # 提取颜色特征
            if self.color_extractor:
                try:
                    color_features = self.color_extractor.extract_features(image)
                    
                    # 验证颜色特征
                    if color_features is not None and color_features.size > 0:
                        # 检查并处理异常值
                        color_features = np.nan_to_num(color_features, nan=0.0, posinf=1e6, neginf=0.0)
                        features['color'] = color_features
                    else:
                        logger.warning("Color features extraction failed")
                        
                except Exception as e:
                    logger.error(f"Error extracting color features: {str(e)}")
            
            # 提取纹理特征
            if self.texture_extractor:
                try:
                    texture_features = self.texture_extractor.extract_features(image)
                    
                    # 验证纹理特征
                    if texture_features is not None and texture_features.size > 0:
                        # 检查并处理异常值
                        texture_features = np.nan_to_num(texture_features, nan=0.0, posinf=1e6, neginf=0.0)
                        features['texture'] = texture_features
                    else:
                        logger.warning("Texture features extraction failed")
                        
                except Exception as e:
                    logger.error(f"Error extracting texture features: {str(e)}")
            
            # 提取形状特征
            if self.shape_extractor:
                try:
                    shape_features = self.shape_extractor.extract_features(image)
                    
                    # 验证形状特征
                    if shape_features is not None and shape_features.size > 0:
                        # 检查并处理异常值
                        shape_features = np.nan_to_num(shape_features, nan=0.0, posinf=1e6, neginf=0.0)
                        features['shape'] = shape_features
                    else:
                        logger.warning("Shape features extraction failed")
                        
                except Exception as e:
                    logger.error(f"Error extracting shape features: {str(e)}")
            
        except Exception as e:
            logger.error(f"Error extracting traditional features: {str(e)}")
        
        return features
    
    def extract_combined_features(self, image: Image.Image) -> np.ndarray:
        """提取合并的传统特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 合并的特征向量
        """
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return np.array([], dtype=np.float32)
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return np.array([], dtype=np.float32)
            
            feature_dict = self.extract_features(image)
            
            # 合并所有特征
            combined_features = []
            
            for feature_type in ['color', 'texture', 'shape']:
                if feature_type in feature_dict:
                    try:
                        features = feature_dict[feature_type]
                        
                        # 验证特征
                        if features is not None and features.size > 0:
                            # 确保是一维数组
                            if features.ndim > 1:
                                features = features.flatten()
                            
                            # 检查并处理异常值
                            features = np.nan_to_num(features, nan=0.0, posinf=1e6, neginf=0.0)
                            
                            combined_features.extend(features)
                        else:
                            logger.warning(f"{feature_type} features are empty or None")
                            
                    except Exception as e:
                        logger.error(f"Error processing {feature_type} features: {str(e)}")
            
            # 转换为numpy数组
            try:
                if combined_features:
                    result = np.array(combined_features, dtype=np.float32)
                    
                    # 最终验证
                    if result.size == 0:
                        logger.warning("Combined features array is empty")
                        return np.array([], dtype=np.float32)
                    
                    # 最终清理异常值
                    result = np.nan_to_num(result, nan=0.0, posinf=1e6, neginf=0.0)
                    
                    return result
                else:
                    logger.warning("No valid features extracted")
                    return np.array([], dtype=np.float32)
                    
            except Exception as e:
                logger.error(f"Error creating combined features array: {str(e)}")
                return np.array([], dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error extracting combined features: {str(e)}")
            return np.array([], dtype=np.float32)