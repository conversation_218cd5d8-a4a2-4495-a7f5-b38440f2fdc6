# -*- coding: utf-8 -*-
"""
特征数据库模块

该模块实现特征数据的存储、管理和检索功能，支持多种数据库类型。
"""

import logging
import pickle
import json
import numpy as np
import sqlite3
from typing import Dict, List, Any, Optional, Union, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
import h5py
from abc import ABC, abstractmethod

from utils.log_utils import LoggerMixin
from utils.file_utils import FileManager
from database.vector_database import VectorDatabase, create_vector_database


@dataclass
class FeatureRecord:
    """特征记录"""
    image_path: str
    features: Dict[str, np.ndarray]
    metadata: Dict[str, Any]
    timestamp: float
    feature_version: str = "1.0"


class FeatureDatabaseInterface(ABC):
    """特征数据库接口"""
    
    @abstractmethod
    def load(self) -> bool:
        """加载数据库"""
        pass
    
    @abstractmethod
    def save(self) -> bool:
        """保存数据库"""
        pass
    
    @abstractmethod
    def add_features(self, image_path: str, features: Dict[str, np.ndarray], 
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """添加特征"""
        pass
    
    @abstractmethod
    def get_features(self, image_path: str) -> Optional[Dict[str, np.ndarray]]:
        """获取特征"""
        pass
    
    @abstractmethod
    def update_features(self, image_path: str, features: Dict[str, np.ndarray], 
                       metadata: Optional[Dict[str, Any]] = None) -> bool:
        """更新特征"""
        pass
    
    @abstractmethod
    def delete_features(self, image_path: str) -> bool:
        """删除特征"""
        pass
    
    @abstractmethod
    def get_all_features(self) -> Dict[str, Dict[str, np.ndarray]]:
        """获取所有特征"""
        pass
    
    @abstractmethod
    def get_image_paths(self) -> List[str]:
        """获取所有图像路径"""
        pass


class StandardFeatureDatabase(FeatureDatabaseInterface, LoggerMixin):
    """标准特征数据库（基于pickle）"""
    
    def __init__(self, db_path: Union[str, Path]):
        super().__init__()
        self.db_path = Path(db_path)
        self.features = {}
        self.metadata = {}
        self.image_paths = []
        
        # 确保目录存在
        file_manager = FileManager()
        file_manager.ensure_dir(self.db_path.parent)
    
    def load(self) -> bool:
        """加载数据库"""
        try:
            if not self.db_path.exists():
                self.logger.info(f"数据库文件不存在，将创建新数据库: {self.db_path}")
                return True
            
            with open(self.db_path, 'rb') as f:
                data = pickle.load(f)
            
            self.features = data.get('features', {})
            self.metadata = data.get('metadata', {})
            self.image_paths = data.get('image_paths', [])
            
            self.logger.info(f"标准数据库加载成功，包含 {len(self.image_paths)} 条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"标准数据库加载失败: {e}")
            return False
    
    def save(self) -> bool:
        """保存数据库"""
        try:
            data = {
                'features': self.features,
                'metadata': self.metadata,
                'image_paths': self.image_paths
            }
            
            # 创建临时文件，确保原子性写入
            temp_path = self.db_path.with_suffix('.tmp')
            with open(temp_path, 'wb') as f:
                pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
            
            # 原子性替换
            temp_path.replace(self.db_path)
            
            self.logger.debug(f"标准数据库保存成功: {self.db_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"标准数据库保存失败: {e}")
            return False
    
    def add_features(self, image_path: str, features: Dict[str, np.ndarray], 
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """添加特征"""
        try:
            if image_path in self.features:
                self.logger.warning(f"图像 {image_path} 的特征已存在，将被覆盖")
            
            self.features[image_path] = features
            self.metadata[image_path] = metadata or {}
            
            if image_path not in self.image_paths:
                self.image_paths.append(image_path)
            
            return True
            
        except Exception as e:
            self.logger.error(f"添加特征失败 {image_path}: {e}")
            return False
    
    def get_features(self, image_path: str) -> Optional[Dict[str, np.ndarray]]:
        """获取特征"""
        return self.features.get(image_path)
    
    def update_features(self, image_path: str, features: Dict[str, np.ndarray], 
                       metadata: Optional[Dict[str, Any]] = None) -> bool:
        """更新特征"""
        try:
            if image_path not in self.features:
                self.logger.warning(f"图像 {image_path} 不存在，将添加新记录")
                return self.add_features(image_path, features, metadata)
            
            # 更新特征
            self.features[image_path].update(features)
            
            # 更新元数据
            if metadata:
                self.metadata[image_path].update(metadata)
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新特征失败 {image_path}: {e}")
            return False
    
    def delete_features(self, image_path: str) -> bool:
        """删除特征"""
        try:
            if image_path not in self.features:
                self.logger.warning(f"图像 {image_path} 不存在")
                return False
            
            del self.features[image_path]
            del self.metadata[image_path]
            self.image_paths.remove(image_path)
            
            self.logger.debug(f"删除特征成功: {image_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除特征失败 {image_path}: {e}")
            return False
    
    def get_all_features(self) -> Dict[str, Dict[str, np.ndarray]]:
        """获取所有特征"""
        return self.features.copy()
    
    def get_image_paths(self) -> List[str]:
        """获取所有图像路径"""
        return self.image_paths.copy()


class ChunkedFeatureDatabase(FeatureDatabaseInterface, LoggerMixin):
    """分块特征数据库（适用于大规模数据）"""
    
    def __init__(self, db_path: Union[str, Path], chunk_size: int = 1000):
        super().__init__()
        self.db_path = Path(db_path)
        self.chunk_size = chunk_size
        self.chunks = {}
        self.index = {}  # image_path -> chunk_id
        self.metadata = {}
        
        # 确保目录存在
        file_manager = FileManager()
        file_manager.ensure_dir(self.db_path)
    
    def load(self) -> bool:
        """加载数据库"""
        try:
            # 加载索引文件
            index_path = self.db_path / 'index.json'
            if index_path.exists():
                with open(index_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.index = data.get('index', {})
                    self.metadata = data.get('metadata', {})
            
            # 加载所有分块
            for chunk_file in self.db_path.glob('chunk_*.pkl'):
                chunk_id = chunk_file.stem
                try:
                    with open(chunk_file, 'rb') as f:
                        self.chunks[chunk_id] = pickle.load(f)
                except Exception as e:
                    self.logger.error(f"加载分块失败 {chunk_id}: {e}")
            
            self.logger.info(f"分块数据库加载成功，包含 {len(self.index)} 条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"分块数据库加载失败: {e}")
            return False
    
    def save(self) -> bool:
        """保存数据库"""
        try:
            # 保存索引文件
            index_data = {
                'index': self.index,
                'metadata': self.metadata
            }
            
            index_path = self.db_path / 'index.json'
            with open(index_path, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, ensure_ascii=False, indent=2)
            
            # 保存所有分块
            for chunk_id, chunk_data in self.chunks.items():
                chunk_path = self.db_path / f'{chunk_id}.pkl'
                with open(chunk_path, 'wb') as f:
                    pickle.dump(chunk_data, f, protocol=pickle.HIGHEST_PROTOCOL)
            
            self.logger.debug(f"分块数据库保存成功: {self.db_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"分块数据库保存失败: {e}")
            return False
    
    def _get_chunk_id(self, image_path: str) -> str:
        """获取或创建分块ID"""
        if image_path in self.index:
            return self.index[image_path]
        
        # 找到当前最小的分块
        chunk_sizes = {}
        for path, chunk_id in self.index.items():
            chunk_sizes[chunk_id] = chunk_sizes.get(chunk_id, 0) + 1
        
        # 找到未满的分块
        for chunk_id, size in chunk_sizes.items():
            if size < self.chunk_size:
                return chunk_id
        
        # 创建新分块
        chunk_id = f"chunk_{len(chunk_sizes):04d}"
        self.chunks[chunk_id] = {}
        return chunk_id
    
    def add_features(self, image_path: str, features: Dict[str, np.ndarray], 
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """添加特征"""
        try:
            chunk_id = self._get_chunk_id(image_path)
            
            if chunk_id not in self.chunks:
                self.chunks[chunk_id] = {}
            
            self.chunks[chunk_id][image_path] = features
            self.index[image_path] = chunk_id
            self.metadata[image_path] = metadata or {}
            
            return True
            
        except Exception as e:
            self.logger.error(f"添加特征失败 {image_path}: {e}")
            return False
    
    def get_features(self, image_path: str) -> Optional[Dict[str, np.ndarray]]:
        """获取特征"""
        try:
            if image_path not in self.index:
                return None
            
            chunk_id = self.index[image_path]
            if chunk_id not in self.chunks:
                return None
            
            return self.chunks[chunk_id].get(image_path)
            
        except Exception as e:
            self.logger.error(f"获取特征失败 {image_path}: {e}")
            return None
    
    def update_features(self, image_path: str, features: Dict[str, np.ndarray], 
                       metadata: Optional[Dict[str, Any]] = None) -> bool:
        """更新特征"""
        try:
            if image_path not in self.index:
                return self.add_features(image_path, features, metadata)
            
            chunk_id = self.index[image_path]
            if chunk_id in self.chunks and image_path in self.chunks[chunk_id]:
                self.chunks[chunk_id][image_path].update(features)
                
                if metadata:
                    self.metadata[image_path].update(metadata)
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"更新特征失败 {image_path}: {e}")
            return False
    
    def delete_features(self, image_path: str) -> bool:
        """删除特征"""
        try:
            if image_path not in self.index:
                return False
            
            chunk_id = self.index[image_path]
            if chunk_id in self.chunks and image_path in self.chunks[chunk_id]:
                del self.chunks[chunk_id][image_path]
            
            del self.index[image_path]
            if image_path in self.metadata:
                del self.metadata[image_path]
            
            return True
            
        except Exception as e:
            self.logger.error(f"删除特征失败 {image_path}: {e}")
            return False
    
    def get_all_features(self) -> Dict[str, Dict[str, np.ndarray]]:
        """获取所有特征"""
        all_features = {}
        for chunk_data in self.chunks.values():
            all_features.update(chunk_data)
        return all_features
    
    def get_image_paths(self) -> List[str]:
        """获取所有图像路径"""
        return list(self.index.keys())


class VectorFeatureDatabase(FeatureDatabaseInterface, LoggerMixin):
    """向量特征数据库（基于HDF5，适用于高维特征）"""
    
    def __init__(self, db_path: Union[str, Path], vector_db_type: str = 'simple'):
        super().__init__()
        self.db_path = Path(db_path)
        self.h5_file = None
        self.metadata = {}
        self.vector_db = None
        self.vector_db_type = vector_db_type
        self.file_manager = FileManager()
        
        # 确保目录存在
        self.file_manager.ensure_dir(self.db_path.parent)
    
    def load(self) -> bool:
        """加载数据库"""
        try:
            if not self.db_path.exists():
                self.logger.info(f"向量数据库文件不存在，将创建新数据库: {self.db_path}")
                return True
            
            self.h5_file = h5py.File(self.db_path, 'r+')
            
            # 加载元数据
            metadata_path = self.db_path.with_suffix('.meta.json')
            if metadata_path.exists():
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
            
            self.logger.info(f"向量数据库加载成功，包含 {len(list(self.h5_file.keys()))} 条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"向量数据库加载失败: {e}")
            return False
    
    def save(self) -> bool:
        """保存数据库"""
        try:
            if self.h5_file:
                self.h5_file.flush()
            
            # 保存元数据
            metadata_path = self.db_path.with_suffix('.meta.json')
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
            
            self.logger.debug(f"向量数据库保存成功: {self.db_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"向量数据库保存失败: {e}")
            return False
    
    def _ensure_h5_file(self):
        """确保HDF5文件已打开"""
        if self.h5_file is None:
            self.h5_file = h5py.File(self.db_path, 'a')
    
    def _encode_path(self, image_path: str) -> str:
        """编码路径为HDF5兼容的键"""
        return image_path.replace('/', '_').replace('\\', '_').replace(':', '_')
    
    def add_features(self, image_path: str, features: Dict[str, np.ndarray], 
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """添加特征"""
        try:
            self._ensure_h5_file()
            
            encoded_path = self._encode_path(image_path)
            
            # 创建组
            if encoded_path in self.h5_file:
                del self.h5_file[encoded_path]
            
            group = self.h5_file.create_group(encoded_path)
            
            # 保存特征
            for feature_name, feature_data in features.items():
                group.create_dataset(feature_name, data=feature_data, compression='gzip')
            
            # 保存元数据
            self.metadata[image_path] = metadata or {}
            
            return True
            
        except Exception as e:
            self.logger.error(f"添加特征失败 {image_path}: {e}")
            return False
    
    def get_features(self, image_path: str) -> Optional[Dict[str, np.ndarray]]:
        """获取特征"""
        try:
            if self.h5_file is None:
                return None
            
            encoded_path = self._encode_path(image_path)
            
            if encoded_path not in self.h5_file:
                return None
            
            group = self.h5_file[encoded_path]
            features = {}
            
            for feature_name in group.keys():
                features[feature_name] = group[feature_name][:]
            
            return features
            
        except Exception as e:
            self.logger.error(f"获取特征失败 {image_path}: {e}")
            return None
    
    def update_features(self, image_path: str, features: Dict[str, np.ndarray], 
                       metadata: Optional[Dict[str, Any]] = None) -> bool:
        """更新特征"""
        try:
            self._ensure_h5_file()
            
            encoded_path = self._encode_path(image_path)
            
            if encoded_path not in self.h5_file:
                return self.add_features(image_path, features, metadata)
            
            group = self.h5_file[encoded_path]
            
            # 更新特征
            for feature_name, feature_data in features.items():
                if feature_name in group:
                    del group[feature_name]
                group.create_dataset(feature_name, data=feature_data, compression='gzip')
            
            # 更新元数据
            if metadata:
                if image_path not in self.metadata:
                    self.metadata[image_path] = {}
                self.metadata[image_path].update(metadata)
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新特征失败 {image_path}: {e}")
            return False
    
    def delete_features(self, image_path: str) -> bool:
        """删除特征"""
        try:
            if self.h5_file is None:
                return False
            
            encoded_path = self._encode_path(image_path)
            
            if encoded_path in self.h5_file:
                del self.h5_file[encoded_path]
            
            if image_path in self.metadata:
                del self.metadata[image_path]
            
            return True
            
        except Exception as e:
            self.logger.error(f"删除特征失败 {image_path}: {e}")
            return False
    
    def get_all_features(self) -> Dict[str, Dict[str, np.ndarray]]:
        """获取所有特征"""
        if self.h5_file is None:
            return {}
        
        all_features = {}
        for encoded_path in self.h5_file.keys():
            # 解码路径
            original_path = None
            for path in self.metadata.keys():
                if self._encode_path(path) == encoded_path:
                    original_path = path
                    break
            
            if original_path:
                all_features[original_path] = self.get_features(original_path)
        
        return all_features
    
    def get_image_paths(self) -> List[str]:
        """获取所有图像路径"""
        return list(self.metadata.keys())
    
    def build_index(self, feature_name: str = 'default', **kwargs) -> bool:
        """构建向量索引
        
        Args:
            feature_name: 特征名称
            **kwargs: 向量数据库参数
            
        Returns:
            bool: 构建是否成功
        """
        try:
            if not self.h5_file:
                self.logger.error("数据库未加载")
                return False
            
            # 收集所有特征向量
            features = {}
            for encoded_path in self.h5_file.keys():
                group = self.h5_file[encoded_path]
                if feature_name in group:
                    # 找到原始路径
                    original_path = None
                    for path in self.metadata.keys():
                        if self._encode_path(path) == encoded_path:
                            original_path = path
                            break
                    
                    if original_path:
                        features[original_path] = group[feature_name][:]
            
            if not features:
                self.logger.warning(f"未找到特征 '{feature_name}'")
                return False
            
            # 创建向量数据库
            if self.vector_db is None:
                self.vector_db = create_vector_database(self.vector_db_type, **kwargs)
            
            # 构建索引
            return self.vector_db.build_index(features, **kwargs)
            
        except Exception as e:
            self.logger.error(f"构建向量索引失败: {e}")
            return False
    
    def search(self, query_vector: np.ndarray, k: int = 10, **kwargs) -> List[Tuple[str, float]]:
        """搜索相似向量
        
        Args:
            query_vector: 查询向量
            k: 返回结果数量
            **kwargs: 搜索参数
            
        Returns:
            List[Tuple[str, float]]: 搜索结果列表，每个元素为(图像路径, 相似度分数)
        """
        try:
            if self.vector_db is None:
                self.logger.error("向量索引未构建")
                return []
            
            return self.vector_db.search(query_vector, k, **kwargs)
            
        except Exception as e:
            self.logger.error(f"向量搜索失败: {e}")
            return []
    
    def save_index(self, index_path: Optional[Path] = None) -> bool:
        """保存向量索引
        
        Args:
            index_path: 索引保存路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            if self.vector_db is None:
                return False
            
            if index_path is None:
                index_path = self.db_path.with_suffix('.index')
            
            return self.vector_db.save_index(index_path)
            
        except Exception as e:
            self.logger.error(f"保存向量索引失败: {e}")
            return False
    
    def load_index(self, index_path: Optional[Path] = None) -> bool:
        """加载向量索引
        
        Args:
            index_path: 索引文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            if index_path is None:
                index_path = self.db_path.with_suffix('.index')
            
            if not index_path.exists():
                self.logger.warning(f"索引文件不存在: {index_path}")
                return False
            
            if self.vector_db is None:
                self.vector_db = create_vector_database(self.vector_db_type)
            
            return self.vector_db.load_index(index_path)
            
        except Exception as e:
            self.logger.error(f"加载向量索引失败: {e}")
            return False
    
    def close(self):
        """关闭数据库"""
        if self.h5_file:
            self.h5_file.close()
            self.h5_file = None
        if self.vector_db:
            self.vector_db = None
    
    def __del__(self):
        """析构函数"""
        self.close()


class FeatureDatabase(LoggerMixin):
    """特征数据库管理器"""
    
    def __init__(self, db_path: Union[str, Path], db_type: str = 'standard', **kwargs):
        """初始化特征数据库
        
        Args:
            db_path: 数据库路径
            db_type: 数据库类型，可选值：'standard', 'chunked', 'vector'
            **kwargs: 其他参数
        """
        super().__init__()
        self.db_path = Path(db_path)
        self.db_type = db_type
        
        # 创建具体的数据库实例
        if db_type == 'standard':
            self.db = StandardFeatureDatabase(db_path)
        elif db_type == 'chunked':
            chunk_size = kwargs.get('chunk_size', 1000)
            self.db = ChunkedFeatureDatabase(db_path, chunk_size)
        elif db_type == 'vector':
            self.db = VectorFeatureDatabase(db_path)
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
        
        self.logger.info(f"特征数据库初始化完成，类型: {db_type}")
    
    def load(self) -> bool:
        """加载数据库"""
        return self.db.load()
    
    def save(self) -> bool:
        """保存数据库"""
        return self.db.save()
    
    def add_features(self, image_path: str, features: Dict[str, np.ndarray], 
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """添加特征"""
        return self.db.add_features(image_path, features, metadata)
    
    def get_features(self, image_path: str) -> Optional[Dict[str, np.ndarray]]:
        """获取特征"""
        return self.db.get_features(image_path)
    
    def update_features(self, image_path: str, features: Dict[str, np.ndarray], 
                       metadata: Optional[Dict[str, Any]] = None) -> bool:
        """更新特征"""
        return self.db.update_features(image_path, features, metadata)
    
    def delete_features(self, image_path: str) -> bool:
        """删除特征"""
        return self.db.delete_features(image_path)
    
    def get_all_features(self) -> Dict[str, Dict[str, np.ndarray]]:
        """获取所有特征"""
        return self.db.get_all_features()
    
    def get_image_paths(self) -> List[str]:
        """获取所有图像路径"""
        return self.db.get_image_paths()
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        return {
            'db_path': str(self.db_path),
            'db_type': self.db_type,
            'image_count': len(self.db.get_image_paths()),
            'exists': self.db_path.exists() if self.db_type != 'chunked' else self.db_path.is_dir()
        }
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self.db, 'close'):
            self.db.close()
    
    def __del__(self):
        """析构函数"""
        self.cleanup()