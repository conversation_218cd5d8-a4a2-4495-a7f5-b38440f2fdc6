#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特征提取功能
"""

import sys
import os
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_single_image_extraction():
    """测试单个图像的特征提取"""
    print("=== 测试单个图像的特征提取 ===\n")
    
    try:
        # 导入必要的模块
        from features.config.config_manager import ConfigManager
        from features.config.feature_config import FeatureExtractorConfig, TraditionalFeatureConfig
        from features.core.feature_extractor import FeatureExtractor
        from features.storage.feature_storage import FeatureStorage
        from database.fabric_repository import FabricRepository
        from database.database_manager import DatabaseManager

        # 初始化组件
        print("1. 初始化组件...")
        config_manager = ConfigManager()
        config_manager.load_all_configs()  # 加载所有配置

        db_manager = DatabaseManager()
        fabric_repository = FabricRepository(db_manager)

        # 创建特征提取器配置
        extractor_config = FeatureExtractorConfig(
            model_name="resnet50",
            use_gpu=False,  # 使用CPU避免GPU问题
            use_cache=True,
            cache_dir="cache"
        )

        traditional_config = TraditionalFeatureConfig(
            extract_color=True,
            extract_texture=True,
            extract_shape=True
        )

        feature_extractor = FeatureExtractor(extractor_config, traditional_config)
        feature_storage = FeatureStorage(fabric_repository)
        
        print("✓ 组件初始化成功")
        
        # 选择测试图像
        test_images_dir = project_root / "test_images"
        if not test_images_dir.exists():
            print("❌ 测试图像目录不存在")
            return False
        
        test_images = list(test_images_dir.glob("*.jpg")) + list(test_images_dir.glob("*.png"))
        if not test_images:
            print("❌ 没有找到测试图像")
            return False
        
        test_image = test_images[0]
        print(f"2. 测试图像: {test_image}")
        
        # 提取特征
        print("3. 提取特征...")
        result = feature_extractor.extract_features(str(test_image), extract_traditional=True)
        
        if not result or not result.success:
            error_msg = getattr(result, 'error_message', '未知错误') if result else "结果为空"
            print(f"❌ 特征提取失败: {error_msg}")
            return False
        
        print("✓ 特征提取成功")
        print(f"  深度特征维度: {len(result.features) if result.features is not None else 0}")
        
        # 检查特征向量
        if hasattr(result, 'feature_vectors') and result.feature_vectors:
            print("  提取的特征类型:")
            for feature_type, features in result.feature_vectors.items():
                if hasattr(features, 'vector'):
                    # FeatureVector对象
                    print(f"    {feature_type}: {features.dimension} 维")
                elif hasattr(features, '__len__'):
                    # 数组对象
                    print(f"    {feature_type}: {len(features)} 维")
                else:
                    print(f"    {feature_type}: 未知类型 {type(features)}")
        else:
            print("  ⚠️ 没有提取到多种特征类型")
            return False
        
        # 创建测试图像记录
        print("4. 创建图像记录...")
        test_image_path = str(test_image)  # 使用实际的图像路径
        fabric_image = fabric_repository.create_fabric_image(test_image_path)
        
        if not fabric_image:
            print("❌ 创建图像记录失败")
            return False
        
        print(f"✓ 创建图像记录成功，ID: {fabric_image.id}")
        
        # 存储特征
        print("5. 存储特征...")
        # 将FeatureVector对象转换为数组字典
        feature_arrays = {}
        for feature_type, feature_vector in result.feature_vectors.items():
            if hasattr(feature_vector, 'vector'):
                feature_arrays[feature_type] = feature_vector.vector
            else:
                feature_arrays[feature_type] = feature_vector

        success = feature_storage.store_multiple_features(
            fabric_image.id, feature_arrays
        )
        
        if not success:
            print("❌ 特征存储失败")
            return False
        
        print("✓ 特征存储成功")
        
        # 验证数据库
        print("6. 验证数据库...")
        db_path = 'data/fabric_search.db'
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询新添加的特征
            cursor.execute('''
                SELECT feature_type, LENGTH(feature_data) as data_size 
                FROM image_features 
                WHERE image_id = ? 
                ORDER BY feature_type
            ''', (fabric_image.id,))
            
            new_features = cursor.fetchall()
            if new_features:
                print(f"图像 {fabric_image.id} 的特征:")
                for feature_type, data_size in new_features:
                    print(f"  {feature_type}: {data_size} 字节")
            else:
                print(f"⚠️ 图像 {fabric_image.id} 没有找到特征记录")
                return False
            
            # 查询所有特征类型
            cursor.execute('SELECT DISTINCT feature_type FROM image_features ORDER BY feature_type;')
            all_feature_types = cursor.fetchall()
            print("\n数据库中所有特征类型:")
            for feature_type in all_feature_types:
                cursor.execute('SELECT COUNT(*) FROM image_features WHERE feature_type = ?;', feature_type)
                count = cursor.fetchone()[0]
                print(f"  {feature_type[0]}: {count} 条记录")
            
            conn.close()
        else:
            print("❌ 数据库文件不存在")
            return False
        
        print("\n🎉 测试完成！多特征提取和存储功能正常工作。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_single_image_extraction()
    sys.exit(0 if success else 1)
