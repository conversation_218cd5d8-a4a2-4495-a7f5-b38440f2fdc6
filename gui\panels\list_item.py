"""
列表项组件

包含列表视图中的单个项目组件
"""

import os
from typing import Dict
from PyQt6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap

from database.models import FabricImage
from utils.logger_mixin import LoggerMixin
from gui.panels.similarity_widget import SimilarityBarWidget


class ListItemWidget(QWidget, LoggerMixin):
    """列表项组件"""
    
    # 信号
    itemClicked = pyqtSignal(FabricImage)
    itemDoubleClicked = pyqtSignal(FabricImage)
    
    def __init__(self, fabric_image: FabricImage, feature_scores: Dict[str, float] = None, parent=None):
        super().__init__(parent)
        self.fabric_image = fabric_image
        self.feature_scores = feature_scores or {}
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(10)
        
        # 缩略图部分
        thumbnail_layout = QVBoxLayout()
        thumbnail_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 缩略图
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setFixedSize(100, 100)
        self.thumbnail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.thumbnail_label.setStyleSheet(
            "border: 1px solid #ddd; background-color: #f9f9f9;"
        )
        thumbnail_layout.addWidget(self.thumbnail_label)
        
        # 加载缩略图
        try:
            self.logger.info(f"=== 列表项加载缩略图 ===")
            self.logger.info(f"文件路径: {self.fabric_image.file_path}")
            
            # 检查文件路径是否为相对路径，如果是，转换为绝对路径
            file_path = self.fabric_image.file_path
            if not os.path.isabs(file_path):
                # 使用当前工作目录作为基准路径
                file_path = os.path.join(os.getcwd(), file_path)
                self.logger.info(f"转换为绝对路径: {file_path}")
                
                # 如果文件仍然不存在，尝试使用test_images目录
                if not os.path.exists(file_path) and file_path.find('test_images') != -1:
                    base_name = os.path.basename(file_path)
                    alt_path = os.path.join(os.getcwd(), 'test_images', base_name)
                    self.logger.info(f"尝试替代路径: {alt_path}")
                    if os.path.exists(alt_path):
                        file_path = alt_path
                        self.logger.info(f"使用替代路径: {file_path}")
                        # 更新FabricImage对象中的路径
                        self.fabric_image.file_path = file_path
            
            if os.path.exists(file_path):
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(
                        100, 100,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.thumbnail_label.setPixmap(scaled_pixmap)
                    self.logger.info("缩略图加载成功")
                else:
                    self.logger.error(f"QPixmap加载失败: {file_path}")
                    self.thumbnail_label.setText("无法加载")
            else:
                self.logger.error(f"文件不存在: {file_path}")
                self.thumbnail_label.setText("文件不存在")
        except Exception as e:
            self.logger.error(f"加载缩略图失败: {e}")
            self.thumbnail_label.setText("加载失败")
        
        main_layout.addLayout(thumbnail_layout)
        
        # 信息部分
        info_layout = QVBoxLayout()
        
        # 文件名
        filename = os.path.basename(self.fabric_image.file_path)
        self.name_label = QLabel(filename)
        self.name_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        info_layout.addWidget(self.name_label)
        
        # 详细信息
        info_parts = []
        
        # 相似度分数
        if hasattr(self.fabric_image, 'similarity_score'):
            info_parts.append(f"相似度: {self.fabric_image.similarity_score:.4f}")
        
        # 类别
        if self.fabric_image.category:
            info_parts.append(f"类别: {self.fabric_image.category}")
        
        # 文件大小
        if self.fabric_image.file_size:
            size_mb = self.fabric_image.file_size / (1024 * 1024)
            if size_mb >= 1:
                info_parts.append(f"大小: {size_mb:.1f}MB")
            else:
                size_kb = self.fabric_image.file_size / 1024
                info_parts.append(f"大小: {size_kb:.1f}KB")
        
        # 图像尺寸
        if self.fabric_image.width and self.fabric_image.height:
            info_parts.append(f"尺寸: {self.fabric_image.width}x{self.fabric_image.height}")
        
        # 标签
        if self.fabric_image.tags:
            tags_text = ", ".join(self.fabric_image.tags[:3])
            if len(self.fabric_image.tags) > 3:
                tags_text += "..."
            info_parts.append(f"标签: {tags_text}")
        
        # 创建信息标签
        self.info_label = QLabel(" | ".join(info_parts))
        self.info_label.setStyleSheet("color: #666; font-size: 11px;")
        info_layout.addWidget(self.info_label)
        
        main_layout.addLayout(info_layout, 1)  # 1是拉伸因子
        
        # 相似度可视化部分
        if self.feature_scores:
            self.similarity_chart = SimilarityBarWidget(self.feature_scores)
            main_layout.addWidget(self.similarity_chart)
        
        # 设置样式
        self.setStyleSheet("""
            ListItemWidget {
                border-bottom: 1px solid #ddd;
                background-color: white;
            }
            ListItemWidget:hover {
                background-color: #f0f8ff;
            }
        """)
        
        # 设置最小高度
        self.setMinimumHeight(120)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.itemClicked.emit(self.fabric_image)
        super().mousePressEvent(event)
    
    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.itemDoubleClicked.emit(self.fabric_image)
        super().mouseDoubleClickEvent(event)