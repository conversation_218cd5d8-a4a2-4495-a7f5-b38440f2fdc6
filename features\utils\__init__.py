#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块

提供特征管理相关的工具函数，包括：
- 数据验证
- 图像处理
- 数学计算
- 文件操作
- 日志记录
"""

from .validators import (
    validate_image_path,
    validate_features,
    validate_search_request,
    validate_config
)
from .image_utils import (
    load_image,
    preprocess_image,
    resize_image,
    normalize_image,
    get_image_info
)
from .math_utils import (
    normalize_vector,
    cosine_similarity,
    euclidean_distance,
    calculate_statistics,
    weighted_average
)
from .file_utils import (
    ensure_directory,
    get_file_size,
    get_file_hash,
    safe_file_operation,
    cleanup_temp_files
)
from .logging_utils import (
    setup_logger,
    log_performance,
    log_error_with_context,
    create_performance_monitor
)

__all__ = [
    # 验证器
    'validate_image_path',
    'validate_features',
    'validate_search_request',
    'validate_config',
    
    # 图像工具
    'load_image',
    'preprocess_image',
    'resize_image',
    'normalize_image',
    'get_image_info',
    
    # 数学工具
    'normalize_vector',
    'cosine_similarity',
    'euclidean_distance',
    'calculate_statistics',
    'weighted_average',
    
    # 文件工具
    'ensure_directory',
    'get_file_size',
    'get_file_hash',
    'safe_file_operation',
    'cleanup_temp_files',
    
    # 日志工具
    'setup_logger',
    'log_performance',
    'log_error_with_context',
    'create_performance_monitor'
]