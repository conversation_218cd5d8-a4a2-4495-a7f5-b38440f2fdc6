#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件夹打开功能测试脚本

测试用户打开文件夹时是否能正确提取所有特征类型
"""

import os
import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_images():
    """创建测试图像"""
    test_dir = project_root / "test_images"
    test_dir.mkdir(exist_ok=True)
    
    # 创建一些简单的测试图像
    from PIL import Image
    import numpy as np
    
    # 创建3张不同颜色的测试图像
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255)]  # 红、绿、蓝
    
    for i, color in enumerate(colors):
        # 创建224x224的纯色图像
        img_array = np.full((224, 224, 3), color, dtype=np.uint8)
        img = Image.fromarray(img_array)
        img_path = test_dir / f"test_image_{i+1}.jpg"
        img.save(img_path)
        logger.info(f"创建测试图像: {img_path}")
    
    return test_dir

def test_folder_opening():
    """测试文件夹打开功能"""
    logger.info("=== 测试文件夹打开功能 ===")
    
    try:
        # 创建测试图像
        test_dir = create_test_images()
        
        # 导入必要的模块
        from features.config.feature_config import FeatureExtractorConfig, TraditionalFeatureConfig
        from features.core.feature_extractor import FeatureExtractor
        from features.storage.feature_storage import FeatureStorage
        from features.batch.batch_processor import BatchProcessor
        
        # 创建配置
        deep_config = FeatureExtractorConfig(
            model_name="resnet50",
            use_gpu=True,
            batch_size=32,
            use_mixed_precision=True,
            optimize_for_gpu=True
        )
        
        traditional_config = TraditionalFeatureConfig(
            extract_color=True,
            extract_texture=True,
            extract_shape=True
        )
        
        # 创建特征提取器
        extractor = FeatureExtractor(deep_config, traditional_config)
        
        # 创建模拟的fabric_repository
        class MockFabricRepository:
            def __init__(self):
                self.data_dir = Path("data")
                self.data_dir.mkdir(exist_ok=True)
                
        # 创建特征存储器
        storage = FeatureStorage(MockFabricRepository())
        
        # 创建批处理器
        processor = BatchProcessor(
            feature_extractor=extractor,
            feature_storage=storage,
            max_workers=2
        )
        
        # 获取测试图像路径
        image_paths = list(test_dir.glob("*.jpg"))
        logger.info(f"找到 {len(image_paths)} 张测试图像")
        
        # 创建批处理任务
        task = processor.create_task(
            name="test_folder_opening",
            image_paths=image_paths,
            description="测试文件夹打开功能"
        )
        
        logger.info(f"创建批处理任务: {task.name}")
        logger.info(f"任务项数量: {len(task.items)}")
        
        # 执行任务
        logger.info("开始执行批处理任务...")
        start_time = time.time()
        
        # 模拟执行（实际执行可能需要更复杂的逻辑）
        for i, item in enumerate(task.items):
            logger.info(f"处理图像 {i+1}/{len(task.items)}: {item.file_path.name}")
            
            # 提取特征
            result = extractor.extract_features(
                str(item.file_path),
                extract_traditional=True
            )
            
            # 检查特征是否提取成功
            if "deep" in result.feature_vectors:
                deep_features = result.feature_vectors["deep"].vector
                logger.info(f"  ✓ 深度特征: {len(deep_features)} 维")
            else:
                logger.warning("  ✗ 深度特征提取失败")

            if "color" in result.feature_vectors:
                color_features = result.feature_vectors["color"].vector
                logger.info(f"  ✓ 颜色特征: {len(color_features)} 维")
            else:
                logger.warning("  ✗ 颜色特征提取失败")

            if "texture" in result.feature_vectors:
                texture_features = result.feature_vectors["texture"].vector
                logger.info(f"  ✓ 纹理特征: {len(texture_features)} 维")
            else:
                logger.warning("  ✗ 纹理特征提取失败")

            if "shape" in result.feature_vectors:
                shape_features = result.feature_vectors["shape"].vector
                logger.info(f"  ✓ 形状特征: {len(shape_features)} 维")
            else:
                logger.warning("  ✗ 形状特征提取失败")
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        logger.info(f"批处理任务完成，耗时: {processing_time:.2f} 秒")
        logger.info(f"平均每张图像处理时间: {processing_time/len(image_paths):.2f} 秒")
        
        # 清理资源
        processor.cleanup()
        extractor.cleanup()
        
        return True
        
    except Exception as e:
        logger.error(f"文件夹打开功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("开始文件夹打开功能测试")
    
    success = test_folder_opening()
    
    if success:
        logger.info("🎉 文件夹打开功能测试通过！")
        logger.info("✓ 用户打开文件夹时能够同时提取深度特征、颜色特征、纹理特征和形状特征")
        return 0
    else:
        logger.error("❌ 文件夹打开功能测试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
