#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件对话框辅助模块

该模块提供文件和目录选择对话框的辅助功能。
"""

from typing import Optional, List, Tuple

from PyQt6.QtWidgets import QWidget, QFileDialog


class FileDialogHelper:
    """文件对话框辅助类"""
    
    @staticmethod
    def get_open_file_name(parent: QWidget, title: str = "选择文件",
                          directory: str = "", 
                          filter_str: str = "所有文件 (*.*)") -> Tuple[Optional[str], str]:
        """打开文件对话框（别名方法）
        
        Args:
            parent: 父控件
            title: 对话框标题
            directory: 初始目录
            filter_str: 文件过滤器
            
        Returns:
            Tuple[Optional[str], str]: 选择的文件路径和使用的过滤器
        """
        file_path, filter_used = QFileDialog.getOpenFileName(
            parent, title, directory, filter_str
        )
        return (file_path if file_path else None), filter_used
    
    @staticmethod
    def open_file(parent: QWidget, title: str = "选择文件",
                 directory: str = "", 
                 filter_str: str = "所有文件 (*.*)") -> Optional[str]:
        """打开文件对话框
        
        Args:
            parent: 父控件
            title: 对话框标题
            directory: 初始目录
            filter_str: 文件过滤器
            
        Returns:
            Optional[str]: 选择的文件路径
        """
        file_path, _ = QFileDialog.getOpenFileName(
            parent, title, directory, filter_str
        )
        return file_path if file_path else None
    
    @staticmethod
    def open_files(parent: QWidget, title: str = "选择文件",
                  directory: str = "", 
                  filter_str: str = "所有文件 (*.*)") -> List[str]:
        """打开多文件选择对话框
        
        Args:
            parent: 父控件
            title: 对话框标题
            directory: 初始目录
            filter_str: 文件过滤器
            
        Returns:
            List[str]: 选择的文件路径列表
        """
        file_paths, _ = QFileDialog.getOpenFileNames(
            parent, title, directory, filter_str
        )
        return file_paths
    
    @staticmethod
    def save_file(parent: QWidget, title: str = "保存文件",
                 directory: str = "", 
                 filter_str: str = "所有文件 (*.*)") -> Optional[str]:
        """保存文件对话框
        
        Args:
            parent: 父控件
            title: 对话框标题
            directory: 初始目录
            filter_str: 文件过滤器
            
        Returns:
            Optional[str]: 保存的文件路径
        """
        file_path, _ = QFileDialog.getSaveFileName(
            parent, title, directory, filter_str
        )
        return file_path if file_path else None
    
    @staticmethod
    def get_save_file_name(parent: QWidget, title: str = "保存文件",
                          directory: str = "", 
                          filter_str: str = "所有文件 (*.*)") -> Tuple[Optional[str], str]:
        """保存文件对话框（别名方法）
        
        Args:
            parent: 父控件
            title: 对话框标题
            directory: 初始目录
            filter_str: 文件过滤器
            
        Returns:
            Tuple[Optional[str], str]: 保存的文件路径和使用的过滤器
        """
        file_path, filter_used = QFileDialog.getSaveFileName(
            parent, title, directory, filter_str
        )
        return (file_path if file_path else None), filter_used
    
    @staticmethod
    def open_directory(parent: QWidget, title: str = "选择目录",
                      directory: str = "") -> Optional[str]:
        """打开目录选择对话框
        
        Args:
            parent: 父控件
            title: 对话框标题
            directory: 初始目录
            
        Returns:
            Optional[str]: 选择的目录路径
        """
        dir_path = QFileDialog.getExistingDirectory(
            parent, title, directory, QFileDialog.Option.ShowDirsOnly
        )
        return dir_path if dir_path else None
    
    @staticmethod
    def get_existing_directory(parent: QWidget, title: str = "选择目录",
                             directory: str = "") -> Optional[str]:
        """打开目录选择对话框（别名方法）
        
        Args:
            parent: 父控件
            title: 对话框标题
            directory: 初始目录
            
        Returns:
            Optional[str]: 选择的目录路径
        """
        return FileDialogHelper.open_directory(parent, title, directory)
    
    @staticmethod
    def get_image_filter() -> str:
        """获取图片文件过滤器
        
        Returns:
            str: 图片文件过滤器字符串
        """
        return ("图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.tiff *.webp);;"
                "所有文件 (*.*)")
    
    @staticmethod
    def get_config_filter() -> str:
        """获取配置文件过滤器
        
        Returns:
            str: 配置文件过滤器字符串
        """
        return ("配置文件 (*.json *.yaml *.yml *.ini);;"
                "JSON文件 (*.json);;"
                "YAML文件 (*.yaml *.yml);;"
                "INI文件 (*.ini);;"
                "所有文件 (*.*)")