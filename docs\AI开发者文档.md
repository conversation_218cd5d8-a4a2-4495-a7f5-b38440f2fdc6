# 布料图片相似度搜索系统 - AI开发者文档

## 目录

1. [系统概述](#1-系统概述)
2. [系统架构](#2-系统架构)
3. [代码结构](#3-代码结构)
4. [核心模块详解](#4-核心模块详解)
5. [API接口文档](#5-api接口文档)
6. [数据结构与格式](#6-数据结构与格式)
7. [开发环境配置](#7-开发环境配置)
8. [扩展开发指南](#8-扩展开发指南)
9. [性能优化](#9-性能优化)
10. [测试与验证](#10-测试与验证)
11. [常见问题与解决方案](#11-常见问题与解决方案)

## 1. 系统概述

布料图片相似度搜索系统是一个专为纺织行业设计的图像检索工具，能够基于多种特征维度（颜色、纹理、形状和深度学习特征）对布料图片进行相似度搜索。系统采用模块化设计，支持GPU加速，提供高效、精准的布料图像检索服务。

### 1.1 核心功能

- **多维度特征提取**：同时提取并分析布料图片的颜色、纹理、形状和深度学习特征
- **多模型支持**：支持多种深度学习模型，包括ResNet50、VGG16、EfficientNet、EfficientNetV2、ConvNeXt、ConvNeXtV2、ViT、Swin等
- **GPU加速**：支持GPU加速特征提取和相似度计算，大幅提升处理速度
- **自适应权重**：根据查询图像特点自动调整各特征维度的权重
- **查询扩展**：通过初步搜索结果扩展查询，提高检索准确性
- **用户反馈学习**：根据用户对搜索结果的反馈优化后续搜索
- **多种数据库类型**：支持标准数据库、分块数据库和向量数据库

### 1.2 技术栈

- **编程语言**：Python 3.8+
- **深度学习框架**：PyTorch
- **图像处理**：OpenCV, Pillow
- **向量索引**：Faiss
- **用户界面**：PyQt5
- **数据存储**：NumPy, JSON
- **并行处理**：multiprocessing, threading

## 2. 系统架构

系统采用模块化分层架构，主要分为四个层次：

### 2.1 架构层次

1. **表示层**：用户界面和命令行接口
2. **业务逻辑层**：搜索引擎和特征提取
3. **数据访问层**：数据库操作和文件访问
4. **基础设施层**：工具函数和配置接口

### 2.2 核心模块关系

系统由六个核心模块组成，它们之间的关系如下：

```
+-------+      +---------------+      +------------------+
|  UI   | ---> | 搜索引擎模块  | ---> | 特征提取模块     |
+-------+      +---------------+      +------------------+
    |                 |                      |
    v                 v                      v
+----------------+    |    +------------------+
| 配置接口模块   | <--+--- | 数据库模块       |
+----------------+         +------------------+
    |                              |
    v                              v
+------------------------------------------+
|              工具函数模块                |
+------------------------------------------+
```

### 2.3 数据流程

系统有三个主要数据流程：

1. **特征提取流程**：
   - 加载图像 -> 预处理 -> 特征提取 -> 特征归一化 -> 存储到数据库

2. **搜索流程**：
   - 加载查询图像 -> 提取特征 -> 计算相似度 -> 结果排序 -> 显示结果

3. **数据库同步流程**：
   - 扫描图像目录 -> 识别新增/删除/修改图像 -> 更新特征数据库

## 3. 代码结构

系统代码采用模块化设计，主要包括以下目录结构：

```
/
├── config/                 # 配置文件和接口
│   ├── app_config.py       # 应用程序配置
│   ├── model_config.py     # 模型配置
│   └── ui_config.py        # 界面配置
├── database/               # 数据库相关功能
│   ├── feature_database.py # 特征数据库
│   ├── vector_database.py  # 向量数据库
│   └── db_utils.py         # 数据库工具函数
├── models/                 # 特征提取模型
│   ├── feature_extractor.py # 特征提取器
│   ├── model_loader.py     # 模型加载器
│   └── batch_processing.py # 批处理
├── search/                 # 搜索引擎功能
│   ├── search_engine.py    # 搜索引擎
│   └── search_strategies.py # 搜索策略
├── ui/                     # 用户界面组件
│   ├── main_window.py      # 主窗口
│   ├── search_panel.py     # 搜索面板
│   └── result_display.py   # 结果显示
├── utils/                  # 工具函数
│   ├── image_utils.py      # 图像处理工具
│   ├── gpu_utils.py        # GPU工具
│   └── log_utils.py        # 日志工具
├── main.py                 # 主程序入口
└── requirements.txt        # 依赖包列表
```

## 4. 核心模块详解

### 4.1 特征提取模块

特征提取模块负责从图像中提取各种特征，是系统的核心组件之一。

#### 4.1.1 feature_extractor.py

```python
class FeatureExtractor:
    def __init__(self, model_name='resnet50', use_gpu=False, feature_types=None):
        """初始化特征提取器
        
        Args:
            model_name (str): 深度学习模型名称
            use_gpu (bool): 是否使用GPU
            feature_types (list): 要提取的特征类型列表
        """
        self.model_name = model_name
        self.use_gpu = use_gpu
        self.feature_types = feature_types or ['deep', 'color', 'texture', 'shape']
        self.model_loader = ModelLoader(model_name, use_gpu)
        self.model = self.model_loader.load_model()
        self.device = torch.device('cuda' if use_gpu and torch.cuda.is_available() else 'cpu')
        
    def extract_features(self, image_path):
        """从单个图像提取特征
        
        Args:
            image_path (str): 图像文件路径
            
        Returns:
            dict: 包含各种特征的字典
        """
        features = {}
        image = self._load_and_preprocess(image_path)
        
        if 'deep' in self.feature_types:
            features['deep'] = self._extract_deep_features(image)
            
        if 'color' in self.feature_types:
            features['color'] = self._extract_color_features(image)
            
        if 'texture' in self.feature_types:
            features['texture'] = self._extract_texture_features(image)
            
        if 'shape' in self.feature_types:
            features['shape'] = self._extract_shape_features(image)
            
        return features
    
    def _extract_deep_features(self, image):
        """提取深度学习特征"""
        with torch.no_grad():
            image_tensor = image.to(self.device)
            features = self.model(image_tensor)
        return features.cpu().numpy()
    
    def _extract_color_features(self, image):
        """提取颜色特征"""
        # 实现颜色特征提取
        pass
    
    def _extract_texture_features(self, image):
        """提取纹理特征"""
        # 实现纹理特征提取
        pass
    
    def _extract_shape_features(self, image):
        """提取形状特征"""
        # 实现形状特征提取
        pass
    
    def _load_and_preprocess(self, image_path):
        """加载并预处理图像"""
        # 实现图像加载和预处理
        pass
```

#### 4.1.2 batch_processing.py

```python
class BatchProcessor:
    def __init__(self, feature_extractor, batch_size=16):
        """初始化批处理器
        
        Args:
            feature_extractor (FeatureExtractor): 特征提取器实例
            batch_size (int): 批处理大小
        """
        self.feature_extractor = feature_extractor
        self.batch_size = batch_size
        
    def process_batch(self, image_paths):
        """批量处理图像
        
        Args:
            image_paths (list): 图像路径列表
            
        Returns:
            dict: 图像路径到特征的映射
        """
        results = {}
        for i in range(0, len(image_paths), self.batch_size):
            batch_paths = image_paths[i:i+self.batch_size]
            batch_results = self._process_single_batch(batch_paths)
            results.update(batch_results)
        return results
    
    def _process_single_batch(self, batch_paths):
        """处理单个批次"""
        # 实现单个批次的处理
        pass
```

#### 4.1.3 model_loader.py

```python
class ModelLoader:
    def __init__(self, model_name, use_gpu=False):
        """初始化模型加载器
        
        Args:
            model_name (str): 模型名称
            use_gpu (bool): 是否使用GPU
        """
        self.model_name = model_name
        self.use_gpu = use_gpu
        self.device = torch.device('cuda' if use_gpu and torch.cuda.is_available() else 'cpu')
        
    def load_model(self):
        """加载预训练模型
        
        Returns:
            torch.nn.Module: 加载的模型
        """
        if self.model_name == 'resnet50':
            model = models.resnet50(pretrained=True)
            model = self._modify_resnet(model)
        elif self.model_name == 'vgg16':
            model = models.vgg16(pretrained=True)
            model = self._modify_vgg(model)
        elif self.model_name.startswith('efficientnet'):
            model = self._load_efficientnet(self.model_name)
        elif self.model_name.startswith('convnext'):
            model = self._load_convnext(self.model_name)
        elif self.model_name == 'vit':
            model = self._load_vit()
        elif self.model_name == 'swin':
            model = self._load_swin()
        else:
            raise ValueError(f"Unsupported model: {self.model_name}")
            
        model = model.to(self.device)
        model.eval()
        return model
    
    def _modify_resnet(self, model):
        """修改ResNet模型以提取特征"""
        # 移除最后的全连接层
        return nn.Sequential(*list(model.children())[:-1])
    
    def _modify_vgg(self, model):
        """修改VGG模型以提取特征"""
        # 移除最后的分类层
        return nn.Sequential(*list(model.children())[:-1])
    
    def _load_efficientnet(self, model_name):
        """加载EfficientNet模型"""
        # 实现EfficientNet模型加载
        pass
    
    def _load_convnext(self, model_name):
        """加载ConvNeXt模型"""
        # 实现ConvNeXt模型加载
        pass
    
    def _load_vit(self):
        """加载Vision Transformer模型"""
        # 实现ViT模型加载
        pass
    
    def _load_swin(self):
        """加载Swin Transformer模型"""
        # 实现Swin模型加载
        pass
```

### 4.2 数据库模块

数据库模块负责存储和管理特征数据，支持多种数据库类型。

#### 4.2.1 feature_database.py

```python
class FeatureDatabase:
    def __init__(self, db_path, db_type='standard'):
        """初始化特征数据库
        
        Args:
            db_path (str): 数据库路径
            db_type (str): 数据库类型，可选值：'standard', 'chunked', 'vector'
        """
        self.db_path = db_path
        self.db_type = db_type
        self.features = {}
        self.image_paths = []
        self.metadata = {}
        
    def load(self):
        """加载数据库
        
        Returns:
            bool: 加载是否成功
        """
        if self.db_type == 'standard':
            return self._load_standard()
        elif self.db_type == 'chunked':
            return self._load_chunked()
        elif self.db_type == 'vector':
            return self._load_vector()
        else:
            raise ValueError(f"Unsupported database type: {self.db_type}")
    
    def save(self):
        """保存数据库
        
        Returns:
            bool: 保存是否成功
        """
        if self.db_type == 'standard':
            return self._save_standard()
        elif self.db_type == 'chunked':
            return self._save_chunked()
        elif self.db_type == 'vector':
            return self._save_vector()
        else:
            raise ValueError(f"Unsupported database type: {self.db_type}")
    
    def add_features(self, image_path, features):
        """添加特征
        
        Args:
            image_path (str): 图像路径
            features (dict): 特征字典
            
        Returns:
            bool: 添加是否成功
        """
        # 实现特征添加
        pass
    
    def get_features(self, image_path):
        """获取特征
        
        Args:
            image_path (str): 图像路径
            
        Returns:
            dict: 特征字典
        """
        # 实现特征获取
        pass
    
    def update_features(self, image_path, features):
        """更新特征
        
        Args:
            image_path (str): 图像路径
            features (dict): 特征字典
            
        Returns:
            bool: 更新是否成功
        """
        # 实现特征更新
        pass
    
    def delete_features(self, image_path):
        """删除特征
        
        Args:
            image_path (str): 图像路径
            
        Returns:
            bool: 删除是否成功
        """
        # 实现特征删除
        pass
    
    def _load_standard(self):
        """加载标准数据库"""
        # 实现标准数据库加载
        pass
    
    def _load_chunked(self):
        """加载分块数据库"""
        # 实现分块数据库加载
        pass
    
    def _load_vector(self):
        """加载向量数据库"""
        # 实现向量数据库加载
        pass
    
    def _save_standard(self):
        """保存标准数据库"""
        # 实现标准数据库保存
        pass
    
    def _save_chunked(self):
        """保存分块数据库"""
        # 实现分块数据库保存
        pass
    
    def _save_vector(self):
        """保存向量数据库"""
        # 实现向量数据库保存
        pass
```

#### 4.2.2 vector_database.py

```python
class VectorDatabase(FeatureDatabase):
    def __init__(self, db_path, index_type='Flat', metric='L2'):
        """初始化向量数据库
        
        Args:
            db_path (str): 数据库路径
            index_type (str): 索引类型，如'Flat', 'IVF100,Flat'等
            metric (str): 距离度量，如'L2', 'IP'等
        """
        super().__init__(db_path, 'vector')
        self.index_type = index_type
        self.metric = metric
        self.index = None
        
    def build_index(self, features):
        """构建索引
        
        Args:
            features (numpy.ndarray): 特征矩阵
            
        Returns:
            bool: 构建是否成功
        """
        # 实现索引构建
        pass
    
    def search(self, query_features, k=10):
        """搜索最近邻
        
        Args:
            query_features (numpy.ndarray): 查询特征
            k (int): 返回的结果数量
            
        Returns:
            tuple: (距离, 索引)
        """
        # 实现最近邻搜索
        pass
```

#### 4.2.3 特征数据库改进方案

为了提高系统性能和可扩展性，特征数据库采用了新的结构设计：

```
data/
├── dl_features/                    # 深度学习特征目录
│   ├── resnet50_features.npz       # ResNet50特征
│   ├── vgg16_features.npz          # VGG16特征
│   └── metadata.json               # 深度学习特征元数据
├── color_features/                  # 颜色特征目录
│   ├── hsv_histogram.npz           # HSV直方图特征
│   └── metadata.json               # 颜色特征元数据
├── texture_features/                # 纹理特征目录
│   ├── glcm_features.npz           # 灰度共生矩阵特征
│   └── metadata.json               # 纹理特征元数据
├── shape_features/                  # 形状特征目录
│   ├── contour_features.npz        # 轮廓特征
│   └── metadata.json               # 形状特征元数据
├── feedback/                        # 反馈数据目录
│   ├── user_feedback.json          # 用户反馈数据
│   └── metadata.json               # 反馈数据元数据
├── index/                           # 索引目录
│   ├── image_index.json            # 图像索引映射
│   └── feature_index.json          # 特征索引映射
└── config/                          # 配置目录
    └── database_config.json         # 数据库配置
```

特征数据文件（.npz格式）的结构如下：

```python
# 深度学习特征文件结构
{
    'features': np.array,           # 特征矩阵 (N, D)
    'image_paths': np.array,        # 图像路径列表
    'image_hashes': np.array,       # 图像哈希值（用于快速查找）
    'extraction_time': np.array,    # 特征提取时间戳
    'feature_dim': int,             # 特征维度
    'model_version': str,           # 模型版本
    'preprocessing_params': dict    # 预处理参数
}
```

### 4.3 搜索引擎模块

搜索引擎模块负责执行相似度搜索，支持多种搜索策略。

#### 4.3.1 search_engine.py

```python
class SearchEngine:
    def __init__(self, feature_database, feature_extractor):
        """初始化搜索引擎
        
        Args:
            feature_database (FeatureDatabase): 特征数据库实例
            feature_extractor (FeatureExtractor): 特征提取器实例
        """
        self.feature_database = feature_database
        self.feature_extractor = feature_extractor
        self.search_strategies = {
            'weighted': WeightedSearchStrategy(),
            'adaptive': AdaptiveSearchStrategy(),
            'expansion': ExpansionSearchStrategy(),
            'color': SingleFeatureSearchStrategy('color'),
            'texture': SingleFeatureSearchStrategy('texture'),
            'shape': SingleFeatureSearchStrategy('shape'),
            'deep': SingleFeatureSearchStrategy('deep')
        }
        
    def search(self, query_image_path, strategy_name='weighted', weights=None, top_n=20, **kwargs):
        """执行搜索
        
        Args:
            query_image_path (str): 查询图像路径
            strategy_name (str): 搜索策略名称
            weights (dict): 特征权重字典
            top_n (int): 返回的结果数量
            **kwargs: 其他参数
            
        Returns:
            list: 搜索结果列表
        """
        # 提取查询图像特征
        query_features = self.feature_extractor.extract_features(query_image_path)
        
        # 获取搜索策略
        if strategy_name not in self.search_strategies:
            raise ValueError(f"Unsupported search strategy: {strategy_name}")
        strategy = self.search_strategies[strategy_name]
        
        # 执行搜索
        results = strategy.search(query_features, self.feature_database, weights, top_n, **kwargs)
        
        return results
```

#### 4.3.2 search_strategies.py

```python
class SearchStrategy:
    """搜索策略基类"""
    
    def search(self, query_features, feature_database, weights, top_n, **kwargs):
        """执行搜索
        
        Args:
            query_features (dict): 查询特征
            feature_database (FeatureDatabase): 特征数据库
            weights (dict): 特征权重
            top_n (int): 返回的结果数量
            **kwargs: 其他参数
            
        Returns:
            list: 搜索结果列表
        """
        raise NotImplementedError("Subclasses must implement this method")

class WeightedSearchStrategy(SearchStrategy):
    """加权搜索策略"""
    
    def search(self, query_features, feature_database, weights, top_n, **kwargs):
        """执行加权搜索"""
        # 实现加权搜索
        pass

class AdaptiveSearchStrategy(SearchStrategy):
    """自适应搜索策略"""
    
    def search(self, query_features, feature_database, weights, top_n, **kwargs):
        """执行自适应搜索"""
        # 实现自适应搜索
        pass

class ExpansionSearchStrategy(SearchStrategy):
    """查询扩展搜索策略"""
    
    def search(self, query_features, feature_database, weights, top_n, **kwargs):
        """执行查询扩展搜索"""
        # 实现查询扩展搜索
        pass

class SingleFeatureSearchStrategy(SearchStrategy):
    """单特征搜索策略"""
    
    def __init__(self, feature_type):
        """初始化单特征搜索策略
        
        Args:
            feature_type (str): 特征类型
        """
        self.feature_type = feature_type
    
    def search(self, query_features, feature_database, weights, top_n, **kwargs):
        """执行单特征搜索"""
        # 实现单特征搜索
        pass
```

### 4.4 UI模块

用户界面模块提供图形化界面，方便用户操作。

#### 4.4.1 main_window.py

```python
class MainWindow(QMainWindow):
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        self.setWindowTitle("布料图片相似度搜索系统")
        self.resize(1200, 800)
        
        # 初始化组件
        self.search_panel = SearchPanel()
        self.result_display = ResultDisplay()
        
        # 设置布局
        self._setup_layout()
        
        # 设置菜单和工具栏
        self._setup_menu()
        self._setup_toolbar()
        
        # 连接信号和槽
        self._connect_signals()
    
    def _setup_layout(self):
        """设置布局"""
        # 实现布局设置
        pass
    
    def _setup_menu(self):
        """设置菜单"""
        # 实现菜单设置
        pass
    
    def _setup_toolbar(self):
        """设置工具栏"""
        # 实现工具栏设置
        pass
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 实现信号连接
        pass
```

#### 4.4.2 search_panel.py

```python
class SearchPanel(QWidget):
    def __init__(self):
        """初始化搜索面板"""
        super().__init__()
        
        # 初始化组件
        self.query_image_label = QLabel("查询图像")
        self.select_image_button = QPushButton("选择图像")
        self.search_mode_combo = QComboBox()
        self.result_count_spin = QSpinBox()
        self.feature_weights_group = QGroupBox("特征权重")
        self.search_button = QPushButton("搜索")
        
        # 设置组件
        self._setup_components()
        
        # 设置布局
        self._setup_layout()
    
    def _setup_components(self):
        """设置组件"""
        # 实现组件设置
        pass
    
    def _setup_layout(self):
        """设置布局"""
        # 实现布局设置
        pass
```

#### 4.4.3 result_display.py

```python
class ResultDisplay(QWidget):
    def __init__(self):
        """初始化结果显示"""
        super().__init__()
        
        # 初始化组件
        self.view_mode_buttons = QButtonGroup()
        self.detail_view = QTableView()
        self.grid_view = QListView()
        self.stack = QStackedWidget()
        
        # 设置组件
        self._setup_components()
        
        # 设置布局
        self._setup_layout()
    
    def _setup_components(self):
        """设置组件"""
        # 实现组件设置
        pass
    
    def _setup_layout(self):
        """设置布局"""
        # 实现布局设置
        pass
    
    def display_results(self, results):
        """显示搜索结果
        
        Args:
            results (list): 搜索结果列表
        """
        # 实现结果显示
        pass
```

### 4.5 配置接口模块

配置接口模块负责管理系统配置，提供统一的配置访问接口。

#### 4.5.1 app_config.py

```python
class AppConfig:
    def __init__(self, config_file=None):
        """初始化应用程序配置
        
        Args:
            config_file (str): 配置文件路径
        """
        self.config_file = config_file or 'config/app_config.json'
        self.config = self._load_config()
    
    def _load_config(self):
        """加载配置
        
        Returns:
            dict: 配置字典
        """
        # 实现配置加载
        pass
    
    def save_config(self):
        """保存配置
        
        Returns:
            bool: 保存是否成功
        """
        # 实现配置保存
        pass
    
    def get(self, key, default=None):
        """获取配置值
        
        Args:
            key (str): 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        # 实现配置获取
        pass
    
    def set(self, key, value):
        """设置配置值
        
        Args:
            key (str): 配置键
            value: 配置值
            
        Returns:
            bool: 设置是否成功
        """
        # 实现配置设置
        pass
```

### 4.6 工具函数模块

工具函数模块提供各种辅助功能，支持其他模块的工作。

#### 4.6.1 image_utils.py

```python
def load_image(image_path, target_size=None):
    """加载图像
    
    Args:
        image_path (str): 图像路径
        target_size (tuple): 目标大小
        
    Returns:
        PIL.Image: 加载的图像
    """
    # 实现图像加载
    pass

def preprocess_image(image, model_name):
    """预处理图像
    
    Args:
        image (PIL.Image): 图像
        model_name (str): 模型名称
        
    Returns:
        torch.Tensor: 预处理后的图像张量
    """
    # 实现图像预处理
    pass

def extract_color_histogram(image, color_space='hsv', bins=32):
    """提取颜色直方图
    
    Args:
        image (PIL.Image): 图像
        color_space (str): 颜色空间
        bins (int): 直方图箱数
        
    Returns:
        numpy.ndarray: 颜色直方图
    """
    # 实现颜色直方图提取
    pass

def extract_texture_features(image):
    """提取纹理特征
    
    Args:
        image (PIL.Image): 图像
        
    Returns:
        numpy.ndarray: 纹理特征
    """
    # 实现纹理特征提取
    pass

def extract_shape_features(image):
    """提取形状特征
    
    Args:
        image (PIL.Image): 图像
        
    Returns:
        numpy.ndarray: 形状特征
    """
    # 实现形状特征提取
    pass
```

#### 4.6.2 gpu_utils.py

```python
def check_gpu_availability():
    """检查GPU可用性
    
    Returns:
        bool: GPU是否可用
    """
    # 实现GPU可用性检查
    pass

def get_gpu_memory_info():
    """获取GPU内存信息
    
    Returns:
        dict: GPU内存信息
    """
    # 实现GPU内存信息获取
    pass

@contextmanager
def allocate_gpu_memory(memory_mb):
    """分配GPU内存
    
    Args:
        memory_mb (int): 内存大小（MB）
        
    Yields:
        None
    """
    # 实现GPU内存分配
    pass
```

## 5. API接口文档

### 5.1 命令行接口

系统提供以下命令行接口：

```bash
# 创建特征数据库
python main.py --mode create_db --folder 图像文件夹路径 --db 数据库路径 --model resnet50 --use-gpu

# 同步特征数据库
python main.py --mode sync_db --folder 图像文件夹路径 --db 数据库路径 --use-gpu

# 搜索图像
python main.py --mode search --query 查询图像路径 --db 数据库路径 --top-n 10 --use-gpu

# 模型微调
python main.py --tune_model --folder 训练图像文件夹 --tuned_model_path 微调模型保存路径 --epochs 10 --learning_rate 0.001
```

### 5.2 Python API

系统提供以下Python API：

```python
# 创建特征提取器
from models.feature_extractor import FeatureExtractor

feature_extractor = FeatureExtractor(
    model_name='resnet50',  # 可选：'resnet50', 'vgg16', 'efficientnet', 'vit', 'swin'等
    use_gpu=True,           # 是否使用GPU
    feature_types=['deep', 'color', 'texture', 'shape']  # 要提取的特征类型
)

# 提取特征
features = feature_extractor.extract_features('path/to/image.jpg')

# 创建特征数据库
from database.feature_database import FeatureDatabase

db = FeatureDatabase(
    db_path='path/to/database',  # 数据库路径
    db_type='standard'           # 可选：'standard', 'chunked', 'vector'
)

# 加载数据库
db.load()

# 添加特征
db.add_features('path/to/image.jpg', features)

# 保存数据库
db.save()

# 创建搜索引擎
from search.search_engine import SearchEngine

search_engine = SearchEngine(
    feature_database=db,
    feature_extractor=feature_extractor
)

# 执行搜索
results = search_engine.search(
    query_image_path='path/to/query.jpg',  # 查询图像路径
    strategy_name='weighted',              # 搜索策略
    weights={'deep': 0.5, 'color': 0.2, 'texture': 0.2, 'shape': 0.1},  # 特征权重
    top_n=20                               # 返回结果数量
)
```

## 6. 数据结构与格式

### 6.1 特征数据格式

系统使用以下数据格式存储特征：

#### 6.1.1 深度学习特征

```python
{
    'features': np.array,           # 特征矩阵 (N, D)
    'image_paths': np.array,        # 图像路径列表
    'image_hashes': np.array,       # 图像哈希值
    'extraction_time': np.array,    # 特征提取时间戳
    'feature_dim': int,             # 特征维度
    'model_version': str,           # 模型版本
    'preprocessing_params': dict    # 预处理参数
}
```

#### 6.1.2 颜色特征

```python
{
    'features': np.array,           # 颜色特征矩阵
    'image_paths': np.array,        # 图像路径列表
    'image_hashes': np.array,       # 图像哈希值
    'extraction_time': np.array,    # 提取时间戳
    'color_space': str,             # 颜色空间（HSV/RGB/LAB）
    'bins': int,                    # 直方图箱数
    'feature_dim': int              # 特征维度
}
```

#### 6.1.3 纹理特征

```python
{
    'features': np.array,           # 纹理特征矩阵
    'image_paths': np.array,        # 图像路径列表
    'image_hashes': np.array,       # 图像哈希值
    'extraction_time': np.array,    # 提取时间戳
    'method': str,                  # 提取方法（GLCM/LBP/Gabor）
    'parameters': dict,             # 方法参数
    'feature_dim': int              # 特征维度
}
```

#### 6.1.4 形状特征

```python
{
    'features': np.array,           # 形状特征矩阵
    'image_paths': np.array,        # 图像路径列表
    'image_hashes': np.array,       # 图像哈希值
    'extraction_time': np.array,    # 提取时间戳
    'method': str,                  # 提取方法（contour/edge/moment）
    'parameters': dict,             # 方法参数
    'feature_dim': int              # 特征维度
}
```

### 6.2 搜索结果格式

搜索结果以列表形式返回，每个结果项包含以下信息：

```python
{
    'image_path': str,              # 图像路径
    'similarity': float,             # 总相似度分数
    'feature_similarities': {        # 各特征维度的相似度
        'deep': float,
        'color': float,
        'texture': float,
        'shape': float
    },
    'distance': float,               # 距离（越小越相似）
    'rank': int                      # 排名
}
```

## 7. 开发环境配置

### 7.1 环境要求

- **操作系统**：Windows 10/11、macOS 10.14+、Linux（Ubuntu 18.04+）
- **Python**：Python 3.8或更高版本
- **GPU**（推荐）：NVIDIA GPU（支持CUDA）用于加速特征提取和搜索
- **内存**：至少8GB，推荐16GB或更高
- **存储**：至少10GB可用空间，具体取决于图像库大小

### 7.2 依赖安装

```bash
# 基础依赖
pip install numpy scipy scikit-learn pillow opencv-python matplotlib

# 深度学习框架
pip install torch torchvision

# 向量索引
pip install faiss-cpu  # 或 faiss-gpu 用于GPU加速

# 用户界面
pip install PyQt5

# 工具库
pip install tqdm pyyaml h5py
```

### 7.3 GPU配置

对于GPU加速，需要安装CUDA和cuDNN：

```bash
# 安装GPU版PyTorch（以CUDA 11.8为例）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装GPU版Faiss
pip install faiss-gpu
```

### 7.4 开发环境设置

```python
# 设置环境变量
import os
os.environ['PYTHONPATH'] = os.path.dirname(os.path.abspath(__file__))

# 配置GPU使用
import torch
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# 配置日志
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)
```

## 8. 扩展开发指南

### 8.1 添加新的特征提取模型

要添加新的深度学习模型，需要修改`models/model_loader.py`：

```python
def load_model(self, model_name):
    # 现有模型加载代码...
    
    # 添加新模型
    elif model_name == 'new_model':
        # 导入模型
        from torchvision.models import new_model
        # 加载预训练模型
        model = new_model(pretrained=True)
        # 修改模型结构以提取特征
        model = self._modify_new_model(model)
        return model
        
def _modify_new_model(self, model):
    """修改新模型以提取特征"""
    # 移除分类层，保留特征提取部分
    return nn.Sequential(*list(model.children())[:-1])
```

同时，需要在`config/models.py`中添加新模型的配置：

```python
AVAILABLE_MODELS = {
    # 现有模型...
    'new_model': {
        'feature_dim': 2048,  # 特征维度
        'input_size': (224, 224),  # 输入图像大小
        'preprocessing': 'standard',  # 预处理方法
        'batch_size': 32  # 批处理大小
    }
}
```

### 8.2 添加新的搜索策略

要添加新的搜索策略，需要在`search/search_strategies.py`中创建新的策略类：

```python
class NewSearchStrategy(SearchStrategy):
    """新的搜索策略"""
    
    def search(self, query_features, feature_database, weights, top_n, **kwargs):
        """执行新的搜索策略
        
        Args:
            query_features (dict): 查询特征
            feature_database (FeatureDatabase): 特征数据库
            weights (dict): 特征权重
            top_n (int): 返回的结果数量
            **kwargs: 其他参数
            
        Returns:
            list: 搜索结果列表
        """
        # 实现新的搜索策略
        results = []
        # ...
        return results
```

然后，在`search/search_engine.py`中注册新策略：

```python
def __init__(self, feature_database, feature_extractor):
    # 现有代码...
    self.search_strategies = {
        # 现有策略...
        'new_strategy': NewSearchStrategy()
    }
```

### 8.3 添加新的数据库类型

要添加新的数据库类型，需要在`database/`目录下创建新的数据库类：

```python
class NewDatabase(FeatureDatabase):
    """新的数据库类型"""
    
    def __init__(self, db_path):
        """初始化新数据库
        
        Args:
            db_path (str): 数据库路径
        """
        super().__init__(db_path, 'new_type')
        # 初始化特定属性
        
    def load(self):
        """加载数据库
        
        Returns:
            bool: 加载是否成功
        """
        # 实现数据库加载
        return True
        
    def save(self):
        """保存数据库
        
        Returns:
            bool: 保存是否成功
        """
        # 实现数据库保存
        return True
```

然后，在`database/__init__.py`中注册新数据库类型：

```python
from .feature_database import FeatureDatabase
from .vector_database import VectorDatabase
from .new_database import NewDatabase

def create_database(db_path, db_type='standard'):
    """创建数据库实例
    
    Args:
        db_path (str): 数据库路径
        db_type (str): 数据库类型
        
    Returns:
        FeatureDatabase: 数据库实例
    """
    if db_type == 'standard':
        return FeatureDatabase(db_path)
    elif db_type == 'vector':
        return VectorDatabase(db_path)
    elif db_type == 'new_type':
        return NewDatabase(db_path)
    else:
        raise ValueError(f"Unsupported database type: {db_type}")
```

### 8.4 添加新的UI组件

要添加新的UI组件，需要在`ui/`目录下创建新的组件类：

```python
class NewComponent(QWidget):
    """新的UI组件"""
    
    def __init__(self, parent=None):
        """初始化组件
        
        Args:
            parent (QWidget): 父组件
        """
        super().__init__(parent)
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 创建组件
        label = QLabel("New Component")
        button = QPushButton("Action")
        
        # 添加组件到布局
        layout.addWidget(label)
        layout.addWidget(button)
        
        # 连接信号和槽
        button.clicked.connect(self._on_button_clicked)
        
    def _on_button_clicked(self):
        """按钮点击事件处理"""
        # 实现事件处理
        pass
```

## 9. 性能优化

### 9.1 GPU加速

系统支持GPU加速特征提取和相似度计算，大幅提升处理速度：

```python
# 检查GPU可用性
def is_gpu_available():
    """检查GPU是否可用
    
    Returns:
        bool: GPU是否可用
    """
    return torch.cuda.is_available()

# 获取GPU信息
def get_gpu_info():
    """获取GPU信息
    
    Returns:
        dict: GPU信息
    """
    if not is_gpu_available():
        return {"available": False}
    
    return {
        "available": True,
        "name": torch.cuda.get_device_name(0),
        "count": torch.cuda.device_count(),
        "memory_total": torch.cuda.get_device_properties(0).total_memory,
        "memory_reserved": torch.cuda.memory_reserved(0),
        "memory_allocated": torch.cuda.memory_allocated(0)
    }

# 使用GPU进行批处理
def batch_process_gpu(images, model, batch_size=32):
    """使用GPU进行批处理
    
    Args:
        images (list): 图像列表
        model (torch.nn.Module): 模型
        batch_size (int): 批处理大小
        
    Returns:
        numpy.ndarray: 特征矩阵
    """
    device = torch.device('cuda')
    model = model.to(device)
    model.eval()
    
    features = []
    with torch.no_grad():
        for i in range(0, len(images), batch_size):
            batch = images[i:i+batch_size]
            batch_tensor = torch.stack(batch).to(device)
            batch_features = model(batch_tensor)
            features.append(batch_features.cpu().numpy())
    
    return np.vstack(features)
```

### 9.2 批处理优化

使用批处理可以提高特征提取和相似度计算的效率：

```python
# 批量特征提取
def extract_features_batch(image_paths, feature_extractor, batch_size=32):
    """批量提取特征
    
    Args:
        image_paths (list): 图像路径列表
        feature_extractor (FeatureExtractor): 特征提取器
        batch_size (int): 批处理大小
        
    Returns:
        dict: 特征字典
    """
    results = {}
    for i in range(0, len(image_paths), batch_size):
        batch_paths = image_paths[i:i+batch_size]
        batch_results = feature_extractor.extract_features_batch(batch_paths)
        results.update(batch_results)
    return results

# 批量相似度计算
def compute_similarities_batch(query_features, database_features, batch_size=1000):
    """批量计算相似度
    
    Args:
        query_features (numpy.ndarray): 查询特征
        database_features (numpy.ndarray): 数据库特征
        batch_size (int): 批处理大小
        
    Returns:
        numpy.ndarray: 相似度矩阵
    """
    n = len(database_features)
    similarities = np.zeros(n)
    
    for i in range(0, n, batch_size):
        batch_features = database_features[i:i+batch_size]
        batch_similarities = compute_cosine_similarity(query_features, batch_features)
        similarities[i:i+batch_size] = batch_similarities
    
    return similarities
```

### 9.3 内存优化

优化内存使用，减少内存占用：

```python
# 按需加载特征
def load_features_on_demand(db_path, feature_type):
    """按需加载特征
    
    Args:
        db_path (str): 数据库路径
        feature_type (str): 特征类型
        
    Returns:
        numpy.ndarray: 特征矩阵
    """
    feature_file = os.path.join(db_path, f"{feature_type}_features.npz")
    with np.load(feature_file) as data:
        return data['features']

# 使用内存映射文件
def load_large_features(feature_file):
    """加载大型特征文件
    
    Args:
        feature_file (str): 特征文件路径
        
    Returns:
        numpy.ndarray: 特征矩阵
    """
    return np.load(feature_file, mmap_mode='r')

# 清理GPU内存
def clear_gpu_memory():
    """清理GPU内存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
```

### 9.4 向量索引优化

使用Faiss向量索引加速相似度搜索：

```python
# 创建向量索引
def create_vector_index(features, index_type='Flat', metric='L2'):
    """创建向量索引
    
    Args:
        features (numpy.ndarray): 特征矩阵
        index_type (str): 索引类型
        metric (str): 距离度量
        
    Returns:
        faiss.Index: 向量索引
    """
    d = features.shape[1]  # 特征维度
    
    if metric == 'L2':
        index = faiss.IndexFlatL2(d)  # L2距离
    elif metric == 'IP':
        index = faiss.IndexFlatIP(d)  # 内积（余弦相似度）
    else:
        raise ValueError(f"Unsupported metric: {metric}")
    
    if index_type.startswith('IVF'):
        # IVF索引，格式：'IVF100,Flat'
        parts = index_type.split(',', 1)
        nlist = int(parts[0][3:])  # 聚类中心数量
        sub_index_type = parts[1] if len(parts) > 1 else 'Flat'
        
        if sub_index_type == 'Flat':
            quantizer = faiss.IndexFlatL2(d)
            index = faiss.IndexIVFFlat(quantizer, d, nlist)
        elif sub_index_type == 'PQ':
            m = 8  # 子向量数量
            quantizer = faiss.IndexFlatL2(d)
            index = faiss.IndexIVFPQ(quantizer, d, nlist, m, 8)
    
    # 添加特征到索引
    index.add(features)
    
    return index

# 使用向量索引搜索
def search_vector_index(index, query_features, k=10):
    """使用向量索引搜索
    
    Args:
        index (faiss.Index): 向量索引
        query_features (numpy.ndarray): 查询特征
        k (int): 返回的结果数量
        
    Returns:
        tuple: (距离, 索引)
    """
    return index.search(query_features, k)
```

## 10. 测试与验证

### 10.1 单元测试

使用pytest进行单元测试，确保各模块功能正确：

```python
# 特征提取测试
def test_feature_extraction():
    """测试特征提取"""
    # 创建特征提取器
    feature_extractor = FeatureExtractor(model_name='resnet50', use_gpu=False)
    
    # 提取特征
    image_path = 'tests/data/test_image.jpg'
    features = feature_extractor.extract_features(image_path)
    
    # 验证特征
    assert 'deep' in features
    assert 'color' in features
    assert 'texture' in features
    assert 'shape' in features
    assert features['deep'].shape[1] == 2048  # ResNet50特征维度

# 数据库测试
def test_database_operations():
    """测试数据库操作"""
    # 创建数据库
    db_path = 'tests/data/test_db'
    db = FeatureDatabase(db_path)
    
    # 添加特征
    image_path = 'tests/data/test_image.jpg'
    features = {'deep': np.random.rand(1, 2048)}
    db.add_features(image_path, features)
    
    # 获取特征
    retrieved_features = db.get_features(image_path)
    assert 'deep' in retrieved_features
    assert np.array_equal(features['deep'], retrieved_features['deep'])
    
    # 更新特征
    updated_features = {'deep': np.random.rand(1, 2048)}
    db.update_features(image_path, updated_features)
    retrieved_features = db.get_features(image_path)
    assert np.array_equal(updated_features['deep'], retrieved_features['deep'])
    
    # 删除特征
    db.delete_features(image_path)
    assert image_path not in db.image_paths
```

### 10.2 集成测试

测试模块间的交互和数据流：

```python
# 搜索引擎测试
def test_search_engine():
    """测试搜索引擎"""
    # 创建特征提取器
    feature_extractor = FeatureExtractor(model_name='resnet50', use_gpu=False)
    
    # 创建数据库
    db_path = 'tests/data/test_db'
    db = FeatureDatabase(db_path)
    
    # 添加测试数据
    for i in range(10):
        image_path = f'tests/data/test_image_{i}.jpg'
        features = {
            'deep': np.random.rand(1, 2048),
            'color': np.random.rand(1, 64),
            'texture': np.random.rand(1, 32),
            'shape': np.random.rand(1, 16)
        }
        db.add_features(image_path, features)
    
    # 创建搜索引擎
    search_engine = SearchEngine(db, feature_extractor)
    
    # 执行搜索
    query_image_path = 'tests/data/query_image.jpg'
    results = search_engine.search(
        query_image_path,
        strategy_name='weighted',
        weights={'deep': 0.5, 'color': 0.2, 'texture': 0.2, 'shape': 0.1},
        top_n=5
    )
    
    # 验证结果
    assert len(results) == 5
    assert 'image_path' in results[0]
    assert 'similarity' in results[0]
    assert 'feature_similarities' in results[0]
    assert 'distance' in results[0]
    assert 'rank' in results[0]
```

### 10.3 性能测试

测试系统在不同条件下的性能表现：

```python
# 大规模数据测试
def test_large_scale_performance():
    """测试大规模数据性能"""
    # 创建大量测试数据
    n_images = 10000
    feature_dim = 2048
    features = np.random.rand(n_images, feature_dim).astype(np.float32)
    image_paths = [f'test_image_{i}.jpg' for i in range(n_images)]
    
    # 创建向量索引
    import time
    start_time = time.time()
    index = create_vector_index(features)
    index_time = time.time() - start_time
    print(f"Index creation time: {index_time:.2f}s")
    
    # 执行搜索
    query = np.random.rand(1, feature_dim).astype(np.float32)
    start_time = time.time()
    distances, indices = search_vector_index(index, query, k=100)
    search_time = time.time() - start_time
    print(f"Search time: {search_time:.4f}s")
    
    # 验证性能
    assert index_time < 10.0  # 索引创建时间应小于10秒
    assert search_time < 0.1  # 搜索时间应小于0.1秒
```

### 10.4 验证方法

验证搜索结果的准确性：

```python
# 计算准确率
def compute_accuracy(results, ground_truth):
    """计算搜索准确率
    
    Args:
        results (list): 搜索结果列表
        ground_truth (list): 真实结果列表
        
    Returns:
        float: 准确率
    """
    correct = 0
    for result in results:
        if result['image_path'] in ground_truth:
            correct += 1
    return correct / len(results)

# 计算平均精度
def compute_average_precision(results, ground_truth):
    """计算平均精度
    
    Args:
        results (list): 搜索结果列表
        ground_truth (list): 真实结果列表
        
    Returns:
        float: 平均精度
    """
    precision_sum = 0
    correct = 0
    
    for i, result in enumerate(results):
        if result['image_path'] in ground_truth:
            correct += 1
            precision = correct / (i + 1)
            precision_sum += precision
    
    return precision_sum / len(ground_truth) if ground_truth else 0
```

## 11. 常见问题与解决方案

### 11.1 特征提取问题

#### 11.1.1 GPU内存不足

**问题**：GPU内存不足，无法处理大批量图像。

**解决方案**：

```python
# 减小批处理大小
batch_size = 8  # 默认值可能是32或64
feature_extractor = FeatureExtractor(model_name='resnet50', use_gpu=True)
processor = BatchProcessor(feature_extractor, batch_size=batch_size)

# 或使用内存管理
import torch

def extract_with_memory_management(images, model, max_batch_size=32):
    """使用内存管理提取特征
    
    Args:
        images (list): 图像列表
        model (torch.nn.Module): 模型
        max_batch_size (int): 最大批处理大小
        
    Returns:
        numpy.ndarray: 特征矩阵
    """
    device = torch.device('cuda')
    model = model.to(device)
    model.eval()
    
    # 动态调整批处理大小
    batch_size = max_batch_size
    while batch_size > 1:
        try:
            # 尝试处理一个批次
            with torch.no_grad():
                batch = images[:batch_size]
                batch_tensor = torch.stack(batch).to(device)
                _ = model(batch_tensor)
            break
        except RuntimeError as e:
            # 内存不足，减小批处理大小
            if 'out of memory' in str(e):
                torch.cuda.empty_cache()
                batch_size //= 2
                print(f"Reduced batch size to {batch_size}")
            else:
                raise
    
    # 使用调整后的批处理大小处理所有图像
    features = []
    with torch.no_grad():
        for i in range(0, len(images), batch_size):
            batch = images[i:i+batch_size]
            batch_tensor = torch.stack(batch).to(device)
            batch_features = model(batch_tensor)
            features.append(batch_features.cpu().numpy())
            torch.cuda.empty_cache()  # 清理缓存
    
    return np.vstack(features)
```

#### 11.1.2 模型加载错误

**问题**：无法加载预训练模型。

**解决方案**：

```python
# 检查模型可用性
def check_model_availability(model_name):
    """检查模型可用性
    
    Args:
        model_name (str): 模型名称
        
    Returns:
        bool: 模型是否可用
    """
    try:
        if model_name == 'resnet50':
            import torchvision.models as models
            _ = models.resnet50(pretrained=True)
        elif model_name == 'vgg16':
            import torchvision.models as models
            _ = models.vgg16(pretrained=True)
        # 其他模型...
        return True
    except Exception as e:
        print(f"Error loading model {model_name}: {e}")
        return False

# 使用本地模型文件
def load_local_model(model_name, model_path):
    """加载本地模型文件
    
    Args:
        model_name (str): 模型名称
        model_path (str): 模型文件路径
        
    Returns:
        torch.nn.Module: 加载的模型
    """
    if model_name == 'resnet50':
        import torchvision.models as models
        model = models.resnet50(pretrained=False)
        model.load_state_dict(torch.load(model_path))
    # 其他模型...
    return model
```

### 11.2 数据库问题

#### 11.2.1 数据库损坏

**问题**：特征数据库损坏或不一致。

**解决方案**：

```python
# 验证数据库完整性
def validate_database(db_path):
    """验证数据库完整性
    
    Args:
        db_path (str): 数据库路径
        
    Returns:
        bool: 数据库是否完整
    """
    try:
        # 检查必要文件
        required_files = [
            'metadata.json',
            'image_index.json',
            'feature_index.json'
        ]
        for file in required_files:
            if not os.path.exists(os.path.join(db_path, file)):
                print(f"Missing required file: {file}")
                return False
        
        # 加载元数据
        with open(os.path.join(db_path, 'metadata.json'), 'r') as f:
            metadata = json.load(f)
        
        # 验证特征文件
        feature_types = metadata.get('feature_types', [])
        for feature_type in feature_types:
            feature_file = os.path.join(db_path, f"{feature_type}_features.npz")
            if not os.path.exists(feature_file):
                print(f"Missing feature file: {feature_file}")
                return False
            
            # 验证特征数据
            try:
                with np.load(feature_file) as data:
                    if 'features' not in data or 'image_paths' not in data:
                        print(f"Invalid feature file: {feature_file}")
                        return False
            except Exception as e:
                print(f"Error loading feature file {feature_file}: {e}")
                return False
        
        return True
    except Exception as e:
        print(f"Error validating database: {e}")
        return False

# 修复数据库
def repair_database(db_path):
    """修复数据库
    
    Args:
        db_path (str): 数据库路径
        
    Returns:
        bool: 修复是否成功
    """
    try:
        # 创建备份
        backup_path = f"{db_path}_backup_{int(time.time())}"
        shutil.copytree(db_path, backup_path)
        print(f"Created backup at {backup_path}")
        
        # 加载可用数据
        valid_features = {}
        valid_image_paths = set()
        
        # 尝试加载元数据
        metadata_path = os.path.join(db_path, 'metadata.json')
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                feature_types = metadata.get('feature_types', [])
            except:
                feature_types = ['deep', 'color', 'texture', 'shape']
        else:
            feature_types = ['deep', 'color', 'texture', 'shape']
        
        # 尝试加载特征数据
        for feature_type in feature_types:
            feature_file = os.path.join(db_path, f"{feature_type}_features.npz")
            if os.path.exists(feature_file):
                try:
                    with np.load(feature_file) as data:
                        if 'features' in data and 'image_paths' in data:
                            features = data['features']
                            image_paths = data['image_paths']
                            valid_features[feature_type] = features
                            valid_image_paths.update(image_paths)
                except:
                    pass
        
        # 重建数据库
        if valid_image_paths:
            # 创建新的元数据
            new_metadata = {
                'feature_types': list(valid_features.keys()),
                'image_count': len(valid_image_paths),
                'creation_time': time.time(),
                'version': '2.0'
            }
            
            # 保存新的元数据
            with open(metadata_path, 'w') as f:
                json.dump(new_metadata, f, indent=2)
            
            # 保存新的特征数据
            for feature_type, features in valid_features.items():
                feature_file = os.path.join(db_path, f"{feature_type}_features.npz")
                np.savez_compressed(feature_file, features=features, image_paths=list(valid_image_paths))
            
            # 创建索引文件
            image_index = {path: i for i, path in enumerate(valid_image_paths)}
            with open(os.path.join(db_path, 'image_index.json'), 'w') as f:
                json.dump(image_index, f, indent=2)
            
            feature_index = {feature_type: os.path.join(db_path, f"{feature_type}_features.npz") for feature_type in valid_features.keys()}
            with open(os.path.join(db_path, 'feature_index.json'), 'w') as f:
                json.dump(feature_index, f, indent=2)
            
            print(f"Repaired database with {len(valid_image_paths)} images and {len(valid_features)} feature types")
            return True
        else:
            print("No valid data found, cannot repair database")
            return False
    except Exception as e:
        print(f"Error repairing database: {e}")
        return False
```

### 11.3 搜索问题

#### 11.3.1 搜索结果不准确

**问题**：搜索结果与预期不符。

**解决方案**：

```python
# 调整特征权重
def optimize_weights(query_image_path, ground_truth_paths, feature_database, feature_extractor):
    """优化特征权重
    
    Args:
        query_image_path (str): 查询图像路径
        ground_truth_paths (list): 真实结果路径列表
        feature_database (FeatureDatabase): 特征数据库
        feature_extractor (FeatureExtractor): 特征提取器
        
    Returns:
        dict: 优化后的权重
    """
    # 提取查询特征
    query_features = feature_extractor.extract_features(query_image_path)
    
    # 定义权重范围
    weight_range = np.arange(0.1, 1.0, 0.1)
    
    # 初始化最佳权重和最佳准确率
    best_weights = {'deep': 0.5, 'color': 0.2, 'texture': 0.2, 'shape': 0.1}
    best_accuracy = 0.0
    
    # 网格搜索最佳权重
    for deep_weight in weight_range:
        for color_weight in weight_range:
            for texture_weight in weight_range:
                # 确保权重和为1
                shape_weight = 1.0 - deep_weight - color_weight - texture_weight
                if shape_weight < 0:
                    continue
                
                # 设置当前权重
                weights = {
                    'deep': deep_weight,
                    'color': color_weight,
                    'texture': texture_weight,
                    'shape': shape_weight
                }
                
                # 使用当前权重搜索
                search_engine = SearchEngine(feature_database, feature_extractor)
                results = search_engine.search(
                    query_image_path,
                    strategy_name='weighted',
                    weights=weights,
                    top_n=len(ground_truth_paths)
                )
                
                # 计算准确率
                accuracy = compute_accuracy([r['image_path'] for r in results], ground_truth_paths)
                
                # 更新最佳权重
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    best_weights = weights
    
    print(f"Best weights: {best_weights}, Accuracy: {best_accuracy:.2f}")
    return best_weights

# 使用查询扩展
def search_with_query_expansion(query_image_path, feature_database, feature_extractor, top_n=20, expansion_count=3):
    """使用查询扩展搜索
    
    Args:
        query_image_path (str): 查询图像路径
        feature_database (FeatureDatabase): 特征数据库
        feature_extractor (FeatureExtractor): 特征提取器
        top_n (int): 返回的结果数量
        expansion_count (int): 扩展查询数量
        
    Returns:
        list: 搜索结果列表
    """
    # 初始搜索
    search_engine = SearchEngine(feature_database, feature_extractor)
    initial_results = search_engine.search(
        query_image_path,
        strategy_name='weighted',
        top_n=expansion_count
    )
    
    # 提取初始结果的特征
    expansion_features = {}
    for feature_type in ['deep', 'color', 'texture', 'shape']:
        if feature_type in feature_extractor.feature_types:
            # 获取查询特征
            query_feature = feature_extractor.extract_features(query_image_path)[feature_type]
            
            # 获取扩展特征
            expansion_features[feature_type] = [query_feature]
            for result in initial_results:
                result_feature = feature_database.get_features(result['image_path'])[feature_type]
                expansion_features[feature_type].append(result_feature)
            
            # 计算平均特征
            expansion_features[feature_type] = np.mean(expansion_features[feature_type], axis=0, keepdims=True)
    
    # 使用扩展特征搜索
    expanded_results = search_engine.search_by_features(
        expansion_features,
        strategy_name='weighted',
        top_n=top_n
    )
    
    return expanded_results
```

#### 11.3.2 搜索速度慢

**问题**：搜索速度慢，特别是在大型数据库上。

**解决方案**：

```python
# 使用向量量化
def create_pq_index(features, m=8):
    """创建乘积量化索引
    
    Args:
        features (numpy.ndarray): 特征矩阵
        m (int): 子向量数量
        
    Returns:
        faiss.Index: 向量索引
    """
    d = features.shape[1]  # 特征维度
    
    # 创建乘积量化索引
    index = faiss.IndexPQ(d, m, 8)  # 8位编码
    index.train(features)
    index.add(features)
    
    return index

# 使用多GPU加速
def create_multi_gpu_index(features, ngpus=2):
    """创建多GPU索引
    
    Args:
        features (numpy.ndarray): 特征矩阵
        ngpus (int): GPU数量
        
    Returns:
        faiss.Index: 向量索引
    """
    d = features.shape[1]  # 特征维度
    
    # 创建基础索引
    index = faiss.IndexFlatL2(d)
    
    # 创建多GPU索引
    gpu_resources = [faiss.StandardGpuResources() for i in range(ngpus)]
    config = faiss.GpuMultipleClonerOptions()
    config.shard = True  # 分片到多个GPU
    index = faiss.index_cpu_to_gpu_multiple_py(gpu_resources, index, config)
    
    # 添加特征到索引
    index.add(features)
    
    return index

# 使用异步搜索
async def async_search(query_features, index, k=10):
    """异步搜索
    
    Args:
        query_features (numpy.ndarray): 查询特征
        index (faiss.Index): 向量索引
        k (int): 返回的结果数量
        
    Returns:
        tuple: (距离, 索引)
    """
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, lambda: index.search(query_features, k))
```
