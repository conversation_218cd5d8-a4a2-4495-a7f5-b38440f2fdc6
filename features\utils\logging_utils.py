"""日志工具模块

提供日志记录和性能监控相关的工具函数。
"""

import logging
import time
import functools
import traceback
from typing import Any, Callable, Dict, Optional
from pathlib import Path
from datetime import datetime
import threading
import psutil
import os


def setup_logger(name: str, log_file: Optional[str] = None, 
                 level: int = logging.INFO, 
                 format_string: Optional[str] = None,
                 force: bool = False) -> logging.Logger:
    """设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径
        level: 日志级别
        format_string: 日志格式字符串
        force: 是否强制重新配置日志记录器，即使已有处理器
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复添加处理器，除非强制重新配置
    if logger.handlers and not force:
        return logger
    
    # 如果强制重新配置，先移除现有处理器
    if force and logger.handlers:
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
    logger.setLevel(level)
    
    # 默认格式
    if format_string is None:
        format_string = (
            '%(asctime)s - %(name)s - %(levelname)s - '
            '[%(filename)s:%(lineno)d] - %(message)s'
        )
    
    formatter = logging.Formatter(format_string)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def log_performance(func: Callable) -> Callable:
    """性能日志装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        Callable: 装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        try:
            result = func(*args, **kwargs)
            
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            
            execution_time = end_time - start_time
            memory_delta = end_memory - start_memory
            
            logger.info(
                f"函数 {func.__name__} 执行完成 - "
                f"耗时: {execution_time:.3f}s, "
                f"内存变化: {memory_delta:+.2f}MB"
            )
            
            return result
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.error(
                f"函数 {func.__name__} 执行失败 - "
                f"耗时: {execution_time:.3f}s, "
                f"错误: {str(e)}"
            )
            raise
            
    return wrapper


def log_error_with_context(logger: logging.Logger, error: Exception, 
                          context: Dict[str, Any], 
                          include_traceback: bool = True) -> None:
    """记录带上下文的错误日志
    
    Args:
        logger: 日志记录器
        error: 异常对象
        context: 上下文信息
        include_traceback: 是否包含堆栈跟踪
    """
    error_msg = f"错误: {str(error)}"
    
    if context:
        context_str = ", ".join([f"{k}={v}" for k, v in context.items()])
        error_msg += f" - 上下文: {context_str}"
    
    if include_traceback:
        error_msg += f"\n堆栈跟踪:\n{traceback.format_exc()}"
    
    logger.error(error_msg)


class PerformanceMonitor:
    """性能监控器
    
    提供实时性能监控功能，包括CPU、内存、执行时间等指标。
    """
    
    def __init__(self, name: str, logger: Optional[logging.Logger] = None):
        """初始化性能监控器
        
        Args:
            name: 监控器名称
            logger: 日志记录器
        """
        self.name = name
        self.logger = logger or logging.getLogger(__name__)
        
        # 性能数据
        self._start_time: Optional[float] = None
        self._start_memory: Optional[float] = None
        self._start_cpu: Optional[float] = None
        
        # 统计数据
        self._execution_times = []
        self._memory_usages = []
        self._cpu_usages = []
        
        # 线程锁
        self._lock = threading.RLock()
        
    def start(self) -> None:
        """开始监控"""
        with self._lock:
            self._start_time = time.time()
            
            try:
                process = psutil.Process()
                self._start_memory = process.memory_info().rss / 1024 / 1024  # MB
                self._start_cpu = process.cpu_percent()
            except Exception as e:
                self.logger.warning(f"获取系统信息失败: {e}")
                self._start_memory = 0
                self._start_cpu = 0
                
    def stop(self) -> Dict[str, float]:
        """停止监控并返回性能数据
        
        Returns:
            Dict[str, float]: 性能数据
        """
        with self._lock:
            if self._start_time is None:
                self.logger.warning("监控器未启动")
                return {}
                
            end_time = time.time()
            execution_time = end_time - self._start_time
            
            try:
                process = psutil.Process()
                end_memory = process.memory_info().rss / 1024 / 1024  # MB
                end_cpu = process.cpu_percent()
                
                memory_delta = end_memory - (self._start_memory or 0)
                cpu_delta = end_cpu - (self._start_cpu or 0)
                
            except Exception as e:
                self.logger.warning(f"获取系统信息失败: {e}")
                memory_delta = 0
                cpu_delta = 0
                
            # 记录统计数据
            self._execution_times.append(execution_time)
            self._memory_usages.append(memory_delta)
            self._cpu_usages.append(cpu_delta)
            
            performance_data = {
                'execution_time': execution_time,
                'memory_delta': memory_delta,
                'cpu_delta': cpu_delta
            }
            
            self.logger.info(
                f"性能监控 [{self.name}] - "
                f"执行时间: {execution_time:.3f}s, "
                f"内存变化: {memory_delta:+.2f}MB, "
                f"CPU变化: {cpu_delta:+.2f}%"
            )
            
            # 重置状态
            self._start_time = None
            self._start_memory = None
            self._start_cpu = None
            
            return performance_data
            
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            if not self._execution_times:
                return {}
                
            return {
                'total_executions': len(self._execution_times),
                'avg_execution_time': sum(self._execution_times) / len(self._execution_times),
                'max_execution_time': max(self._execution_times),
                'min_execution_time': min(self._execution_times),
                'avg_memory_usage': sum(self._memory_usages) / len(self._memory_usages),
                'max_memory_usage': max(self._memory_usages),
                'min_memory_usage': min(self._memory_usages),
                'avg_cpu_usage': sum(self._cpu_usages) / len(self._cpu_usages),
                'max_cpu_usage': max(self._cpu_usages),
                'min_cpu_usage': min(self._cpu_usages)
            }
            
    def reset_statistics(self) -> None:
        """重置统计数据"""
        with self._lock:
            self._execution_times.clear()
            self._memory_usages.clear()
            self._cpu_usages.clear()
            
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()
        
        if exc_type is not None:
            self.logger.error(
                f"性能监控 [{self.name}] 期间发生异常: {exc_type.__name__}: {exc_val}"
            )


def create_performance_monitor(name: str, 
                             logger: Optional[logging.Logger] = None) -> PerformanceMonitor:
    """创建性能监控器
    
    Args:
        name: 监控器名称
        logger: 日志记录器
        
    Returns:
        PerformanceMonitor: 性能监控器实例
    """
    return PerformanceMonitor(name, logger)


class TimingContext:
    """计时上下文管理器"""
    
    def __init__(self, name: str, logger: Optional[logging.Logger] = None):
        """初始化计时上下文
        
        Args:
            name: 操作名称
            logger: 日志记录器
        """
        self.name = name
        self.logger = logger or logging.getLogger(__name__)
        self.start_time: Optional[float] = None
        
    def __enter__(self):
        """进入上下文"""
        self.start_time = time.time()
        self.logger.debug(f"开始执行: {self.name}")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if self.start_time is not None:
            execution_time = time.time() - self.start_time
            
            if exc_type is None:
                self.logger.info(f"完成执行: {self.name} (耗时: {execution_time:.3f}s)")
            else:
                self.logger.error(
                    f"执行失败: {self.name} (耗时: {execution_time:.3f}s, "
                    f"错误: {exc_type.__name__}: {exc_val})"
                )


def timing_context(name: str, logger: Optional[logging.Logger] = None) -> TimingContext:
    """创建计时上下文管理器
    
    Args:
        name: 操作名称
        logger: 日志记录器
        
    Returns:
        TimingContext: 计时上下文管理器
    """
    return TimingContext(name, logger)


class MemoryTracker:
    """内存跟踪器"""
    
    def __init__(self, name: str, logger: Optional[logging.Logger] = None):
        """初始化内存跟踪器
        
        Args:
            name: 跟踪器名称
            logger: 日志记录器
        """
        self.name = name
        self.logger = logger or logging.getLogger(__name__)
        self.start_memory: Optional[float] = None
        
    def start(self) -> None:
        """开始跟踪"""
        try:
            self.start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            self.logger.debug(f"开始内存跟踪: {self.name} (当前内存: {self.start_memory:.2f}MB)")
        except Exception as e:
            self.logger.warning(f"获取内存信息失败: {e}")
            self.start_memory = 0
            
    def stop(self) -> float:
        """停止跟踪并返回内存变化
        
        Returns:
            float: 内存变化量(MB)
        """
        if self.start_memory is None:
            self.logger.warning("内存跟踪器未启动")
            return 0.0
            
        try:
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            memory_delta = end_memory - self.start_memory
            
            self.logger.info(
                f"内存跟踪完成: {self.name} "
                f"(变化: {memory_delta:+.2f}MB, 当前: {end_memory:.2f}MB)"
            )
            
            return memory_delta
            
        except Exception as e:
            self.logger.warning(f"获取内存信息失败: {e}")
            return 0.0
            
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()


def memory_tracker(name: str, logger: Optional[logging.Logger] = None) -> MemoryTracker:
    """创建内存跟踪器
    
    Args:
        name: 跟踪器名称
        logger: 日志记录器
        
    Returns:
        MemoryTracker: 内存跟踪器实例
    """
    return MemoryTracker(name, logger)