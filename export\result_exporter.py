#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果导出模块

该模块实现搜索结果的多种格式导出功能。
"""

import json
import csv
import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime
import shutil
from PIL import Image
import pandas as pd

from utils.logger_mixin import LoggerMixin
# 避免循环导入
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from models.batch_processing import BatchResult
    from search.image_search import SearchResult


@dataclass
class ExportResult:
    """导出结果"""
    image_path: str
    similarity_score: float
    rank: int
    features: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class ExportConfig:
    """导出配置"""
    format: str = 'json'                    # 导出格式: json, csv, xml, html, excel
    include_images: bool = False            # 是否包含图像文件
    include_features: bool = False          # 是否包含特征数据
    include_metadata: bool = True           # 是否包含元数据
    max_results: Optional[int] = None       # 最大结果数量
    image_size: Optional[tuple] = None      # 图像尺寸（用于缩略图）
    compression_quality: int = 85           # 图像压缩质量
    create_thumbnails: bool = False         # 是否创建缩略图
    output_structure: str = 'flat'          # 输出结构: flat, hierarchical


class ResultExporter(LoggerMixin):
    """结果导出器"""
    
    def __init__(self, config: Optional[ExportConfig] = None):
        super().__init__()
        self.config = config or ExportConfig()
        
        # 支持的导出格式
        self.supported_formats = {
            'json': self._export_json,
            'csv': self._export_csv,
            'xml': self._export_xml,
            'html': self._export_html,
            'excel': self._export_excel
        }
    
    def export_results(self, 
                      results: List[ExportResult],
                      output_path: Union[str, Path],
                      query_info: Optional[Dict[str, Any]] = None) -> bool:
        """
        导出搜索结果
        
        Args:
            results: 搜索结果列表
            output_path: 输出路径
            query_info: 查询信息
            
        Returns:
            bool: 导出是否成功
        """
        try:
            output_path = Path(output_path)
            
            # 检查导出格式
            if self.config.format not in self.supported_formats:
                raise ValueError(f"不支持的导出格式: {self.config.format}")
            
            # 限制结果数量
            if self.config.max_results is not None:
                results = results[:self.config.max_results]
            
            # 创建输出目录
            if self.config.include_images or self.config.create_thumbnails:
                output_dir = output_path.parent / output_path.stem
                output_dir.mkdir(parents=True, exist_ok=True)
            else:
                output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 处理图像文件
            if self.config.include_images or self.config.create_thumbnails:
                self._process_images(results, output_dir)
            
            # 导出数据
            export_func = self.supported_formats[self.config.format]
            success = export_func(results, output_path, query_info)
            
            if success:
                self.logger.info(f"成功导出 {len(results)} 个结果到 {output_path}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"导出结果失败: {str(e)}")
            return False
    
    def _process_images(self, results: List[ExportResult], output_dir: Path) -> None:
        """处理图像文件"""
        images_dir = output_dir / 'images'
        thumbnails_dir = output_dir / 'thumbnails'
        
        if self.config.include_images:
            images_dir.mkdir(exist_ok=True)
        
        if self.config.create_thumbnails:
            thumbnails_dir.mkdir(exist_ok=True)
        
        for i, result in enumerate(results):
            try:
                source_path = Path(result.image_path)
                if not source_path.exists():
                    self.logger.warning(f"图像文件不存在: {source_path}")
                    continue
                
                # 生成新的文件名
                new_filename = f"{i+1:04d}_{source_path.name}"
                
                # 复制原图像
                if self.config.include_images:
                    dest_path = images_dir / new_filename
                    shutil.copy2(source_path, dest_path)
                    # 更新结果中的路径
                    result.image_path = str(dest_path.relative_to(output_dir))
                
                # 创建缩略图
                if self.config.create_thumbnails:
                    self._create_thumbnail(source_path, thumbnails_dir / new_filename)
                
            except Exception as e:
                self.logger.warning(f"处理图像失败 {result.image_path}: {str(e)}")
    
    def _create_thumbnail(self, source_path: Path, dest_path: Path) -> None:
        """创建缩略图"""
        try:
            with Image.open(source_path) as img:
                # 设置缩略图尺寸
                if self.config.image_size:
                    thumbnail_size = self.config.image_size
                else:
                    thumbnail_size = (200, 200)
                
                # 创建缩略图
                img.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)
                
                # 保存缩略图
                img.save(
                    dest_path,
                    quality=self.config.compression_quality,
                    optimize=True
                )
                
        except Exception as e:
            self.logger.warning(f"创建缩略图失败 {source_path}: {str(e)}")
    
    def _export_json(self, 
                    results: List[ExportResult], 
                    output_path: Path, 
                    query_info: Optional[Dict[str, Any]]) -> bool:
        """导出为JSON格式"""
        try:
            # 准备导出数据
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'format': 'json',
                    'total_results': len(results),
                    'config': asdict(self.config)
                },
                'query_info': query_info or {},
                'results': []
            }
            
            # 处理结果数据
            for result in results:
                result_data = result.to_dict()
                
                # 根据配置过滤数据
                if not self.config.include_features:
                    result_data.pop('features', None)
                
                if not self.config.include_metadata:
                    result_data.pop('metadata', None)
                
                export_data['results'].append(result_data)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            self.logger.error(f"JSON导出失败: {str(e)}")
            return False
    
    def _export_csv(self, 
                   results: List[ExportResult], 
                   output_path: Path, 
                   query_info: Optional[Dict[str, Any]]) -> bool:
        """导出为CSV格式"""
        try:
            # 准备CSV数据
            csv_data = []
            
            for result in results:
                row = {
                    'rank': result.rank,
                    'image_path': result.image_path,
                    'similarity_score': result.similarity_score
                }
                
                # 添加元数据
                if self.config.include_metadata and result.metadata:
                    for key, value in result.metadata.items():
                        row[f'metadata_{key}'] = value
                
                # 添加特征数据（简化）
                if self.config.include_features and result.features:
                    for key, value in result.features.items():
                        if isinstance(value, (int, float, str)):
                            row[f'feature_{key}'] = value
                        else:
                            row[f'feature_{key}'] = str(value)
                
                csv_data.append(row)
            
            # 写入CSV文件
            if csv_data:
                df = pd.DataFrame(csv_data)
                df.to_csv(output_path, index=False, encoding='utf-8')
            
            return True
            
        except Exception as e:
            self.logger.error(f"CSV导出失败: {str(e)}")
            return False
    
    def _export_xml(self, 
                   results: List[ExportResult], 
                   output_path: Path, 
                   query_info: Optional[Dict[str, Any]]) -> bool:
        """导出为XML格式"""
        try:
            # 创建根元素
            root = ET.Element('search_results')
            
            # 添加导出信息
            export_info = ET.SubElement(root, 'export_info')
            ET.SubElement(export_info, 'timestamp').text = datetime.now().isoformat()
            ET.SubElement(export_info, 'format').text = 'xml'
            ET.SubElement(export_info, 'total_results').text = str(len(results))
            
            # 添加查询信息
            if query_info:
                query_elem = ET.SubElement(root, 'query_info')
                for key, value in query_info.items():
                    ET.SubElement(query_elem, key).text = str(value)
            
            # 添加结果
            results_elem = ET.SubElement(root, 'results')
            
            for result in results:
                result_elem = ET.SubElement(results_elem, 'result')
                ET.SubElement(result_elem, 'rank').text = str(result.rank)
                ET.SubElement(result_elem, 'image_path').text = result.image_path
                ET.SubElement(result_elem, 'similarity_score').text = str(result.similarity_score)
                
                # 添加元数据
                if self.config.include_metadata and result.metadata:
                    metadata_elem = ET.SubElement(result_elem, 'metadata')
                    for key, value in result.metadata.items():
                        ET.SubElement(metadata_elem, key).text = str(value)
                
                # 添加特征数据
                if self.config.include_features and result.features:
                    features_elem = ET.SubElement(result_elem, 'features')
                    for key, value in result.features.items():
                        ET.SubElement(features_elem, key).text = str(value)
            
            # 写入文件
            tree = ET.ElementTree(root)
            tree.write(output_path, encoding='utf-8', xml_declaration=True)
            
            return True
            
        except Exception as e:
            self.logger.error(f"XML导出失败: {str(e)}")
            return False
    
    def _export_html(self, 
                    results: List[ExportResult], 
                    output_path: Path, 
                    query_info: Optional[Dict[str, Any]]) -> bool:
        """导出为HTML格式"""
        try:
            # HTML模板
            html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索结果报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .result { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .result img { max-width: 200px; max-height: 200px; float: left; margin-right: 15px; }
        .result-info { overflow: hidden; }
        .score { font-weight: bold; color: #007bff; }
        .metadata { background-color: #f8f9fa; padding: 10px; margin-top: 10px; border-radius: 3px; }
        .clear { clear: both; }
    </style>
</head>
<body>
    <div class="header">
        <h1>图像搜索结果报告</h1>
        <p><strong>导出时间:</strong> {timestamp}</p>
        <p><strong>结果数量:</strong> {total_results}</p>
        {query_info_html}
    </div>
    
    <div class="results">
        {results_html}
    </div>
</body>
</html>
            """
            
            # 生成查询信息HTML
            query_info_html = ""
            if query_info:
                query_info_html = "<h3>查询信息:</h3><ul>"
                for key, value in query_info.items():
                    query_info_html += f"<li><strong>{key}:</strong> {value}</li>"
                query_info_html += "</ul>"
            
            # 生成结果HTML
            results_html = ""
            for result in results:
                # 图像路径处理
                img_src = result.image_path
                if self.config.create_thumbnails:
                    img_src = f"thumbnails/{Path(result.image_path).name}"
                elif self.config.include_images:
                    img_src = f"images/{Path(result.image_path).name}"
                
                result_html = f"""
                <div class="result">
                    <img src="{img_src}" alt="Result {result.rank}" onerror="this.style.display='none'">
                    <div class="result-info">
                        <h3>排名: {result.rank}</h3>
                        <p><strong>相似度:</strong> <span class="score">{result.similarity_score:.4f}</span></p>
                        <p><strong>图像路径:</strong> {result.image_path}</p>
                """
                
                # 添加元数据
                if self.config.include_metadata and result.metadata:
                    result_html += '<div class="metadata"><h4>元数据:</h4><ul>'
                    for key, value in result.metadata.items():
                        result_html += f"<li><strong>{key}:</strong> {value}</li>"
                    result_html += '</ul></div>'
                
                result_html += '</div><div class="clear"></div></div>'
                results_html += result_html
            
            # 生成完整HTML
            html_content = html_template.format(
                timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                total_results=len(results),
                query_info_html=query_info_html,
                results_html=results_html
            )
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return True
            
        except Exception as e:
            self.logger.error(f"HTML导出失败: {str(e)}")
            return False
    
    def _export_excel(self, 
                     results: List[ExportResult], 
                     output_path: Path, 
                     query_info: Optional[Dict[str, Any]]) -> bool:
        """导出为Excel格式"""
        try:
            # 准备数据
            data = []
            
            for result in results:
                row = {
                    '排名': result.rank,
                    '图像路径': result.image_path,
                    '相似度': result.similarity_score
                }
                
                # 添加元数据
                if self.config.include_metadata and result.metadata:
                    for key, value in result.metadata.items():
                        row[f'元数据_{key}'] = value
                
                data.append(row)
            
            # 创建Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 写入结果数据
                df = pd.DataFrame(data)
                df.to_excel(writer, sheet_name='搜索结果', index=False)
                
                # 写入查询信息
                if query_info:
                    query_df = pd.DataFrame([
                        {'参数': key, '值': value} for key, value in query_info.items()
                    ])
                    query_df.to_excel(writer, sheet_name='查询信息', index=False)
                
                # 写入导出信息
                export_info_df = pd.DataFrame([
                    {'参数': '导出时间', '值': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
                    {'参数': '结果数量', '值': len(results)},
                    {'参数': '导出格式', '值': 'excel'}
                ])
                export_info_df.to_excel(writer, sheet_name='导出信息', index=False)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Excel导出失败: {str(e)}")
            return False
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的导出格式"""
        return list(self.supported_formats.keys())
    
    def validate_config(self) -> bool:
        """验证配置"""
        if self.config.format not in self.supported_formats:
            self.logger.error(f"不支持的导出格式: {self.config.format}")
            return False
        
        if self.config.max_results is not None and self.config.max_results <= 0:
            self.logger.error("最大结果数量必须大于0")
            return False
        
        if self.config.compression_quality < 1 or self.config.compression_quality > 100:
            self.logger.error("压缩质量必须在1-100之间")
            return False
        
        return True


class BatchResultExporter(LoggerMixin):
    """批处理结果导出器"""
    
    def __init__(self, config: Optional[ExportConfig] = None):
        super().__init__()
        self.config = config or ExportConfig()
        
        # 支持的导出格式
        self.supported_formats = {
            'json': self._export_batch_json,
            'csv': self._export_batch_csv,
            'html': self._export_batch_html,
            'excel': self._export_batch_excel
        }
    
    def export_batch_results(self, 
                           results: List[Any],
                           output_path: Union[str, Path],
                           summary_info: Optional[Dict[str, Any]] = None) -> bool:
        """导出批处理结果
        
        Args:
            results: 批处理结果列表
            output_path: 输出路径
            summary_info: 汇总信息
            
        Returns:
            bool: 导出是否成功
        """
        try:
            output_path = Path(output_path)
            
            # 检查导出格式
            if self.config.format not in self.supported_formats:
                raise ValueError(f"不支持的导出格式: {self.config.format}")
            
            # 创建输出目录
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 导出数据
            export_func = self.supported_formats[self.config.format]
            success = export_func(results, output_path, summary_info)
            
            if success:
                self.logger.info(f"成功导出 {len(results)} 个批处理结果到 {output_path}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"导出批处理结果失败: {str(e)}")
            return False
    
    def _export_batch_json(self, 
                          results: List[Any], 
                          output_path: Path, 
                          summary_info: Optional[Dict[str, Any]]) -> bool:
        """导出批处理结果为JSON格式"""
        try:
            # 准备导出数据
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'format': 'json',
                    'total_tasks': len(results),
                    'type': 'batch_processing'
                },
                'summary': summary_info or {},
                'results': []
            }
            
            # 处理结果数据
            for result in results:
                result_data = {
                    'task_id': result.task_id,
                    'status': result.status,
                    'processing_time': result.processing_time,
                    'worker_id': result.worker_id,
                    'completed_at': result.completed_at.isoformat() if result.completed_at else None,
                    'result': result.result,
                    'error': result.error
                }
                export_data['results'].append(result_data)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            return True
            
        except Exception as e:
            self.logger.error(f"批处理结果JSON导出失败: {str(e)}")
            return False
    
    def _export_batch_csv(self, 
                         results: List[Any], 
                         output_path: Path, 
                         summary_info: Optional[Dict[str, Any]]) -> bool:
        """导出批处理结果为CSV格式"""
        try:
            # 准备CSV数据
            csv_data = []
            
            for result in results:
                row = {
                    'task_id': result.task_id,
                    'status': result.status,
                    'processing_time': result.processing_time,
                    'worker_id': result.worker_id,
                    'completed_at': result.completed_at.isoformat() if result.completed_at else '',
                    'error': result.error or ''
                }
                
                # 处理结果数据
                if result.result:
                    if isinstance(result.result, dict):
                        for key, value in result.result.items():
                            row[f'result_{key}'] = value
                    else:
                        row['result'] = str(result.result)
                
                csv_data.append(row)
            
            # 写入CSV文件
            if csv_data:
                df = pd.DataFrame(csv_data)
                df.to_csv(output_path, index=False, encoding='utf-8')
            
            return True
            
        except Exception as e:
            self.logger.error(f"批处理结果CSV导出失败: {str(e)}")
            return False
    
    def _export_batch_html(self, 
                          results: List[Any], 
                          output_path: Path, 
                          summary_info: Optional[Dict[str, Any]]) -> bool:
        """导出批处理结果为HTML格式"""
        try:
            # 计算统计信息
            total_tasks = len(results)
            successful_tasks = len([r for r in results if r.status == 'success'])
            failed_tasks = len([r for r in results if r.status == 'failed'])
            avg_time = sum(r.processing_time for r in results) / total_tasks if total_tasks > 0 else 0
            
            # HTML模板
            html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批处理结果报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .summary-card {{ background: #f8f9fa; padding: 20px; border-radius: 6px; text-align: center; }}
        .summary-card h3 {{ margin: 0 0 10px 0; color: #666; }}
        .summary-card .number {{ font-size: 2em; font-weight: bold; color: #2c3e50; }}
        .success .number {{ color: #27ae60; }}
        .failed .number {{ color: #e74c3c; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #f8f9fa; font-weight: bold; color: #2c3e50; }}
        tr:hover {{ background-color: #f8f9fa; }}
        .status-badge {{ padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; text-transform: uppercase; }}
        .status-badge.success {{ background-color: #d4edda; color: #155724; }}
        .status-badge.failed {{ background-color: #f8d7da; color: #721c24; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>批处理结果报告</h1>
            <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总任务数</h3>
                <div class="number">{total_tasks}</div>
            </div>
            <div class="summary-card success">
                <h3>成功任务</h3>
                <div class="number">{successful_tasks}</div>
            </div>
            <div class="summary-card failed">
                <h3>失败任务</h3>
                <div class="number">{failed_tasks}</div>
            </div>
            <div class="summary-card">
                <h3>平均处理时间</h3>
                <div class="number">{avg_time:.2f}s</div>
            </div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>任务ID</th>
                    <th>状态</th>
                    <th>处理时间</th>
                    <th>工作器</th>
                    <th>完成时间</th>
                    <th>备注</th>
                </tr>
            </thead>
            <tbody>
            """
            
            # 添加表格行
            for result in results:
                status_class = 'success' if result.status == 'success' else 'failed'
                completed_time = result.completed_at.strftime('%H:%M:%S') if result.completed_at else ''
                note = result.error if result.status == 'failed' else '成功'
                
                html_template += f"""
                <tr>
                    <td>{result.task_id}</td>
                    <td><span class="status-badge {status_class}">{result.status}</span></td>
                    <td>{result.processing_time:.3f}s</td>
                    <td>{result.worker_id}</td>
                    <td>{completed_time}</td>
                    <td>{note}</td>
                </tr>
                """
            
            html_template += """
            </tbody>
        </table>
    </div>
</body>
</html>
            """
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_template)
            
            return True
            
        except Exception as e:
            self.logger.error(f"批处理结果HTML导出失败: {str(e)}")
            return False
    
    def _export_batch_excel(self, 
                           results: List[Any], 
                           output_path: Path, 
                           summary_info: Optional[Dict[str, Any]]) -> bool:
        """导出批处理结果为Excel格式"""
        try:
            # 准备数据
            data = []
            
            for result in results:
                row = {
                    '任务ID': result.task_id,
                    '状态': result.status,
                    '处理时间(秒)': result.processing_time,
                    '工作器ID': result.worker_id,
                    '完成时间': result.completed_at.strftime('%Y-%m-%d %H:%M:%S') if result.completed_at else '',
                    '错误信息': result.error or ''
                }
                
                # 处理结果数据
                if result.result:
                    if isinstance(result.result, dict):
                        for key, value in result.result.items():
                            row[f'结果_{key}'] = value
                    else:
                        row['结果'] = str(result.result)
                
                data.append(row)
            
            # 创建Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 写入结果数据
                df = pd.DataFrame(data)
                df.to_excel(writer, sheet_name='批处理结果', index=False)
                
                # 写入汇总信息
                if summary_info:
                    summary_df = pd.DataFrame([
                        {'参数': key, '值': value} for key, value in summary_info.items()
                    ])
                    summary_df.to_excel(writer, sheet_name='汇总信息', index=False)
                
                # 写入统计信息
                total_tasks = len(results)
                successful_tasks = len([r for r in results if r.status == 'success'])
                failed_tasks = len([r for r in results if r.status == 'failed'])
                avg_time = sum(r.processing_time for r in results) / total_tasks if total_tasks > 0 else 0
                
                stats_df = pd.DataFrame([
                    {'统计项': '总任务数', '值': total_tasks},
                    {'统计项': '成功任务数', '值': successful_tasks},
                    {'统计项': '失败任务数', '值': failed_tasks},
                    {'统计项': '成功率(%)', '值': f"{(successful_tasks/total_tasks*100):.1f}" if total_tasks > 0 else "0"},
                    {'统计项': '平均处理时间(秒)', '值': f"{avg_time:.3f}"}
                ])
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
            
            return True
            
        except Exception as e:
            self.logger.error(f"批处理结果Excel导出失败: {str(e)}")
            return False


class ReportGenerator(LoggerMixin):
    """报告生成器"""
    
    def __init__(self):
        super().__init__()
    
    def generate_summary_report(self, 
                              results: List[ExportResult],
                              output_path: Union[str, Path],
                              query_info: Optional[Dict[str, Any]] = None) -> bool:
        """
        生成汇总报告
        
        Args:
            results: 搜索结果
            output_path: 输出路径
            query_info: 查询信息
            
        Returns:
            bool: 生成是否成功
        """
        try:
            # 计算统计信息
            stats = self._calculate_statistics(results)
            
            # 生成报告内容
            report_content = self._generate_report_content(stats, query_info)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"汇总报告已生成: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"生成汇总报告失败: {str(e)}")
            return False
    
    def _calculate_statistics(self, results: List[ExportResult]) -> Dict[str, Any]:
        """计算统计信息"""
        if not results:
            return {}
        
        scores = [result.similarity_score for result in results]
        
        return {
            'total_results': len(results),
            'avg_similarity': sum(scores) / len(scores),
            'max_similarity': max(scores),
            'min_similarity': min(scores),
            'score_distribution': self._get_score_distribution(scores)
        }
    
    def _get_score_distribution(self, scores: List[float]) -> Dict[str, int]:
        """获取分数分布"""
        distribution = {
            '0.9-1.0': 0,
            '0.8-0.9': 0,
            '0.7-0.8': 0,
            '0.6-0.7': 0,
            '0.5-0.6': 0,
            '<0.5': 0
        }
        
        for score in scores:
            if score >= 0.9:
                distribution['0.9-1.0'] += 1
            elif score >= 0.8:
                distribution['0.8-0.9'] += 1
            elif score >= 0.7:
                distribution['0.7-0.8'] += 1
            elif score >= 0.6:
                distribution['0.6-0.7'] += 1
            elif score >= 0.5:
                distribution['0.5-0.6'] += 1
            else:
                distribution['<0.5'] += 1
        
        return distribution
    
    def _generate_report_content(self, 
                               stats: Dict[str, Any], 
                               query_info: Optional[Dict[str, Any]]) -> str:
        """生成报告内容"""
        content = []
        content.append("# 图像搜索结果汇总报告\n")
        content.append(f"**生成时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        # 查询信息
        if query_info:
            content.append("## 查询信息\n")
            for key, value in query_info.items():
                content.append(f"- **{key}:** {value}")
            content.append("")
        
        # 统计信息
        if stats:
            content.append("## 统计信息\n")
            content.append(f"- **总结果数:** {stats.get('total_results', 0)}")
            content.append(f"- **平均相似度:** {stats.get('avg_similarity', 0):.4f}")
            content.append(f"- **最高相似度:** {stats.get('max_similarity', 0):.4f}")
            content.append(f"- **最低相似度:** {stats.get('min_similarity', 0):.4f}")
            content.append("")
            
            # 分数分布
            distribution = stats.get('score_distribution', {})
            if distribution:
                content.append("## 相似度分布\n")
                for range_str, count in distribution.items():
                    content.append(f"- **{range_str}:** {count} 个结果")
                content.append("")
        
        return "\n".join(content)