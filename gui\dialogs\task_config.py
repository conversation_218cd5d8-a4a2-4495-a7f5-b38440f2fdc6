"""任务进度对话框配置模块

该模块定义了任务进度对话框的配置类和相关数据结构。
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
from datetime import datetime


@dataclass
class TaskDisplayConfig:
    """任务显示配置"""
    auto_hide_on_complete: bool = True
    update_interval: int = 200  # 毫秒，减少间隔以提高更新频率
    show_details_panel: bool = True
    table_headers: list = None
    
    def __post_init__(self):
        if self.table_headers is None:
            self.table_headers = ["任务名称", "状态", "进度", "开始时间", "耗时"]


@dataclass
class TaskStatusInfo:
    """任务状态信息"""
    status_code: str
    display_text: str
    color: tuple  # RGB颜色值
    
    @classmethod
    def get_status_map(cls) -> Dict[str, 'TaskStatusInfo']:
        """获取状态映射"""
        return {
            "pending": cls("pending", "等待中", (128, 128, 128)),
            "running": cls("running", "运行中", (0, 0, 255)),
            "completed": cls("completed", "已完成", (0, 128, 0)),
            "failed": cls("failed", "失败", (255, 0, 0))
        }


@dataclass
class TaskProgressInfo:
    """任务进度信息"""
    task_id: str
    name: str
    status: str
    progress: float
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error: Optional[str] = None
    result: Optional[Any] = None
    metadata: Optional[Dict[str, Any]] = None
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """获取任务耗时（秒）"""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.now()
        return (end_time - self.started_at).total_seconds()
    
    @property
    def duration_text(self) -> str:
        """获取耗时文本"""
        duration = self.duration_seconds
        if duration is None:
            return "-"
        
        hours = int(duration // 3600)
        minutes = int((duration % 3600) // 60)
        seconds = int(duration % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    @property
    def start_time_text(self) -> str:
        """获取开始时间文本"""
        if not self.started_at:
            return "-"
        return self.started_at.strftime("%H:%M:%S")