#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度对话框辅助模块

该模块提供进度对话框的辅助功能。
"""

from PyQt6.QtWidgets import QWidget, QProgressDialog


class ProgressDialogHelper:
    """进度对话框辅助类"""
    
    @staticmethod
    def create_progress_dialog(parent: QWidget, title: str, label_text: str,
                             minimum: int = 0, maximum: int = 100,
                             modal: bool = True) -> QProgressDialog:
        """创建进度对话框
        
        Args:
            parent: 父控件
            title: 对话框标题
            label_text: 标签文本
            minimum: 最小值
            maximum: 最大值
            modal: 是否模态
            
        Returns:
            QProgressDialog: 进度对话框
        """
        progress = QProgressDialog(label_text, "取消", minimum, maximum, parent)
        progress.setWindowTitle(title)
        progress.setModal(modal)
        progress.setAutoClose(True)
        progress.setAutoReset(True)
        
        return progress
    
    @staticmethod
    def create_indeterminate_progress(parent: QWidget, title: str, 
                                    label_text: str) -> QProgressDialog:
        """创建不确定进度对话框
        
        Args:
            parent: 父控件
            title: 对话框标题
            label_text: 标签文本
            
        Returns:
            QProgressDialog: 进度对话框
        """
        progress = QProgressDialog(label_text, "取消", 0, 0, parent)
        progress.setWindowTitle(title)
        progress.setModal(True)
        
        return progress