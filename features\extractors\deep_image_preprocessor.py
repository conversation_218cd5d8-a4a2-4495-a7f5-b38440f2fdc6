#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习图像预处理器

负责图像的预处理和变换操作。
"""

import logging
import torch
import numpy as np
from PIL import Image
from torchvision import transforms
from typing import Optional, List, Union

from ..utils.image_utils import preprocess_image, create_default_transforms

logger = logging.getLogger(__name__)


class DeepImagePreprocessor:
    """深度学习图像预处理器"""
    
    def __init__(self, input_size: int = 224, 
                 mean: Optional[List[float]] = None,
                 std: Optional[List[float]] = None,
                 device: str = 'cpu'):
        """初始化图像预处理器
        
        Args:
            input_size: 输入图像尺寸
            mean: 归一化均值
            std: 归一化标准差
            device: 计算设备
        """
        self.input_size = input_size
        self.mean = mean or [0.485, 0.456, 0.406]
        self.std = std or [0.229, 0.224, 0.225]
        self.device = device
        self.transforms = None
        
        self._setup_transforms()
        logger.debug(f"初始化图像预处理器，输入尺寸: {input_size}")
    
    def _setup_transforms(self):
        """设置图像变换"""
        try:
            # 确保input_size是整数
            if isinstance(self.input_size, (list, tuple)):
                input_size = self.input_size[0]
            else:
                input_size = self.input_size
            
            self.transforms = transforms.Compose([
                transforms.Resize(int(input_size * 1.14)),
                transforms.CenterCrop(input_size),
                transforms.ToTensor(),
                transforms.Normalize(mean=self.mean, std=self.std)
            ])
            
            logger.debug(f"图像变换设置完成，输入尺寸: {input_size}")
            
        except Exception as e:
            logger.error(f"设置图像变换失败: {str(e)}")
            # 使用默认变换
            try:
                self.transforms = create_default_transforms(self.input_size)
                logger.info("使用默认图像变换")
            except Exception as fallback_e:
                logger.error(f"创建默认变换也失败: {fallback_e}")
                raise RuntimeError(f"无法设置图像变换: {str(e)}")
    
    def preprocess_single_image(self, image: Image.Image) -> torch.Tensor:
        """预处理单个图像
        
        Args:
            image: 输入图像
            
        Returns:
            torch.Tensor: 预处理后的张量
        """
        try:
            # 验证输入图像
            if image is None:
                raise ValueError("输入图像为空")
            
            # 转换图像格式
            if image.mode not in ['RGB', 'L']:
                try:
                    image = image.convert('RGB')
                except Exception as e:
                    raise ValueError(f"无法转换图像格式: {e}")
            
            # 应用变换
            if self.transforms:
                try:
                    input_tensor = self.transforms(image)
                except Exception as e:
                    logger.warning(f"图像变换失败，使用默认预处理: {e}")
                    input_tensor = preprocess_image(image, self.input_size)
            else:
                input_tensor = preprocess_image(image, self.input_size)
            
            # 验证tensor
            if input_tensor is None:
                raise RuntimeError("图像预处理失败")
            
            # 添加batch维度
            input_tensor = input_tensor.unsqueeze(0)
            
            return input_tensor
            
        except Exception as e:
            logger.error(f"图像预处理失败: {str(e)}")
            raise
    
    def preprocess_batch_images(self, images: List[Image.Image]) -> torch.Tensor:
        """批量预处理图像
        
        Args:
            images: 图像列表
            
        Returns:
            torch.Tensor: 批量预处理后的张量
        """
        try:
            if not images:
                raise ValueError("图像列表为空")
            
            processed_tensors = []
            
            for i, image in enumerate(images):
                try:
                    # 预处理单个图像（不添加batch维度）
                    if image is None:
                        logger.warning(f"第 {i} 个图像为空，跳过")
                        continue
                    
                    # 转换图像格式
                    if image.mode not in ['RGB', 'L']:
                        image = image.convert('RGB')
                    
                    # 应用变换
                    if self.transforms:
                        tensor = self.transforms(image)
                    else:
                        tensor = preprocess_image(image, self.input_size)
                    
                    if tensor is not None:
                        processed_tensors.append(tensor)
                    else:
                        logger.warning(f"第 {i} 个图像预处理失败，跳过")
                        
                except Exception as e:
                    logger.warning(f"第 {i} 个图像预处理失败: {str(e)}，跳过")
                    continue
            
            if not processed_tensors:
                raise RuntimeError("没有成功预处理的图像")
            
            # 堆叠成批量张量
            batch_tensor = torch.stack(processed_tensors)
            
            logger.debug(f"批量预处理完成，处理了 {len(processed_tensors)}/{len(images)} 个图像")
            
            return batch_tensor
            
        except Exception as e:
            logger.error(f"批量图像预处理失败: {str(e)}")
            raise
    
    def move_to_device(self, tensor: torch.Tensor, target_device: Optional[str] = None) -> torch.Tensor:
        """将张量移动到指定设备
        
        Args:
            tensor: 输入张量
            target_device: 目标设备，如果为None则使用默认设备
            
        Returns:
            torch.Tensor: 移动后的张量
        """
        try:
            device = target_device or self.device
            
            # 安全地移动到设备
            try:
                tensor = tensor.to(device)
                return tensor
            except Exception as e:
                logger.warning(f"无法将tensor移动到设备 {device}: {e}")
                
                # 如果目标设备是GPU但失败了，尝试使用CPU
                if device != 'cpu':
                    logger.info("尝试使用CPU设备")
                    self.device = 'cpu'
                    tensor = tensor.to('cpu')
                    return tensor
                else:
                    raise
                    
        except Exception as e:
            logger.error(f"移动张量到设备失败: {str(e)}")
            raise
    
    def validate_tensor(self, tensor: torch.Tensor) -> bool:
        """验证张量的有效性
        
        Args:
            tensor: 输入张量
            
        Returns:
            bool: 张量是否有效
        """
        try:
            if tensor is None:
                return False
            
            if not isinstance(tensor, torch.Tensor):
                return False
            
            if tensor.numel() == 0:
                return False
            
            # 检查是否包含异常值
            if torch.isnan(tensor).any() or torch.isinf(tensor).any():
                logger.warning("张量包含NaN或无穷大值")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证张量失败: {str(e)}")
            return False
    
    def update_config(self, input_size: Optional[int] = None,
                     mean: Optional[List[float]] = None,
                     std: Optional[List[float]] = None):
        """更新预处理配置
        
        Args:
            input_size: 新的输入尺寸
            mean: 新的归一化均值
            std: 新的归一化标准差
        """
        try:
            updated = False
            
            if input_size is not None and input_size != self.input_size:
                self.input_size = input_size
                updated = True
            
            if mean is not None and mean != self.mean:
                self.mean = mean
                updated = True
            
            if std is not None and std != self.std:
                self.std = std
                updated = True
            
            if updated:
                self._setup_transforms()
                logger.info("预处理配置已更新")
            
        except Exception as e:
            logger.error(f"更新预处理配置失败: {str(e)}")
            raise
    
    def get_config(self) -> dict:
        """获取当前配置
        
        Returns:
            dict: 当前配置信息
        """
        return {
            'input_size': self.input_size,
            'mean': self.mean,
            'std': self.std,
            'device': self.device
        }