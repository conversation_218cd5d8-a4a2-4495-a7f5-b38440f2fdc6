#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU优化测试脚本

测试GPU优化功能是否正常工作
"""

import os
import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_gpu_availability():
    """测试GPU可用性"""
    logger.info("=== 测试GPU可用性 ===")
    
    try:
        from utils.gpu_utils import get_gpu_manager
        
        gpu_manager = get_gpu_manager()
        
        if gpu_manager.is_available():
            logger.info("✓ GPU可用")
            
            # 获取GPU信息
            gpu_count = gpu_manager.get_device_count()
            logger.info(f"GPU数量: {gpu_count}")
            
            for i in range(gpu_count):
                gpu_info = gpu_manager.get_gpu_info(i)
                logger.info(f"GPU {i}: {gpu_info.name} (显存: {gpu_info.total_memory_mb:.0f}MB)")
            
            # 测试最佳设备选择
            best_device = gpu_manager.get_best_device_id()
            logger.info(f"最佳GPU设备: {best_device}")
            
            # 测试批处理大小优化
            optimal_batch_size = gpu_manager.get_optimal_batch_size(
                base_batch_size=32,
                memory_per_sample_mb=15.0
            )
            logger.info(f"优化批处理大小: 32 -> {optimal_batch_size}")
            
            return True
        else:
            logger.warning("✗ GPU不可用，将使用CPU")
            return False
            
    except Exception as e:
        logger.error(f"GPU测试失败: {e}")
        return False

def test_feature_extractor_gpu():
    """测试特征提取器GPU配置"""
    logger.info("=== 测试特征提取器GPU配置 ===")
    
    try:
        from features.config.feature_config import FeatureExtractorConfig
        from features.core.feature_extractor import FeatureExtractor
        
        # 创建GPU优化配置
        config = FeatureExtractorConfig(
            model_name="resnet50",
            use_gpu=True,
            batch_size=32,
            use_mixed_precision=True,
            optimize_for_gpu=True,
            gpu_memory_fraction=0.9
        )
        
        logger.info(f"初始批处理大小: {config.batch_size}")
        
        # 创建特征提取器（会自动优化GPU设置）
        extractor = FeatureExtractor(config)
        
        logger.info(f"优化后批处理大小: {config.batch_size}")
        logger.info(f"GPU配置: 使用GPU={config.use_gpu}, 混合精度={config.use_mixed_precision}")
        
        return True
        
    except Exception as e:
        logger.error(f"特征提取器GPU配置测试失败: {e}")
        return False

def test_batch_processor_optimization():
    """测试批处理器优化"""
    logger.info("=== 测试批处理器优化 ===")
    
    try:
        from features.config.feature_config import FeatureExtractorConfig
        from features.core.feature_extractor import FeatureExtractor
        from features.batch.batch_processor import BatchProcessor
        from features.storage.feature_storage import FeatureStorage
        
        # 创建配置
        config = FeatureExtractorConfig(
            model_name="resnet50",
            use_gpu=True,
            optimize_for_gpu=True
        )
        
        # 创建组件
        extractor = FeatureExtractor(config)

        # 创建一个模拟的fabric_repository
        class MockFabricRepository:
            def __init__(self):
                self.data_dir = Path("data")

        storage = FeatureStorage(MockFabricRepository())
        
        # 创建批处理器（会自动优化工作线程数）
        processor = BatchProcessor(
            feature_extractor=extractor,
            feature_storage=storage,
            max_workers=8  # 基础工作线程数
        )
        
        logger.info(f"优化后工作线程数: {processor.max_workers}")
        
        return True
        
    except Exception as e:
        logger.error(f"批处理器优化测试失败: {e}")
        return False

def test_config_optimization():
    """测试配置优化"""
    logger.info("=== 测试配置优化 ===")
    
    try:
        import yaml
        
        # 读取配置文件
        config_path = project_root / "config.yaml"
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查GPU优化配置
            feature_config = config.get('feature_extraction', {})
            performance_config = config.get('performance', {})
            search_config = config.get('search', {})
            
            logger.info("配置文件GPU优化设置:")
            logger.info(f"  特征提取批处理大小: {feature_config.get('batch_size', 'N/A')}")
            logger.info(f"  GPU内存使用比例: {feature_config.get('gpu_memory_fraction', 'N/A')}")
            logger.info(f"  混合精度: {feature_config.get('use_mixed_precision', 'N/A')}")
            logger.info(f"  工作线程数: {feature_config.get('num_workers', 'N/A')}")
            logger.info(f"  性能GPU内存比例: {performance_config.get('gpu_memory_fraction', 'N/A')}")
            logger.info(f"  搜索使用GPU: {search_config.get('use_gpu', 'N/A')}")
            
            return True
        else:
            logger.warning("配置文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"配置优化测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始GPU优化测试")
    
    results = []
    
    # 运行测试
    results.append(("GPU可用性", test_gpu_availability()))
    results.append(("特征提取器GPU配置", test_feature_extractor_gpu()))
    results.append(("批处理器优化", test_batch_processor_optimization()))
    results.append(("配置优化", test_config_optimization()))
    
    # 输出结果
    logger.info("=== 测试结果 ===")
    all_passed = True
    for test_name, passed in results:
        status = "✓ 通过" if passed else "✗ 失败"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        logger.info("🎉 所有GPU优化测试通过！")
        return 0
    else:
        logger.error("❌ 部分GPU优化测试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
