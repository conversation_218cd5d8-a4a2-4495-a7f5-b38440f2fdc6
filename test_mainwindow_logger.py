#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主窗口logger修复

该脚本用于测试主窗口的logger是否正常工作。
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_mainwindow_logger():
    """测试主窗口logger"""
    logger.info("=" * 50)
    logger.info("测试主窗口logger")
    logger.info("=" * 50)
    
    try:
        # 导入必要的模块
        from PyQt6.QtWidgets import QApplication
        from gui.core.main_window import MainWindow
        
        # 创建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口实例
        main_window = MainWindow()
        
        # 测试logger是否存在
        if hasattr(main_window, 'logger'):
            logger.info("✅ 主窗口logger属性存在")
            
            # 测试logger是否可用
            main_window.logger.info("测试主窗口logger - 这是一条测试消息")
            logger.info("✅ 主窗口logger可正常使用")
            
            return True
        else:
            logger.error("❌ 主窗口logger属性不存在")
            return False
            
    except Exception as e:
        logger.error(f"测试主窗口logger失败: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return False


def test_search_config_creation():
    """测试搜索配置创建（模拟场景）"""
    logger.info("=" * 50)
    logger.info("测试搜索配置创建")
    logger.info("=" * 50)
    
    try:
        from gui.search.models import SearchConfig, SearchMode
        
        # 创建搜索配置
        config = SearchConfig(mode=SearchMode.SIMILARITY)
        config.feature_weights = {
            'deep_learning': 0.0,
            'color': 1.0,
            'texture': 0.0,
            'shape': 0.0
        }
        
        logger.info(f"创建的搜索配置: {config}")
        logger.info(f"特征权重: {config.feature_weights}")
        
        # 模拟主窗口中的日志记录
        if hasattr(config, 'feature_weights') and config.feature_weights:
            logger.info(f"搜索配置特征权重: {config.feature_weights}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试搜索配置创建失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始测试主窗口logger修复")
    logger.info("=" * 80)
    
    results = []
    
    # 测试1: 主窗口logger
    results.append(test_mainwindow_logger())
    
    # 测试2: 搜索配置创建
    results.append(test_search_config_creation())
    
    # 总结
    logger.info("=" * 80)
    logger.info("测试总结")
    logger.info("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    logger.info(f"通过测试: {passed}/{total}")
    
    if passed == total:
        logger.info("✅ 所有测试通过 - 主窗口logger修复有效")
    else:
        logger.error("❌ 部分测试失败 - 需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
