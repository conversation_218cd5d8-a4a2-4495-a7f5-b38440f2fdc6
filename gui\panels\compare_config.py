#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像对比面板配置

该模块定义图像对比面板的配置类和枚举。
"""

from enum import Enum
from dataclasses import dataclass
from typing import Optional, Tuple


class SyncMode(Enum):
    """同步模式"""
    NONE = "none"
    ZOOM = "zoom"
    PAN = "pan"
    BOTH = "both"


class ViewMode(Enum):
    """视图模式"""
    SIDE_BY_SIDE = "side_by_side"
    OVERLAY = "overlay"
    SPLIT_VIEW = "split_view"


@dataclass
class CompareConfig:
    """图像对比配置"""
    
    # 同步设置
    sync_zoom: bool = True
    sync_pan: bool = True
    
    # 视图设置
    view_mode: ViewMode = ViewMode.SIDE_BY_SIDE
    auto_fit: bool = True
    
    # 窗口设置
    window_width: int = 1000
    window_height: int = 600
    maximized: bool = True
    
    # 快捷键设置
    enable_shortcuts: bool = True
    
    # 更新间隔
    update_interval: int = 500  # 毫秒
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'sync_zoom': self.sync_zoom,
            'sync_pan': self.sync_pan,
            'view_mode': self.view_mode.value,
            'auto_fit': self.auto_fit,
            'window_width': self.window_width,
            'window_height': self.window_height,
            'maximized': self.maximized,
            'enable_shortcuts': self.enable_shortcuts,
            'update_interval': self.update_interval
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'CompareConfig':
        """从字典创建配置"""
        config = cls()
        config.sync_zoom = data.get('sync_zoom', config.sync_zoom)
        config.sync_pan = data.get('sync_pan', config.sync_pan)
        config.view_mode = ViewMode(data.get('view_mode', config.view_mode.value))
        config.auto_fit = data.get('auto_fit', config.auto_fit)
        config.window_width = data.get('window_width', config.window_width)
        config.window_height = data.get('window_height', config.window_height)
        config.maximized = data.get('maximized', config.maximized)
        config.enable_shortcuts = data.get('enable_shortcuts', config.enable_shortcuts)
        config.update_interval = data.get('update_interval', config.update_interval)
        return config


@dataclass
class ImageInfo:
    """图像信息"""
    path: str
    filename: str = ""
    width: int = 0
    height: int = 0
    size_mb: float = 0.0
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.filename:
            import os
            self.filename = os.path.basename(self.path)
    
    def get_display_text(self) -> str:
        """获取显示文本"""
        if self.width > 0 and self.height > 0:
            return f"{self.filename} ({self.width}x{self.height}, {self.size_mb:.2f}MB)"
        else:
            return f"{self.filename}"