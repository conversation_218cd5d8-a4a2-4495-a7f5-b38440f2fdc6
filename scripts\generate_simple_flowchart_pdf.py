#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成简化版GUI参数传递与搜索策略流程图PDF

该脚本生成清晰简洁的流程图PDF文档
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch, FancyArrowPatch
import numpy as np
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_simple_flowchart_pdf():
    """创建简化版流程图PDF"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(14, 18))
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 18)
    ax.axis('off')
    
    # 定义颜色方案
    colors = {
        'gui': '#e3f2fd',           # 浅蓝色 - GUI层
        'param': '#f3e5f5',         # 浅紫色 - 参数层
        'config': '#e8f5e8',        # 浅绿色 - 配置层
        'engine': '#fff3e0',        # 浅橙色 - 引擎层
        'strategy': '#fce4ec',      # 浅粉色 - 策略层
        'result': '#f1f8e9'         # 浅黄绿色 - 结果层
    }
    
    # 定义节点
    nodes = [
        # 第1层：GUI参数收集
        {'pos': (7, 17), 'text': '用户界面层\nGUI Parameter Collection', 'color': colors['gui'], 'size': (4, 0.8)},
        
        # 第2层：参数组件
        {'pos': (3, 15.5), 'text': '特征权重设置\nFeatureWeightsWidget\nget_weights()', 'color': colors['param'], 'size': (3.5, 1)},
        {'pos': (7, 15.5), 'text': '特征参数设置\nFeatureParamsWidget\nget_params()', 'color': colors['param'], 'size': (3.5, 1)},
        {'pos': (11, 15.5), 'text': '高级搜索设置\nAdvancedSearchWidget\nget_search_params()', 'color': colors['param'], 'size': (3.5, 1)},
        
        # 第3层：参数输出
        {'pos': (3, 13.5), 'text': '权重字典\n{\n  "deep_learning": 0.4,\n  "color": 0.3,\n  "texture": 0.2,\n  "shape": 0.1\n}', 'color': colors['param'], 'size': (3.2, 1.5)},
        {'pos': (7, 13.5), 'text': '特征参数\n{\n  "hist_bins": 32,\n  "lbp_radius": 3,\n  "fourier_desc": 64\n}', 'color': colors['param'], 'size': (3.2, 1.5)},
        {'pos': (11, 13.5), 'text': '搜索参数\n{\n  "max_results": 50,\n  "similarity_threshold": 0.7,\n  "filters": {...}\n}', 'color': colors['param'], 'size': (3.2, 1.5)},
        
        # 第4层：配置聚合
        {'pos': (7, 11.5), 'text': 'SearchPanel.get_search_config()\n参数聚合与配置创建', 'color': colors['config'], 'size': (5, 0.8)},
        {'pos': (7, 10.2), 'text': 'SearchConfig对象\n统一的搜索配置', 'color': colors['config'], 'size': (4, 0.8)},
        
        # 第5层：参数转换
        {'pos': (7, 8.8), 'text': 'SearchHandler._config_to_query()\nGUI配置 → 搜索查询转换', 'color': colors['config'], 'size': (5, 0.8)},
        {'pos': (7, 7.5), 'text': 'SearchQuery对象\n搜索引擎查询格式', 'color': colors['config'], 'size': (4, 0.8)},
        
        # 第6层：搜索引擎
        {'pos': (7, 6.2), 'text': 'SearchEngine.search()\n搜索引擎执行', 'color': colors['engine'], 'size': (4, 0.8)},
        
        # 第7层：策略选择
        {'pos': (7, 5), 'text': '搜索策略选择\nStrategy Selection', 'color': colors['strategy'], 'size': (3.5, 0.6)},
        
        # 第8层：具体策略
        {'pos': (2, 3.5), 'text': 'WeightedSearch\n加权搜索\n∑(wi × si)', 'color': colors['strategy'], 'size': (2.5, 1)},
        {'pos': (5, 3.5), 'text': 'AdaptiveSearch\n自适应搜索\n动态权重调整', 'color': colors['strategy'], 'size': (2.5, 1)},
        {'pos': (8, 3.5), 'text': 'QueryExpansion\n查询扩展\n两轮搜索', 'color': colors['strategy'], 'size': (2.5, 1)},
        {'pos': (11, 3.5), 'text': 'HybridSearch\n混合搜索\n多策略融合', 'color': colors['strategy'], 'size': (2.5, 1)},
        
        # 第9层：相似度计算
        {'pos': (7, 2), 'text': '相似度计算\nFAISS索引 + NumPy计算', 'color': colors['strategy'], 'size': (4, 0.8)},
        
        # 第10层：结果处理
        {'pos': (7, 0.8), 'text': 'SearchResult对象\n结果排序 + 过滤 + 返回GUI', 'color': colors['result'], 'size': (4.5, 0.8)},
    ]
    
    # 绘制节点
    for node in nodes:
        x, y = node['pos']
        width, height = node['size']
        
        # 创建圆角矩形
        box = FancyBboxPatch(
            (x - width/2, y - height/2), width, height,
            boxstyle="round,pad=0.1",
            facecolor=node['color'],
            edgecolor='#333333',
            linewidth=1.5
        )
        ax.add_patch(box)
        
        # 添加文本
        ax.text(x, y, node['text'], 
               ha='center', va='center', 
               fontsize=9, fontweight='bold',
               wrap=True)
    
    # 定义连接线
    connections = [
        # GUI到参数组件
        ((7, 16.6), (3, 16)),
        ((7, 16.6), (7, 16)),
        ((7, 16.6), (11, 16)),
        
        # 参数组件到输出
        ((3, 15), (3, 14.25)),
        ((7, 15), (7, 14.25)),
        ((11, 15), (11, 14.25)),
        
        # 参数输出到配置聚合
        ((3, 12.75), (6, 11.9)),
        ((7, 12.75), (7, 11.9)),
        ((11, 12.75), (8, 11.9)),
        
        # 配置聚合到SearchConfig
        ((7, 11.1), (7, 10.6)),
        
        # SearchConfig到参数转换
        ((7, 9.8), (7, 9.2)),
        
        # 参数转换到SearchQuery
        ((7, 8.4), (7, 7.9)),
        
        # SearchQuery到搜索引擎
        ((7, 7.1), (7, 6.6)),
        
        # 搜索引擎到策略选择
        ((7, 5.8), (7, 5.3)),
        
        # 策略选择到具体策略
        ((6, 4.7), (2, 4)),
        ((6.5, 4.7), (5, 4)),
        ((7.5, 4.7), (8, 4)),
        ((8, 4.7), (11, 4)),
        
        # 具体策略到相似度计算
        ((2, 3), (6, 2.4)),
        ((5, 3), (6.5, 2.4)),
        ((8, 3), (7.5, 2.4)),
        ((11, 3), (8, 2.4)),
        
        # 相似度计算到结果
        ((7, 1.6), (7, 1.2)),
    ]
    
    # 绘制连接线
    for start, end in connections:
        arrow = FancyArrowPatch(start, end,
                              arrowstyle='->', 
                              mutation_scale=20,
                              color='#333333',
                              linewidth=2,
                              alpha=0.8)
        ax.add_patch(arrow)
    
    # 添加标题
    ax.text(7, 17.8, 'Fabric Search - GUI参数传递与搜索策略流程图', 
           ha='center', va='center', fontsize=16, fontweight='bold')
    
    # 添加图例
    legend_elements = [
        mpatches.Patch(color=colors['gui'], label='GUI界面层'),
        mpatches.Patch(color=colors['param'], label='参数收集层'),
        mpatches.Patch(color=colors['config'], label='配置转换层'),
        mpatches.Patch(color=colors['engine'], label='搜索引擎层'),
        mpatches.Patch(color=colors['strategy'], label='策略执行层'),
        mpatches.Patch(color=colors['result'], label='结果处理层')
    ]
    
    ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0.02, 0.98))
    
    # 保存文件
    output_dir = Path('docs/diagrams')
    output_dir.mkdir(exist_ok=True)
    
    # 保存PDF
    pdf_path = output_dir / 'GUI参数传递流程图_简化版.pdf'
    plt.savefig(pdf_path, format='pdf', bbox_inches='tight', dpi=300, 
                facecolor='white', edgecolor='none')
    print(f"PDF流程图已生成: {pdf_path}")
    
    # 保存PNG
    png_path = output_dir / 'GUI参数传递流程图_简化版.png'
    plt.savefig(png_path, format='png', bbox_inches='tight', dpi=300,
                facecolor='white', edgecolor='none')
    print(f"PNG流程图已生成: {png_path}")
    
    plt.close()

if __name__ == "__main__":
    create_simple_flowchart_pdf()
