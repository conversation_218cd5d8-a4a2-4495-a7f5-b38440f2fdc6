#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示设置页面

该模块提供应用程序显示设置的用户界面。
"""

from typing import Dict, Any

from PyQt6.QtWidgets import QVBoxLayout, QHBoxLayout
from PyQt6.QtCore import Qt

from .settings_base import BaseSettingsWidget


class DisplaySettingsWidget(BaseSettingsWidget):
    """显示设置组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # 主题设置
        theme_group = self.widget_factory.create_group_box(title="主题设置", layout_type="form")
        theme_layout = theme_group.layout()
        
        self.theme_combo = self.widget_factory.create_combo_box(
            items=["浅色主题", "深色主题", "自动"]
        )
        theme_layout.addRow("主题:", self.theme_combo)
        
        layout.addWidget(theme_group)
        
        # 字体设置
        font_group = self.widget_factory.create_group_box(title="字体设置", layout_type="form")
        font_layout = font_group.layout()
        
        # 字体族
        self.font_combo = self.widget_factory.create_combo_box(
            items=["Microsoft YaHei", "SimHei", "Arial", "Calibri"]
        )
        font_layout.addRow("字体:", self.font_combo)
        
        # 字体大小
        self.font_size_spin = self.widget_factory.create_spin_box(
            minimum=8, maximum=24, value=10, suffix=" pt"
        )
        font_layout.addRow("字体大小:", self.font_size_spin)
        
        layout.addWidget(font_group)
        
        # 界面设置
        ui_group = self.widget_factory.create_group_box(title="界面设置", layout_type="form")
        ui_layout = ui_group.layout()
        
        # 缩略图大小
        self.thumbnail_slider = self.widget_factory.create_slider(
            minimum=100, maximum=300, value=150,
            orientation=Qt.Orientation.Horizontal,
            tick_position=self.widget_factory.get_slider_tick_position("below")
        )
        self.thumbnail_label = self.widget_factory.create_label("150px")
        
        thumbnail_layout = QHBoxLayout()
        thumbnail_layout.addWidget(self.thumbnail_slider)
        thumbnail_layout.addWidget(self.thumbnail_label)
        
        ui_layout.addRow("缩略图大小:", thumbnail_layout)
        
        layout.addWidget(ui_group)
        
        # 其他设置
        other_group = self.widget_factory.create_group_box(title="其他设置", layout_type="vbox")
        other_layout = other_group.layout()
        
        self.show_tooltips_cb = self.widget_factory.create_checkbox(
            "显示工具提示", checked=True
        )
        other_layout.addWidget(self.show_tooltips_cb)
        
        self.animation_cb = self.widget_factory.create_checkbox(
            "启用动画效果", checked=True
        )
        other_layout.addWidget(self.animation_cb)
        
        layout.addWidget(other_group)
        
        layout.addStretch()
        
        # 连接信号
        self.thumbnail_slider.valueChanged.connect(self.update_thumbnail_label)
    
    def update_thumbnail_label(self, value: int):
        """更新缩略图标签"""
        self.thumbnail_label.setText(f"{value}px")
    
    def get_settings(self) -> Dict[str, Any]:
        """获取设置"""
        theme_map = {
            "浅色主题": "light",
            "深色主题": "dark",
            "自动": "auto"
        }
        
        return {
            "theme": theme_map.get(self.theme_combo.currentText(), "light"),
            "font_family": self.font_combo.currentText(),
            "font_size": self.font_size_spin.value(),
            "thumbnail_size": self.thumbnail_slider.value(),
            "show_tooltips": self.show_tooltips_cb.isChecked(),
            "animation_enabled": self.animation_cb.isChecked()
        }
    
    def set_settings(self, settings: Dict[str, Any]):
        """设置设置"""
        theme_map = {
            "light": "浅色主题",
            "dark": "深色主题",
            "auto": "自动"
        }
        
        theme_text = theme_map.get(settings.get("theme", "light"), "浅色主题")
        self.theme_combo.setCurrentText(theme_text)
        
        self.font_combo.setCurrentText(settings.get("font_family", "Microsoft YaHei"))
        self.font_size_spin.setValue(settings.get("font_size", 10))
        
        thumbnail_size = settings.get("thumbnail_size", 150)
        self.thumbnail_slider.setValue(thumbnail_size)
        self.update_thumbnail_label(thumbnail_size)
        
        self.show_tooltips_cb.setChecked(settings.get("show_tooltips", True))
        self.animation_cb.setChecked(settings.get("animation_enabled", True))