# 开发参考手册

## 目录

1. [系统简介](#1-系统简介)
2. [安装与启动](#2-安装与启动)
3. [用户界面概览](#3-用户界面概览)
4. [基本操作指南](#4-基本操作指南)
5. [高级功能](#5-高级功能)
6. [数据库管理](#6-数据库管理)
7. [结果导出](#7-结果导出)
8. [系统配置](#8-系统配置)
9. [故障排除](#9-故障排除)
10. [常见问题解答](#10-常见问题解答)

## 1. 系统简介

布料图片相似度搜索系统是一款专为纺织行业设计的图像检索工具，能够基于多种特征维度（颜色、纹理、形状和深度学习特征）对布料图片进行相似度搜索。系统采用现代化的图形用户界面，支持GPU加速，提供高效、精准的布料图像检索服务。

### 1.1 主要功能

- **多维度特征搜索**：同时分析颜色、纹理、形状和深度学习特征
- **多种搜索模式**：支持混合搜索、单特征搜索、自适应搜索等多种模式
- **灵活的结果展示**：支持详细视图和网格视图两种展示方式
- **数据库管理**：支持创建、同步和管理特征数据库
- **结果导出**：支持将搜索结果导出为HTML报告
- **GPU加速**：支持GPU加速特征提取和相似度计算

### 1.2 适用场景

- 布料设计参考：寻找相似布料设计作为创作参考
- 产品匹配：为现有布料寻找相似替代品
- 趋势分析：分析相似布料的流行趋势
- 质量控制：检查布料生产的一致性
- 库存管理：快速查找库存中的相似布料

## 2. 安装与启动

### 2.1 系统要求

- **操作系统**：Windows 10/11、macOS 10.14+、Linux（Ubuntu 18.04+）
- **处理器**：Intel Core i5 或同等性能的处理器
- **内存**：最低8GB，推荐16GB或更高
- **存储空间**：最低10GB可用空间
- **Python环境**：Python 3.8或更高版本
- **GPU**（可选）：NVIDIA GPU（支持CUDA）用于加速

### 2.2 安装步骤

1. **安装Python环境**：
   - 从[Python官网](https://www.python.org/downloads/)下载并安装Python 3.8或更高版本
   - 确保将Python添加到系统PATH中

2. **下载系统包**：
   - 从官方渠道下载布料图片相似度搜索系统包
   - 解压到本地目录

3. **安装依赖**：
   - 打开命令提示符或终端
   - 导航到系统目录
   - 运行以下命令安装依赖：
     ```
     pip install -r requirements.txt
     ```

4. **GPU支持**（可选）：
   - 如需启用GPU加速，请确保已安装NVIDIA GPU驱动
   - 安装支持CUDA的PyTorch版本：
     ```
     pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
     ```

### 2.3 启动系统

#### Windows系统

- **普通模式**：双击`启动布料搜索.bat`
- **GPU加速模式**：双击`启动布料搜索(GPU加速).bat`

#### macOS/Linux系统

- **普通模式**：
  ```
  python main.py --mode gui
  ```
- **GPU加速模式**：
  ```
  python main.py --mode gui --use-gpu
  ```

## 3. 用户界面概览

### 3.1 主界面布局

布料图片相似度搜索系统的主界面由以下几个部分组成：

- **菜单栏**：提供文件、编辑、视图、工具和帮助等菜单
- **工具栏**：提供常用操作的快捷按钮
- **搜索面板**：配置搜索参数和选择查询图像
- **结果显示区**：显示搜索结果
- **状态栏**：显示系统状态和操作信息

### 3.2 菜单栏

- **文件菜单**：
  - 打开图像：打开查询图像
  - 导出结果：导出搜索结果
  - 退出：退出程序

- **编辑菜单**：
  - 复制：复制选中的图像或信息
  - 首选项：设置系统首选项

- **视图菜单**：
  - 详细视图：切换到详细视图模式
  - 网格视图：切换到网格视图模式
  - 放大/缩小：调整结果显示大小

- **工具菜单**：
  - 创建数据库：创建新的特征数据库
  - 同步数据库：同步现有特征数据库
  - 模型选择：选择特征提取模型

- **帮助菜单**：
  - 帮助内容：查看帮助文档
  - 关于：查看系统信息

### 3.3 工具栏

工具栏提供以下常用操作的快捷按钮：

- **打开图像**：打开查询图像
- **搜索**：开始搜索
- **停止**：停止当前操作
- **导出**：导出搜索结果
- **视图切换**：切换详细视图/网格视图
- **缩放控制**：放大/缩小结果显示

### 3.4 搜索面板

搜索面板包含以下配置选项：

- **查询图像**：显示当前查询图像
- **图像选择**：选择查询图像的按钮
- **搜索模式**：选择搜索模式（混合搜索、单特征搜索等）
- **结果数量**：设置返回的结果数量
- **特征权重**：调整各特征维度的权重
- **高级选项**：展开/收起高级搜索选项

### 3.5 结果显示区

结果显示区支持两种视图模式：

- **详细视图**：显示每个结果的详细信息，包括图像、相似度分数和特征分析
- **网格视图**：以网格形式显示多个结果图像，便于快速浏览

## 4. 基本操作指南

### 4.1 执行基本搜索

1. **选择查询图像**：
   - 点击工具栏上的"打开图像"按钮
   - 或点击搜索面板上的"选择图像"按钮
   - 在文件对话框中选择要搜索的布料图像

2. **配置搜索参数**：
   - 选择搜索模式（默认为"混合搜索"）
   - 设置返回的结果数量（默认为20）
   - 调整特征权重（可选）

3. **开始搜索**：
   - 点击工具栏上的"搜索"按钮
   - 或点击搜索面板上的"开始搜索"按钮

4. **查看结果**：
   - 搜索完成后，结果将显示在结果显示区
   - 可以切换详细视图/网格视图查看结果
   - 可以点击结果图像查看大图和详细信息

### 4.2 调整搜索参数

#### 4.2.1 调整特征权重

1. 在搜索面板上，找到"特征权重"部分
2. 使用滑块调整各特征维度的权重：
   - 颜色权重：控制颜色特征的重要性
   - 纹理权重：控制纹理特征的重要性
   - 形状权重：控制形状特征的重要性
   - 深度学习权重：控制深度学习特征的重要性
3. 权重调整后，点击"搜索"按钮重新执行搜索

#### 4.2.2 选择搜索模式

1. 在搜索面板上，找到"搜索模式"下拉菜单
2. 选择合适的搜索模式：
   - **混合搜索**：综合考虑所有特征
   - **颜色搜索**：仅考虑颜色特征
   - **纹理搜索**：仅考虑纹理特征
   - **形状搜索**：仅考虑形状特征
   - **深度学习搜索**：仅考虑深度学习特征
   - **自适应搜索**：自动调整特征权重
3. 选择模式后，点击"搜索"按钮执行搜索

### 4.3 结果浏览与排序

#### 4.3.1 切换视图模式

1. 点击工具栏上的"详细视图"或"网格视图"按钮
2. 或在"视图"菜单中选择相应的视图模式

#### 4.3.2 排序结果

1. 在详细视图模式下，点击列标题可按该列排序
2. 可选的排序方式包括：
   - 加权相似度（默认）
   - 颜色相似度
   - 纹理相似度
   - 形状相似度
   - 深度学习相似度
   - 文件名

#### 4.3.3 调整显示大小

1. 使用工具栏上的缩放控制按钮
2. 或在"视图"菜单中选择"放大"或"缩小"
3. 或使用键盘快捷键：Ctrl+加号（放大）、Ctrl+减号（缩小）

## 5. 高级功能

### 5.1 自适应搜索

自适应搜索会根据查询图像的特点自动调整各特征维度的权重，提高搜索准确性。

1. 在搜索面板上，选择"搜索模式"为"自适应搜索"
2. 点击"搜索"按钮执行搜索
3. 系统会分析查询图像的特点，自动调整权重
4. 搜索结果中会显示自动调整后的权重值

### 5.2 查询扩展

查询扩展通过初步搜索结果扩展查询，提高检索准确性。

1. 在搜索面板上，展开"高级选项"
2. 勾选"启用查询扩展"
3. 设置扩展数量（默认为3）
4. 点击"搜索"按钮执行搜索
5. 系统会使用初步搜索结果中的前几个结果扩展查询

### 5.3 用户反馈学习

用户反馈学习通过用户对搜索结果的反馈优化后续搜索。

1. 在搜索结果中，对每个结果可以标记为"相关"或"不相关"
2. 标记完成后，点击"应用反馈"按钮
3. 系统会根据反馈调整搜索参数
4. 点击"搜索"按钮重新执行搜索
5. 新的搜索结果会考虑用户的反馈信息

### 5.4 图像比较

图像比较功能允许用户并排比较查询图像和搜索结果。

1. 在搜索结果中，右键点击某个结果图像
2. 在弹出菜单中选择"与查询图像比较"
3. 系统会打开比较视图，并排显示查询图像和选中的结果图像
4. 在比较视图中可以：
   - 同步缩放两个图像
   - 查看各特征维度的相似度分数
   - 查看特征匹配的可视化表示

## 6. 数据库管理

### 6.1 创建特征数据库

1. 点击"工具"菜单，选择"创建数据库"
2. 或点击工具栏上的"创建数据库"按钮
3. 在弹出的对话框中：
   - 选择图像文件夹
   - 选择数据库保存路径
   - 选择特征提取模型
   - 配置其他参数（可选）
4. 点击"开始创建"按钮
5. 系统会显示进度条，指示创建进度
6. 创建完成后，系统会显示创建结果

### 6.2 同步特征数据库

1. 点击"工具"菜单，选择"同步数据库"
2. 或点击工具栏上的"同步数据库"按钮
3. 在弹出的对话框中：
   - 选择图像文件夹
   - 选择要同步的数据库
4. 点击"开始同步"按钮
5. 系统会扫描图像文件夹，识别新增、删除或修改的图像
6. 系统会显示进度条，指示同步进度
7. 同步完成后，系统会显示同步结果

### 6.3 数据库信息查看

1. 点击"工具"菜单，选择"数据库信息"
2. 在弹出的对话框中，可以查看：
   - 数据库基本信息（创建时间、大小等）
   - 包含的图像数量
   - 使用的特征提取模型
   - 特征维度信息
   - 数据库统计信息

### 6.4 数据库备份与恢复

#### 6.4.1 备份数据库

1. 点击"文件"菜单，选择"备份数据库"
2. 在弹出的对话框中：
   - 选择要备份的数据库
   - 选择备份保存路径
3. 点击"开始备份"按钮
4. 备份完成后，系统会显示备份结果

#### 6.4.2 恢复数据库

1. 点击"文件"菜单，选择"恢复数据库"
2. 在弹出的对话框中：
   - 选择备份文件
   - 选择恢复路径
3. 点击"开始恢复"按钮
4. 恢复完成后，系统会显示恢复结果

## 7. 结果导出

### 7.1 导出为HTML报告

1. 执行搜索并获取结果后
2. 点击"文件"菜单，选择"导出结果"
3. 或点击工具栏上的"导出"按钮
4. 在弹出的对话框中：
   - 选择导出格式为"HTML报告"
   - 选择导出路径
   - 配置导出选项（可选）
5. 点击"导出"按钮
6. 导出完成后，系统会显示导出结果
7. 可以选择"打开报告"查看导出的HTML报告

### 7.2 导出为CSV文件

1. 执行搜索并获取结果后
2. 点击"文件"菜单，选择"导出结果"
3. 在弹出的对话框中：
   - 选择导出格式为"CSV文件"
   - 选择导出路径
   - 选择要导出的字段
4. 点击"导出"按钮
5. 导出完成后，系统会显示导出结果

### 7.3 导出结果图像

1. 执行搜索并获取结果后
2. 点击"文件"菜单，选择"导出结果图像"
3. 在弹出的对话框中：
   - 选择导出路径
   - 选择导出数量
   - 配置导出选项（可选）
4. 点击"导出"按钮
5. 导出完成后，系统会显示导出结果

## 8. 系统配置

### 8.1 模型选择

1. 点击"工具"菜单，选择"模型选择"
2. 在弹出的对话框中，可以选择不同的特征提取模型：
   - ResNet50（默认）
   - VGG16
   - EfficientNet
   - EfficientNetV2
   - ConvNeXt
   - ConvNeXtV2
   - ViT
   - Swin
3. 点击"应用"按钮保存设置
4. 注意：更换模型后，需要使用新模型重新创建或同步数据库

### 8.2 GPU加速设置

1. 点击"编辑"菜单，选择"首选项"
2. 在弹出的对话框中，找到"性能"选项卡
3. 勾选或取消勾选"使用GPU加速"选项
4. 如果系统检测到多个GPU，可以选择要使用的GPU
5. 点击"应用"按钮保存设置
6. 注意：更改GPU设置后，需要重启程序才能生效

### 8.3 界面设置

1. 点击"编辑"菜单，选择"首选项"
2. 在弹出的对话框中，找到"界面"选项卡
3. 可以配置以下选项：
   - 默认视图模式（详细视图/网格视图）
   - 结果显示数量
   - 缩略图大小
   - 界面主题
   - 字体大小
4. 点击"应用"按钮保存设置

### 8.4 搜索设置

1. 点击"编辑"菜单，选择"首选项"
2. 在弹出的对话框中，找到"搜索"选项卡
3. 可以配置以下选项：
   - 默认搜索模式
   - 默认特征权重
   - 批处理大小
   - 相似度阈值
   - 查询扩展设置
4. 点击"应用"按钮保存设置

## 9. 故障排除

### 9.1 常见错误

#### 9.1.1 数据库错误

- **错误**：无法加载数据库
  - **解决方法**：检查数据库文件是否存在且未损坏，必要时重新创建数据库

- **错误**：数据库与当前模型不兼容
  - **解决方法**：使用创建数据库时的模型，或使用当前模型重新创建数据库

#### 9.1.2 图像错误

- **错误**：无法加载图像
  - **解决方法**：检查图像文件是否存在且格式正确，尝试使用其他图像

- **错误**：图像格式不支持
  - **解决方法**：使用支持的图像格式（JPG、PNG、BMP等）

#### 9.1.3 GPU错误

- **错误**：GPU加速不可用
  - **解决方法**：检查GPU驱动是否正确安装，确认PyTorch是否为CUDA版本

- **错误**：GPU内存不足
  - **解决方法**：减小批处理大小，或关闭GPU加速使用CPU计算

### 9.2 性能问题

#### 9.2.1 搜索速度慢

- **问题**：搜索操作耗时过长
  - **解决方法**：
    - 启用GPU加速（如果可用）
    - 减小数据库大小
    - 减小返回结果数量
    - 使用更高效的特征提取模型
    - 关闭不必要的特征维度

#### 9.2.2 内存占用高

- **问题**：程序占用过多内存
  - **解决方法**：
    - 使用分块数据库
    - 减小批处理大小
    - 关闭不必要的特征维度
    - 使用PCA降维减少特征维度

### 9.3 日志查看

系统会自动记录日志，可以通过查看日志文件来诊断问题：

1. 日志文件位于`logs`目录下
2. 日志文件名格式为`app_YYYY-MM-DD.log`
3. 可以使用文本编辑器打开日志文件查看详细信息

## 10. 常见问题解答

### 10.1 功能相关问题

**Q: 如何选择最合适的特征提取模型？**

A: 不同模型在不同类型的布料图像上表现各异。一般来说，ResNet50是一个较好的通用选择，而对于细节丰富的布料图像，VGG16可能表现更好。您可以通过实验比较不同模型的效果，选择最适合您的图像库的模型。

**Q: 如何提高搜索准确性？**

A: 提高搜索准确性的方法包括：
- 调整各特征维度的权重，增加重要特征的权重
- 使用自适应搜索模式，让系统自动调整权重
- 启用查询扩展功能
- 提供用户反馈，帮助系统学习
- 使用更高质量的查询图像
- 确保数据库中的图像质量一致

**Q: 系统支持哪些图像格式？**

A: 系统支持常见的图像格式，包括JPG、JPEG、PNG、BMP、TIFF等。

### 10.2 技术相关问题

**Q: 如何处理大规模图像库？**

A: 对于大规模图像库（10万+图像），建议：
- 使用向量数据库类型
- 启用GPU加速
- 考虑使用PCA降维
- 使用分块数据库减少内存占用
- 适当增加批处理大小提高处理效率

**Q: 如何在没有GPU的情况下提高性能？**

A: 在没有GPU的情况下，可以通过以下方式提高性能：
- 使用更轻量级的特征提取模型（如EfficientNet）
- 减小数据库大小
- 使用PCA降维减少特征维度
- 优化批处理大小
- 关闭不必要的特征维度

**Q: 系统是否支持分布式处理？**

A: 当前版本不支持分布式处理。未来版本计划添加分布式处理支持，以提高大规模数据处理能力。

### 10.3 使用技巧

**Q: 如何快速找到最相似的布料？**

A: 快速找到最相似布料的技巧：
- 使用高质量的查询图像
- 选择合适的搜索模式（通常是混合搜索或自适应搜索）
- 根据布料特点调整特征权重（如颜色明显的布料增加颜色权重）
- 使用详细视图查看相似度分数
- 尝试不同的排序方式找到最相关结果

**Q: 如何有效管理多个数据库？**

A: 有效管理多个数据库的建议：
- 为不同类型的布料创建单独的数据库
- 使用有意义的命名方式
- 定期同步数据库保持更新
- 创建数据库备份
- 记录每个数据库的创建参数（模型、设置等）

**Q: 如何解释搜索结果中的相似度分数？**

A: 相似度分数的解释：
- 分数范围从0到1，1表示完全相似
- 加权相似度是综合考虑所有特征的总分
- 各特征维度的相似度显示在详细视图中
- 通常0.7以上表示高度相似
- 0.5-0.7表示中等相似
- 0.5以下表示相似度较低

---

© 2025 布料图片相似度搜索系统 - 保留所有权利