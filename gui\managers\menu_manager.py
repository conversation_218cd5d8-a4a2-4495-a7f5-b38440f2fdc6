#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菜单管理器

该模块负责管理主窗口的菜单栏和菜单项。
"""

from typing import Optional, Callable
from PyQt6.QtWidgets import QMainWindow, QMenuBar, QMenu
from PyQt6.QtGui import QAction, QKeySequence
from PyQt6.QtCore import QObject, pyqtSignal

from utils.logger_mixin import LoggerMixin
from gui.themes import ThemeType


class MenuManager(QObject, LoggerMixin):
    """菜单管理器"""
    
    # 信号
    openImageRequested = pyqtSignal()
    openFolderRequested = pyqtSignal()
    importDatabaseRequested = pyqtSignal()
    exportResultsRequested = pyqtSignal()
    settingsRequested = pyqtSignal()
    fullscreenToggled = pyqtSignal()
    themeChanged = pyqtSignal(ThemeType)
    modelSelectorRequested = pyqtSignal()
    taskManagerRequested = pyqtSignal()
    toolbarToggled = pyqtSignal()
    rebuildIndexRequested = pyqtSignal()
    clearCacheRequested = pyqtSignal()
    aboutRequested = pyqtSignal()
    
    def __init__(self, main_window: QMainWindow):
        super().__init__()
        self.main_window = main_window
        self.menubar = None
        
        # 菜单引用
        self.file_menu = None
        self.edit_menu = None
        self.view_menu = None
        self.tools_menu = None
        self.help_menu = None
        
        # 动作引用
        self.fullscreen_action = None
        self.light_theme_action = None
        self.dark_theme_action = None
        self.toolbar_action = None
        
        self.setup_menus()
    
    def setup_menus(self):
        """设置菜单"""
        try:
            self.menubar = self.main_window.menuBar()
            
            self._create_file_menu()
            self._create_edit_menu()
            self._create_view_menu()
            self._create_tools_menu()
            self._create_help_menu()
            
            self.logger.info("菜单设置完成")
            
        except Exception as e:
            self.logger.error(f"设置菜单失败: {e}")
    
    def _create_file_menu(self):
        """创建文件菜单"""
        self.file_menu = self.menubar.addMenu("文件(&F)")
        
        # 打开图像
        open_action = QAction("打开图像(&O)", self.main_window)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.setStatusTip("打开图像文件")
        open_action.triggered.connect(self.openImageRequested.emit)
        self.file_menu.addAction(open_action)
        
        # 打开文件夹
        open_folder_action = QAction("打开文件夹(&F)", self.main_window)
        open_folder_action.setShortcut(QKeySequence("Ctrl+Shift+O"))
        open_folder_action.setStatusTip("打开图像文件夹")
        open_folder_action.triggered.connect(self.openFolderRequested.emit)
        self.file_menu.addAction(open_folder_action)
        
        self.file_menu.addSeparator()
        
        # 导入数据库
        import_action = QAction("导入数据库(&I)", self.main_window)
        import_action.setStatusTip("导入图像数据库")
        import_action.triggered.connect(self.importDatabaseRequested.emit)
        self.file_menu.addAction(import_action)
        
        # 导出结果
        export_action = QAction("导出结果(&E)", self.main_window)
        export_action.setStatusTip("导出搜索结果")
        export_action.triggered.connect(self.exportResultsRequested.emit)
        self.file_menu.addAction(export_action)
        
        self.file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self.main_window)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.main_window.close)
        self.file_menu.addAction(exit_action)
    
    def _create_edit_menu(self):
        """创建编辑菜单"""
        self.edit_menu = self.menubar.addMenu("编辑(&E)")
        
        # 设置
        settings_action = QAction("设置(&S)", self.main_window)
        settings_action.setShortcut(QKeySequence.StandardKey.Preferences)
        settings_action.setStatusTip("打开设置对话框")
        settings_action.triggered.connect(self.settingsRequested.emit)
        self.edit_menu.addAction(settings_action)
    
    def _create_view_menu(self):
        """创建视图菜单"""
        self.view_menu = self.menubar.addMenu("视图(&V)")
        
        # 全屏
        self.fullscreen_action = QAction("全屏(&F)", self.main_window)
        self.fullscreen_action.setShortcut(QKeySequence.StandardKey.FullScreen)
        self.fullscreen_action.setCheckable(True)
        self.fullscreen_action.setStatusTip("切换全屏模式")
        self.fullscreen_action.triggered.connect(self.fullscreenToggled.emit)
        self.view_menu.addAction(self.fullscreen_action)
        
        self.view_menu.addSeparator()
        
        # 主题切换
        theme_menu = self.view_menu.addMenu("主题(&T)")
        
        self.light_theme_action = QAction("浅色主题", self.main_window)
        self.light_theme_action.setCheckable(True)
        self.light_theme_action.triggered.connect(
            lambda: self.themeChanged.emit(ThemeType.LIGHT)
        )
        theme_menu.addAction(self.light_theme_action)
        
        self.dark_theme_action = QAction("深色主题", self.main_window)
        self.dark_theme_action.setCheckable(True)
        self.dark_theme_action.triggered.connect(
            lambda: self.themeChanged.emit(ThemeType.DARK)
        )
        theme_menu.addAction(self.dark_theme_action)
    
    def _create_tools_menu(self):
        """创建工具菜单"""
        self.tools_menu = self.menubar.addMenu("工具(&T)")
        
        # 选择模型
        select_model_action = QAction("选择特征提取模型(&M)", self.main_window)
        select_model_action.setStatusTip("选择用于特征提取的深度学习模型")
        select_model_action.triggered.connect(self.modelSelectorRequested.emit)
        self.tools_menu.addAction(select_model_action)
        
        # 任务管理
        task_manager_action = QAction("任务管理器(&T)", self.main_window)
        task_manager_action.setStatusTip("查看和管理后台任务")
        task_manager_action.triggered.connect(self.taskManagerRequested.emit)
        self.tools_menu.addAction(task_manager_action)
        
        self.tools_menu.addSeparator()
        
        # 工具栏开关
        self.toolbar_action = QAction("显示工具栏(&B)", self.main_window)
        self.toolbar_action.setCheckable(True)
        self.toolbar_action.setChecked(True)
        self.toolbar_action.setStatusTip("显示或隐藏工具栏")
        self.toolbar_action.triggered.connect(self.toolbarToggled.emit)
        self.tools_menu.addAction(self.toolbar_action)
        
        self.tools_menu.addSeparator()
        
        # 重建索引
        rebuild_index_action = QAction("重建索引(&R)", self.main_window)
        rebuild_index_action.setStatusTip("重建搜索索引")
        rebuild_index_action.triggered.connect(self.rebuildIndexRequested.emit)
        self.tools_menu.addAction(rebuild_index_action)
        
        # 清除缓存
        clear_cache_action = QAction("清除缓存(&C)", self.main_window)
        clear_cache_action.setStatusTip("清除所有缓存")
        clear_cache_action.triggered.connect(self.clearCacheRequested.emit)
        self.tools_menu.addAction(clear_cache_action)
    
    def _create_help_menu(self):
        """创建帮助菜单"""
        self.help_menu = self.menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self.main_window)
        about_action.setStatusTip("关于 Fabric Search")
        about_action.triggered.connect(self.aboutRequested.emit)
        self.help_menu.addAction(about_action)
    
    def update_fullscreen_state(self, is_fullscreen: bool):
        """更新全屏状态
        
        Args:
            is_fullscreen: 是否全屏
        """
        if self.fullscreen_action:
            self.fullscreen_action.setChecked(is_fullscreen)
    
    def update_theme_state(self, theme_type: ThemeType):
        """更新主题状态
        
        Args:
            theme_type: 主题类型
        """
        if self.light_theme_action and self.dark_theme_action:
            self.light_theme_action.setChecked(theme_type == ThemeType.LIGHT)
            self.dark_theme_action.setChecked(theme_type == ThemeType.DARK)
    
    def update_toolbar_state(self, is_visible: bool):
        """更新工具栏状态
        
        Args:
            is_visible: 工具栏是否可见
        """
        if self.toolbar_action:
            self.toolbar_action.setChecked(is_visible)