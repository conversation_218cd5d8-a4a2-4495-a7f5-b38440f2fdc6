#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度特征提取器模块

该模块实现基于深度学习模型的特征提取。
重构后使用模块化组件。
"""

import logging
from typing import Optional, Dict, Any, List
import numpy as np
from PIL import Image

from ..config.feature_config import FeatureExtractorConfig
from ..data_models.feature_models import FeatureExtractionResult
from .deep_extractor_core import DeepFeatureExtractor as CoreExtractor

logger = logging.getLogger(__name__)


class DeepFeatureExtractor:
    """深度特征提取器 - 重构后的版本"""
    
    def __init__(self, config: FeatureExtractorConfig):
        """初始化深度特征提取器
        
        Args:
            config: 特征提取器配置
        """
        self.config = config
        self.core_extractor = CoreExtractor(config)
        
        logger.info(f"深度特征提取器初始化完成，模型: {config.model_name}")
    
    def extract_features_from_image(self, image: Image.Image) -> np.ndarray:
        """从单个图像提取特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 特征向量
        """
        return self.core_extractor.extract_features_from_image(image)
    
    def extract_features_batch(self, images: List[Image.Image]) -> List[np.ndarray]:
        """批量提取特征
        
        Args:
            images: 图像列表
            
        Returns:
            List[np.ndarray]: 特征向量列表
        """
        return self.core_extractor.extract_features_batch(images)
    
    def extract_features(self, input_data) -> FeatureExtractionResult:
        """提取特征的统一接口
        
        Args:
            input_data: 输入数据（图像或图像列表）
            
        Returns:
            FeatureExtractionResult: 特征提取结果
        """
        return self.core_extractor.extract_features(input_data)
    
    def _process_features(self, features):
        """处理特征（保持向后兼容性）"""
        # 委托给核心提取器的内部方法
        if hasattr(self.core_extractor, '_process_model_output'):
            return self.core_extractor._process_model_output(features)
        return features
    
    def _normalize_features(self, features: np.ndarray) -> np.ndarray:
        """归一化特征（保持向后兼容性）"""
        # 使用特征处理器进行归一化
        return self.core_extractor.feature_processor.process_features(features)
    
    def change_model(self, model_name: str) -> bool:
        """更换模型
        
        Args:
            model_name: 新模型名称
            
        Returns:
            bool: 是否成功
        """
        return self.core_extractor.change_model(model_name)
    
    def get_feature_dimension(self) -> int:
        """获取特征维度"""
        return self.core_extractor.get_feature_dimension()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return self.core_extractor.get_model_info()
    
    def cleanup(self):
        """清理资源"""
        self.core_extractor.cleanup()
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except Exception:
            pass