#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数学工具模块

提供各种数学计算功能，包括：
- 向量归一化
- 相似度计算
- 距离计算
- 统计计算
- 加权平均
"""

import numpy as np
from typing import List, Tuple, Union, Optional
from scipy.spatial.distance import cosine, euclidean


def normalize_vector(vector: np.ndarray, method: str = 'l2') -> np.ndarray:
    """
    向量归一化
    
    Args:
        vector: 输入向量
        method: 归一化方法 ('l2', 'l1', 'max')
        
    Returns:
        np.ndarray: 归一化后的向量
    """
    if vector.size == 0:
        return vector
        
    if method == 'l2':
        norm = np.linalg.norm(vector)
        if norm == 0:
            return vector
        return vector / norm
    elif method == 'l1':
        norm = np.sum(np.abs(vector))
        if norm == 0:
            return vector
        return vector / norm
    elif method == 'max':
        max_val = np.max(np.abs(vector))
        if max_val == 0:
            return vector
        return vector / max_val
    else:
        raise ValueError(f"不支持的归一化方法: {method}")


def cosine_similarity(vector1: np.ndarray, vector2: np.ndarray) -> float:
    """
    计算余弦相似度
    
    Args:
        vector1: 第一个向量
        vector2: 第二个向量
        
    Returns:
        float: 余弦相似度 (0-1之间，1表示完全相似)
    """
    if vector1.size == 0 or vector2.size == 0:
        return 0.0
        
    # 确保向量维度相同
    if vector1.shape != vector2.shape:
        raise ValueError("向量维度不匹配")
        
    # 计算余弦距离并转换为相似度
    try:
        cos_dist = cosine(vector1.flatten(), vector2.flatten())
        # cosine函数返回距离，需要转换为相似度
        similarity = 1 - cos_dist
        return max(0.0, min(1.0, similarity))  # 确保在[0,1]范围内
    except ValueError:
        # 处理零向量的情况
        return 0.0


def euclidean_distance(vector1: np.ndarray, vector2: np.ndarray) -> float:
    """
    计算欧几里得距离
    
    Args:
        vector1: 第一个向量
        vector2: 第二个向量
        
    Returns:
        float: 欧几里得距离
    """
    if vector1.size == 0 or vector2.size == 0:
        return float('inf')
        
    # 确保向量维度相同
    if vector1.shape != vector2.shape:
        raise ValueError("向量维度不匹配")
        
    return euclidean(vector1.flatten(), vector2.flatten())


def manhattan_distance(vector1: np.ndarray, vector2: np.ndarray) -> float:
    """
    计算曼哈顿距离
    
    Args:
        vector1: 第一个向量
        vector2: 第二个向量
        
    Returns:
        float: 曼哈顿距离
    """
    if vector1.size == 0 or vector2.size == 0:
        return float('inf')
        
    # 确保向量维度相同
    if vector1.shape != vector2.shape:
        raise ValueError("向量维度不匹配")
        
    return np.sum(np.abs(vector1 - vector2))


def calculate_statistics(data: np.ndarray) -> dict:
    """
    计算数据的统计信息
    
    Args:
        data: 输入数据
        
    Returns:
        dict: 包含统计信息的字典
    """
    if data.size == 0:
        return {
            'mean': 0.0,
            'std': 0.0,
            'min': 0.0,
            'max': 0.0,
            'median': 0.0,
            'count': 0
        }
        
    return {
        'mean': float(np.mean(data)),
        'std': float(np.std(data)),
        'min': float(np.min(data)),
        'max': float(np.max(data)),
        'median': float(np.median(data)),
        'count': int(data.size)
    }


def weighted_average(values: List[float], weights: List[float]) -> float:
    """
    计算加权平均值
    
    Args:
        values: 数值列表
        weights: 权重列表
        
    Returns:
        float: 加权平均值
    """
    if len(values) != len(weights):
        raise ValueError("数值和权重列表长度不匹配")
        
    if len(values) == 0:
        return 0.0
        
    values_array = np.array(values)
    weights_array = np.array(weights)
    
    # 检查权重和是否为零
    weight_sum = np.sum(weights_array)
    if weight_sum == 0:
        return 0.0
        
    return float(np.sum(values_array * weights_array) / weight_sum)


def softmax(x: np.ndarray, temperature: float = 1.0) -> np.ndarray:
    """
    计算softmax函数
    
    Args:
        x: 输入数组
        temperature: 温度参数，控制分布的尖锐程度
        
    Returns:
        np.ndarray: softmax结果
    """
    if x.size == 0:
        return x
        
    # 应用温度缩放
    x_scaled = x / temperature
    
    # 为了数值稳定性，减去最大值
    x_shifted = x_scaled - np.max(x_scaled)
    
    # 计算softmax
    exp_x = np.exp(x_shifted)
    return exp_x / np.sum(exp_x)


def pearson_correlation(x: np.ndarray, y: np.ndarray) -> float:
    """
    计算皮尔逊相关系数
    
    Args:
        x: 第一个数组
        y: 第二个数组
        
    Returns:
        float: 皮尔逊相关系数
    """
    if x.size == 0 or y.size == 0:
        return 0.0
        
    if x.shape != y.shape:
        raise ValueError("数组维度不匹配")
        
    # 计算相关系数矩阵
    corr_matrix = np.corrcoef(x.flatten(), y.flatten())
    
    # 返回相关系数
    correlation = corr_matrix[0, 1]
    
    # 处理NaN情况
    if np.isnan(correlation):
        return 0.0
        
    return float(correlation)


def normalize_scores(scores: List[float], method: str = 'minmax') -> List[float]:
    """
    归一化分数
    
    Args:
        scores: 分数列表
        method: 归一化方法 ('minmax', 'zscore')
        
    Returns:
        List[float]: 归一化后的分数
    """
    if not scores:
        return []
        
    scores_array = np.array(scores)
    
    if method == 'minmax':
        min_score = np.min(scores_array)
        max_score = np.max(scores_array)
        
        if min_score == max_score:
            return [1.0] * len(scores)
            
        normalized = (scores_array - min_score) / (max_score - min_score)
        
    elif method == 'zscore':
        mean_score = np.mean(scores_array)
        std_score = np.std(scores_array)
        
        if std_score == 0:
            return [0.0] * len(scores)
            
        normalized = (scores_array - mean_score) / std_score
        
    else:
        raise ValueError(f"不支持的归一化方法: {method}")
        
    return normalized.tolist()


def calculate_entropy(probabilities: np.ndarray) -> float:
    """
    计算信息熵
    
    Args:
        probabilities: 概率分布
        
    Returns:
        float: 信息熵
    """
    if probabilities.size == 0:
        return 0.0
        
    # 过滤掉零概率
    probs = probabilities[probabilities > 0]
    
    if probs.size == 0:
        return 0.0
        
    return float(-np.sum(probs * np.log2(probs)))


def moving_average(data: List[float], window_size: int) -> List[float]:
    """
    计算移动平均
    
    Args:
        data: 数据列表
        window_size: 窗口大小
        
    Returns:
        List[float]: 移动平均结果
    """
    if not data or window_size <= 0:
        return []
        
    if window_size > len(data):
        window_size = len(data)
        
    result = []
    for i in range(len(data) - window_size + 1):
        window = data[i:i + window_size]
        avg = sum(window) / len(window)
        result.append(avg)
        
    return result