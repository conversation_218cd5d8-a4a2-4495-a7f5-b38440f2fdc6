#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征提取模块

该模块提供图像特征提取和相似度计算功能。
重构后的模块化架构，支持深度学习和传统特征提取。
"""

__version__ = "1.0.0"
__author__ = "Fabric Search Team"

# 导入主要类
from .feature_extractor import (
    FeatureExtractor,
    BatchExtractor,
    ExtractorFactory,
    InputValidator,
    ImageValidator,
    CacheManager,
    FeatureExtractorConfig,
    TraditionalFeatureConfig,
    FeatureExtractionResult,
    create_feature_extractor
)

# 导入管理器和搜索处理器
from .core.manager import FeatureManager
from .core.search import SearchHandler as SimilarityCalculator

# 导入批处理器
from .batch.batch_processor import BatchProcessor

# 导入存储相关
from .storage.feature_storage import FeatureStorage

# 导入搜索引擎
from .search.search_engine import SearchEngine

__all__ = [
    # 核心类
    'FeatureExtractor',
    'BatchExtractor',
    'ExtractorFactory',
    
    # 验证器
    'InputValidator',
    'ImageValidator',
    
    # 缓存管理
    'CacheManager',
    
    # 配置类
    'FeatureExtractorConfig',
    'TraditionalFeatureConfig',
    
    # 数据模型
    'FeatureExtractionResult',
    
    # 管理器和处理器
    'FeatureManager',
    'SimilarityCalculator',
    'BatchProcessor',
    
    # 存储和搜索
    'FeatureStorage',
    'SearchEngine',
    
    # 便捷函数
    'create_feature_extractor'
]