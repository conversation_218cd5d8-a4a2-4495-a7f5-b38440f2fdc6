#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动画辅助模块

该模块提供GUI动画效果的辅助功能。
"""

from PyQt6.QtWidgets import QWidget
from PyQt6.QtCore import QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QGuiApplication


class AnimationHelper:
    """动画辅助类"""
    
    @staticmethod
    def fade_in(widget: QWidget, duration: int = 300):
        """淡入动画
        
        Args:
            widget: 目标控件
            duration: 动画时长(毫秒)
        """
        widget.setWindowOpacity(0.0)
        widget.show()
        
        animation = QPropertyAnimation(widget, b"windowOpacity")
        animation.setDuration(duration)
        animation.setStartValue(0.0)
        animation.setEndValue(1.0)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        animation.start()
        
        return animation
    
    @staticmethod
    def fade_out(widget: QWidget, duration: int = 300, 
                 hide_on_finish: bool = True):
        """淡出动画
        
        Args:
            widget: 目标控件
            duration: 动画时长(毫秒)
            hide_on_finish: 动画结束后是否隐藏控件
        """
        animation = QPropertyAnimation(widget, b"windowOpacity")
        animation.setDuration(duration)
        animation.setStartValue(1.0)
        animation.setEndValue(0.0)
        animation.setEasingCurve(QEasingCurve.Type.InCubic)
        
        if hide_on_finish:
            animation.finished.connect(widget.hide)
        
        animation.start()
        return animation
    
    @staticmethod
    def slide_in(widget: QWidget, direction: str = "left", 
                 duration: int = 300):
        """滑入动画
        
        Args:
            widget: 目标控件
            direction: 滑入方向 (left, right, up, down)
            duration: 动画时长(毫秒)
        """
        geometry = widget.geometry()
        
        # 设置起始位置
        if direction == "left":
            start_pos = QRect(-geometry.width(), geometry.y(), 
                            geometry.width(), geometry.height())
        elif direction == "right":
            start_pos = QRect(widget.parent().width(), geometry.y(),
                            geometry.width(), geometry.height())
        elif direction == "up":
            start_pos = QRect(geometry.x(), -geometry.height(),
                            geometry.width(), geometry.height())
        else:  # down
            start_pos = QRect(geometry.x(), widget.parent().height(),
                            geometry.width(), geometry.height())
        
        widget.setGeometry(start_pos)
        widget.show()
        
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(geometry)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        animation.start()
        
        return animation