#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
界面配置模块

该模块定义了用户界面的配置类，管理界面的各种设置和参数。
"""

import logging
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass, field
from enum import Enum


class ViewMode(Enum):
    """视图模式枚举"""
    DETAILED = 'detailed'
    GRID = 'grid'


class Theme(Enum):
    """主题枚举"""
    DEFAULT = 'default'
    DARK = 'dark'
    LIGHT = 'light'


class SortOrder(Enum):
    """排序顺序枚举"""
    ASCENDING = 'asc'
    DESCENDING = 'desc'


@dataclass
class UIConfig:
    """界面配置数据类"""
    # 窗口设置
    window_width: int = 1200
    window_height: int = 800
    window_maximized: bool = False
    window_position: Optional[Tuple[int, int]] = None
    
    # 视图设置
    default_view_mode: ViewMode = ViewMode.DETAILED
    thumbnail_size: int = 150
    max_thumbnail_size: int = 300
    min_thumbnail_size: int = 50
    grid_columns: int = 4
    show_image_info: bool = True
    show_similarity_scores: bool = True
    
    # 主题设置
    theme: Theme = Theme.DEFAULT
    font_family: str = 'Arial'
    font_size: int = 10
    icon_size: int = 24
    
    # 搜索面板设置
    search_panel_width: int = 300
    search_panel_collapsed: bool = False
    show_advanced_options: bool = False
    auto_search_on_image_change: bool = False
    
    # 结果显示设置
    default_result_count: int = 20
    max_result_count: int = 100
    auto_refresh_results: bool = True
    show_progress_bar: bool = True
    show_status_messages: bool = True
    
    # 排序设置
    default_sort_column: str = 'similarity'
    default_sort_order: SortOrder = SortOrder.DESCENDING
    
    # 导出设置
    default_export_format: str = 'html'
    export_include_thumbnails: bool = True
    export_thumbnail_size: int = 200
    
    # 性能设置
    lazy_loading: bool = True
    cache_thumbnails: bool = True
    max_cache_size: int = 100
    
    # 快捷键设置
    shortcuts: Dict[str, str] = field(default_factory=lambda: {
        'open_image': 'Ctrl+O',
        'search': 'Ctrl+F',
        'export': 'Ctrl+E',
        'quit': 'Ctrl+Q',
        'toggle_view': 'Ctrl+T',
        'zoom_in': 'Ctrl++',
        'zoom_out': 'Ctrl+-',
        'reset_zoom': 'Ctrl+0'
    })


class UIConfigManager:
    """界面配置管理器"""
    
    def __init__(self):
        """初始化界面配置管理器"""
        self._config = UIConfig()
        self._observers = []
    
    def get_config(self) -> UIConfig:
        """获取界面配置
        
        Returns:
            UIConfig: 界面配置
        """
        return self._config
    
    def update_config(self, **kwargs) -> bool:
        """更新界面配置
        
        Args:
            **kwargs: 配置参数
            
        Returns:
            bool: 更新是否成功
        """
        try:
            for key, value in kwargs.items():
                if hasattr(self._config, key):
                    setattr(self._config, key, value)
                    logging.debug(f"界面配置更新: {key} = {value}")
                else:
                    logging.warning(f"未知的界面配置项: {key}")
            
            # 通知观察者
            self._notify_observers()
            return True
            
        except Exception as e:
            logging.error(f"更新界面配置失败: {e}")
            return False
    
    def reset_to_default(self) -> bool:
        """重置为默认配置
        
        Returns:
            bool: 重置是否成功
        """
        try:
            self._config = UIConfig()
            self._notify_observers()
            logging.info("界面配置已重置为默认值")
            return True
            
        except Exception as e:
            logging.error(f"重置界面配置失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """验证界面配置
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 验证窗口尺寸
            if self._config.window_width < 800 or self._config.window_height < 600:
                logging.warning("窗口尺寸过小，建议至少800x600")
            
            # 验证缩略图尺寸
            if not (self._config.min_thumbnail_size <= 
                   self._config.thumbnail_size <= 
                   self._config.max_thumbnail_size):
                logging.error("缩略图尺寸超出范围")
                return False
            
            # 验证网格列数
            if self._config.grid_columns < 1 or self._config.grid_columns > 10:
                logging.error("网格列数必须在1-10之间")
                return False
            
            # 验证字体大小
            if self._config.font_size < 8 or self._config.font_size > 24:
                logging.warning("字体大小建议在8-24之间")
            
            # 验证结果数量
            if not (1 <= self._config.default_result_count <= 
                   self._config.max_result_count):
                logging.error("默认结果数量超出范围")
                return False
            
            logging.info("界面配置验证通过")
            return True
            
        except Exception as e:
            logging.error(f"验证界面配置失败: {e}")
            return False
    
    def get_window_geometry(self) -> Dict[str, Any]:
        """获取窗口几何信息
        
        Returns:
            Dict[str, Any]: 窗口几何信息
        """
        return {
            'width': self._config.window_width,
            'height': self._config.window_height,
            'maximized': self._config.window_maximized,
            'position': self._config.window_position
        }
    
    def set_window_geometry(self, width: int, height: int, 
                           maximized: bool = False, 
                           position: Optional[Tuple[int, int]] = None) -> bool:
        """设置窗口几何信息
        
        Args:
            width: 窗口宽度
            height: 窗口高度
            maximized: 是否最大化
            position: 窗口位置
            
        Returns:
            bool: 设置是否成功
        """
        return self.update_config(
            window_width=width,
            window_height=height,
            window_maximized=maximized,
            window_position=position
        )
    
    def get_view_settings(self) -> Dict[str, Any]:
        """获取视图设置
        
        Returns:
            Dict[str, Any]: 视图设置
        """
        return {
            'view_mode': self._config.default_view_mode,
            'thumbnail_size': self._config.thumbnail_size,
            'grid_columns': self._config.grid_columns,
            'show_image_info': self._config.show_image_info,
            'show_similarity_scores': self._config.show_similarity_scores
        }
    
    def get_theme_settings(self) -> Dict[str, Any]:
        """获取主题设置
        
        Returns:
            Dict[str, Any]: 主题设置
        """
        return {
            'theme': self._config.theme,
            'font_family': self._config.font_family,
            'font_size': self._config.font_size,
            'icon_size': self._config.icon_size
        }
    
    def get_shortcuts(self) -> Dict[str, str]:
        """获取快捷键设置
        
        Returns:
            Dict[str, str]: 快捷键映射
        """
        return self._config.shortcuts.copy()
    
    def set_shortcut(self, action: str, shortcut: str) -> bool:
        """设置快捷键
        
        Args:
            action: 动作名称
            shortcut: 快捷键
            
        Returns:
            bool: 设置是否成功
        """
        try:
            self._config.shortcuts[action] = shortcut
            self._notify_observers()
            logging.info(f"快捷键设置: {action} = {shortcut}")
            return True
            
        except Exception as e:
            logging.error(f"设置快捷键失败: {e}")
            return False
    
    def get_performance_settings(self) -> Dict[str, Any]:
        """获取性能设置
        
        Returns:
            Dict[str, Any]: 性能设置
        """
        return {
            'lazy_loading': self._config.lazy_loading,
            'cache_thumbnails': self._config.cache_thumbnails,
            'max_cache_size': self._config.max_cache_size
        }
    
    def add_observer(self, observer):
        """添加配置变更观察者
        
        Args:
            observer: 观察者对象，需要实现on_config_changed方法
        """
        if observer not in self._observers:
            self._observers.append(observer)
    
    def remove_observer(self, observer):
        """移除配置变更观察者
        
        Args:
            observer: 观察者对象
        """
        if observer in self._observers:
            self._observers.remove(observer)
    
    def _notify_observers(self):
        """通知所有观察者配置已变更"""
        for observer in self._observers:
            try:
                if hasattr(observer, 'on_config_changed'):
                    observer.on_config_changed(self._config)
            except Exception as e:
                logging.error(f"通知观察者失败: {e}")
    
    def export_config(self) -> Dict[str, Any]:
        """导出配置为字典
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        return {
            'window': {
                'width': self._config.window_width,
                'height': self._config.window_height,
                'maximized': self._config.window_maximized,
                'position': self._config.window_position
            },
            'view': {
                'default_mode': self._config.default_view_mode.value,
                'thumbnail_size': self._config.thumbnail_size,
                'grid_columns': self._config.grid_columns,
                'show_image_info': self._config.show_image_info,
                'show_similarity_scores': self._config.show_similarity_scores
            },
            'theme': {
                'theme': self._config.theme.value,
                'font_family': self._config.font_family,
                'font_size': self._config.font_size,
                'icon_size': self._config.icon_size
            },
            'search': {
                'panel_width': self._config.search_panel_width,
                'panel_collapsed': self._config.search_panel_collapsed,
                'show_advanced_options': self._config.show_advanced_options,
                'auto_search': self._config.auto_search_on_image_change
            },
            'results': {
                'default_count': self._config.default_result_count,
                'max_count': self._config.max_result_count,
                'auto_refresh': self._config.auto_refresh_results,
                'show_progress': self._config.show_progress_bar,
                'show_status': self._config.show_status_messages
            },
            'sort': {
                'default_column': self._config.default_sort_column,
                'default_order': self._config.default_sort_order.value
            },
            'export': {
                'default_format': self._config.default_export_format,
                'include_thumbnails': self._config.export_include_thumbnails,
                'thumbnail_size': self._config.export_thumbnail_size
            },
            'performance': {
                'lazy_loading': self._config.lazy_loading,
                'cache_thumbnails': self._config.cache_thumbnails,
                'max_cache_size': self._config.max_cache_size
            },
            'shortcuts': self._config.shortcuts
        }
    
    def import_config(self, config_dict: Dict[str, Any]) -> bool:
        """从字典导入配置
        
        Args:
            config_dict: 配置字典
            
        Returns:
            bool: 导入是否成功
        """
        try:
            # 窗口配置
            if 'window' in config_dict:
                window_config = config_dict['window']
                self.update_config(
                    window_width=window_config.get('width', self._config.window_width),
                    window_height=window_config.get('height', self._config.window_height),
                    window_maximized=window_config.get('maximized', self._config.window_maximized),
                    window_position=window_config.get('position', self._config.window_position)
                )
            
            # 视图配置
            if 'view' in config_dict:
                view_config = config_dict['view']
                view_mode = ViewMode(view_config.get('default_mode', self._config.default_view_mode.value))
                self.update_config(
                    default_view_mode=view_mode,
                    thumbnail_size=view_config.get('thumbnail_size', self._config.thumbnail_size),
                    grid_columns=view_config.get('grid_columns', self._config.grid_columns),
                    show_image_info=view_config.get('show_image_info', self._config.show_image_info),
                    show_similarity_scores=view_config.get('show_similarity_scores', self._config.show_similarity_scores)
                )
            
            # 主题配置
            if 'theme' in config_dict:
                theme_config = config_dict['theme']
                theme = Theme(theme_config.get('theme', self._config.theme.value))
                self.update_config(
                    theme=theme,
                    font_family=theme_config.get('font_family', self._config.font_family),
                    font_size=theme_config.get('font_size', self._config.font_size),
                    icon_size=theme_config.get('icon_size', self._config.icon_size)
                )
            
            # 其他配置...
            
            logging.info("界面配置导入成功")
            return True
            
        except Exception as e:
            logging.error(f"导入界面配置失败: {e}")
            return False
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"UIConfigManager(theme={self._config.theme.value}, view={self._config.default_view_mode.value})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"UIConfigManager(config={self._config})"


# 全局界面配置管理器实例
ui_config_manager = UIConfigManager()