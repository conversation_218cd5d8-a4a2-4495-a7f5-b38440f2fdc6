#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征权重设置组件

该模块提供特征权重的设置界面，包括深度、颜色、纹理和形状特征的权重配置。
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSlider, QCheckBox,
    QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from gui.widget_factory import WidgetFactory


class FeatureWeightsWidget(QWidget):
    """特征权重设置组件"""
    
    # 信号定义
    weightsChanged = pyqtSignal(dict)  # 权重变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.widget_factory = WidgetFactory()
        self._setup_ui()
        self._connect_signals()
        
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建分组框
        self.advanced_group = self.widget_factory.create_group_box("高级参数 - 特征权重")
        advanced_layout = QVBoxLayout(self.advanced_group)
        
        # 深度特征
        self._setup_deep_feature(advanced_layout)
        
        # 颜色特征
        self._setup_color_feature(advanced_layout)
        
        # 纹理特征
        self._setup_texture_feature(advanced_layout)
        
        # 形状特征
        self._setup_shape_feature(advanced_layout)
        
        layout.addWidget(self.advanced_group)
        
    def _setup_deep_feature(self, layout):
        """设置深度特征"""
        deep_layout = QHBoxLayout()
        self.deep_feature_cb = self.widget_factory.create_checkbox("深度特征", checked=True)
        self.deep_weight_slider = self.widget_factory.create_slider(
            minimum=0, maximum=100, value=25,
            orientation=Qt.Orientation.Horizontal,
            tick_position=QSlider.TickPosition.TicksBelow
        )
        self.deep_weight_label = self.widget_factory.create_label("25%")
        deep_layout.addWidget(self.deep_feature_cb)
        deep_layout.addWidget(self.deep_weight_slider)
        deep_layout.addWidget(self.deep_weight_label)
        layout.addLayout(deep_layout)
        
    def _setup_color_feature(self, layout):
        """设置颜色特征"""
        color_layout = QHBoxLayout()
        self.color_feature_cb = self.widget_factory.create_checkbox("颜色特征", checked=True)
        self.color_weight_slider = self.widget_factory.create_slider(
            minimum=0, maximum=100, value=25,
            orientation=Qt.Orientation.Horizontal,
            tick_position=QSlider.TickPosition.TicksBelow
        )
        self.color_weight_label = self.widget_factory.create_label("25%")
        color_layout.addWidget(self.color_feature_cb)
        color_layout.addWidget(self.color_weight_slider)
        color_layout.addWidget(self.color_weight_label)
        layout.addLayout(color_layout)
        
    def _setup_texture_feature(self, layout):
        """设置纹理特征"""
        texture_layout = QHBoxLayout()
        self.texture_feature_cb = self.widget_factory.create_checkbox("纹理特征", checked=True)
        self.texture_weight_slider = self.widget_factory.create_slider(
            minimum=0, maximum=100, value=25,
            orientation=Qt.Orientation.Horizontal,
            tick_position=QSlider.TickPosition.TicksBelow
        )
        self.texture_weight_label = self.widget_factory.create_label("25%")
        texture_layout.addWidget(self.texture_feature_cb)
        texture_layout.addWidget(self.texture_weight_slider)
        texture_layout.addWidget(self.texture_weight_label)
        layout.addLayout(texture_layout)
        
    def _setup_shape_feature(self, layout):
        """设置形状特征"""
        shape_layout = QHBoxLayout()
        self.shape_feature_cb = self.widget_factory.create_checkbox("形状特征", checked=True)
        self.shape_weight_slider = self.widget_factory.create_slider(
            minimum=0, maximum=100, value=25,
            orientation=Qt.Orientation.Horizontal,
            tick_position=QSlider.TickPosition.TicksBelow
        )
        self.shape_weight_label = self.widget_factory.create_label("25%")
        shape_layout.addWidget(self.shape_feature_cb)
        shape_layout.addWidget(self.shape_weight_slider)
        shape_layout.addWidget(self.shape_weight_label)
        layout.addLayout(shape_layout)
        
    def _connect_signals(self):
        """连接信号"""
        # 特征权重滑块
        self.deep_weight_slider.valueChanged.connect(self._update_deep_weight)
        self.color_weight_slider.valueChanged.connect(self._update_color_weight)
        self.texture_weight_slider.valueChanged.connect(self._update_texture_weight)
        self.shape_weight_slider.valueChanged.connect(self._update_shape_weight)
        
        # 特征选择复选框
        self.deep_feature_cb.toggled.connect(self._update_deep_feature)
        self.color_feature_cb.toggled.connect(self._update_color_feature)
        self.texture_feature_cb.toggled.connect(self._update_texture_feature)
        self.shape_feature_cb.toggled.connect(self._update_shape_feature)
        
        # 权重变化通知
        self.deep_weight_slider.valueChanged.connect(self._emit_weights_changed)
        self.color_weight_slider.valueChanged.connect(self._emit_weights_changed)
        self.texture_weight_slider.valueChanged.connect(self._emit_weights_changed)
        self.shape_weight_slider.valueChanged.connect(self._emit_weights_changed)
        
        self.deep_feature_cb.toggled.connect(self._emit_weights_changed)
        self.color_feature_cb.toggled.connect(self._emit_weights_changed)
        self.texture_feature_cb.toggled.connect(self._emit_weights_changed)
        self.shape_feature_cb.toggled.connect(self._emit_weights_changed)
        
    def _update_deep_weight(self, value):
        """更新深度特征权重标签"""
        self.deep_weight_label.setText(f"{value}%")
        self.deep_weight_slider.setToolTip(f"深度特征权重: {value}%")
        
    def _update_color_weight(self, value):
        """更新颜色特征权重标签"""
        self.color_weight_label.setText(f"{value}%")
        self.color_weight_slider.setToolTip(f"颜色特征权重: {value}%")
        
    def _update_texture_weight(self, value):
        """更新纹理特征权重标签"""
        self.texture_weight_label.setText(f"{value}%")
        self.texture_weight_slider.setToolTip(f"纹理特征权重: {value}%")
        
    def _update_shape_weight(self, value):
        """更新形状特征权重标签"""
        self.shape_weight_label.setText(f"{value}%")
        self.shape_weight_slider.setToolTip(f"形状特征权重: {value}%")
        
    def _update_deep_feature(self, enabled):
        """更新深度特征启用状态"""
        self.deep_weight_slider.setEnabled(enabled)
        self.deep_weight_label.setEnabled(enabled)
        if not enabled:
            self.deep_weight_slider.setStyleSheet("QSlider { color: #ccc; }")
        else:
            self.deep_weight_slider.setStyleSheet("")
            
    def _update_color_feature(self, enabled):
        """更新颜色特征启用状态"""
        self.color_weight_slider.setEnabled(enabled)
        self.color_weight_label.setEnabled(enabled)
        if not enabled:
            self.color_weight_slider.setStyleSheet("QSlider { color: #ccc; }")
        else:
            self.color_weight_slider.setStyleSheet("")
            
    def _update_texture_feature(self, enabled):
        """更新纹理特征启用状态"""
        self.texture_weight_slider.setEnabled(enabled)
        self.texture_weight_label.setEnabled(enabled)
        if not enabled:
            self.texture_weight_slider.setStyleSheet("QSlider { color: #ccc; }")
        else:
            self.texture_weight_slider.setStyleSheet("")
            
    def _update_shape_feature(self, enabled):
        """更新形状特征启用状态"""
        self.shape_weight_slider.setEnabled(enabled)
        self.shape_weight_label.setEnabled(enabled)
        if not enabled:
            self.shape_weight_slider.setStyleSheet("QSlider { color: #ccc; }")
        else:
            self.shape_weight_slider.setStyleSheet("")
            
    def _emit_weights_changed(self):
        """发送权重变化信号"""
        weights = self.get_feature_weights()
        self.weightsChanged.emit(weights)
        
    def get_feature_weights(self):
        """获取特征权重
        
        Returns:
            dict: 特征权重字典
        """
        return self.get_weights()
        
    def get_weights(self):
        """获取特征权重（标准化格式）
        
        Returns:
            dict: 特征权重字典，键名符合搜索引擎要求
        """
        # 计算特征权重总和
        total_weight = 0
        if self.deep_feature_cb.isChecked():
            total_weight += self.deep_weight_slider.value()
        if self.color_feature_cb.isChecked():
            total_weight += self.color_weight_slider.value()
        if self.texture_feature_cb.isChecked():
            total_weight += self.texture_weight_slider.value()
        if self.shape_feature_cb.isChecked():
            total_weight += self.shape_weight_slider.value()
        
        # 归一化权重
        weights = {}
        if total_weight > 0:
            if self.deep_feature_cb.isChecked():
                weights['deep_learning'] = self.deep_weight_slider.value() / total_weight
            else:
                weights['deep_learning'] = 0.0
                
            if self.color_feature_cb.isChecked():
                weights['color'] = self.color_weight_slider.value() / total_weight
            else:
                weights['color'] = 0.0
                
            if self.texture_feature_cb.isChecked():
                weights['texture'] = self.texture_weight_slider.value() / total_weight
            else:
                weights['texture'] = 0.0
                
            if self.shape_feature_cb.isChecked():
                weights['shape'] = self.shape_weight_slider.value() / total_weight
            else:
                weights['shape'] = 0.0
        else:
            # 如果所有特征都未选中，使用默认权重
            weights = {
                'deep_learning': 0.25,
                'color': 0.25,
                'texture': 0.25,
                'shape': 0.25
            }
            
        return weights
        
    def set_feature_weights(self, weights):
        """设置特征权重
        
        Args:
            weights (dict): 特征权重字典
        """
        if 'deep' in weights:
            value = int(weights['deep'] * 100)
            self.deep_weight_slider.setValue(value)
            self.deep_feature_cb.setChecked(value > 0)
            
        if 'color' in weights:
            value = int(weights['color'] * 100)
            self.color_weight_slider.setValue(value)
            self.color_feature_cb.setChecked(value > 0)
            
        if 'texture' in weights:
            value = int(weights['texture'] * 100)
            self.texture_weight_slider.setValue(value)
            self.texture_feature_cb.setChecked(value > 0)
            
        if 'shape' in weights:
            value = int(weights['shape'] * 100)
            self.shape_weight_slider.setValue(value)
            self.shape_feature_cb.setChecked(value > 0)
            
    def reset_weights(self):
        """重置权重到默认值"""
        self.deep_feature_cb.setChecked(True)
        self.color_feature_cb.setChecked(True)
        self.texture_feature_cb.setChecked(True)
        self.shape_feature_cb.setChecked(True)
        
        self.deep_weight_slider.setValue(25)
        self.color_weight_slider.setValue(25)
        self.texture_weight_slider.setValue(25)
        self.shape_weight_slider.setValue(25)