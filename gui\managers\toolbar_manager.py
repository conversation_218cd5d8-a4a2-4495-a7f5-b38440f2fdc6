#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具栏管理器

该模块负责管理主窗口的工具栏和工具按钮。
"""

from typing import Optional
from PyQt6.QtWidgets import QMainWindow, QToolBar, QWidget, QSizePolicy
from PyQt6.QtGui import QAction, QIcon
from PyQt6.QtCore import QObject, pyqtSignal, QSize

from utils.logger_mixin import LoggerMixin


class ToolbarManager(QObject, LoggerMixin):
    """工具栏管理器"""
    
    # 信号
    openImageRequested = pyqtSignal()
    openFolderRequested = pyqtSignal()
    settingsRequested = pyqtSignal()
    modelSelectorRequested = pyqtSignal()
    taskManagerRequested = pyqtSignal()
    rebuildIndexRequested = pyqtSignal()
    clearCacheRequested = pyqtSignal()
    
    def __init__(self, main_window: QMainWindow):
        super().__init__()
        self.main_window = main_window
        self.toolbar = None
        
        self.setup_toolbar()
    
    def setup_toolbar(self):
        """设置工具栏"""
        try:
            self.toolbar = self.main_window.addToolBar("主工具栏")
            self.toolbar.setObjectName("main_toolbar")
            self.toolbar.setIconSize(QSize(24, 24))
            
            self._add_toolbar_actions()
            
            # 添加弹性空间
            spacer = QWidget()
            spacer.setSizePolicy(QSizePolicy.Policy.Expanding, 
                               QSizePolicy.Policy.Preferred)
            self.toolbar.addWidget(spacer)
            
            self.logger.info("工具栏设置完成")
            
        except Exception as e:
            self.logger.error(f"设置工具栏失败: {e}")
    
    def _add_toolbar_actions(self):
        """添加工具栏动作"""
        # 打开图像
        open_image_action = QAction("打开图像", self.main_window)
        open_image_action.setIcon(self._get_icon("open_image"))
        open_image_action.setToolTip("打开图像文件")
        open_image_action.triggered.connect(self.openImageRequested.emit)
        self.toolbar.addAction(open_image_action)
        
        # 打开文件夹
        open_folder_action = QAction("打开文件夹", self.main_window)
        open_folder_action.setIcon(self._get_icon("open_folder"))
        open_folder_action.setToolTip("打开图像文件夹")
        open_folder_action.triggered.connect(self.openFolderRequested.emit)
        self.toolbar.addAction(open_folder_action)
        
        self.toolbar.addSeparator()
        
        # 设置
        settings_action = QAction("设置", self.main_window)
        settings_action.setIcon(self._get_icon("settings"))
        settings_action.setToolTip("打开设置对话框")
        settings_action.triggered.connect(self.settingsRequested.emit)
        self.toolbar.addAction(settings_action)
        
        # 选择模型
        model_action = QAction("选择模型", self.main_window)
        model_action.setIcon(self._get_icon("model"))
        model_action.setToolTip("选择特征提取模型")
        model_action.triggered.connect(self.modelSelectorRequested.emit)
        self.toolbar.addAction(model_action)
        
        self.toolbar.addSeparator()
        
        # 任务管理器
        task_manager_action = QAction("任务管理器", self.main_window)
        task_manager_action.setIcon(self._get_icon("task_manager"))
        task_manager_action.setToolTip("查看和管理后台任务")
        task_manager_action.triggered.connect(self.taskManagerRequested.emit)
        self.toolbar.addAction(task_manager_action)
        
        self.toolbar.addSeparator()
        
        # 重建索引
        rebuild_action = QAction("重建索引", self.main_window)
        rebuild_action.setIcon(self._get_icon("rebuild"))
        rebuild_action.setToolTip("重建搜索索引")
        rebuild_action.triggered.connect(self.rebuildIndexRequested.emit)
        self.toolbar.addAction(rebuild_action)
        
        # 清除缓存
        clear_cache_action = QAction("清除缓存", self.main_window)
        clear_cache_action.setIcon(self._get_icon("clear_cache"))
        clear_cache_action.setToolTip("清除所有缓存")
        clear_cache_action.triggered.connect(self.clearCacheRequested.emit)
        self.toolbar.addAction(clear_cache_action)
    
    def _get_icon(self, icon_name: str) -> QIcon:
        """获取图标
        
        Args:
            icon_name: 图标名称
            
        Returns:
            QIcon: 图标对象
        """
        # 这里可以根据实际的图标资源路径来加载图标
        # 暂时返回空图标
        return QIcon()
    
    def set_visible(self, visible: bool):
        """设置工具栏可见性
        
        Args:
            visible: 是否可见
        """
        if self.toolbar:
            self.toolbar.setVisible(visible)
    
    def is_visible(self) -> bool:
        """获取工具栏可见性
        
        Returns:
            bool: 是否可见
        """
        return self.toolbar.isVisible() if self.toolbar else False
    
    def toggle_visibility(self):
        """切换工具栏可见性"""
        if self.toolbar:
            self.toolbar.setVisible(not self.toolbar.isVisible())