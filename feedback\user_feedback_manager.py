#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户反馈管理模块

该模块实现用户反馈的收集、存储和学习功能，用于优化搜索结果。
"""

import time
import json
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

from utils.log_utils import LoggerMixin
from database.models import FabricImage
from search.search_strategies import FeatureWeights


class FeedbackType(Enum):
    """反馈类型"""
    RELEVANT = "relevant"  # 相关
    IRRELEVANT = "irrelevant"  # 不相关
    HIGHLY_RELEVANT = "highly_relevant"  # 高度相关
    BOOKMARK = "bookmark"  # 收藏
    RATING = "rating"  # 评分


@dataclass
class UserFeedback:
    """用户反馈"""
    query_image_path: str
    result_image_id: int
    feedback_type: FeedbackType
    feedback_value: float  # 反馈值（0-1之间）
    timestamp: datetime
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    additional_info: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['feedback_type'] = self.feedback_type.value
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserFeedback':
        """从字典创建"""
        data = data.copy()
        data['feedback_type'] = FeedbackType(data['feedback_type'])
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


@dataclass
class FeedbackStatistics:
    """反馈统计"""
    total_feedbacks: int = 0
    positive_feedbacks: int = 0
    negative_feedbacks: int = 0
    average_rating: float = 0.0
    feedback_distribution: Dict[str, int] = None
    
    def __post_init__(self):
        if self.feedback_distribution is None:
            self.feedback_distribution = {}


class UserFeedbackManager(LoggerMixin):
    """用户反馈管理器"""
    
    def __init__(self, feedback_storage_path: Optional[str] = None):
        """
        初始化用户反馈管理器
        
        Args:
            feedback_storage_path: 反馈存储路径
        """
        super().__init__()
        
        self.feedback_storage_path = feedback_storage_path or "data/user_feedback.json"
        self.feedbacks: List[UserFeedback] = []
        self.feature_weights_history: List[Tuple[datetime, FeatureWeights]] = []
        
        # 学习参数
        self.learning_rate = 0.1
        self.weight_decay = 0.95
        self.min_feedbacks_for_learning = 5
        
        # 加载历史反馈
        self._load_feedbacks()
    
    def add_feedback(self, feedback: UserFeedback):
        """
        添加用户反馈
        
        Args:
            feedback: 用户反馈
        """
        self.feedbacks.append(feedback)
        self.logger.info(
            f"添加用户反馈: {feedback.feedback_type.value} "
            f"for image {feedback.result_image_id}"
        )
        
        # 保存反馈
        self._save_feedbacks()
        
        # 触发学习
        if len(self.feedbacks) >= self.min_feedbacks_for_learning:
            self._update_feature_weights()
    
    def add_relevance_feedback(self, 
                             query_image_path: str,
                             result_image_id: int,
                             is_relevant: bool,
                             session_id: Optional[str] = None):
        """
        添加相关性反馈
        
        Args:
            query_image_path: 查询图像路径
            result_image_id: 结果图像ID
            is_relevant: 是否相关
            session_id: 会话ID
        """
        feedback_type = FeedbackType.RELEVANT if is_relevant else FeedbackType.IRRELEVANT
        feedback_value = 1.0 if is_relevant else 0.0
        
        feedback = UserFeedback(
            query_image_path=query_image_path,
            result_image_id=result_image_id,
            feedback_type=feedback_type,
            feedback_value=feedback_value,
            timestamp=datetime.now(),
            session_id=session_id
        )
        
        self.add_feedback(feedback)
    
    def add_rating_feedback(self,
                          query_image_path: str,
                          result_image_id: int,
                          rating: float,
                          session_id: Optional[str] = None):
        """
        添加评分反馈
        
        Args:
            query_image_path: 查询图像路径
            result_image_id: 结果图像ID
            rating: 评分（0-5）
            session_id: 会话ID
        """
        # 将评分归一化到0-1
        normalized_rating = max(0.0, min(1.0, rating / 5.0))
        
        feedback = UserFeedback(
            query_image_path=query_image_path,
            result_image_id=result_image_id,
            feedback_type=FeedbackType.RATING,
            feedback_value=normalized_rating,
            timestamp=datetime.now(),
            session_id=session_id
        )
        
        self.add_feedback(feedback)
    
    def get_feedback_statistics(self) -> FeedbackStatistics:
        """
        获取反馈统计信息
        
        Returns:
            FeedbackStatistics: 反馈统计
        """
        stats = FeedbackStatistics()
        
        if not self.feedbacks:
            return stats
        
        stats.total_feedbacks = len(self.feedbacks)
        
        # 统计正负反馈
        positive_count = 0
        negative_count = 0
        total_rating = 0.0
        rating_count = 0
        
        feedback_distribution = {}
        
        for feedback in self.feedbacks:
            feedback_type_str = feedback.feedback_type.value
            feedback_distribution[feedback_type_str] = feedback_distribution.get(feedback_type_str, 0) + 1
            
            if feedback.feedback_type in [FeedbackType.RELEVANT, FeedbackType.HIGHLY_RELEVANT, FeedbackType.BOOKMARK]:
                positive_count += 1
            elif feedback.feedback_type == FeedbackType.IRRELEVANT:
                negative_count += 1
            
            if feedback.feedback_type == FeedbackType.RATING:
                total_rating += feedback.feedback_value
                rating_count += 1
        
        stats.positive_feedbacks = positive_count
        stats.negative_feedbacks = negative_count
        stats.feedback_distribution = feedback_distribution
        
        if rating_count > 0:
            stats.average_rating = total_rating / rating_count
        
        return stats
    
    def get_learned_feature_weights(self) -> Optional[FeatureWeights]:
        """
        获取学习到的特征权重
        
        Returns:
            Optional[FeatureWeights]: 学习到的特征权重
        """
        if self.feature_weights_history:
            return self.feature_weights_history[-1][1]
        return None
    
    def _update_feature_weights(self):
        """
        根据用户反馈更新特征权重
        """
        try:
            # 获取最近的反馈
            recent_feedbacks = self._get_recent_feedbacks()
            
            if len(recent_feedbacks) < self.min_feedbacks_for_learning:
                return
            
            # 分析反馈模式
            feature_performance = self._analyze_feature_performance(recent_feedbacks)
            
            # 计算新的特征权重
            new_weights = self._calculate_adaptive_weights(feature_performance)
            
            # 保存权重历史
            self.feature_weights_history.append((datetime.now(), new_weights))
            
            # 限制历史记录数量
            if len(self.feature_weights_history) > 100:
                self.feature_weights_history = self.feature_weights_history[-50:]
            
            self.logger.info(f"更新特征权重: {new_weights.to_dict()}")
            
        except Exception as e:
            self.logger.error(f"更新特征权重失败: {str(e)}")
    
    def _get_recent_feedbacks(self, days: int = 7) -> List[UserFeedback]:
        """
        获取最近的反馈
        
        Args:
            days: 天数
            
        Returns:
            List[UserFeedback]: 最近的反馈列表
        """
        cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
        
        recent_feedbacks = []
        for feedback in self.feedbacks:
            if feedback.timestamp.timestamp() >= cutoff_time:
                recent_feedbacks.append(feedback)
        
        return recent_feedbacks
    
    def _analyze_feature_performance(self, feedbacks: List[UserFeedback]) -> Dict[str, float]:
        """
        分析各特征维度的性能
        
        Args:
            feedbacks: 反馈列表
            
        Returns:
            Dict[str, float]: 特征性能分数
        """
        # 这里简化处理，实际应该根据具体的特征相似度分数来分析
        feature_performance = {
            'color': 0.0,
            'texture': 0.0,
            'shape': 0.0,
            'deep_learning': 0.0
        }
        
        # 统计正负反馈
        positive_feedbacks = [f for f in feedbacks if f.feedback_value > 0.5]
        negative_feedbacks = [f for f in feedbacks if f.feedback_value <= 0.5]
        
        # 简化的性能评估：假设深度学习特征在正反馈中表现更好
        if positive_feedbacks:
            feature_performance['deep_learning'] += len(positive_feedbacks) * 0.1
            feature_performance['color'] += len(positive_feedbacks) * 0.05
            feature_performance['texture'] += len(positive_feedbacks) * 0.05
            feature_performance['shape'] += len(positive_feedbacks) * 0.03
        
        if negative_feedbacks:
            feature_performance['deep_learning'] -= len(negative_feedbacks) * 0.05
            feature_performance['color'] -= len(negative_feedbacks) * 0.03
            feature_performance['texture'] -= len(negative_feedbacks) * 0.03
            feature_performance['shape'] -= len(negative_feedbacks) * 0.02
        
        return feature_performance
    
    def _calculate_adaptive_weights(self, feature_performance: Dict[str, float]) -> FeatureWeights:
        """
        计算自适应权重
        
        Args:
            feature_performance: 特征性能分数
            
        Returns:
            FeatureWeights: 新的特征权重
        """
        # 获取当前权重
        current_weights = FeatureWeights()
        if self.feature_weights_history:
            current_weights = self.feature_weights_history[-1][1]
        
        # 计算权重调整
        new_weights = FeatureWeights(
            color_weight=current_weights.color_weight + 
                        self.learning_rate * feature_performance.get('color', 0.0),
            texture_weight=current_weights.texture_weight + 
                          self.learning_rate * feature_performance.get('texture', 0.0),
            shape_weight=current_weights.shape_weight + 
                        self.learning_rate * feature_performance.get('shape', 0.0),
            deep_learning_weight=current_weights.deep_learning_weight + 
                                self.learning_rate * feature_performance.get('deep_learning', 0.0)
        )
        
        # 确保权重为正数
        new_weights.color_weight = max(0.1, new_weights.color_weight)
        new_weights.texture_weight = max(0.1, new_weights.texture_weight)
        new_weights.shape_weight = max(0.1, new_weights.shape_weight)
        new_weights.deep_learning_weight = max(0.1, new_weights.deep_learning_weight)
        
        # 归一化权重
        new_weights.normalize()
        
        return new_weights
    
    def _save_feedbacks(self):
        """
        保存反馈到文件
        """
        try:
            import os
            os.makedirs(os.path.dirname(self.feedback_storage_path), exist_ok=True)
            
            data = {
                'feedbacks': [feedback.to_dict() for feedback in self.feedbacks],
                'feature_weights_history': [
                    {
                        'timestamp': timestamp.isoformat(),
                        'weights': weights.to_dict()
                    }
                    for timestamp, weights in self.feature_weights_history
                ]
            }
            
            with open(self.feedback_storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存反馈失败: {str(e)}")
    
    def _load_feedbacks(self):
        """
        从文件加载反馈
        """
        try:
            if not os.path.exists(self.feedback_storage_path):
                return
            
            with open(self.feedback_storage_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 加载反馈
            self.feedbacks = []
            for feedback_data in data.get('feedbacks', []):
                try:
                    feedback = UserFeedback.from_dict(feedback_data)
                    self.feedbacks.append(feedback)
                except Exception as e:
                    self.logger.warning(f"加载反馈记录失败: {str(e)}")
            
            # 加载权重历史
            self.feature_weights_history = []
            for weight_data in data.get('feature_weights_history', []):
                try:
                    timestamp = datetime.fromisoformat(weight_data['timestamp'])
                    weights_dict = weight_data['weights']
                    weights = FeatureWeights(
                        color_weight=weights_dict.get('color', 0.25),
                        texture_weight=weights_dict.get('texture', 0.25),
                        shape_weight=weights_dict.get('shape', 0.25),
                        deep_learning_weight=weights_dict.get('deep_learning', 0.25)
                    )
                    self.feature_weights_history.append((timestamp, weights))
                except Exception as e:
                    self.logger.warning(f"加载权重历史失败: {str(e)}")
            
            self.logger.info(f"加载了 {len(self.feedbacks)} 条反馈记录")
            
        except Exception as e:
            self.logger.error(f"加载反馈失败: {str(e)}")
    
    def clear_old_feedbacks(self, days: int = 30):
        """
        清理旧的反馈记录
        
        Args:
            days: 保留天数
        """
        cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
        
        old_count = len(self.feedbacks)
        self.feedbacks = [
            feedback for feedback in self.feedbacks
            if feedback.timestamp.timestamp() >= cutoff_time
        ]
        
        removed_count = old_count - len(self.feedbacks)
        if removed_count > 0:
            self.logger.info(f"清理了 {removed_count} 条旧反馈记录")
            self._save_feedbacks()