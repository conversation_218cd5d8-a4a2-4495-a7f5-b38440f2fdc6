# GUI参数传递代码示例

本文档通过具体的代码示例展示GUI参数如何传递到搜索引擎的完整过程。

## 1. GUI参数收集示例

### 1.1 特征权重参数收集

<augment_code_snippet path="gui/search/feature_weights_widget.py" mode="EXCERPT">
````python
def get_weights(self):
    """获取特征权重（标准化格式）"""
    weights = {}
    total_weight = 0
    
    # 收集各特征权重
    if self.deep_feature_cb.isChecked():
        deep_weight = self.deep_weight_slider.value()
        weights['deep_learning'] = deep_weight
        total_weight += deep_weight
    
    if self.color_feature_cb.isChecked():
        color_weight = self.color_weight_slider.value()
        weights['color'] = color_weight
        total_weight += color_weight
    
    # 归一化权重
    if total_weight > 0:
        for key in weights:
            weights[key] = weights[key] / total_weight
    
    return weights
````
</augment_code_snippet>

### 1.2 传统特征参数收集

<augment_code_snippet path="gui/search/feature_params_widget.py" mode="EXCERPT">
````python
def get_params(self):
    """获取特征参数（标准化格式）"""
    return {
        'extract_color': True,
        'extract_texture': True,
        'extract_shape': True,
        'hist_bins': self.color_bins_slider.value(),
        'n_dominant_colors': self.dominant_colors_slider.value(),
        'lbp_radius': self.lbp_radius_slider.value(),
        'lbp_n_points': self.lbp_points_slider.value(),
        'n_fourier_descriptors': self.fourier_descriptors_slider.value()
    }
````
</augment_code_snippet>

### 1.3 高级搜索参数收集

<augment_code_snippet path="gui/search/advanced_widget.py" mode="EXCERPT">
````python
def get_search_params(self) -> Dict[str, Any]:
    """获取搜索参数"""
    params = {
        "max_results": self.max_results_spin.value(),
        "sort_by": self._get_sort_field(),
        "sort_order": "desc" if self.sort_order_combo.currentIndex() == 0 else "asc"
    }
    
    # 文件过滤器
    file_filters = {}
    if self.enable_file_filter_cb.isChecked():
        extensions = self.file_extensions_edit.text().strip()
        if extensions:
            ext_list = [ext.strip() for ext in extensions.split(',')]
            file_filters["extensions"] = ext_list
    
    if file_filters:
        params["file_filters"] = file_filters
    
    return params
````
</augment_code_snippet>

## 2. 参数聚合示例

### 2.1 SearchPanel参数聚合

<augment_code_snippet path="gui/search/search_panel.py" mode="EXCERPT">
````python
def get_search_config(self) -> SearchConfig:
    """获取搜索配置"""
    try:
        config = SearchConfig(mode=self.current_mode)
        
        if self.current_mode == SearchMode.SIMILARITY:
            params = self.similarity_widget.get_search_params()
            
            # 设置相似度搜索特有参数
            if 'similarity_threshold' in params:
                config.similarity_threshold = params['similarity_threshold']
            if 'feature_weights' in params:
                config.feature_weights = params['feature_weights']
            if 'feature_extraction_params' in params:
                config.feature_extraction_params = params['feature_extraction_params']
            if 'max_results' in params:
                config.max_results = params['max_results']
                
        return config
            
    except Exception as e:
        self.logger.error(f"获取搜索配置失败: {e}")
        return SearchConfig(mode=SearchMode.SIMILARITY)
````
</augment_code_snippet>

## 3. 参数转换示例

### 3.1 SearchConfig到SearchQuery转换

<augment_code_snippet path="gui/managers/search_handler.py" mode="EXCERPT">
````python
def _config_to_query(self, config: SearchConfig) -> SearchQuery:
    """将搜索配置转换为搜索查询"""
    # 确定查询类型
    query_type = SearchType.TEXT_SEARCH  # 默认值
    
    search_mode = config.mode.value if hasattr(config, 'mode') else "text"
    
    if search_mode == "similarity":
        query_type = SearchType.IMAGE_SIMILARITY
    elif search_mode == "text":
        query_type = SearchType.TEXT_SEARCH
    elif search_mode == "category":
        query_type = SearchType.CATEGORY_BROWSE
    
    # 创建查询对象
    query = SearchQuery(query_type=query_type)
    
    # 设置特征权重
    if hasattr(config, 'feature_weights') and config.feature_weights:
        query.feature_weights = config.feature_weights
    
    # 设置特征提取参数
    if hasattr(config, 'feature_extraction_params'):
        query.feature_extraction_params = config.feature_extraction_params
    
    # 设置搜索策略
    if hasattr(config, 'search_strategy'):
        query.search_strategy = config.search_strategy
    
    return query
````
</augment_code_snippet>

## 4. 搜索策略执行示例

### 4.1 加权搜索策略

<augment_code_snippet path="search/search_strategies.py" mode="EXCERPT">
````python
class WeightedSearchStrategy(SearchStrategy):
    """加权搜索策略"""
    
    def search(self, query_features, database_features, fabric_images, top_k, **kwargs):
        """执行加权搜索"""
        # 计算各特征的相似度
        feature_similarities = {}
        
        for feature_type, query_feature in query_features.items():
            if feature_type in database_features:
                similarities = self.similarity_calculator.calculate_similarities(
                    query_feature, database_features[feature_type]
                )
                feature_similarities[feature_type] = similarities
        
        # 加权融合相似度分数
        final_similarities = self._weighted_fusion(feature_similarities)
        
        # 排序并返回结果
        return self._create_search_results(
            final_similarities, feature_similarities, 
            fabric_images, top_k, "weighted"
        )
    
    def _weighted_fusion(self, feature_similarities):
        """加权融合相似度分数"""
        weights = self.feature_weights.to_dict()
        
        num_images = len(next(iter(feature_similarities.values())))
        final_similarities = np.zeros(num_images)
        
        # 加权求和
        for feature_type, similarities in feature_similarities.items():
            weight = weights.get(feature_type, 0.0)
            final_similarities += weight * similarities
            
        return final_similarities
````
</augment_code_snippet>

### 4.2 自适应搜索策略

<augment_code_snippet path="search/search_strategies.py" mode="EXCERPT">
````python
class AdaptiveSearchStrategy(SearchStrategy):
    """自适应搜索策略"""
    
    def search(self, query_features, database_features, fabric_images, top_k, **kwargs):
        """执行自适应搜索"""
        
        # 分析查询图像特征，自动调整权重
        adaptive_weights = self._analyze_query_features(query_features)
        
        # 使用自适应权重执行加权搜索
        weighted_strategy = WeightedSearchStrategy(
            self.similarity_calculator, adaptive_weights
        )
        
        results = weighted_strategy.search(
            query_features, database_features, fabric_images, top_k, **kwargs
        )
        
        # 更新策略名称
        for result in results:
            result.strategy_used = "adaptive"
        
        return results
    
    def _analyze_query_features(self, query_features):
        """分析查询特征，自动调整权重"""
        weights = FeatureWeights()
        feature_variances = {}
        
        # 计算各特征的方差
        for feature_type, features in query_features.items():
            if len(features) > 1:
                feature_variances[feature_type] = np.var(features)
        
        # 根据方差调整权重
        total_variance = sum(feature_variances.values())
        
        if total_variance > 0:
            if 'color' in feature_variances:
                weights.color_weight = feature_variances['color'] / total_variance
            if 'texture' in feature_variances:
                weights.texture_weight = feature_variances['texture'] / total_variance
        
        weights.normalize()
        return weights
````
</augment_code_snippet>

## 5. 完整的参数传递流程示例

```python
# 1. GUI参数收集
feature_weights = {
    'deep_learning': 0.4,
    'color': 0.3,
    'texture': 0.2,
    'shape': 0.1
}

feature_params = {
    'hist_bins': 32,
    'lbp_radius': 3,
    'n_fourier_descriptors': 64
}

# 2. 创建搜索配置
search_config = SearchConfig(
    mode=SearchMode.SIMILARITY,
    feature_weights=feature_weights,
    feature_extraction_params=feature_params,
    max_results=50,
    similarity_threshold=0.7
)

# 3. 转换为搜索查询
search_query = SearchQuery(
    query_type=SearchType.IMAGE_SIMILARITY,
    feature_weights=feature_weights,
    feature_extraction_params=feature_params,
    top_k=50,
    similarity_threshold=0.7
)

# 4. 执行搜索策略
strategy = WeightedSearchStrategy(
    similarity_calculator,
    FeatureWeights(**feature_weights)
)

results = strategy.search(
    query_features,
    database_features,
    fabric_images,
    top_k=50
)
```

这个完整的示例展示了参数如何从GUI界面一步步传递到搜索引擎，并最终执行具体的搜索策略。
