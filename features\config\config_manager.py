"""配置管理器模块

提供统一的配置管理功能，包括配置加载、保存、验证和更新。
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
import logging
from datetime import datetime

from .feature_config import FeatureConfig
from .search_config import SearchConfig
from .model_config import ModelConfig
from .performance_config import PerformanceConfig


class ConfigManager:
    """配置管理器
    
    提供统一的配置管理功能，包括：
    - 配置文件加载和保存
    - 配置验证
    - 配置更新和合并
    - 环境变量覆盖
    """
    
    def __init__(self, config_dir: Union[str, Path] = "./config"):
        """初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # 配置对象
        self._feature_config: Optional[FeatureConfig] = None
        self._search_config: Optional[SearchConfig] = None
        self._model_config: Optional[ModelConfig] = None
        self._performance_config: Optional[PerformanceConfig] = None
        
        # 配置文件路径
        self._config_files = {
            'feature': self.config_dir / 'feature_config.yaml',
            'search': self.config_dir / 'search_config.yaml',
            'model': self.config_dir / 'model_config.yaml',
            'performance': self.config_dir / 'performance_config.yaml'
        }
        
    def load_all_configs(self) -> bool:
        """加载所有配置
        
        Returns:
            bool: 是否成功加载
        """
        try:
            self._feature_config = self.load_feature_config()
            self._search_config = self.load_search_config()
            self._model_config = self.load_model_config()
            self._performance_config = self.load_performance_config()
            
            # 验证配置
            if not self.validate_all_configs():
                self.logger.warning("配置验证失败，使用默认配置")
                self._create_default_configs()
                
            self.logger.info("所有配置加载完成")
            return True
            
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            self._create_default_configs()
            return False
            
    def load_feature_config(self) -> FeatureConfig:
        """加载特征配置
        
        Returns:
            FeatureConfig: 特征配置对象
        """
        config_file = self._config_files['feature']
        if config_file.exists():
            try:
                data = self._load_config_file(config_file)
                return FeatureConfig.from_dict(data)
            except Exception as e:
                self.logger.error(f"加载特征配置失败: {e}")
                
        return FeatureConfig()
        
    def load_search_config(self) -> SearchConfig:
        """加载搜索配置
        
        Returns:
            SearchConfig: 搜索配置对象
        """
        config_file = self._config_files['search']
        if config_file.exists():
            try:
                data = self._load_config_file(config_file)
                return SearchConfig.from_dict(data)
            except Exception as e:
                self.logger.error(f"加载搜索配置失败: {e}")
                
        return SearchConfig()
        
    def load_model_config(self) -> ModelConfig:
        """加载模型配置
        
        Returns:
            ModelConfig: 模型配置对象
        """
        config_file = self._config_files['model']
        if config_file.exists():
            try:
                data = self._load_config_file(config_file)
                return ModelConfig.from_dict(data)
            except Exception as e:
                self.logger.error(f"加载模型配置失败: {e}")
                
        return ModelConfig()
        
    def load_performance_config(self) -> PerformanceConfig:
        """加载性能配置
        
        Returns:
            PerformanceConfig: 性能配置对象
        """
        config_file = self._config_files['performance']
        if config_file.exists():
            try:
                data = self._load_config_file(config_file)
                return PerformanceConfig.from_dict(data)
            except Exception as e:
                self.logger.error(f"加载性能配置失败: {e}")
                
        return PerformanceConfig()
        
    def save_all_configs(self) -> bool:
        """保存所有配置
        
        Returns:
            bool: 是否成功保存
        """
        try:
            if self._feature_config:
                self.save_feature_config(self._feature_config)
            if self._search_config:
                self.save_search_config(self._search_config)
            if self._model_config:
                self.save_model_config(self._model_config)
            if self._performance_config:
                self.save_performance_config(self._performance_config)
                
            self.logger.info("所有配置保存完成")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False
            
    def save_feature_config(self, config: FeatureConfig) -> bool:
        """保存特征配置
        
        Args:
            config: 特征配置对象
            
        Returns:
            bool: 是否成功保存
        """
        try:
            config_file = self._config_files['feature']
            self._save_config_file(config_file, config.to_dict())
            self._feature_config = config
            return True
        except Exception as e:
            self.logger.error(f"保存特征配置失败: {e}")
            return False
            
    def save_search_config(self, config: SearchConfig) -> bool:
        """保存搜索配置
        
        Args:
            config: 搜索配置对象
            
        Returns:
            bool: 是否成功保存
        """
        try:
            config_file = self._config_files['search']
            self._save_config_file(config_file, config.to_dict())
            self._search_config = config
            return True
        except Exception as e:
            self.logger.error(f"保存搜索配置失败: {e}")
            return False
            
    def save_model_config(self, config: ModelConfig) -> bool:
        """保存模型配置
        
        Args:
            config: 模型配置对象
            
        Returns:
            bool: 是否成功保存
        """
        try:
            config_file = self._config_files['model']
            self._save_config_file(config_file, config.to_dict())
            self._model_config = config
            return True
        except Exception as e:
            self.logger.error(f"保存模型配置失败: {e}")
            return False
            
    def save_performance_config(self, config: PerformanceConfig) -> bool:
        """保存性能配置
        
        Args:
            config: 性能配置对象
            
        Returns:
            bool: 是否成功保存
        """
        try:
            config_file = self._config_files['performance']
            self._save_config_file(config_file, config.to_dict())
            self._performance_config = config
            return True
        except Exception as e:
            self.logger.error(f"保存性能配置失败: {e}")
            return False
            
    def validate_all_configs(self) -> bool:
        """验证所有配置
        
        Returns:
            bool: 是否所有配置都有效
        """
        try:
            valid = True
            
            if self._feature_config and not self._feature_config.validate():
                self.logger.error("特征配置验证失败")
                valid = False
                
            if self._search_config and not self._search_config.validate():
                self.logger.error("搜索配置验证失败")
                valid = False
                
            if self._model_config and not self._model_config.validate():
                self.logger.error("模型配置验证失败")
                valid = False
                
            if self._performance_config and not self._performance_config.validate():
                self.logger.error("性能配置验证失败")
                valid = False
                
            return valid
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
            
    def update_config(self, config_type: str, updates: Dict[str, Any]) -> bool:
        """更新配置
        
        Args:
            config_type: 配置类型 ('feature', 'search', 'model', 'performance')
            updates: 更新的配置项
            
        Returns:
            bool: 是否成功更新
        """
        try:
            if config_type == 'feature' and self._feature_config:
                current_dict = self._feature_config.to_dict()
                current_dict.update(updates)
                new_config = FeatureConfig.from_dict(current_dict)
                if new_config.validate():
                    self._feature_config = new_config
                    return self.save_feature_config(new_config)
                    
            elif config_type == 'search' and self._search_config:
                current_dict = self._search_config.to_dict()
                current_dict.update(updates)
                new_config = SearchConfig.from_dict(current_dict)
                if new_config.validate():
                    self._search_config = new_config
                    return self.save_search_config(new_config)
                    
            elif config_type == 'model' and self._model_config:
                current_dict = self._model_config.to_dict()
                current_dict.update(updates)
                new_config = ModelConfig.from_dict(current_dict)
                if new_config.validate():
                    self._model_config = new_config
                    return self.save_model_config(new_config)
                    
            elif config_type == 'performance' and self._performance_config:
                current_dict = self._performance_config.to_dict()
                current_dict.update(updates)
                new_config = PerformanceConfig.from_dict(current_dict)
                if new_config.validate():
                    self._performance_config = new_config
                    return self.save_performance_config(new_config)
                    
            return False
            
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
            return False
            
    def get_config(self, config_type: str) -> Optional[Union[FeatureConfig, SearchConfig, ModelConfig, PerformanceConfig]]:
        """获取配置对象
        
        Args:
            config_type: 配置类型
            
        Returns:
            配置对象
        """
        if config_type == 'feature':
            return self._feature_config
        elif config_type == 'search':
            return self._search_config
        elif config_type == 'model':
            return self._model_config
        elif config_type == 'performance':
            return self._performance_config
        else:
            return None
            
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置
        
        Returns:
            Dict[str, Any]: 所有配置的字典表示
        """
        return {
            'feature': self._feature_config.to_dict() if self._feature_config else {},
            'search': self._search_config.to_dict() if self._search_config else {},
            'model': self._model_config.to_dict() if self._model_config else {},
            'performance': self._performance_config.to_dict() if self._performance_config else {}
        }
        
    def reset_to_defaults(self, config_type: Optional[str] = None) -> bool:
        """重置为默认配置
        
        Args:
            config_type: 配置类型，如果为None则重置所有配置
            
        Returns:
            bool: 是否成功重置
        """
        try:
            if config_type is None or config_type == 'feature':
                self._feature_config = FeatureConfig()
                self.save_feature_config(self._feature_config)
                
            if config_type is None or config_type == 'search':
                self._search_config = SearchConfig()
                self.save_search_config(self._search_config)
                
            if config_type is None or config_type == 'model':
                self._model_config = ModelConfig()
                self.save_model_config(self._model_config)
                
            if config_type is None or config_type == 'performance':
                self._performance_config = PerformanceConfig()
                self.save_performance_config(self._performance_config)
                
            self.logger.info(f"配置已重置为默认值: {config_type or 'all'}")
            return True
            
        except Exception as e:
            self.logger.error(f"重置配置失败: {e}")
            return False
            
    def backup_configs(self, backup_dir: Optional[Union[str, Path]] = None) -> bool:
        """备份配置文件
        
        Args:
            backup_dir: 备份目录，如果为None则使用默认备份目录
            
        Returns:
            bool: 是否成功备份
        """
        try:
            if backup_dir is None:
                backup_dir = self.config_dir / 'backups' / datetime.now().strftime('%Y%m%d_%H%M%S')
            else:
                backup_dir = Path(backup_dir)
                
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制配置文件
            for config_name, config_file in self._config_files.items():
                if config_file.exists():
                    backup_file = backup_dir / config_file.name
                    backup_file.write_text(config_file.read_text(), encoding='utf-8')
                    
            self.logger.info(f"配置已备份到: {backup_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"备份配置失败: {e}")
            return False
            
    def _load_config_file(self, config_file: Path) -> Dict[str, Any]:
        """加载配置文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            Dict[str, Any]: 配置数据
        """
        with open(config_file, 'r', encoding='utf-8') as f:
            if config_file.suffix.lower() == '.json':
                return json.load(f)
            else:
                return yaml.safe_load(f)
                
    def _save_config_file(self, config_file: Path, data: Dict[str, Any]) -> None:
        """保存配置文件
        
        Args:
            config_file: 配置文件路径
            data: 配置数据
        """
        with open(config_file, 'w', encoding='utf-8') as f:
            if config_file.suffix.lower() == '.json':
                json.dump(data, f, indent=2, ensure_ascii=False)
            else:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
                
    def _create_default_configs(self) -> None:
        """创建默认配置"""
        self._feature_config = FeatureConfig()
        self._search_config = SearchConfig()
        self._model_config = ModelConfig()
        self._performance_config = PerformanceConfig()
        
        # 保存默认配置
        self.save_all_configs()
        
    @property
    def feature_config(self) -> FeatureConfig:
        """获取特征配置"""
        if self._feature_config is None:
            self._feature_config = self.load_feature_config()
        return self._feature_config
        
    @property
    def search_config(self) -> SearchConfig:
        """获取搜索配置"""
        if self._search_config is None:
            self._search_config = self.load_search_config()
        return self._search_config
        
    @property
    def model_config(self) -> ModelConfig:
        """获取模型配置"""
        if self._model_config is None:
            self._model_config = self.load_model_config()
        return self._model_config
        
    @property
    def performance_config(self) -> PerformanceConfig:
        """获取性能配置"""
        if self._performance_config is None:
            self._performance_config = self.load_performance_config()
        return self._performance_config