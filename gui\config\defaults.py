#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI默认配置模块

该模块定义GUI相关的默认配置。
"""

from .constants import *
from .settings import SettingsData
from .result_config import ViewMode, SortMode, ResultConfig

# 默认UI配置
DEFAULT_UI_CONFIG = {
    "window_size": DEFAULT_WINDOW_SIZE,
    "min_size": MIN_WINDOW_SIZE,
    "font_size": DEFAULT_FONT_SIZE,
    "thumbnail_size": DEFAULT_THUMBNAIL_SIZE,
    "animation_duration": ANIMATION_DURATION,
    "spacing": DEFAULT_SPACING,
    "margin": DEFAULT_MARGIN,
    "theme": LIGHT_THEME
}

# 默认结果配置
DEFAULT_RESULT_CONFIG = ResultConfig(
    view_mode=ViewMode.GRID,
    sort_mode=SortMode.RELEVANCE,
    items_per_page=25,
    thumbnail_size=DEFAULT_THUMBNAIL_SIZE,
    show_details=True,
    show_similarity_scores=True
)

# 默认设置数据
DEFAULT_SETTINGS_DATA = SettingsData(
    language="zh_CN",
    auto_save=True,
    backup_enabled=True,
    backup_interval=30,
    default_similarity_threshold=0.0,
    max_search_results=50,
    search_timeout=30,
    enable_search_history=True,
    max_history_items=1000,
    theme=LIGHT_THEME,
    font_family="Microsoft YaHei",
    font_size=DEFAULT_FONT_SIZE,
    thumbnail_size=DEFAULT_THUMBNAIL_SIZE,
    show_tooltips=True,
    animation_enabled=True,
    use_gpu=True,
    max_threads=0,
    cache_size=512,
    preload_thumbnails=True,
    log_level="INFO",
    debug_mode=False,
    auto_update=True,
    telemetry_enabled=False
)