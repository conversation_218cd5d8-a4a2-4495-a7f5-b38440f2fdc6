#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索历史管理模块

该模块用于记录和管理用户的搜索历史。
"""

import logging
import json
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid

from database.repositories.search_history_repository import SearchHistoryRepository
from database.models import SearchHistory, UserPreference
from utils.log_utils import LoggerMixin
from utils.file_utils import FileManager


class SearchType(Enum):
    """搜索类型"""
    IMAGE_SIMILARITY = "image_similarity"
    TEXT_SEARCH = "text_search"
    CATEGORY_BROWSE = "category_browse"
    TAG_SEARCH = "tag_search"
    ADVANCED_FILTER = "advanced_filter"
    BATCH_SEARCH = "batch_search"


@dataclass
class SearchSession:
    """搜索会话"""
    session_id: str
    user_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    search_count: int = 0
    total_results: int = 0
    searches: List[SearchHistory] = field(default_factory=list)
    
    def add_search(self, search_history: SearchHistory):
        """添加搜索记录
        
        Args:
            search_history: 搜索历史记录
        """
        self.searches.append(search_history)
        self.search_count += 1
        self.total_results += search_history.result_count
    
    def end_session(self):
        """结束会话"""
        self.end_time = datetime.now()
    
    def get_duration(self) -> Optional[timedelta]:
        """获取会话持续时间
        
        Returns:
            Optional[timedelta]: 持续时间
        """
        if self.end_time:
            return self.end_time - self.start_time
        return None


@dataclass
class SearchStatistics:
    """搜索统计信息"""
    total_searches: int = 0
    searches_by_type: Dict[str, int] = field(default_factory=dict)
    searches_by_date: Dict[str, int] = field(default_factory=dict)
    average_results_per_search: float = 0.0
    most_searched_categories: List[tuple] = field(default_factory=list)
    most_searched_tags: List[tuple] = field(default_factory=list)
    search_success_rate: float = 0.0
    average_search_time: float = 0.0


class SearchHistoryManager(LoggerMixin):
    """搜索历史管理器"""
    
    def __init__(self, search_repository: SearchHistoryRepository,
                 max_history_days: int = 90,
                 max_sessions_per_user: int = 100):
        """初始化搜索历史管理器
        
        Args:
            search_repository: 搜索数据仓库
            max_history_days: 最大历史保留天数
            max_sessions_per_user: 每用户最大会话数
        """
        super().__init__()
        
        self.search_repository = search_repository
        self.max_history_days = max_history_days
        self.max_sessions_per_user = max_sessions_per_user
        
        # 当前活跃会话
        self.active_sessions: Dict[str, SearchSession] = {}
        
        # 文件管理器
        self.file_manager = FileManager()
        
        # 自动清理旧记录
        self._cleanup_old_records()
    
    def start_search_session(self, user_id: str) -> str:
        """开始搜索会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: 会话ID
        """
        try:
            session_id = str(uuid.uuid4())
            
            session = SearchSession(
                session_id=session_id,
                user_id=user_id,
                start_time=datetime.now()
            )
            
            self.active_sessions[session_id] = session
            
            self.logger.debug(f"开始搜索会话: {session_id} (用户: {user_id})")
            return session_id
            
        except Exception as e:
            self.logger.error(f"开始搜索会话失败: {e}")
            return ""
    
    def end_search_session(self, session_id: str) -> bool:
        """结束搜索会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 是否成功结束
        """
        try:
            if session_id in self.active_sessions:
                session = self.active_sessions[session_id]
                session.end_session()
                
                # 保存会话统计信息
                self._save_session_statistics(session)
                
                # 移除活跃会话
                del self.active_sessions[session_id]
                
                self.logger.debug(f"结束搜索会话: {session_id}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"结束搜索会话失败: {e}")
            return False
    
    def record_search(self, user_id: str, search_type: SearchType,
                     query_data: Dict[str, Any], result_count: int,
                     search_time: float, session_id: Optional[str] = None,
                     metadata: Optional[Dict[str, Any]] = None) -> Optional[int]:
        """记录搜索历史
        
        Args:
            user_id: 用户ID
            search_type: 搜索类型
            query_data: 查询数据
            result_count: 结果数量
            search_time: 搜索耗时
            session_id: 会话ID
            metadata: 元数据
            
        Returns:
            Optional[int]: 搜索历史记录ID
        """
        try:
            if not self.search_repository:
                self.logger.warning("搜索仓库未初始化，无法记录搜索历史")
                return None
                
            # 创建搜索历史记录
            search_history = SearchHistory(
                user_id=user_id,
                search_type=search_type.value,
                query_data=json.dumps(query_data, ensure_ascii=False),
                result_count=result_count,
                search_time=search_time,
                session_id=session_id,
                metadata=json.dumps(metadata or {}, ensure_ascii=False),
                created_at=datetime.now()
            )
            
            # 从query_data中提取特定字段
            if search_type == SearchType.IMAGE_SIMILARITY and 'query_image_path' in query_data:
                search_history.query_image_path = query_data['query_image_path']
            
            if 'text_query' in query_data:
                search_history.query_text = query_data['text_query']
            
            # 保存到数据库
            # 将SearchHistory对象转换为add_search_record方法所需的参数
            search_id = self.search_repository.add_search_record(
                search_query=search_history.query_text or '',
                search_type=search_history.search_type,
                search_params=json.loads(search_history.query_data) if search_history.query_data else {},
                result_count=search_history.result_count,
                execution_time=search_history.search_time
            )
            
            if search_id:
                search_history.id = search_id
                
                # 添加到活跃会话
                if session_id and session_id in self.active_sessions:
                    self.active_sessions[session_id].add_search(search_history)
                
                self.logger.debug(f"记录搜索历史: {search_id}")
                return search_id
            
            return None
            
        except Exception as e:
            self.logger.error(f"记录搜索历史失败: {e}")
            return None
    
    def get_user_search_history(self, user_id: str, limit: int = 50,
                               search_type: Optional[SearchType] = None,
                               days: Optional[int] = None) -> List[SearchHistory]:
        """获取用户搜索历史
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            search_type: 搜索类型过滤
            days: 天数过滤
            
        Returns:
            List[SearchHistory]: 搜索历史列表
        """
        try:
            if not self.search_repository:
                self.logger.warning("搜索仓库未初始化，无法获取用户搜索历史")
                return []
                
            # 计算时间范围
            start_time = None
            if days:
                start_time = datetime.now() - timedelta(days=days)
            
            # 获取搜索历史
            # 使用search_history方法替代get_search_history_by_user
            query = f"%{user_id}%"  # 假设user_id存储在metadata或其他字段中
            search_histories = self.search_repository.search_history(
                query=query,
                search_type=search_type.value if search_type else None,
                start_date=start_time,
                limit=limit
            )
            
            # 转换为SearchHistory对象
            search_histories = [self._dict_to_search_history(record) for record in search_histories]
            
            return search_histories
            
        except Exception as e:
            self.logger.error(f"获取用户搜索历史失败: {e}")
            return []
    
    def _dict_to_search_history(self, record: Dict[str, Any]) -> SearchHistory:
        """将字典转换为SearchHistory对象
        
        Args:
            record: 搜索记录字典
            
        Returns:
            SearchHistory: 搜索历史对象
        """
        try:
            # 创建SearchHistory对象
            search_history = SearchHistory(
                id=record.get('id'),
                user_id=record.get('user_id', ''),
                search_type=record.get('search_type', ''),
                query_text=record.get('search_query', ''),
                query_data=json.dumps(record.get('search_params', {})),
                result_count=record.get('result_count', 0),
                search_time=record.get('execution_time', 0.0),
                created_at=record.get('search_time'),
                session_id=record.get('session_id', '')
            )
            return search_history
        except Exception as e:
            self.logger.error(f"转换搜索记录字典为对象失败: {e}")
            # 返回一个空的SearchHistory对象
            return SearchHistory()
    
    def get_session_history(self, session_id: str) -> List[SearchHistory]:
        """获取会话搜索历史
        
        Args:
            session_id: 会话ID
            
        Returns:
            List[SearchHistory]: 搜索历史列表
        """
        try:
            # 先检查活跃会话
            if session_id in self.active_sessions:
                return self.active_sessions[session_id].searches.copy()
            
            if not self.search_repository:
                self.logger.warning("搜索仓库未初始化，无法获取会话搜索历史")
                return []
            
            # 从数据库获取
            # 使用search_history方法替代get_search_history_by_session
            query = f"%{session_id}%"  # 假设session_id存储在metadata或其他字段中
            search_histories = self.search_repository.search_history(
                query=query,
                limit=100  # 设置一个合理的限制
            )
            
            # 转换为SearchHistory对象
            search_histories = [self._dict_to_search_history(record) for record in search_histories]
            
            return search_histories
            
        except Exception as e:
            self.logger.error(f"获取会话搜索历史失败: {e}")
            return []
    
    def get_popular_searches(self, days: int = 7, limit: int = 10) -> List[Dict[str, Any]]:
        """获取热门搜索
        
        Args:
            days: 统计天数
            limit: 限制数量
            
        Returns:
            List[Dict[str, Any]]: 热门搜索列表
        """
        try:
            if not self.search_repository:
                self.logger.warning("搜索仓库未初始化，无法获取热门搜索")
                return []
                
            start_time = datetime.now() - timedelta(days=days)
            
            # 获取时间范围内的搜索历史
            search_histories = self.search_repository.get_search_history_by_time_range(
                start_time=start_time,
                end_time=datetime.now()
            )
            
            # 统计查询频率
            query_counts = {}
            
            for search in search_histories:
                try:
                    query_data = json.loads(search.query_data)
                    
                    # 提取关键查询信息
                    key = self._extract_search_key(search.search_type, query_data)
                    
                    if key:
                        if key not in query_counts:
                            query_counts[key] = {
                                'query': key,
                                'count': 0,
                                'search_type': search.search_type,
                                'avg_results': 0,
                                'total_results': 0
                            }
                        
                        query_counts[key]['count'] += 1
                        query_counts[key]['total_results'] += search.result_count
                        query_counts[key]['avg_results'] = (
                            query_counts[key]['total_results'] / query_counts[key]['count']
                        )
                
                except Exception as e:
                    self.logger.warning(f"解析查询数据失败: {e}")
                    continue
            
            # 排序并返回前N个
            popular_searches = sorted(
                query_counts.values(),
                key=lambda x: x['count'],
                reverse=True
            )[:limit]
            
            return popular_searches
            
        except Exception as e:
            self.logger.error(f"获取热门搜索失败: {e}")
            return []
    
    def _extract_search_key(self, search_type: str, query_data: Dict[str, Any]) -> Optional[str]:
        """提取搜索关键信息
        
        Args:
            search_type: 搜索类型
            query_data: 查询数据
            
        Returns:
            Optional[str]: 搜索关键信息
        """
        try:
            if search_type == SearchType.TEXT_SEARCH.value:
                return query_data.get('query', '')
            elif search_type == SearchType.CATEGORY_BROWSE.value:
                return query_data.get('category', '')
            elif search_type == SearchType.TAG_SEARCH.value:
                tags = query_data.get('tags', [])
                return ', '.join(tags) if tags else None
            elif search_type == SearchType.IMAGE_SIMILARITY.value:
                # 对于图片相似度搜索，使用图片路径的文件名
                image_path = query_data.get('query_image_path', '')
                if image_path:
                    return Path(image_path).name
                return None
            else:
                return str(query_data)
                
        except Exception:
            return None
    
    def get_search_statistics(self, user_id: Optional[str] = None,
                            days: int = 30) -> SearchStatistics:
        """获取搜索统计信息
        
        Args:
            user_id: 用户ID，None表示全局统计
            days: 统计天数
            
        Returns:
            SearchStatistics: 搜索统计信息
        """
        try:
            start_time = datetime.now() - timedelta(days=days)
            
            # 获取搜索历史
            if user_id:
                search_histories = self.search_repository.get_search_history_by_user(
                    user_id=user_id,
                    start_time=start_time
                )
            else:
                search_histories = self.search_repository.get_search_history_by_time_range(
                    start_time=start_time,
                    end_time=datetime.now()
                )
            
            # 计算统计信息
            stats = SearchStatistics()
            stats.total_searches = len(search_histories)
            
            if not search_histories:
                return stats
            
            # 按类型统计
            type_counts = {}
            date_counts = {}
            total_results = 0
            total_search_time = 0.0
            successful_searches = 0
            category_counts = {}
            tag_counts = {}
            
            for search in search_histories:
                # 按类型统计
                search_type = search.search_type
                type_counts[search_type] = type_counts.get(search_type, 0) + 1
                
                # 按日期统计
                date_key = search.created_at.strftime('%Y-%m-%d')
                date_counts[date_key] = date_counts.get(date_key, 0) + 1
                
                # 结果统计
                total_results += search.result_count
                total_search_time += search.search_time
                
                if search.result_count > 0:
                    successful_searches += 1
                
                # 解析查询数据统计类别和标签
                try:
                    query_data = json.loads(search.query_data)
                    
                    if 'category' in query_data:
                        category = query_data['category']
                        category_counts[category] = category_counts.get(category, 0) + 1
                    
                    if 'tags' in query_data:
                        tags = query_data['tags']
                        if isinstance(tags, list):
                            for tag in tags:
                                tag_counts[tag] = tag_counts.get(tag, 0) + 1
                
                except Exception:
                    continue
            
            # 填充统计信息
            stats.searches_by_type = type_counts
            stats.searches_by_date = date_counts
            stats.average_results_per_search = total_results / stats.total_searches
            stats.search_success_rate = successful_searches / stats.total_searches
            stats.average_search_time = total_search_time / stats.total_searches
            
            # 最热门的类别和标签
            stats.most_searched_categories = sorted(
                category_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]
            
            stats.most_searched_tags = sorted(
                tag_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取搜索统计信息失败: {e}")
            return SearchStatistics()
    
    def save_user_preference(self, user_id: str, preference_key: str,
                           preference_value: Any) -> bool:
        """保存用户偏好
        
        Args:
            user_id: 用户ID
            preference_key: 偏好键
            preference_value: 偏好值
            
        Returns:
            bool: 是否保存成功
        """
        try:
            user_preference = UserPreference(
                user_id=user_id,
                preference_key=preference_key,
                preference_value=json.dumps(preference_value, ensure_ascii=False),
                updated_at=datetime.now()
            )
            
            success = self.search_repository.upsert_user_preference(user_preference)
            
            if success:
                self.logger.debug(f"保存用户偏好: {user_id}.{preference_key}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"保存用户偏好失败: {e}")
            return False
    
    def get_user_preference(self, user_id: str, preference_key: str,
                          default_value: Any = None) -> Any:
        """获取用户偏好
        
        Args:
            user_id: 用户ID
            preference_key: 偏好键
            default_value: 默认值
            
        Returns:
            Any: 偏好值
        """
        try:
            user_preference = self.search_repository.get_user_preference(
                user_id=user_id,
                preference_key=preference_key
            )
            
            if user_preference:
                return json.loads(user_preference.preference_value)
            
            return default_value
            
        except Exception as e:
            self.logger.error(f"获取用户偏好失败: {e}")
            return default_value
    
    def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """获取用户所有偏好
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 偏好字典
        """
        try:
            user_preferences = self.search_repository.get_user_preferences(user_id)
            
            preferences = {}
            for pref in user_preferences:
                try:
                    preferences[pref.preference_key] = json.loads(pref.preference_value)
                except Exception as e:
                    self.logger.warning(f"解析偏好值失败 {pref.preference_key}: {e}")
                    preferences[pref.preference_key] = pref.preference_value
            
            return preferences
            
        except Exception as e:
            self.logger.error(f"获取用户偏好失败: {e}")
            return {}
    
    def delete_user_preference(self, user_id: str, preference_key: str) -> bool:
        """删除用户偏好
        
        Args:
            user_id: 用户ID
            preference_key: 偏好键
            
        Returns:
            bool: 是否删除成功
        """
        try:
            success = self.search_repository.delete_user_preference(
                user_id=user_id,
                preference_key=preference_key
            )
            
            if success:
                self.logger.debug(f"删除用户偏好: {user_id}.{preference_key}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"删除用户偏好失败: {e}")
            return False
    
    def export_search_history(self, user_id: str, file_path: str,
                            days: Optional[int] = None) -> bool:
        """导出搜索历史
        
        Args:
            user_id: 用户ID
            file_path: 导出文件路径
            days: 天数限制
            
        Returns:
            bool: 是否导出成功
        """
        try:
            # 获取搜索历史
            search_histories = self.get_user_search_history(
                user_id=user_id,
                limit=10000,
                days=days
            )
            
            # 转换为可序列化的格式
            export_data = {
                'user_id': user_id,
                'export_time': datetime.now().isoformat(),
                'total_searches': len(search_histories),
                'searches': []
            }
            
            for search in search_histories:
                search_data = {
                    'id': search.id,
                    'search_type': search.search_type,
                    'query_data': json.loads(search.query_data),
                    'result_count': search.result_count,
                    'search_time': search.search_time,
                    'session_id': search.session_id,
                    'metadata': json.loads(search.metadata) if search.metadata else {},
                    'created_at': search.created_at.isoformat()
                }
                export_data['searches'].append(search_data)
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"搜索历史导出成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出搜索历史失败: {e}")
            return False
    
    def _cleanup_old_records(self):
        """清理旧记录"""
        try:
            if not self.search_repository:
                self.logger.warning("搜索仓库未初始化，跳过清理旧记录")
                return
                
            cutoff_date = datetime.now() - timedelta(days=self.max_history_days)
            
            deleted_count = self.search_repository.delete_old_searches(self.max_history_days)
            
            if deleted_count > 0:
                self.logger.info(f"清理旧搜索记录: {deleted_count} 条")
                
        except Exception as e:
            self.logger.error(f"清理旧记录失败: {e}")
    
    def _save_session_statistics(self, session: SearchSession):
        """保存会话统计信息
        
        Args:
            session: 搜索会话
        """
        try:
            # 保存会话统计为用户偏好
            session_stats = {
                'session_id': session.session_id,
                'start_time': session.start_time.isoformat(),
                'end_time': session.end_time.isoformat() if session.end_time else None,
                'search_count': session.search_count,
                'total_results': session.total_results,
                'duration_seconds': session.get_duration().total_seconds() if session.get_duration() else 0
            }
            
            # 保存最近会话统计
            recent_sessions_key = f"recent_sessions"
            recent_sessions = self.get_user_preference(
                session.user_id, recent_sessions_key, []
            )
            
            # 添加新会话，保持最近的会话数量限制
            recent_sessions.insert(0, session_stats)
            recent_sessions = recent_sessions[:self.max_sessions_per_user]
            
            self.save_user_preference(
                session.user_id, recent_sessions_key, recent_sessions
            )
            
        except Exception as e:
            self.logger.error(f"保存会话统计信息失败: {e}")
    
    def get_active_sessions(self) -> Dict[str, SearchSession]:
        """获取活跃会话
        
        Returns:
            Dict[str, SearchSession]: 活跃会话字典
        """
        return self.active_sessions.copy()
    
    def get_recent_searches(self, user_id: Optional[str] = None, days: int = 7, limit: int = 20) -> List[SearchHistory]:
        """获取最近的搜索历史
        
        Args:
            user_id: 用户ID
            days: 天数
            limit: 限制数量
            
        Returns:
            List[SearchHistory]: 搜索历史列表
        """
        try:
            return self.search_repository.get_recent_searches(user_id=user_id, days=days, limit=limit)
        except Exception as e:
            self.logger.error(f"获取最近搜索历史失败: {e}")
            return []
    
    def cleanup_inactive_sessions(self, timeout_minutes: int = 30):
        """清理不活跃的会话
        
        Args:
            timeout_minutes: 超时分钟数
        """
        try:
            timeout_threshold = datetime.now() - timedelta(minutes=timeout_minutes)
            
            inactive_sessions = []
            for session_id, session in self.active_sessions.items():
                # 检查最后一次搜索时间
                last_search_time = session.start_time
                if session.searches:
                    last_search_time = max(
                        search.created_at for search in session.searches
                    )
                
                if last_search_time < timeout_threshold:
                    inactive_sessions.append(session_id)
            
            # 结束不活跃的会话
            for session_id in inactive_sessions:
                self.end_search_session(session_id)
            
            if inactive_sessions:
                self.logger.info(f"清理不活跃会话: {len(inactive_sessions)} 个")
                
        except Exception as e:
            self.logger.error(f"清理不活跃会话失败: {e}")
    
    def save(self):
        """保存搜索历史管理器状态
        
        该方法用于保存当前状态，主要是结束所有活跃会话
        """
        try:
            # 结束所有活跃会话
            active_session_ids = list(self.active_sessions.keys())
            for session_id in active_session_ids:
                self.end_search_session(session_id)
            
            self.logger.info(f"搜索历史管理器状态已保存，结束了 {len(active_session_ids)} 个活跃会话")
            
        except Exception as e:
            self.logger.error(f"保存搜索历史管理器状态失败: {e}")