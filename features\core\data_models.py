"""核心数据模型

定义特征管理器使用的核心数据结构。
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass


@dataclass
class FeatureIndexInfo:
    """特征索引信息"""
    total_features: int = 0
    index_size_mb: float = 0.0
    last_updated: Optional[str] = None
    model_name: Optional[str] = None
    feature_dim: Optional[int] = None


@dataclass
class SearchRequest:
    """搜索请求"""
    query_image_path: Optional[str] = None
    query_features: Optional[Any] = None
    query_id: Optional[str] = None
    top_k: int = 20
    similarity_threshold: float = 0.0
    categories: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    feature_weights: Optional[Dict[str, float]] = None
    similarity_metric: Optional[str] = None
    use_weighted_search: bool = False
    use_faiss: bool = True


@dataclass
class SearchResponse:
    """搜索响应"""
    results: List[Dict[str, Any]]
    total_found: int
    search_time: float
    query_info: Dict[str, Any]
    success: bool = True
    error_message: Optional[str] = None


@dataclass
class ProcessingResult:
    """处理结果"""
    success: bool
    processed: int
    failed: int
    task_id: Optional[str] = None
    error: Optional[str] = None