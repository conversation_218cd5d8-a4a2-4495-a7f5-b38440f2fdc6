"""特征序列化模块

提供特征数据的序列化和反序列化功能，支持多种格式。
"""

import pickle
import json
import numpy as np
from typing import Any, Optional, Union, Dict
from pathlib import Path
import logging
from enum import Enum


class SerializationFormat(Enum):
    """序列化格式枚举"""
    PICKLE = "pickle"
    NUMPY = "numpy"
    JSON = "json"
    BINARY = "binary"


class FeatureSerializer:
    """特征序列化器
    
    提供特征数据的序列化和反序列化功能，支持：
    - Pickle格式（默认）
    - NumPy二进制格式
    - JSON格式（用于元数据）
    - 原始二进制格式
    """
    
    def __init__(self):
        """初始化序列化器"""
        self.logger = logging.getLogger(__name__)
        
    def serialize_features(self, features: np.ndarray, 
                          format_type: SerializationFormat = SerializationFormat.PICKLE) -> Optional[bytes]:
        """序列化特征数据
        
        Args:
            features: 特征数组
            format_type: 序列化格式
            
        Returns:
            Optional[bytes]: 序列化后的字节数据，失败返回None
        """
        try:
            # 验证输入
            if features is None:
                self.logger.error("特征向量不能为空")
                return None
            
            if not isinstance(features, np.ndarray):
                try:
                    features = np.array(features)
                except Exception as e:
                    self.logger.error(f"无法将输入转换为numpy数组: {e}")
                    return None
            
            if features.size == 0:
                self.logger.error("特征向量不能为空数组")
                return None
            
            # 检查异常值
            if np.isnan(features).any():
                self.logger.warning("特征向量包含NaN值，将被替换为0")
                features = np.nan_to_num(features, nan=0.0)
            
            if np.isinf(features).any():
                self.logger.warning("特征向量包含无穷大值，将被截断")
                features = np.nan_to_num(features, posinf=1.0, neginf=-1.0)
            
            # 确保特征是一维数组
            if features.ndim > 1:
                features = features.flatten()
                
            if format_type == SerializationFormat.PICKLE:
                return pickle.dumps(features)
            elif format_type == SerializationFormat.NUMPY:
                return features.tobytes()
            elif format_type == SerializationFormat.BINARY:
                return features.astype(np.float32).tobytes()
            elif format_type == SerializationFormat.JSON:
                # JSON格式主要用于元数据，特征数据转为列表
                return json.dumps(features.tolist()).encode('utf-8')
            else:
                self.logger.error(f"不支持的序列化格式: {format_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"序列化特征失败: {e}")
            return None
            
    def deserialize_features(self, data: bytes, 
                           format_type: SerializationFormat = SerializationFormat.PICKLE,
                           dtype: np.dtype = np.float32,
                           shape: Optional[tuple] = None) -> Optional[np.ndarray]:
        """反序列化特征数据
        
        Args:
            data: 序列化的字节数据
            format_type: 序列化格式
            dtype: 数据类型（用于numpy和binary格式）
            shape: 数组形状（用于numpy和binary格式）
            
        Returns:
            Optional[np.ndarray]: 反序列化后的特征数组，失败返回None
        """
        try:
            # 验证输入
            if data is None:
                self.logger.error("序列化数据不能为空")
                return None
            
            if not isinstance(data, bytes):
                if isinstance(data, str):
                    try:
                        data = data.encode('utf-8')
                    except Exception as e:
                        self.logger.error(f"无法将字符串转换为bytes: {e}")
                        return None
                else:
                    self.logger.error("序列化数据必须是bytes类型")
                    return None
            
            if len(data) == 0:
                self.logger.error("序列化数据不能为空")
                return None
                
            if format_type == SerializationFormat.PICKLE:
                features = pickle.loads(data)
                if not isinstance(features, np.ndarray):
                    try:
                        features = np.array(features)
                    except Exception as e:
                        self.logger.error(f"无法将反序列化结果转换为numpy数组: {e}")
                        return None
                
            elif format_type == SerializationFormat.NUMPY:
                features = np.frombuffer(data, dtype=dtype)
                if shape:
                    features = features.reshape(shape)
                
            elif format_type == SerializationFormat.BINARY:
                features = np.frombuffer(data, dtype=dtype)
                if shape:
                    features = features.reshape(shape)
                
            elif format_type == SerializationFormat.JSON:
                json_str = data.decode('utf-8')
                features_list = json.loads(json_str)
                features = np.array(features_list, dtype=dtype)
                
            else:
                self.logger.error(f"不支持的反序列化格式: {format_type}")
                return None
            
            # 验证反序列化结果
            if features is None:
                self.logger.error("反序列化结果为空")
                return None
            
            if features.size == 0:
                self.logger.error("反序列化后的特征向量为空")
                return None
            
            # 检查异常值
            if np.isnan(features).any():
                self.logger.warning("反序列化的特征向量包含NaN值，将被替换为0")
                features = np.nan_to_num(features, nan=0.0)
            
            if np.isinf(features).any():
                self.logger.warning("反序列化的特征向量包含无穷大值，将被截断")
                features = np.nan_to_num(features, posinf=1.0, neginf=-1.0)
            
            # 确保是一维数组
            if features.ndim > 1:
                features = features.flatten()
            
            return features
                
        except Exception as e:
            self.logger.error(f"反序列化特征失败: {e}")
            return None
            
    def serialize_metadata(self, metadata: Dict[str, Any]) -> Optional[bytes]:
        """序列化元数据
        
        Args:
            metadata: 元数据字典
            
        Returns:
            Optional[bytes]: 序列化后的字节数据
        """
        try:
            # 验证输入
            if metadata is None:
                metadata = {}
            
            if not isinstance(metadata, dict):
                self.logger.error("元数据必须是字典类型")
                return None
            
            # 清理元数据，移除不可序列化的对象
            cleaned_metadata = self._clean_metadata(metadata)
            
            # 序列化为JSON
            try:
                json_str = json.dumps(cleaned_metadata, ensure_ascii=False, separators=(',', ':'))
                return json_str.encode('utf-8')
            except (TypeError, ValueError) as e:
                self.logger.warning(f"元数据JSON序列化失败，使用pickle: {e}")
                # 如果JSON序列化失败，使用pickle
                return pickle.dumps(cleaned_metadata)
                
        except Exception as e:
            self.logger.error(f"序列化元数据失败: {e}")
            # 返回空字典的序列化结果
            return json.dumps({}).encode('utf-8')
            
    def deserialize_metadata(self, data: bytes) -> Optional[Dict[str, Any]]:
        """反序列化元数据
        
        Args:
            data: 序列化的字节数据
            
        Returns:
            Optional[Dict[str, Any]]: 反序列化后的元数据字典
        """
        try:
            # 验证输入
            if data is None or len(data) == 0:
                return {}
            
            if not isinstance(data, bytes):
                if isinstance(data, str):
                    try:
                        data = data.encode('utf-8')
                    except Exception as e:
                        self.logger.warning(f"字符串转换为bytes失败: {e}")
                        return {}
                else:
                    self.logger.warning("元数据不是bytes类型")
                    return {}
            
            # 尝试JSON反序列化
            try:
                json_str = data.decode('utf-8')
                metadata = json.loads(json_str)
                if isinstance(metadata, dict):
                    return metadata
                else:
                    self.logger.warning("JSON反序列化结果不是字典类型")
                    return {}
            except (UnicodeDecodeError, json.JSONDecodeError):
                # 如果JSON反序列化失败，尝试pickle
                try:
                    metadata = pickle.loads(data)
                    if isinstance(metadata, dict):
                        return metadata
                    else:
                        self.logger.warning("pickle反序列化结果不是字典类型")
                        return {}
                except Exception as e:
                    self.logger.warning(f"pickle反序列化也失败: {e}")
                    return {}
                    
        except Exception as e:
            self.logger.error(f"反序列化元数据失败: {e}")
            return {}
            
    def save_features_to_file(self, features: np.ndarray, file_path: Path,
                             format_type: SerializationFormat = SerializationFormat.NUMPY) -> bool:
        """将特征保存到文件
        
        Args:
            features: 特征数组
            file_path: 保存路径
            format_type: 保存格式
            
        Returns:
            bool: 是否成功保存
        """
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            if format_type == SerializationFormat.NUMPY:
                np.save(file_path, features)
            elif format_type == SerializationFormat.PICKLE:
                with open(file_path, 'wb') as f:
                    pickle.dump(features, f)
            elif format_type == SerializationFormat.BINARY:
                with open(file_path, 'wb') as f:
                    f.write(features.astype(np.float32).tobytes())
            elif format_type == SerializationFormat.JSON:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(features.tolist(), f, ensure_ascii=False, indent=2)
            else:
                self.logger.error(f"不支持的文件保存格式: {format_type}")
                return False
                
            self.logger.debug(f"特征保存成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存特征到文件失败: {e}")
            return False
            
    def load_features_from_file(self, file_path: Path,
                               format_type: SerializationFormat = SerializationFormat.NUMPY,
                               dtype: np.dtype = np.float32,
                               shape: Optional[tuple] = None) -> Optional[np.ndarray]:
        """从文件加载特征
        
        Args:
            file_path: 文件路径
            format_type: 文件格式
            dtype: 数据类型（用于binary格式）
            shape: 数组形状（用于binary格式）
            
        Returns:
            Optional[np.ndarray]: 加载的特征数组
        """
        try:
            if not file_path.exists():
                self.logger.error(f"文件不存在: {file_path}")
                return None
                
            if format_type == SerializationFormat.NUMPY:
                return np.load(file_path)
            elif format_type == SerializationFormat.PICKLE:
                with open(file_path, 'rb') as f:
                    return pickle.load(f)
            elif format_type == SerializationFormat.BINARY:
                with open(file_path, 'rb') as f:
                    data = f.read()
                    features = np.frombuffer(data, dtype=dtype)
                    if shape:
                        features = features.reshape(shape)
                    return features
            elif format_type == SerializationFormat.JSON:
                with open(file_path, 'r', encoding='utf-8') as f:
                    features_list = json.load(f)
                    return np.array(features_list, dtype=dtype)
            else:
                self.logger.error(f"不支持的文件加载格式: {format_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"从文件加载特征失败: {e}")
            return None
            
    def get_serialized_size(self, features: np.ndarray,
                           format_type: SerializationFormat = SerializationFormat.PICKLE) -> int:
        """获取序列化后的数据大小
        
        Args:
            features: 特征数组
            format_type: 序列化格式
            
        Returns:
            int: 序列化后的字节数，失败返回-1
        """
        try:
            serialized_data = self.serialize_features(features, format_type)
            return len(serialized_data) if serialized_data else -1
        except Exception as e:
            self.logger.error(f"获取序列化大小失败: {e}")
            return -1
            
    def validate_features(self, features: np.ndarray) -> bool:
        """验证特征数据的有效性
        
        Args:
            features: 特征数组
            
        Returns:
            bool: 是否有效
        """
        try:
            if not isinstance(features, np.ndarray):
                return False
                
            if len(features) == 0:
                return False
                
            if np.isnan(features).any() or np.isinf(features).any():
                return False
                
            return True
            
        except Exception:
            return False
    
    def _clean_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """清理元数据，移除不可序列化的对象
        
        Args:
            metadata: 原始元数据
            
        Returns:
            Dict[str, Any]: 清理后的元数据
        """
        cleaned = {}
        
        for key, value in metadata.items():
            try:
                # 检查键是否为字符串
                if not isinstance(key, str):
                    key = str(key)
                
                # 处理不同类型的值
                if value is None:
                    cleaned[key] = None
                elif isinstance(value, (str, int, float, bool)):
                    cleaned[key] = value
                elif isinstance(value, (list, tuple)):
                    # 递归清理列表/元组
                    cleaned[key] = [self._clean_value(item) for item in value]
                elif isinstance(value, dict):
                    # 递归清理字典
                    cleaned[key] = self._clean_metadata(value)
                elif isinstance(value, np.ndarray):
                    # 将numpy数组转换为列表
                    cleaned[key] = value.tolist()
                else:
                    # 尝试转换为字符串
                    try:
                        cleaned[key] = str(value)
                    except Exception:
                        self.logger.warning(f"无法序列化元数据项 {key}，跳过")
                        continue
                        
            except Exception as e:
                self.logger.warning(f"清理元数据项 {key} 失败: {e}")
                continue
        
        return cleaned
    
    def _clean_value(self, value):
        """清理单个值"""
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            return value
        elif isinstance(value, (list, tuple)):
            return [self._clean_value(item) for item in value]
        elif isinstance(value, dict):
            return self._clean_metadata(value)
        elif isinstance(value, np.ndarray):
            return value.tolist()
        else:
            try:
                return str(value)
            except Exception:
                return None