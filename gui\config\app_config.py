#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序配置管理

该模块提供应用程序级别的配置管理功能。
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

from PyQt6.QtCore import QSettings, QStandardPaths


@dataclass
class WindowConfig:
    """窗口配置"""
    width: int = 1400
    height: int = 900
    x: int = -1
    y: int = -1
    maximized: bool = False
    fullscreen: bool = False
    toolbar_visible: bool = True
    statusbar_visible: bool = True


@dataclass
class DatabaseConfig:
    """数据库配置"""
    path: str = "./data/fabric_search.db"  # 设置默认数据库路径
    auto_backup: bool = True
    backup_interval: int = 24  # 小时
    max_backups: int = 5


@dataclass
class SearchConfig:
    """搜索配置"""
    max_results: int = 100
    similarity_threshold: float = 0.5
    enable_cache: bool = True
    cache_size: int = 1000
    search_timeout: int = 30  # 秒


@dataclass
class UIConfig:
    """UI配置"""
    theme: str = "light"
    font_family: str = "Microsoft YaHei"
    font_size: int = 9
    language: str = "zh_CN"
    animation_enabled: bool = True


class AppConfig:
    """应用程序配置管理器"""
    
    def __init__(self):
        self.settings = QSettings("FabricSearch", "FabricSearchV2")
        
        # 配置文件路径
        self.config_dir = Path(QStandardPaths.writableLocation(
            QStandardPaths.StandardLocation.AppConfigLocation
        )) / "FabricSearch"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.config_file = self.config_dir / "config.json"
        
        # 默认配置
        self.window = WindowConfig()
        self.database = DatabaseConfig()
        self.search = SearchConfig()
        self.ui = UIConfig()
        
        # 加载配置
        self.load()
    
    def load(self):
        """加载配置"""
        try:
            # 从QSettings加载
            self._load_from_qsettings()
            
            # 从JSON文件加载（如果存在）
            if self.config_file.exists():
                self._load_from_json()
                
        except Exception as e:
            print(f"加载配置失败: {e}")
    
    def save(self):
        """保存配置"""
        try:
            # 保存到QSettings
            self._save_to_qsettings()
            
            # 保存到JSON文件
            self._save_to_json()
            
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def _load_from_qsettings(self):
        """从QSettings加载配置"""
        # 窗口配置
        self.window.width = self.settings.value("window/width", self.window.width, int)
        self.window.height = self.settings.value("window/height", self.window.height, int)
        self.window.x = self.settings.value("window/x", self.window.x, int)
        self.window.y = self.settings.value("window/y", self.window.y, int)
        self.window.maximized = self.settings.value("window/maximized", self.window.maximized, bool)
        self.window.fullscreen = self.settings.value("window/fullscreen", self.window.fullscreen, bool)
        self.window.toolbar_visible = self.settings.value("window/toolbar_visible", self.window.toolbar_visible, bool)
        self.window.statusbar_visible = self.settings.value("window/statusbar_visible", self.window.statusbar_visible, bool)
        
        # 数据库配置 - 确保路径不为空
        db_path = self.settings.value("database/path", self.database.path, str)
        self.database.path = db_path if db_path else self.database.path
        self.database.auto_backup = self.settings.value("database/auto_backup", self.database.auto_backup, bool)
        self.database.backup_interval = self.settings.value("database/backup_interval", self.database.backup_interval, int)
        self.database.max_backups = self.settings.value("database/max_backups", self.database.max_backups, int)
        
        # 搜索配置
        self.search.max_results = self.settings.value("search/max_results", self.search.max_results, int)
        self.search.similarity_threshold = self.settings.value("search/similarity_threshold", self.search.similarity_threshold, float)
        self.search.enable_cache = self.settings.value("search/enable_cache", self.search.enable_cache, bool)
        self.search.cache_size = self.settings.value("search/cache_size", self.search.cache_size, int)
        self.search.search_timeout = self.settings.value("search/search_timeout", self.search.search_timeout, int)
        
        # UI配置
        self.ui.theme = self.settings.value("ui/theme", self.ui.theme, str)
        self.ui.font_family = self.settings.value("ui/font_family", self.ui.font_family, str)
        self.ui.font_size = self.settings.value("ui/font_size", self.ui.font_size, int)
        self.ui.language = self.settings.value("ui/language", self.ui.language, str)
        self.ui.animation_enabled = self.settings.value("ui/animation_enabled", self.ui.animation_enabled, bool)
    
    def _save_to_qsettings(self):
        """保存配置到QSettings"""
        # 窗口配置
        self.settings.setValue("window/width", self.window.width)
        self.settings.setValue("window/height", self.window.height)
        self.settings.setValue("window/x", self.window.x)
        self.settings.setValue("window/y", self.window.y)
        self.settings.setValue("window/maximized", self.window.maximized)
        self.settings.setValue("window/fullscreen", self.window.fullscreen)
        self.settings.setValue("window/toolbar_visible", self.window.toolbar_visible)
        self.settings.setValue("window/statusbar_visible", self.window.statusbar_visible)
        
        # 数据库配置
        self.settings.setValue("database/path", self.database.path)
        self.settings.setValue("database/auto_backup", self.database.auto_backup)
        self.settings.setValue("database/backup_interval", self.database.backup_interval)
        self.settings.setValue("database/max_backups", self.database.max_backups)
        
        # 搜索配置
        self.settings.setValue("search/max_results", self.search.max_results)
        self.settings.setValue("search/similarity_threshold", self.search.similarity_threshold)
        self.settings.setValue("search/enable_cache", self.search.enable_cache)
        self.settings.setValue("search/cache_size", self.search.cache_size)
        self.settings.setValue("search/search_timeout", self.search.search_timeout)
        
        # UI配置
        self.settings.setValue("ui/theme", self.ui.theme)
        self.settings.setValue("ui/font_family", self.ui.font_family)
        self.settings.setValue("ui/font_size", self.ui.font_size)
        self.settings.setValue("ui/language", self.ui.language)
        self.settings.setValue("ui/animation_enabled", self.ui.animation_enabled)
        
        self.settings.sync()
    
    def _load_from_json(self):
        """从JSON文件加载配置"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'window' in data:
            self.window = WindowConfig(**data['window'])
        if 'database' in data:
            self.database = DatabaseConfig(**data['database'])
        if 'search' in data:
            self.search = SearchConfig(**data['search'])
        if 'ui' in data:
            self.ui = UIConfig(**data['ui'])
    
    def _save_to_json(self):
        """保存配置到JSON文件"""
        data = {
            'window': asdict(self.window),
            'database': asdict(self.database),
            'search': asdict(self.search),
            'ui': asdict(self.ui)
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def get_config_dict(self) -> Dict[str, Any]:
        """获取配置字典
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        return {
            'window': asdict(self.window),
            'database': asdict(self.database),
            'search': asdict(self.search),
            'ui': asdict(self.ui)
        }
    
    def update_config(self, config_dict: Dict[str, Any]):
        """更新配置
        
        Args:
            config_dict: 配置字典
        """
        if 'window' in config_dict:
            self.window = WindowConfig(**config_dict['window'])
        if 'database' in config_dict:
            self.database = DatabaseConfig(**config_dict['database'])
        if 'search' in config_dict:
            self.search = SearchConfig(**config_dict['search'])
        if 'ui' in config_dict:
            self.ui = UIConfig(**config_dict['ui'])
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self.window = WindowConfig()
        self.database = DatabaseConfig()
        self.search = SearchConfig()
        self.ui = UIConfig()
    
    def export_config(self, file_path: str):
        """导出配置到文件
        
        Args:
            file_path: 导出文件路径
        """
        data = self.get_config_dict()
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def import_config(self, file_path: str):
        """从文件导入配置
        
        Args:
            file_path: 导入文件路径
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.update_config(data)
        self.save()