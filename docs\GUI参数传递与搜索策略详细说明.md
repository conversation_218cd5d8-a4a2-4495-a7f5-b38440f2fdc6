# Fabric Search 项目 - GUI参数传递与搜索策略详细说明

## 概述

本文档详细描述了 Fabric Search 项目中 GUI 搜索参数的传递流程以及搜索策略的工作机制。整个流程从用户界面的参数设置开始，经过多层转换和处理，最终在搜索引擎中执行具体的搜索策略。

## 1. GUI参数传递流程

### 1.1 用户界面参数设置层

#### 1.1.1 特征权重设置 (FeatureWeightsWidget)

**位置**: `gui/search/feature_weights_widget.py`

**功能**: 允许用户设置不同特征类型的权重比例

**参数收集**:
```python
def get_weights(self):
    """获取特征权重（标准化格式）"""
    # 计算特征权重总和并归一化
    weights = {}
    if total_weight > 0:
        if self.deep_feature_cb.isChecked():
            weights['deep_learning'] = self.deep_weight_slider.value() / total_weight
        if self.color_feature_cb.isChecked():
            weights['color'] = self.color_weight_slider.value() / total_weight
        # ... 其他特征权重
    return weights
```

**输出格式**:
```python
{
    'deep_learning': 0.4,  # 深度学习特征权重
    'color': 0.3,          # 颜色特征权重
    'texture': 0.2,        # 纹理特征权重
    'shape': 0.1           # 形状特征权重
}
```

#### 1.1.2 传统特征参数设置 (FeatureParamsWidget)

**位置**: `gui/search/feature_params_widget.py`

**功能**: 设置传统特征提取的具体参数

**参数收集**:
```python
def get_params(self):
    """获取特征参数（标准化格式）"""
    return {
        'extract_color': True,
        'extract_texture': True,
        'extract_shape': True,
        'hist_bins': self.color_bins_slider.value(),           # 颜色直方图bins数量
        'n_dominant_colors': self.dominant_colors_slider.value(), # 主要颜色数量
        'lbp_radius': self.lbp_radius_slider.value(),          # LBP半径
        'lbp_n_points': self.lbp_points_slider.value(),        # LBP采样点数
        'n_fourier_descriptors': self.fourier_descriptors_slider.value() # 傅里叶描述符数量
    }
```

#### 1.1.3 高级搜索参数 (AdvancedSearchWidget)

**位置**: `gui/search/advanced_widget.py`

**功能**: 设置高级过滤条件和搜索参数

**参数收集**:
```python
def get_search_params(self) -> Dict[str, Any]:
    """获取搜索参数"""
    params = {
        "max_results": self.max_results_spin.value(),
        "sort_by": self._get_sort_field(),
        "sort_order": "desc" if self.sort_order_combo.currentIndex() == 0 else "asc"
    }
    
    # 文件属性过滤
    file_filters = {}
    if extensions:
        file_filters["extensions"] = ext_list
    if min_size > 0 or max_size < 999999:
        file_filters["size_range"] = {"min_mb": min_size, "max_mb": max_size}
    
    # 日期过滤
    if self.enable_date_filter_cb.isChecked():
        params["date_filter"] = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "date_type": date_type
        }
    
    return params
```

### 1.2 参数聚合层 (SearchPanel)

**位置**: `gui/search/search_panel.py`

**功能**: 聚合各个组件的参数，创建统一的搜索配置

**配置创建**:
```python
def get_search_config(self) -> SearchConfig:
    """获取搜索配置"""
    config = SearchConfig(mode=self.current_mode)
    
    if self.current_mode == SearchMode.SIMILARITY:
        params = self.similarity_widget.get_search_params()
        if 'similarity_threshold' in params:
            config.similarity_threshold = params['similarity_threshold']
        if 'feature_weights' in params:
            config.feature_weights = params['feature_weights']
        if 'feature_extraction_params' in params:
            config.feature_extraction_params = params['feature_extraction_params']
    
    return config
```

### 1.3 参数转换层 (SearchHandler)

**位置**: `gui/managers/search_handler.py`

**功能**: 将GUI配置转换为搜索引擎可识别的查询对象

**核心转换方法**:
```python
def _config_to_query(self, config: SearchConfig) -> SearchQuery:
    """将搜索配置转换为搜索查询"""
    # 确定查询类型
    search_mode = config.mode.value
    if search_mode == "similarity":
        query_type = SearchType.IMAGE_SIMILARITY
    elif search_mode == "text":
        query_type = SearchType.TEXT_SEARCH
    # ... 其他类型映射
    
    # 创建查询对象
    query = SearchQuery(query_type=query_type)
    
    # 设置特征权重
    if hasattr(config, 'feature_weights') and config.feature_weights:
        query.feature_weights = config.feature_weights
    
    # 设置特征提取参数
    if hasattr(config, 'feature_extraction_params'):
        query.feature_extraction_params = config.feature_extraction_params
    
    # 设置搜索策略
    if hasattr(config, 'search_strategy'):
        query.search_strategy = config.search_strategy
    
    return query
```

## 2. 搜索策略工作机制

### 2.1 策略类型概览

**位置**: `search/search_strategies.py`

项目支持5种主要搜索策略：

1. **WeightedSearchStrategy** - 加权搜索策略
2. **AdaptiveSearchStrategy** - 自适应搜索策略  
3. **QueryExpansionStrategy** - 查询扩展策略
4. **HybridSearchStrategy** - 混合搜索策略
5. **SingleFeatureSearchStrategy** - 单特征搜索策略

### 2.2 加权搜索策略 (WeightedSearchStrategy)

**工作原理**: 根据用户设置的特征权重，对不同特征的相似度进行加权融合

**核心算法**:
```python
def _weighted_fusion(self, feature_similarities: Dict[str, np.ndarray]) -> np.ndarray:
    """加权融合相似度分数"""
    weights = self.feature_weights.to_dict()
    
    # 初始化最终相似度
    num_images = len(next(iter(feature_similarities.values())))
    final_similarities = np.zeros(num_images)
    
    # 加权求和: final_score = Σ(weight_i × similarity_i)
    for feature_type, similarities in feature_similarities.items():
        weight = weights.get(feature_type, 0.0)
        final_similarities += weight * similarities
    
    return final_similarities
```

**适用场景**: 用户明确知道不同特征的重要性，希望精确控制搜索结果

### 2.3 自适应搜索策略 (AdaptiveSearchStrategy)

**工作原理**: 根据查询图像的特征分布自动调整权重

**核心算法**:
```python
def _analyze_query_features(self, query_features: Dict[str, np.ndarray]) -> FeatureWeights:
    """分析查询特征，自动调整权重"""
    weights = FeatureWeights()
    feature_variances = {}
    
    # 计算各特征的方差
    for feature_type, features in query_features.items():
        if len(features) > 1:
            feature_variances[feature_type] = np.var(features)
    
    # 根据方差调整权重（方差大的特征更重要）
    total_variance = sum(feature_variances.values())
    if total_variance > 0:
        for feature_type, variance in feature_variances.items():
            setattr(weights, f"{feature_type}_weight", variance / total_variance)
    
    weights.normalize()
    return weights
```

**适用场景**: 用户不确定特征权重，希望系统自动优化

### 2.4 查询扩展策略 (QueryExpansionStrategy)

**工作原理**: 通过两轮搜索实现查询扩展，提高搜索准确性

**核心流程**:
```python
def search(self, query_features, database_features, fabric_images, top_k, **kwargs):
    """执行查询扩展搜索"""
    # 第一轮搜索：获取初步结果
    initial_k = max(int(top_k * (1 + self.expansion_ratio)), top_k + 5)
    initial_results = weighted_strategy.search(
        query_features, database_features, fabric_images, initial_k, **kwargs
    )
    
    # 查询扩展：使用初步结果的特征扩展查询
    expanded_features = self._expand_query(query_features, initial_results)
    
    # 第二轮搜索：使用扩展后的查询
    final_results = weighted_strategy.search(
        expanded_features, database_features, fabric_images, top_k, **kwargs
    )
    
    return final_results
```

**适用场景**: 复杂查询，需要更高的搜索准确性

### 2.5 混合搜索策略 (HybridSearchStrategy)

**工作原理**: 综合多种策略的结果，通过投票机制确定最终排序

**核心算法**:
```python
def search(self, query_features, database_features, fabric_images, top_k, **kwargs):
    """执行混合搜索"""
    # 执行多种策略
    weighted_results = self.weighted_strategy.search(...)
    adaptive_results = self.adaptive_strategy.search(...)
    query_expansion_results = self.query_expansion_strategy.search(...)
    
    # 融合多种策略的结果
    final_results = self._fuse_results(
        [weighted_results, adaptive_results, query_expansion_results], top_k
    )
    return final_results

def _fuse_results(self, strategy_results, top_k):
    """融合多种策略的结果"""
    all_results = {}
    
    # 收集所有结果并计算综合分数
    for strategy_idx, results in enumerate(strategy_results):
        for rank, result in enumerate(results):
            image_id = result.fabric_image.id
            if image_id not in all_results:
                all_results[image_id] = {'scores': [], 'ranks': [], 'result': result}
            
            all_results[image_id]['scores'].append(result.similarity_score)
            all_results[image_id]['ranks'].append(1.0 / (rank + 1))  # 排名分数
    
    # 计算最终分数：综合相似度分数和排名分数
    final_results = []
    for image_id, data in all_results.items():
        final_score = np.mean(data['scores']) + 0.1 * np.mean(data['ranks'])
        result = data['result']
        result.similarity_score = final_score
        final_results.append(result)
    
    return sorted(final_results, key=lambda x: x.similarity_score, reverse=True)[:top_k]
```

**适用场景**: 对搜索质量要求极高，愿意牺牲一定性能换取准确性

## 3. 参数传递的关键转换点

### 3.1 GUI → SearchConfig

**转换位置**: `SearchPanel.get_search_config()`

**转换内容**:
- 界面控件值 → 配置对象属性
- 多组件参数 → 统一配置结构

### 3.2 SearchConfig → SearchQuery

**转换位置**: `SearchHandler._config_to_query()`

**转换内容**:
- GUI配置模式 → 搜索引擎查询类型
- 界面参数名 → 搜索引擎参数名
- 参数格式标准化

### 3.3 SearchQuery → 策略执行

**转换位置**: `SearchEngine._execute_search()`

**转换内容**:
- 查询对象 → 策略选择
- 参数传递 → 策略配置
- 执行控制 → 结果收集

## 4. 数据流转示例

### 4.1 完整的相似度搜索流程

1. **用户设置**: 深度特征权重40%，颜色30%，纹理20%，形状10%
2. **参数收集**: `FeatureWeightsWidget.get_weights()` 返回归一化权重字典
3. **配置创建**: `SearchPanel` 创建包含权重的 `SearchConfig`
4. **查询转换**: `SearchHandler` 将配置转换为 `SearchQuery`
5. **策略选择**: 根据 `search_strategy` 参数选择 `WeightedSearchStrategy`
6. **权重应用**: 策略使用权重进行加权融合计算
7. **结果返回**: 返回按加权相似度排序的搜索结果

这个完整的流程确保了用户在GUI中的每一个参数设置都能准确传递到搜索引擎，并按照预期的策略执行搜索。

## 5. 搜索策略详细实现

### 5.1 策略工厂模式

**位置**: `search/search_strategies.py`

项目使用工厂模式创建搜索策略，支持动态策略选择：

```python
class SearchStrategyFactory:
    @staticmethod
    def create_strategy(strategy_type: SearchStrategyType,
                       similarity_calculator: SimilarityCalculator,
                       **kwargs) -> SearchStrategy:
        """创建搜索策略"""

        if strategy_type == SearchStrategyType.WEIGHTED:
            feature_weights = kwargs.get('feature_weights')
            return WeightedSearchStrategy(similarity_calculator, feature_weights)

        elif strategy_type == SearchStrategyType.ADAPTIVE:
            return AdaptiveSearchStrategy(similarity_calculator)

        elif strategy_type == SearchStrategyType.QUERY_EXPANSION:
            expansion_ratio = kwargs.get('expansion_ratio', 0.1)
            return QueryExpansionStrategy(similarity_calculator, expansion_ratio)

        elif strategy_type == SearchStrategyType.HYBRID:
            return HybridSearchStrategy(similarity_calculator)

        elif strategy_type == SearchStrategyType.SINGLE_FEATURE:
            feature_type = kwargs.get('feature_type')
            return SingleFeatureSearchStrategy(similarity_calculator, feature_type)
```

### 5.2 相似度计算核心

所有搜索策略都依赖于 `SimilarityCalculator` 进行底层相似度计算：

**支持的相似度度量**:
- **余弦相似度**: 适用于高维特征向量
- **欧几里得距离**: 适用于低维特征
- **曼哈顿距离**: 对异常值鲁棒
- **卡方距离**: 适用于直方图特征

### 5.3 特征权重归一化

**位置**: `search/search_strategies.py` - `FeatureWeights` 类

```python
@dataclass
class FeatureWeights:
    """特征权重"""
    color_weight: float = 0.25
    texture_weight: float = 0.25
    shape_weight: float = 0.25
    deep_learning_weight: float = 0.25

    def normalize(self):
        """归一化权重，确保总和为1"""
        total = (self.color_weight + self.texture_weight +
                self.shape_weight + self.deep_learning_weight)
        if total > 0:
            self.color_weight /= total
            self.texture_weight /= total
            self.shape_weight /= total
            self.deep_learning_weight /= total
```

### 5.4 搜索结果数据结构

```python
@dataclass
class SearchResult:
    """搜索结果"""
    fabric_image: FabricImage          # 布料图像对象
    similarity_score: float            # 综合相似度分数
    feature_scores: Dict[str, float]   # 各特征维度的相似度分数
    strategy_used: str                 # 使用的搜索策略
```

## 6. 性能优化机制

### 6.1 FAISS索引优化

项目使用FAISS库进行高效的向量相似度搜索：

**索引类型选择**:
- **小数据集(<1000)**: FLAT索引，精确搜索
- **中等数据集(1000-10000)**: IVF_FLAT索引，平衡精度和速度
- **大数据集(>10000)**: IVF_PQ索引，压缩存储，快速搜索

### 6.2 搜索缓存机制

**位置**: `search/search_engine.py`

```python
class SearchEngine:
    def __init__(self, app_config, fabric_repository, feature_manager):
        # 初始化缓存
        self.cache_enabled = app_config.get('enable_cache', True)
        self.cache_size = app_config.get('cache_size', 1000)
        self.cache_ttl = app_config.get('cache_ttl', 3600)  # 1小时

        if self.cache_enabled:
            self.search_cache = {}
            self.cache_timestamps = {}
```

**缓存策略**:
- **LRU淘汰**: 最近最少使用的结果被淘汰
- **TTL过期**: 超过生存时间的缓存自动失效
- **查询哈希**: 基于查询参数生成唯一哈希键

### 6.3 并行处理

对于大规模数据集，支持多线程并行特征提取和相似度计算：

```python
# 并行特征提取
with ThreadPoolExecutor(max_workers=cpu_count()) as executor:
    feature_futures = {
        executor.submit(extract_feature, image): image
        for image in image_batch
    }
```

## 7. 错误处理与日志

### 7.1 异常处理机制

每个搜索策略都包含完整的异常处理：

```python
def search(self, query_features, database_features, fabric_images, top_k, **kwargs):
    """执行搜索"""
    try:
        # 搜索逻辑
        results = self._perform_search(...)
        self.logger.info(f"搜索完成，返回 {len(results)} 个结果")
        return results

    except Exception as e:
        self.logger.error(f"搜索失败: {e}")
        return []
```

### 7.2 详细日志记录

**日志级别**:
- **DEBUG**: 详细的参数传递和计算过程
- **INFO**: 搜索开始、完成和结果统计
- **WARNING**: 参数异常和性能警告
- **ERROR**: 搜索失败和系统错误

## 8. 配置与扩展

### 8.1 搜索配置管理

**位置**: `features/config/search_config.py`

```python
@dataclass
class SearchConfiguration:
    """搜索配置"""
    strategy_type: SearchStrategyType = SearchStrategyType.WEIGHTED
    feature_weights: FeatureWeights = field(default_factory=FeatureWeights)
    similarity_metric: SimilarityMetric = SimilarityMetric.COSINE
    index_type: IndexType = IndexType.FLAT
    top_k: int = 10
    similarity_threshold: float = 0.0
    enable_cache: bool = True
    cache_size: int = 1000
    parallel_workers: int = 4
```

### 8.2 策略扩展接口

新的搜索策略只需继承 `SearchStrategy` 基类并实现 `search` 方法：

```python
class CustomSearchStrategy(SearchStrategy):
    """自定义搜索策略"""

    def search(self, query_features, database_features, fabric_images, top_k, **kwargs):
        """实现自定义搜索逻辑"""
        # 自定义搜索实现
        pass
```

## 9. 总结

Fabric Search 项目的搜索系统采用了分层架构设计，实现了从GUI参数到搜索引擎的完整数据流转。主要特点包括：

1. **参数传递链路清晰**: GUI → SearchConfig → SearchQuery → Strategy
2. **搜索策略多样化**: 支持5种不同的搜索策略，适应不同场景
3. **性能优化完善**: FAISS索引、缓存机制、并行处理
4. **扩展性良好**: 工厂模式、策略模式、配置化设计
5. **错误处理完备**: 异常捕获、日志记录、降级处理

这种设计确保了系统的可维护性、可扩展性和高性能，为用户提供了灵活而强大的图像搜索功能。
