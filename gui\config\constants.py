#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI常量定义模块

该模块定义GUI相关的常量。
"""

from PyQt6.QtCore import QSize

# 窗口尺寸常量
DEFAULT_WINDOW_SIZE = QSize(1200, 800)
MIN_WINDOW_SIZE = QSize(800, 600)
MAX_WINDOW_SIZE = QSize(1920, 1080)

# 字体常量
DEFAULT_FONT_SIZE = 9
MIN_FONT_SIZE = 8
MAX_FONT_SIZE = 24

# 缩略图常量
DEFAULT_THUMBNAIL_SIZE = 150
MIN_THUMBNAIL_SIZE = 100
MAX_THUMBNAIL_SIZE = 300

# 动画常量
ANIMATION_DURATION = 300
FADE_DURATION = 250
SLIDE_DURATION = 350

# 间距常量
DEFAULT_SPACING = 6
DEFAULT_MARGIN = 10
SMALL_SPACING = 3
LARGE_SPACING = 12

# 颜色常量
PRIMARY_COLOR = "#2196F3"
SECONDARY_COLOR = "#FFC107"
SUCCESS_COLOR = "#4CAF50"
WARNING_COLOR = "#FF9800"
ERROR_COLOR = "#F44336"
INFO_COLOR = "#00BCD4"

# 主题常量
LIGHT_THEME = "light"
DARK_THEME = "dark"
AUTO_THEME = "auto"

# 文件过滤器常量
IMAGE_FILTER = "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.tiff *.webp);;所有文件 (*.*)"
CONFIG_FILTER = "配置文件 (*.json *.yaml *.yml *.ini);;JSON文件 (*.json);;YAML文件 (*.yaml *.yml);;INI文件 (*.ini);;所有文件 (*.*)"