#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI设置数据类模块

该模块定义GUI相关的设置数据类和枚举。
"""

import os
from typing import Dict, Any
from enum import Enum
from dataclasses import dataclass


class SettingsCategory(Enum):
    """设置类别"""
    GENERAL = "general"  # 常规设置
    SEARCH = "search"  # 搜索设置
    DISPLAY = "display"  # 显示设置
    PERFORMANCE = "performance"  # 性能设置
    ADVANCED = "advanced"  # 高级设置


@dataclass
class SettingsData:
    """设置数据"""
    # 常规设置
    language: str = "zh_CN"
    auto_save: bool = True
    backup_enabled: bool = True
    backup_interval: int = 30  # 分钟
    
    # 搜索设置
    default_similarity_threshold: float = 0.0
    max_search_results: int = 50
    search_timeout: int = 30  # 秒
    enable_search_history: bool = True
    max_history_items: int = 1000
    
    # 显示设置
    theme: str = "light"
    font_family: str = "Microsoft YaHei"
    font_size: int = 12
    thumbnail_size: int = 150
    show_tooltips: bool = True
    animation_enabled: bool = True
    
    # 性能设置
    use_gpu: bool = True
    max_threads: int = 0  # 0表示使用系统CPU核心数
    cache_size: int = 512  # MB
    preload_thumbnails: bool = True
    
    # 高级设置
    log_level: str = "INFO"
    debug_mode: bool = False
    auto_update: bool = True
    telemetry_enabled: bool = False
    
    def __post_init__(self):
        """初始化后处理"""
        # 如果max_threads为0，设置为系统CPU核心数
        if self.max_threads == 0:
            self.max_threads = os.cpu_count() or 4
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "language": self.language,
            "auto_save": self.auto_save,
            "backup_enabled": self.backup_enabled,
            "backup_interval": self.backup_interval,
            "default_similarity_threshold": self.default_similarity_threshold,
            "max_search_results": self.max_search_results,
            "search_timeout": self.search_timeout,
            "enable_search_history": self.enable_search_history,
            "max_history_items": self.max_history_items,
            "theme": self.theme,
            "font_family": self.font_family,
            "font_size": self.font_size,
            "thumbnail_size": self.thumbnail_size,
            "show_tooltips": self.show_tooltips,
            "animation_enabled": self.animation_enabled,
            "use_gpu": self.use_gpu,
            "max_threads": self.max_threads,
            "cache_size": self.cache_size,
            "preload_thumbnails": self.preload_thumbnails,
            "log_level": self.log_level,
            "debug_mode": self.debug_mode,
            "auto_update": self.auto_update,
            "telemetry_enabled": self.telemetry_enabled
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SettingsData':
        """从字典创建"""
        default_max_threads = os.cpu_count() or 4
        
        return cls(
            language=data.get("language", "zh_CN"),
            auto_save=data.get("auto_save", True),
            backup_enabled=data.get("backup_enabled", True),
            backup_interval=data.get("backup_interval", 30),
            default_similarity_threshold=data.get("default_similarity_threshold", 0.0),
            max_search_results=data.get("max_search_results", 50),
            search_timeout=data.get("search_timeout", 30),
            enable_search_history=data.get("enable_search_history", True),
            max_history_items=data.get("max_history_items", 1000),
            theme=data.get("theme", "light"),
            font_family=data.get("font_family", "Microsoft YaHei"),
            font_size=data.get("font_size", 12),
            thumbnail_size=data.get("thumbnail_size", 150),
            show_tooltips=data.get("show_tooltips", True),
            animation_enabled=data.get("animation_enabled", True),
            use_gpu=data.get("use_gpu", True),
            max_threads=data.get("max_threads", default_max_threads),
            cache_size=data.get("cache_size", 512),
            preload_thumbnails=data.get("preload_thumbnails", True),
            log_level=data.get("log_level", "INFO"),
            debug_mode=data.get("debug_mode", False),
            auto_update=data.get("auto_update", True),
            telemetry_enabled=data.get("telemetry_enabled", False)
        )