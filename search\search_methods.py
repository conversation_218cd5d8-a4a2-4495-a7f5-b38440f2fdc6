#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索方法模块

该模块实现各种搜索方法，如图片相似度搜索、文本搜索等。
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from pathlib import Path

from database.models import FabricImage
from database.fabric_repository import FabricRepository
from features.core.manager import FeatureManager
from features.core.data_models import SearchRequest
from utils.log_utils import LoggerMixin
from .models import SearchQuery


class SearchMethods(LoggerMixin):
    """搜索方法实现"""
    
    def __init__(
        self, 
        fabric_repository: FabricRepository, 
        feature_manager: FeatureManager = None, 
        search_utils=None, 
        config=None
    ):
        """初始化搜索方法
        
        Args:
            fabric_repository: 布料数据仓库
            feature_manager: 特征管理器，必须传入有效实例
            search_utils: 搜索工具类
            config: 配置对象
        """
        super().__init__()
        self.fabric_repository = fabric_repository
        self.search_utils = search_utils
        self.config = config
        self.feature_manager = feature_manager
        
        # 验证feature_manager是否存在
        if self.feature_manager is None:
            self.logger.error("SearchMethods初始化失败：feature_manager为None")
            raise ValueError("feature_manager不能为None")
    
    def search_by_image_similarity(
        self, 
        query: SearchQuery, 
        progress_callback=None
    ) -> List[Tuple[FabricImage, float]]:
        """基于图片相似度搜索
        
        Args:
            query: 搜索查询
            progress_callback: 进度回调函数
            
        Returns:
            List[Tuple[FabricImage, float]]: 搜索结果
        """
        try:
            # 确保特征管理器已初始化，但避免重复创建
            if self.feature_manager is None:
                self.logger.error("特征管理器为None，无法执行图像相似度搜索")
                return []
                
            # 检查搜索索引是否已构建
            try:
                if progress_callback:
                    progress_callback(0.1, "检查搜索索引...")
                    
                # 检查是否有相似度计算器和索引
                if (not hasattr(self.feature_manager, 'similarity_calculator') or 
                    self.feature_manager.similarity_calculator is None or
                    not self.feature_manager.similarity_calculator.has_index()):
                    
                    self.logger.info("搜索索引未构建，正在构建索引...")
                    if progress_callback:
                        progress_callback(0.2, "正在构建搜索索引...")
                        
                    # 尝试构建索引
                    index_built = self.feature_manager.build_search_index(force_rebuild=False)
                    if not index_built:
                        self.logger.error("搜索索引构建失败")
                        return []
                    else:
                        self.logger.info("搜索索引构建成功")
                        
            except Exception as e:
                self.logger.error(f"检查或构建搜索索引时发生错误: {e}")
                return []
                
            # 检查是否提供了查询图像路径或ID
            if not query.query_image_path and query.query_image_id is None:
                self.logger.error("未提供查询图像路径或查询图像ID")
                return []
                
            # 构建特征管理器搜索请求
            search_request = SearchRequest(
                query_image_path=query.query_image_path,
                query_features=None,  # 让feature_manager从图像路径提取特征
                query_id=query.query_image_id,
                top_k=query.page_size,  # 使用page_size确保返回结果数量与用户设置一致
                categories=query.categories,
                tags=query.tags
            )
            
            # 添加特征权重和搜索策略到元数据
            if query.search_strategy and query.search_strategy != "default":
                if not hasattr(search_request, "metadata"):
                    search_request.metadata = {}
                search_request.metadata["search_strategy"] = query.search_strategy
                
            if query.feature_weights:
                if not hasattr(search_request, "metadata"):
                    search_request.metadata = {}
                search_request.metadata["feature_weights"] = query.feature_weights
            
            # 添加传统特征提取参数
            if hasattr(query, 'feature_extraction_params') and query.feature_extraction_params:
                search_request.feature_extraction_params = query.feature_extraction_params
            
            # 执行相似度搜索
            if progress_callback:
                progress_callback(0.4, "正在计算图像特征并搜索相似图像...")
            
            # 检查是否需要使用多特征搜索
            use_multi_feature_search = self._should_use_multi_feature_search(query.feature_weights)

            if use_multi_feature_search:
                self.logger.info("使用多特征加权搜索策略")
                return self._execute_multi_feature_search(query, progress_callback)
            else:
                self.logger.info("使用FAISS深度学习特征搜索")
                # 执行搜索
                try:
                    search_response = self.feature_manager.search_similar_images(search_request)
                except Exception as e:
                    self.logger.error(f"特征管理器搜索异常: {e}")
                    return []  # 返回空结果而不是抛出异常
            
            # 检查搜索结果
            if not search_response or not search_response.success:
                error_msg = search_response.error_message if search_response else "搜索响应为空"
                self.logger.error(f"相似度搜索失败: {error_msg}")
                return []
            
            # 统一应用相似度阈值
            original_count = len(search_response.results) if search_response.results else 0
            
            # 确保相似度阈值是标量值并进行类型验证
            try:
                similarity_threshold = float(query.similarity_threshold) if hasattr(query, 'similarity_threshold') else 0.0
                # 确保阈值在有效范围内 (0.0-1.0)
                similarity_threshold = max(0.0, min(1.0, similarity_threshold))
                self.logger.info(f"相似度阈值设置为: {similarity_threshold:.3f}")
            except (ValueError, TypeError) as e:
                self.logger.warning(f"相似度阈值转换错误，使用默认值0.0: {e}")
                similarity_threshold = 0.0
            
            # 记录搜索结果的分数分布
            if search_response.results and len(search_response.results) > 0:
                try:
                    scores = []
                    for result_item in search_response.results:
                        if isinstance(result_item, dict):
                            # 字典格式: {'id': feature_id, 'similarity': score, ...}
                            similarity_score = result_item.get('similarity', 0)
                            if isinstance(similarity_score, np.ndarray):
                                score = float(similarity_score.item()) if similarity_score.size == 1 else float(similarity_score[0])
                            else:
                                score = float(similarity_score)
                            scores.append(score)
                        elif isinstance(result_item, tuple) and len(result_item) >= 2:
                            # 元组格式: (fabric_image, score)
                            scores.append(float(result_item[1]))
                    
                    if scores:
                        max_score = max(scores)
                        min_score = min(scores)
                        avg_score = sum(scores) / len(scores)
                        self.logger.info(f"应用相似度阈值前: {original_count} 个结果")
                        self.logger.info(f"分数统计 - 最小值: {min_score:.3f}, 最大值: {max_score:.3f}, 平均值: {avg_score:.3f}, 阈值: {similarity_threshold:.3f}")
                except Exception as e:
                    self.logger.error(f"计算分数统计时出错: {e}")
            else:
                self.logger.warning("搜索返回了0个结果，无法应用相似度阈值过滤")
                return []
            
            # 应用阈值过滤
            results = []
            filtered_out = 0
            for result_dict in search_response.results:
                try:
                    # 从字典中获取数据
                    feature_id = result_dict['id']
                    similarity_score = result_dict['similarity']
                    
                    # 确保相似度分数是标量值
                    if isinstance(similarity_score, np.ndarray):
                        # 如果是数组，取第一个元素
                        score = float(similarity_score.item()) if similarity_score.size == 1 else float(similarity_score[0])
                    else:
                        score = float(similarity_score)
                    
                    # 记录每个结果的分数，帮助调试
                    self.logger.debug(f"特征ID: {feature_id}, 相似度分数: {score:.4f}, 阈值: {similarity_threshold:.4f}")
                    
                    # 应用阈值过滤
                    if score >= similarity_threshold:
                        # 获取对应的FabricImage（这里需要根据feature_id查找）
                        fabric_image = self.fabric_repository.get_by_id(feature_id)
                        if fabric_image:
                            # 为FabricImage添加feature_scores属性
                            if not hasattr(fabric_image, 'feature_scores'):
                                # 获取各个特征的相似度分数
                                feature_similarities = result_dict.get('feature_similarities', {})
                                if feature_similarities:
                                    # 如果有各特征的相似度分数，使用它们
                                    fabric_image.feature_scores = feature_similarities
                                else:
                                    # 尝试获取feature_scores字段
                                    feature_scores = result_dict.get('feature_scores', {})
                                    if feature_scores:
                                        fabric_image.feature_scores = feature_scores
                                    else:
                                        # 否则只使用加权总分
                                        fabric_image.feature_scores = {"加权总分": score}
                            
                            results.append((fabric_image, score))
                        else:
                            self.logger.warning(f"未找到ID为 {feature_id} 的图像")
                    else:
                        filtered_out += 1
                except Exception as e:
                    self.logger.error(f"处理相似度分数时出错: {e}")
                    # 如果无法处理分数，跳过此结果
                    continue
            
            filtered_count = len(results)
            self.logger.info(f"应用相似度阈值后: {filtered_count} 个结果 (过滤掉 {filtered_out} 个)")
            
            # 如果过滤后没有结果，但原始结果不为空，记录警告但不自动调整阈值
            if filtered_count == 0 and original_count > 0:
                self.logger.warning(f"相似度阈值 {similarity_threshold:.3f} 过滤掉了所有 {original_count} 个结果")
                self.logger.info("不允许自动调整阈值，请手动调整搜索参数")
            
            # 如果仍然没有结果，记录警告但不返回原始结果
            if not results:
                self.logger.warning("最终没有返回任何搜索结果")
                
                # 检查是否有原始结果但被过滤掉了
                if original_count > 0:
                    self.logger.warning(f"原始结果有 {original_count} 个，但全部被过滤掉了")
                    self.logger.info("严格按照阈值过滤，不返回低于阈值的结果")
                else:
                    self.logger.error("搜索引擎没有找到任何匹配结果")
                    # 检查是否有索引问题
                    if hasattr(self.feature_manager, 'get_index_info'):
                        index_info = self.feature_manager.get_index_info()
                        self.logger.info(f"特征索引信息: {index_info}")
                    
                    # 检查查询图像是否有效
                    if query.query_image_path and os.path.exists(query.query_image_path):
                        self.logger.info(f"查询图像路径有效: {query.query_image_path}")
                    else:
                        self.logger.error(f"查询图像路径无效: {query.query_image_path}")

            
            return results
            
        except Exception as e:
            self.logger.error(f"图片相似度搜索失败: {e}")
            return []

    def _should_use_multi_feature_search(self, feature_weights: Dict[str, float]) -> bool:
        """判断是否应该使用多特征搜索

        Args:
            feature_weights: 特征权重字典

        Returns:
            bool: 是否使用多特征搜索
        """
        if not feature_weights:
            return False

        # 检查是否有非深度学习特征的权重大于0
        non_deep_weights = {k: v for k, v in feature_weights.items()
                           if k != 'deep_learning' and v > 0}

        # 如果有非深度学习特征权重，或者深度学习权重为0，使用多特征搜索
        has_non_deep_features = len(non_deep_weights) > 0
        deep_weight_zero = feature_weights.get('deep_learning', 0) == 0

        self.logger.info(f"特征权重分析: {feature_weights}")
        self.logger.info(f"非深度学习特征: {non_deep_weights}")
        self.logger.info(f"是否有非深度学习特征: {has_non_deep_features}")
        self.logger.info(f"深度学习权重是否为0: {deep_weight_zero}")

        return has_non_deep_features or deep_weight_zero

    def _execute_multi_feature_search(self, query: SearchQuery, progress_callback=None) -> List[Tuple[FabricImage, float]]:
        """执行多特征加权搜索

        Args:
            query: 搜索查询
            progress_callback: 进度回调函数

        Returns:
            List[Tuple[FabricImage, float]]: 搜索结果
        """
        try:
            from search.search_strategies import WeightedSearchStrategy, FeatureWeights
            from features.search.similarity_search import SimilarityCalculator
            from features.core.feature_extractor import FeatureExtractor

            if progress_callback:
                progress_callback(0.1, "初始化多特征搜索...")

            # 使用现有的特征管理器获取特征提取器
            feature_extractor = self.feature_manager.feature_extractor

            # 提取查询图像的所有特征
            if progress_callback:
                progress_callback(0.2, "提取查询图像特征...")

            query_features = {}
            if query.query_image_path:
                self.logger.info(f"开始提取查询图像特征: {query.query_image_path}")
                try:
                    # 提取所有特征（深度学习 + 传统特征）
                    result = feature_extractor.extract_features(
                        query.query_image_path, extract_traditional=True
                    )

                    self.logger.info(f"特征提取结果: success={result.success if result else None}")
                    if result and result.success:
                        # 使用feature_vectors获取不同类型的特征
                        feature_vectors = result.feature_vectors
                        self.logger.info(f"特征向量字典: {list(feature_vectors.keys()) if feature_vectors else None}")

                        # 映射特征名称
                        feature_mapping = {
                            'deep': 'deep_learning',  # 深度学习特征
                            'color': 'color',         # 颜色特征
                            'texture': 'texture',     # 纹理特征
                            'shape': 'shape'          # 形状特征
                        }

                        for old_name, new_name in feature_mapping.items():
                            if old_name in feature_vectors:
                                feature_vector = feature_vectors[old_name]
                                query_features[new_name] = feature_vector.vector
                                self.logger.info(f"映射特征 {old_name} -> {new_name}, 维度: {feature_vector.vector.shape}")
                    else:
                        self.logger.error(f"特征提取失败: {result.error_message if result else '结果为None'}")
                except Exception as e:
                    self.logger.error(f"特征提取异常: {str(e)}", exc_info=True)

            self.logger.info(f"提取的查询特征类型: {list(query_features.keys())}")

            if not query_features:
                self.logger.error("无法提取查询图像特征")
                return []

            # 获取所有图像
            if progress_callback:
                progress_callback(0.3, "获取数据库图像...")

            all_images = self.fabric_repository.get_all(include_inactive=False)
            if not all_images:
                return []

            # 构建数据库特征
            if progress_callback:
                progress_callback(0.4, "构建数据库特征...")

            database_features = {}
            valid_images = []

            for feature_type in query_features.keys():
                database_features[feature_type] = []

            for fabric_image in all_images:
                try:
                    # 获取图像特征
                    image_features = self._get_image_features(fabric_image, list(query_features.keys()))

                    # 检查是否有有效特征
                    has_valid_features = False
                    for feature_type in query_features.keys():
                        if feature_type in image_features and image_features[feature_type] is not None:
                            has_valid_features = True
                            break

                    if has_valid_features:
                        valid_images.append(fabric_image)
                        for feature_type in query_features.keys():
                            if feature_type in image_features and image_features[feature_type] is not None:
                                database_features[feature_type].append(image_features[feature_type])
                            else:
                                # 使用零向量填充缺失特征
                                feature_dim = query_features[feature_type].shape[0]
                                database_features[feature_type].append(np.zeros(feature_dim))

                except Exception as e:
                    self.logger.warning(f"获取图像 {fabric_image.id} 特征失败: {e}")
                    continue

            self.logger.info(f"有效图像数量: {len(valid_images)}")
            self.logger.info(f"数据库特征类型: {list(database_features.keys())}")

            if not valid_images:
                return []

            # 创建特征权重对象
            if progress_callback:
                progress_callback(0.6, "执行加权搜索...")

            weights = FeatureWeights(
                color_weight=query.feature_weights.get('color', 0.0),
                texture_weight=query.feature_weights.get('texture', 0.0),
                shape_weight=query.feature_weights.get('shape', 0.0),
                deep_learning_weight=query.feature_weights.get('deep_learning', 0.0)
            )

            self.logger.info(f"使用特征权重: {weights}")

            # 创建搜索策略
            similarity_calculator = SimilarityCalculator()
            strategy = WeightedSearchStrategy(similarity_calculator, weights)

            # 执行搜索
            search_results = strategy.search(
                query_features=query_features,
                database_features=database_features,
                fabric_images=valid_images,
                top_k=query.top_k or 20
            )

            if progress_callback:
                progress_callback(0.8, "处理搜索结果...")

            # 转换结果格式
            results = []
            for search_result in search_results:
                fabric_image = search_result.fabric_image
                similarity_score = search_result.similarity_score

                # 设置特征分数
                fabric_image.feature_scores = search_result.feature_scores

                results.append((fabric_image, similarity_score))

            if progress_callback:
                progress_callback(1.0, "搜索完成")

            self.logger.info(f"多特征搜索完成，返回 {len(results)} 个结果")
            return results

        except Exception as e:
            self.logger.error(f"多特征搜索失败: {e}")
            return []

    def _get_image_features(self, fabric_image, feature_types: List[str]) -> Dict[str, np.ndarray]:
        """获取图像的指定类型特征

        Args:
            fabric_image: 图像对象
            feature_types: 需要的特征类型列表

        Returns:
            Dict[str, np.ndarray]: 特征字典
        """
        features = {}

        try:
            # 获取深度学习特征
            if 'deep_learning' in feature_types:
                if hasattr(fabric_image, 'features') and fabric_image.features:
                    try:
                        import pickle
                        deep_features = pickle.loads(fabric_image.features)
                        if isinstance(deep_features, np.ndarray) and deep_features.size > 0:
                            features['deep_learning'] = deep_features
                    except Exception as e:
                        self.logger.warning(f"解析深度学习特征失败: {e}")

            # 获取传统特征（优先从数据库获取，如果没有则实时提取）
            traditional_types = [t for t in feature_types if t in ['color', 'texture', 'shape']]
            if traditional_types:
                # 特征类型映射：数据库中的特征类型名称
                feature_type_mapping = {
                    'color': 'color_features',
                    'texture': 'texture_features',
                    'shape': 'shape_features'
                }

                missing_features = []

                # 首先尝试从数据库获取
                if fabric_image.id:
                    for feature_type in traditional_types:
                        db_feature_type = feature_type_mapping.get(feature_type)
                        if db_feature_type:
                            try:
                                # 从数据库获取特征
                                feature_record = self.fabric_repository.get_feature_by_image_id(
                                    fabric_image.id, db_feature_type
                                )
                                if feature_record and feature_record.feature_data:
                                    # 反序列化特征数据
                                    feature_array = np.frombuffer(feature_record.feature_data, dtype=np.float32)
                                    if feature_array.size > 0:
                                        features[feature_type] = feature_array
                                        self.logger.debug(f"从数据库获取{feature_type}特征成功，图像ID: {fabric_image.id}")
                                    else:
                                        missing_features.append(feature_type)
                                        self.logger.debug(f"图像 {fabric_image.id} 的{feature_type}特征为空，需要实时提取")
                                else:
                                    missing_features.append(feature_type)
                                    self.logger.debug(f"图像 {fabric_image.id} 未找到{feature_type}特征，需要实时提取")
                            except Exception as e:
                                missing_features.append(feature_type)
                                self.logger.warning(f"获取图像 {fabric_image.id} 的{feature_type}特征失败: {e}，将实时提取")
                else:
                    # 如果没有图像ID，则需要实时提取所有传统特征
                    missing_features = traditional_types

                # 对于缺失的特征，进行实时提取
                if missing_features and fabric_image.file_path:
                    self.logger.debug(f"实时提取缺失的特征: {missing_features}")
                    try:
                        feature_extractor = self.feature_manager.feature_extractor
                        result = feature_extractor.extract_features(
                            fabric_image.file_path, extract_traditional=True
                        )
                        if result and result.success:
                            feature_vectors = result.feature_vectors
                            # 映射特征名称
                            feature_name_mapping = {
                                'color': 'color',
                                'texture': 'texture',
                                'shape': 'shape'
                            }
                            for old_name, new_name in feature_name_mapping.items():
                                if old_name in feature_vectors and new_name in missing_features:
                                    feature_vector = feature_vectors[old_name]
                                    features[new_name] = feature_vector.vector
                                    self.logger.debug(f"实时提取{new_name}特征成功，图像: {fabric_image.file_path}")
                    except Exception as e:
                        self.logger.warning(f"实时提取传统特征失败: {e}")

        except Exception as e:
            self.logger.error(f"获取图像特征失败: {e}")

        return features
    
    def search_by_text(self, query: SearchQuery, progress_callback=None) -> List[Tuple[FabricImage, float]]:
        """基于文本搜索
        
        Args:
            query: 搜索查询
            progress_callback: 进度回调函数
            
        Returns:
            List[Tuple[FabricImage, float]]: 搜索结果
        """
        try:
            if not query.text_query:
                return []
            
            # 使用repository的搜索方法
            images = self.fabric_repository.search_by_text(query.text_query, False)
            
            # 转换为结果格式并计算分数
            results = []
            search_fields = query.search_fields or ['file_name', 'description', 'tags', 'category']
            
            for fabric_image in images:
                score = self._calculate_text_similarity(fabric_image, query.text_query, search_fields)
                if score > 0:
                    results.append((fabric_image, score))
            
            # 按分数排序
            results.sort(key=lambda x: x[1], reverse=True)
            
            return results[:query.page_size]
            
        except Exception as e:
            self.logger.error(f"文本搜索失败: {e}")
            return []
    
    def search_by_category(self, query: SearchQuery, progress_callback=None) -> List[Tuple[FabricImage, float]]:
        """基于类别搜索
        
        Args:
            query: 搜索查询
            progress_callback: 进度回调函数
            
        Returns:
            List[Tuple[FabricImage, float]]: 搜索结果
        """
        try:
            if not query.categories:
                return []
            
            results = []
            
            for category in query.categories:
                # 使用search_by_category方法搜索类别
                images = self.fabric_repository.search_by_category(category, False)
                for fabric_image in images:
                    # 类别匹配给予固定分数
                    results.append((fabric_image, 1.0))
            
            return results
            
        except Exception as e:
            self.logger.error(f"类别搜索失败: {e}")
            return []
    
    def search_by_tags(self, query: SearchQuery, progress_callback=None) -> List[Tuple[FabricImage, float]]:
        """基于标签搜索
        
        Args:
            query: 搜索查询
            progress_callback: 进度回调函数
            
        Returns:
            List[Tuple[FabricImage, float]]: 搜索结果
        """
        try:
            if not query.tags:
                return []
            
            results = []
            
            # 使用search_by_tags方法搜索标签
            images = self.fabric_repository.search_by_tags(query.tags, match_all=False, include_inactive=False)
            
            for fabric_image in images:
                # 计算标签匹配分数
                score = self._calculate_tag_match_score(fabric_image, query.tags)
                results.append((fabric_image, score))
            
            # 去重并合并分数
            unique_results = {}
            for fabric_image, score in results:
                if fabric_image.id in unique_results:
                    unique_results[fabric_image.id] = (
                        fabric_image, 
                        max(unique_results[fabric_image.id][1], score)
                    )
                else:
                    unique_results[fabric_image.id] = (fabric_image, score)
            
            return list(unique_results.values())
            
        except Exception as e:
            self.logger.error(f"标签搜索失败: {e}")
            return []
    
    def search_by_advanced_filter(self, query: SearchQuery, filter_engine, progress_callback=None) -> List[Tuple[FabricImage, float]]:
        """基于高级过滤器搜索
        
        Args:
            query: 搜索查询
            filter_engine: 过滤引擎
            progress_callback: 进度回调函数
            
        Returns:
            List[Tuple[FabricImage, float]]: 搜索结果
        """
        try:
            # 获取所有图片
            if progress_callback:
                progress_callback(0.35, "获取所有图片数据...")
                
            all_images = self.fabric_repository.get_all(include_inactive=False)
            
            # 应用过滤器
            if progress_callback:
                progress_callback(0.4, "应用高级过滤器...")
                
            filtered_images = filter_engine.apply_filters(
                all_images, query.active_filters
            )
            
            # 转换为结果格式
            results = [(img, 1.0) for img in filtered_images]
            
            return results
            
        except Exception as e:
            self.logger.error(f"高级过滤器搜索失败: {e}")
            return []
    
    def _calculate_text_similarity(self, fabric_image: FabricImage,
                                 text_query: str, search_fields: List[str]) -> float:
        """计算文本相似度
        
        Args:
            fabric_image: 布料图片
            text_query: 文本查询
            search_fields: 搜索字段
            
        Returns:
            float: 相似度分数
        """
        try:
            query_lower = text_query.lower().strip()
            if not query_lower:
                return 0.0
            
            total_score = 0.0
            field_count = 0
            
            # 分词处理（简单的空格分词）
            query_terms = query_lower.split()
            
            # 字段权重配置
            field_weights = {
                'file_name': 1.0,
                'description': 0.8,
                'tags': 0.9,
                'category': 0.7,
                'metadata': 0.5
            }
            
            for field in search_fields:
                field_value = getattr(fabric_image, field, None)
                field_weight = field_weights.get(field, 0.5)
                
                if field_value:
                    field_score = 0.0
                    
                    if isinstance(field_value, str):
                        field_value_lower = field_value.lower()
                        
                        # 完全匹配
                        if query_lower == field_value_lower:
                            field_score = 1.0
                        # 包含匹配
                        elif query_lower in field_value_lower:
                            # 计算匹配程度
                            match_ratio = len(query_lower) / len(field_value_lower)
                            field_score = match_ratio * 0.8
                        else:
                            # 分词匹配
                            matched_terms = 0
                            for term in query_terms:
                                if term in field_value_lower:
                                    matched_terms += 1
                            
                            if matched_terms > 0:
                                field_score = (matched_terms / len(query_terms)) * 0.6
                    
                    elif isinstance(field_value, list):  # 如tags
                        max_item_score = 0.0
                        for item in field_value:
                            if isinstance(item, str):
                                item_lower = item.lower()
                                
                                # 完全匹配
                                if query_lower == item_lower:
                                    max_item_score = max(max_item_score, 1.0)
                                # 包含匹配
                                elif query_lower in item_lower:
                                    match_ratio = len(query_lower) / len(item_lower)
                                    max_item_score = max(max_item_score, match_ratio * 0.8)
                                else:
                                    # 分词匹配
                                    matched_terms = 0
                                    for term in query_terms:
                                        if term in item_lower:
                                            matched_terms += 1
                                    
                                    if matched_terms > 0:
                                        term_score = (matched_terms / len(query_terms)) * 0.6
                                        max_item_score = max(max_item_score, term_score)
                        
                        field_score = max_item_score
                    
                    elif isinstance(field_value, dict):  # 如metadata
                        dict_score = 0.0
                        dict_matches = 0
                        
                        for key, value in field_value.items():
                            if isinstance(value, str):
                                value_lower = value.lower()
                                if query_lower in value_lower:
                                    dict_score += 0.5
                                    dict_matches += 1
                        
                        if dict_matches > 0:
                            field_score = min(dict_score / dict_matches, 1.0)
                    
                    # 应用字段权重
                    if field_score > 0:
                        total_score += field_score * field_weight
                        field_count += 1
            
            # 计算最终分数
            if field_count > 0:
                final_score = total_score / field_count
                # 如果匹配了多个字段，给予额外加分
                if field_count > 1:
                    bonus = min(0.1 * (field_count - 1), 0.3)
                    final_score = min(final_score + bonus, 1.0)
                return final_score
            else:
                return 0.0
            
        except Exception as e:
            self.logger.error(f"计算文本相似度失败: {e}")
            return 0.0
    
    def _calculate_tag_match_score(self, fabric_image: FabricImage, query_tags: List[str]) -> float:
        """计算标签匹配分数
        
        Args:
            fabric_image: 布料图片
            query_tags: 查询标签
            
        Returns:
            float: 匹配分数
        """
        try:
            if not fabric_image.tags or not query_tags:
                return 0.0
            
            image_tags = [tag.lower() for tag in fabric_image.tags]
            query_tags_lower = [tag.lower() for tag in query_tags]
            
            # 计算交集
            matched_tags = set(image_tags) & set(query_tags_lower)
            
            # 匹配分数 = 匹配的标签数 / 查询标签数
            score = len(matched_tags) / len(query_tags_lower) if query_tags_lower else 0.0
            
            return score
            
        except Exception as e:
            self.logger.error(f"计算标签匹配分数失败: {e}")
            return 0.0