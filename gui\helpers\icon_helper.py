#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标辅助模块

该模块提供图标创建和管理的辅助功能。
"""

from PyQt6.QtGui import QIcon, QPixmap, QColor
from PyQt6.QtCore import QSize
from PyQt6.QtWidgets import QApplication, QMessageBox


class IconHelper:
    """图标辅助类"""
    
    @staticmethod
    def load_icon(icon_path: str, size: QSize = None) -> QIcon:
        """加载图标
        
        Args:
            icon_path: 图标路径
            size: 图标尺寸
            
        Returns:
            QIcon: 图标对象
        """
        icon = QIcon(icon_path)
        
        if size and not icon.isNull():
            # 确保图标有指定尺寸
            pixmap = icon.pixmap(size)
            icon = QIcon(pixmap)
        
        return icon
    
    @staticmethod
    def create_colored_icon(color: QColor, 
                           size: QSize = QSize(16, 16)) -> QIcon:
        """创建纯色图标
        
        Args:
            color: 颜色
            size: 尺寸
            
        Returns:
            QIcon: 图标对象
        """
        pixmap = QPixmap(size)
        pixmap.fill(color)
        return QIcon(pixmap)
    
    @staticmethod
    def get_system_icon(icon_type: QMessageBox.Icon) -> QIcon:
        """获取系统图标
        
        Args:
            icon_type: 图标类型
            
        Returns:
            QIcon: 图标对象
        """
        style = QApplication.style()
        
        if icon_type == QMessageBox.Icon.Information:
            return style.standardIcon(
                style.StandardPixmap.SP_MessageBoxInformation)
        elif icon_type == QMessageBox.Icon.Warning:
            return style.standardIcon(
                style.StandardPixmap.SP_MessageBoxWarning)
        elif icon_type == QMessageBox.Icon.Critical:
            return style.standardIcon(
                style.StandardPixmap.SP_MessageBoxCritical)
        elif icon_type == QMessageBox.Icon.Question:
            return style.standardIcon(
                style.StandardPixmap.SP_MessageBoxQuestion)
        else:
            return QIcon()