#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像查看器主组件

该模块提供图像查看器的主要功能实现。
"""

import os
from typing import Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QScrollArea, QMenu, QFileDialog
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QPoint, QRect
from PyQt6.QtGui import (
    QPixmap, QTransform, QWheelEvent, QResizeEvent
)

from utils.log_utils import LoggerMixin
from utils.image_utils import ImageProcessor
from ..gui_utils import GUIUtils, MessageHelper
from ..widget_factory import WidgetFactory
from .models import ViewMode, ViewerState
from .widgets import ImageLabel
from .controls import ZoomWidget, ToolsWidget
from .config import ImageViewerConfig


class ImageViewer(QWidget, LoggerMixin):
    """图像查看器
    
    提供完整的图像查看、编辑和操作功能。
    """
    
    # 信号
    imageLoaded = pyqtSignal(str)  # 图像加载完成
    imageChanged = pyqtSignal()  # 图像变更
    zoomChanged = pyqtSignal(float)  # 缩放变更
    
    def __init__(self, parent=None):
        """初始化图像查看器
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        # 初始化组件
        self.widget_factory = WidgetFactory()
        self.image_processor = ImageProcessor()
        self.gui_utils = GUIUtils()
        
        # 状态管理
        self.viewer_state = ViewerState()
        self.current_image_path = None
        self.original_pixmap = None
        self.current_pixmap = None
        
        # 设置界面
        self._setup_ui()
        self._connect_signals()
        
        # 初始化状态
        self.reset_viewer()
        
        # 为了兼容测试用例，添加属性映射
        self._setup_compatibility()
    
    def _setup_compatibility(self):
        """设置兼容性属性"""
        self.__class__.flipped_horizontal = property(
            lambda self: self.viewer_state.horizontal_flip,
            lambda self, value: setattr(self.viewer_state, 'horizontal_flip', value)
        )
        self.__class__.flipped_vertical = property(
            lambda self: self.viewer_state.vertical_flip,
            lambda self, value: setattr(self.viewer_state, 'vertical_flip', value)
        )
    
    def _setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 工具栏
        self.tools_widget = ToolsWidget()
        layout.addWidget(self.tools_widget)
        
        # 缩放控制
        self.zoom_widget = ZoomWidget()
        layout.addWidget(self.zoom_widget)
        
        # 图像显示区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.image_label = ImageLabel()
        self.scroll_area.setWidget(self.image_label)
        
        layout.addWidget(self.scroll_area)
        
        # 状态栏
        self.status_widget = self._create_status_widget()
        layout.addWidget(self.status_widget)
    
    def _create_status_widget(self) -> QWidget:
        """创建状态栏
        
        Returns:
            QWidget: 状态栏组件
        """
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(
            ImageViewerConfig.WIDGET_MARGIN,
            ImageViewerConfig.STATUS_MARGIN,
            ImageViewerConfig.WIDGET_MARGIN,
            ImageViewerConfig.STATUS_MARGIN
        )
        
        # 图像信息
        self.info_label = self.widget_factory.create_label("无图像")
        layout.addWidget(self.info_label)
        
        layout.addStretch()
        
        # 缩放信息
        self.zoom_info_label = self.widget_factory.create_label("100%")
        layout.addWidget(self.zoom_info_label)
        
        return widget
    
    def _connect_signals(self):
        """连接信号"""
        try:
            # 缩放控制信号
            self.zoom_widget.zoomChanged.connect(self.set_zoom_factor)
            self.zoom_widget.viewModeChanged.connect(self.set_view_mode)
            
            # 工具栏信号
            self.tools_widget.rotateRequested.connect(self.rotate_image)
            self.tools_widget.flipRequested.connect(self.flip_image)
            self.tools_widget.cropRequested.connect(self.crop_image)
            self.tools_widget.saveRequested.connect(self.save_image)
            
            # 图像标签信号
            self.image_label.imageDoubleClicked.connect(self._toggle_fit_mode)
            self.image_label.imageRightClicked.connect(self._show_context_menu)
            
        except Exception as e:
            self.logger.error(f"连接信号失败: {e}")
    
    def load_image(self, image_path: str) -> bool:
        """加载图像
        
        Args:
            image_path: 图像路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            # 使用统一的图像验证逻辑
            from utils.image_validation import get_image_info
            
            # 获取图像信息并验证
            image_info = get_image_info(image_path)
            
            if not image_info['exists']:
                MessageHelper.show_warning(self, "警告", f"文件不存在: {image_path}")
                return False
            
            if not image_info['valid_path']:
                MessageHelper.show_warning(self, "警告", 
                    f"不支持的图像格式: {image_path}\n"
                    f"错误: {image_info.get('error', '未知错误')}")
                return False
            
            if not image_info['valid_content']:
                MessageHelper.show_warning(self, "警告", 
                    f"图像文件损坏或格式不正确: {image_path}\n"
                    f"错误: {image_info.get('error', '未知错误')}")
                return False
            
            # 加载图像
            pixmap = QPixmap(image_path)
            if pixmap.isNull():
                MessageHelper.show_warning(self, "警告", f"无法加载图像: {image_path}")
                return False
            
            # 保存原始图像
            self.current_image_path = image_path
            self.original_pixmap = pixmap
            self.current_pixmap = pixmap
            
            # 重置状态
            self.viewer_state.reset()
            
            # 更新显示
            self._update_image_display()
            self._update_status_info()
            
            # 发送信号
            self.imageLoaded.emit(image_path)
            
            self.logger.info(f"图像加载成功: {image_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载图像失败: {e}")
            MessageHelper.show_error(self, "错误", f"加载图像失败: {e}")
            return False
    
    def set_zoom_factor(self, factor: float):
        """设置缩放因子
        
        Args:
            factor: 缩放因子
        """
        try:
            if not self.original_pixmap:
                return
            
            self.viewer_state.zoom_factor = factor
            self.viewer_state.view_mode = ViewMode.CUSTOM
            
            self._update_image_display()
            self._update_zoom_info()
            
            # 发送信号
            self.zoomChanged.emit(factor)
            
        except Exception as e:
            self.logger.error(f"设置缩放因子失败: {e}")
    
    def set_view_mode(self, mode: ViewMode):
        """设置查看模式
        
        Args:
            mode: 查看模式
        """
        try:
            if not self.original_pixmap:
                return
            
            self.viewer_state.view_mode = mode
            
            if mode == ViewMode.FIT_TO_WINDOW:
                self._fit_to_window()
            elif mode == ViewMode.FIT_TO_WIDTH:
                self._fit_to_width()
            elif mode == ViewMode.FIT_TO_HEIGHT:
                self._fit_to_height()
            elif mode == ViewMode.ACTUAL_SIZE:
                self._actual_size()
            
            self._update_image_display()
            self._update_zoom_info()
            
        except Exception as e:
            self.logger.error(f"设置查看模式失败: {e}")
    
    def _fit_to_window(self):
        """适应窗口"""
        if not self.original_pixmap:
            return
        
        # 获取可用区域大小
        available_size = self.scroll_area.viewport().size()
        pixmap_size = self._get_transformed_pixmap().size()
        
        # 计算缩放因子
        scale_x = available_size.width() / pixmap_size.width()
        scale_y = available_size.height() / pixmap_size.height()
        scale = min(scale_x, scale_y, 1.0)  # 不放大
        
        self.viewer_state.zoom_factor = scale
        self.zoom_widget.set_zoom_factor(scale)
    
    def _fit_to_width(self):
        """适应宽度"""
        if not self.original_pixmap:
            return
        
        available_width = self.scroll_area.viewport().width()
        pixmap_width = self._get_transformed_pixmap().width()
        
        scale = available_width / pixmap_width
        self.viewer_state.zoom_factor = scale
        self.zoom_widget.set_zoom_factor(scale)
    
    def _fit_to_height(self):
        """适应高度"""
        if not self.original_pixmap:
            return
        
        available_height = self.scroll_area.viewport().height()
        pixmap_height = self._get_transformed_pixmap().height()
        
        scale = available_height / pixmap_height
        self.viewer_state.zoom_factor = scale
        self.zoom_widget.set_zoom_factor(scale)
    
    def _actual_size(self):
        """实际大小"""
        self.viewer_state.zoom_factor = 1.0
        self.zoom_widget.set_zoom_factor(1.0)
    
    def rotate_image(self, angle: int):
        """旋转图像
        
        Args:
            angle: 旋转角度
        """
        try:
            if not self.original_pixmap:
                return
            
            # 设置为指定的角度，而不是累加
            self.viewer_state.rotation_angle = angle % 360
            
            self._update_image_display()
            self.imageChanged.emit()
            
            self.logger.debug(f"图像旋转设置为: {angle}°")
            
        except Exception as e:
            self.logger.error(f"旋转图像失败: {e}")
    
    def flip_image(self, direction: str):
        """翻转图像
        
        Args:
            direction: 翻转方向 (horizontal/vertical)
        """
        try:
            if not self.original_pixmap:
                return
            
            if direction == "horizontal":
                self.viewer_state.horizontal_flip = not self.viewer_state.horizontal_flip
            elif direction == "vertical":
                self.viewer_state.vertical_flip = not self.viewer_state.vertical_flip
            
            self._update_image_display()
            self.imageChanged.emit()
            
            self.logger.debug(f"图像翻转: {direction}")
            
        except Exception as e:
            self.logger.error(f"翻转图像失败: {e}")
    
    def flip_horizontal(self):
        """水平翻转图像"""
        try:
            if not self.original_pixmap:
                return
            
            self.viewer_state.horizontal_flip = not self.viewer_state.horizontal_flip
            self._update_image_display()
            self.imageChanged.emit()
            
            self.logger.debug(f"图像水平翻转: {self.viewer_state.horizontal_flip}")
            
        except Exception as e:
            self.logger.error(f"水平翻转图像失败: {e}")
    
    def flip_vertical(self):
        """垂直翻转图像"""
        try:
            if not self.original_pixmap:
                return
            
            self.viewer_state.vertical_flip = not self.viewer_state.vertical_flip
            self._update_image_display()
            self.imageChanged.emit()
            
            self.logger.debug(f"图像垂直翻转: {self.viewer_state.vertical_flip}")
            
        except Exception as e:
            self.logger.error(f"翻转图像失败: {e}")
    
    def crop_image(self):
        """裁剪图像"""
        try:
            # 实现图像裁剪功能
            if not self.original_pixmap:
                MessageHelper.show_warning(self, "警告", "没有可裁剪的图像")
                return
            
            # 获取当前选择区域
            if hasattr(self.image_label, 'selection_rect') and self.image_label.selection_rect:
                # 如果有选择区域，使用选择区域进行裁剪
                rect = self.image_label.selection_rect
                
                # 将屏幕坐标转换为图像坐标
                scale_factor = self.viewer_state.zoom_factor / 100.0
                image_rect = QRect(
                    int(rect.x() / scale_factor),
                    int(rect.y() / scale_factor),
                    int(rect.width() / scale_factor),
                    int(rect.height() / scale_factor)
                )
                
                # 确保裁剪区域在图像范围内
                image_rect = image_rect.intersected(self.original_pixmap.rect())
                
                if image_rect.isValid() and not image_rect.isEmpty():
                    # 执行裁剪
                    cropped_pixmap = self.original_pixmap.copy(image_rect)
                    self.original_pixmap = cropped_pixmap
                    self._update_image_display()
                    self.imageChanged.emit()
                    
                    # 清除选择区域
                    if hasattr(self.image_label, 'clear_selection'):
                        self.image_label.clear_selection()
                    
                    MessageHelper.show_info(self, "成功", "图像裁剪完成")
                    self.logger.info("图像裁剪完成")
                else:
                    MessageHelper.show_warning(self, "警告", "裁剪区域无效")
            else:
                # 如果没有选择区域，提示用户先选择区域
                MessageHelper.show_info(self, "提示", "请先在图像上拖拽选择要裁剪的区域")
                
                # 启用选择模式
                if hasattr(self.image_label, 'enable_selection'):
                    self.image_label.enable_selection(True)
            
        except Exception as e:
            self.logger.error(f"裁剪图像失败: {e}")
    
    def save_image(self):
        """保存图像"""
        try:
            if not self.current_pixmap:
                MessageHelper.show_warning(self, "警告", "没有可保存的图像")
                return
            
            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存图像", "",
                ImageViewerConfig.FILE_FILTER
            )
            
            if file_path:
                # 获取变换后的图像
                transformed_pixmap = self._get_transformed_pixmap()
                
                if transformed_pixmap.save(file_path):
                    MessageHelper.show_info(self, "成功", f"图像已保存到: {file_path}")
                    self.logger.info(f"图像保存成功: {file_path}")
                else:
                    MessageHelper.show_error(self, "错误", "保存图像失败")
            
        except Exception as e:
            self.logger.error(f"保存图像失败: {e}")
            MessageHelper.show_error(self, "错误", f"保存图像失败: {e}")
    
    def _get_transformed_pixmap(self) -> QPixmap:
        """获取变换后的图像
        
        Returns:
            QPixmap: 变换后的图像
        """
        if not self.original_pixmap:
            return QPixmap()
        
        pixmap = self.original_pixmap
        
        # 应用旋转
        if self.viewer_state.rotation_angle != 0:
            transform = QTransform()
            transform.rotate(self.viewer_state.rotation_angle)
            pixmap = pixmap.transformed(transform, Qt.TransformationMode.SmoothTransformation)
        
        # 应用翻转
        if self.viewer_state.horizontal_flip or self.viewer_state.vertical_flip:
            pixmap = pixmap.transformed(
                QTransform().scale(
                    -1 if self.viewer_state.horizontal_flip else 1,
                    -1 if self.viewer_state.vertical_flip else 1
                ),
                Qt.TransformationMode.SmoothTransformation
            )
        
        return pixmap
    
    def _update_image_display(self):
        """更新图像显示"""
        try:
            if not self.original_pixmap:
                self.image_label.clear()
                self.image_label.setText("无图像")
                return
            
            # 获取变换后的图像
            transformed_pixmap = self._get_transformed_pixmap()
            
            # 应用缩放
            if self.viewer_state.zoom_factor != 1.0:
                size = transformed_pixmap.size() * self.viewer_state.zoom_factor
                transformed_pixmap = transformed_pixmap.scaled(
                    size, Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
            
            # 更新显示
            self.image_label.setPixmap(transformed_pixmap)
            self.current_pixmap = transformed_pixmap
            
        except Exception as e:
            self.logger.error(f"更新图像显示失败: {e}")
    
    def _update_status_info(self):
        """更新状态信息"""
        try:
            if not self.current_image_path or not self.original_pixmap:
                self.info_label.setText("无图像")
                return
            
            # 获取图像信息
            image_info = self.image_processor.get_image_info(self.current_image_path)
            if image_info:
                info_text = f"{os.path.basename(self.current_image_path)} - {image_info.width}x{image_info.height} - {image_info.format}"
                self.info_label.setText(info_text)
            
        except Exception as e:
            self.logger.error(f"更新状态信息失败: {e}")
    
    def _update_zoom_info(self):
        """更新缩放信息"""
        zoom_percent = int(self.viewer_state.zoom_factor * 100)
        self.zoom_info_label.setText(f"{zoom_percent}%")
    
    def _toggle_fit_mode(self, pos: QPoint):
        """切换适应模式
        
        Args:
            pos: 点击位置
        """
        if self.viewer_state.view_mode == ViewMode.FIT_TO_WINDOW:
            self.set_view_mode(ViewMode.ACTUAL_SIZE)
            self.zoom_widget.set_view_mode(ViewMode.ACTUAL_SIZE)
        else:
            self.set_view_mode(ViewMode.FIT_TO_WINDOW)
            self.zoom_widget.set_view_mode(ViewMode.FIT_TO_WINDOW)
    
    def _show_context_menu(self, pos: QPoint):
        """显示右键菜单
        
        Args:
            pos: 菜单位置
        """
        try:
            menu = QMenu(self)
            
            # 查看模式
            view_menu = menu.addMenu("查看模式")
            
            fit_window_action = view_menu.addAction("适应窗口")
            fit_window_action.triggered.connect(lambda: self.set_view_mode(ViewMode.FIT_TO_WINDOW))
            
            fit_width_action = view_menu.addAction("适应宽度")
            fit_width_action.triggered.connect(lambda: self.set_view_mode(ViewMode.FIT_TO_WIDTH))
            
            fit_height_action = view_menu.addAction("适应高度")
            fit_height_action.triggered.connect(lambda: self.set_view_mode(ViewMode.FIT_TO_HEIGHT))
            
            actual_size_action = view_menu.addAction("实际大小")
            actual_size_action.triggered.connect(lambda: self.set_view_mode(ViewMode.ACTUAL_SIZE))
            
            menu.addSeparator()
            
            # 变换操作
            transform_menu = menu.addMenu("变换")
            
            rotate_left_action = transform_menu.addAction("逆时针旋转90°")
            rotate_left_action.triggered.connect(lambda: self.rotate_image(-90))
            
            rotate_right_action = transform_menu.addAction("顺时针旋转90°")
            rotate_right_action.triggered.connect(lambda: self.rotate_image(90))
            
            transform_menu.addSeparator()
            
            flip_h_action = transform_menu.addAction("水平翻转")
            flip_h_action.triggered.connect(lambda: self.flip_image("horizontal"))
            
            flip_v_action = transform_menu.addAction("垂直翻转")
            flip_v_action.triggered.connect(lambda: self.flip_image("vertical"))
            
            menu.addSeparator()
            
            # 文件操作
            save_action = menu.addAction("保存")
            save_action.triggered.connect(self.save_image)
            
            # 显示菜单
            global_pos = self.image_label.mapToGlobal(pos)
            menu.exec(global_pos)
            
        except Exception as e:
            self.logger.error(f"显示右键菜单失败: {e}")
    
    def reset_viewer(self):
        """重置查看器"""
        try:
            self.viewer_state.reset()
            self.current_image_path = None
            self.original_pixmap = None
            self.current_pixmap = None
            
            self.image_label.clear()
            self.image_label.setText("拖拽图像文件到此处")
            
            self.zoom_widget.set_zoom_factor(1.0)
            self.zoom_widget.set_view_mode(ViewMode.FIT_TO_WINDOW)
            
            self.info_label.setText("无图像")
            self.zoom_info_label.setText("100%")
            
            self.logger.debug("查看器已重置")
            
        except Exception as e:
            self.logger.error(f"重置查看器失败: {e}")
    
    def wheelEvent(self, event: QWheelEvent):
        """鼠标滚轮事件
        
        Args:
            event: 滚轮事件
        """
        try:
            if not self.original_pixmap:
                return
            
            # Ctrl + 滚轮缩放
            if event.modifiers() & Qt.KeyboardModifier.ControlModifier:
                delta = event.angleDelta().y()
                if delta > 0:
                    self.zoom_widget._zoom_in()
                else:
                    self.zoom_widget._zoom_out()
                event.accept()
            else:
                super().wheelEvent(event)
                
        except Exception as e:
            self.logger.error(f"处理滚轮事件失败: {e}")
    
    def resizeEvent(self, event: QResizeEvent):
        """窗口大小变更事件
        
        Args:
            event: 大小变更事件
        """
        super().resizeEvent(event)
        
        # 如果是适应窗口模式，重新计算缩放
        if (self.viewer_state.view_mode == ViewMode.FIT_TO_WINDOW and 
            self.original_pixmap):
            QTimer.singleShot(100, self._fit_to_window)
    
    def get_current_image_path(self) -> Optional[str]:
        """获取当前图像路径
        
        Returns:
            Optional[str]: 当前图像路径
        """
        return self.current_image_path
    
    def get_viewer_state(self) -> ViewerState:
        """获取查看器状态
        
        Returns:
            ViewerState: 查看器状态
        """
        return self.viewer_state
    
    def has_image(self) -> bool:
        """是否有图像
        
        Returns:
            bool: 是否有图像
        """
        return self.original_pixmap is not None
    
    def cleanup(self):
        """清理资源"""
        try:
            self.reset_viewer()
            self.logger.debug("图像查看器资源清理完成")
        except Exception as e:
            self.logger.error(f"图像查看器资源清理失败: {e}")