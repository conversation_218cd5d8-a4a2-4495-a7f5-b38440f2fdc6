"""统一的图像验证模块

提供统一的图像文件验证和处理功能，确保整个系统中的一致性。
"""

import os
import logging
from pathlib import Path
from typing import Union, Set, Optional, Dict, Any
from PIL import Image

logger = logging.getLogger(__name__)


class ImageValidator:
    """图像验证器
    
    提供统一的图像文件验证功能。
    """
    
    # 支持的图像扩展名（统一标准）
    SUPPORTED_EXTENSIONS: Set[str] = {
        '.jpg', '.jpeg', '.png', '.bmp', '.gif', 
        '.tiff', '.tif', '.webp'
    }
    
    # 最大文件大小（50MB）
    MAX_FILE_SIZE: int = 50 * 1024 * 1024
    
    # 最小文件大小（1KB）
    MIN_FILE_SIZE: int = 1024
    
    @classmethod
    def validate_image_path(cls, image_path: Union[str, Path]) -> bool:
        """验证图像路径
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            bool: 路径是否有效
        """
        if not image_path:
            return False
        
        try:
            path = Path(image_path)
            
            # 检查文件是否存在
            if not path.exists():
                logger.debug(f"图像文件不存在: {path}")
                return False
            
            # 检查是否为文件
            if not path.is_file():
                logger.debug(f"路径不是文件: {path}")
                return False
            
            # 检查文件扩展名
            if path.suffix.lower() not in cls.SUPPORTED_EXTENSIONS:
                logger.debug(f"不支持的文件扩展名: {path.suffix}")
                return False
            
            # 检查文件大小
            file_size = path.stat().st_size
            if file_size < cls.MIN_FILE_SIZE:
                logger.debug(f"文件太小: {file_size} bytes")
                return False
            
            if file_size > cls.MAX_FILE_SIZE:
                logger.debug(f"文件太大: {file_size} bytes")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证图像路径时发生错误 {image_path}: {e}")
            return False
    
    @classmethod
    def validate_image_file(cls, image_path: Union[str, Path]) -> bool:
        """验证图像文件（包含文件内容验证）
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            bool: 文件是否是有效的图像文件
        """
        # 首先进行路径验证
        if not cls.validate_image_path(image_path):
            return False
        
        try:
            # 尝试打开图像文件验证内容
            with Image.open(image_path) as img:
                # 验证图像是否可以正常加载
                img.verify()
                return True
                
        except Exception as e:
            logger.debug(f"图像文件内容验证失败 {image_path}: {e}")
            return False
    
    @classmethod
    def get_image_info(cls, image_path: Union[str, Path]) -> Dict[str, Any]:
        """获取图像文件信息
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            Dict[str, Any]: 图像信息
        """
        info = {
            'path': str(image_path),
            'exists': False,
            'valid_path': False,
            'valid_content': False,
            'size': None,
            'mode': None,
            'format': None,
            'file_size': None,
            'error': None
        }
        
        try:
            path = Path(image_path)
            
            # 检查文件是否存在
            if not path.exists():
                info['error'] = '文件不存在'
                return info
            
            info['exists'] = True
            info['file_size'] = path.stat().st_size
            
            # 验证路径
            info['valid_path'] = cls.validate_image_path(image_path)
            if not info['valid_path']:
                info['error'] = '路径验证失败'
                return info
            
            # 验证内容
            try:
                with Image.open(image_path) as image:
                    info['valid_content'] = True
                    info['size'] = image.size
                    info['mode'] = image.mode
                    info['format'] = image.format
            except Exception as e:
                info['error'] = f'图像内容验证失败: {e}'
                
        except Exception as e:
            info['error'] = f'获取图像信息失败: {e}'
            logger.error(f"获取图像信息时发生错误 {image_path}: {e}")
        
        return info
    
    @classmethod
    def is_supported_extension(cls, file_path: Union[str, Path]) -> bool:
        """检查文件扩展名是否受支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 扩展名是否受支持
        """
        path = Path(file_path)
        return path.suffix.lower() in cls.SUPPORTED_EXTENSIONS


# 全局验证器实例
image_validator = ImageValidator()

# 便捷函数
def validate_image_path(image_path: Union[str, Path]) -> bool:
    """验证图像路径（便捷函数）"""
    return image_validator.validate_image_path(image_path)

def validate_image_file(image_path: Union[str, Path]) -> bool:
    """验证图像文件（便捷函数）"""
    return image_validator.validate_image_file(image_path)

def get_image_info(image_path: Union[str, Path]) -> Dict[str, Any]:
    """获取图像信息（便捷函数）"""
    return image_validator.get_image_info(image_path)

def is_supported_extension(file_path: Union[str, Path]) -> bool:
    """检查扩展名是否受支持（便捷函数）"""
    return image_validator.is_supported_extension(file_path)