#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布料图片搜索模块

该模块提供布料图片的搜索功能，包括标签搜索、文本搜索、日期范围搜索等。
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

from .base_repository import BaseRepository
from ..database_manager import DatabaseManager
from ..utils.database_utils import build_where_clause, build_order_clause, build_limit_clause
from ..exceptions.database_exceptions import RepositoryError
from ..models import FabricImage


class FabricSearchRepository(BaseRepository):
    """布料图片搜索仓库类"""
    
    @property
    def table_name(self) -> str:
        """表名"""
        return "fabric_images"
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化布料图片搜索仓库
        
        Args:
            db_manager: 数据库管理器
        """
        super().__init__(db_manager)
    
    def _row_to_fabric_image(self, row) -> FabricImage:
        """将数据库行转换为FabricImage对象
        
        Args:
            row: 数据库行
            
        Returns:
            FabricImage: 布料图片对象
        """
        try:
            # 反序列化JSON字段
            tags = self._deserialize_field(row['tags']) if row['tags'] else []
            color_info = self._deserialize_field(row['color_info']) if row['color_info'] else {}
            texture_info = self._deserialize_field(row['texture_info']) if row['texture_info'] else {}
            pattern_info = self._deserialize_field(row['pattern_info']) if row['pattern_info'] else {}
            material_info = self._deserialize_field(row['material_info']) if row['material_info'] else {}
            metadata = self._deserialize_field(row['metadata']) if row['metadata'] else {}
            
            # 处理时间字段
            created_at = None
            if row['created_at']:
                try:
                    created_at = datetime.fromisoformat(row['created_at'].replace('Z', '+00:00'))
                except ValueError:
                    created_at = datetime.strptime(row['created_at'], '%Y-%m-%d %H:%M:%S')
            
            updated_at = None
            if row['updated_at']:
                try:
                    updated_at = datetime.fromisoformat(row['updated_at'].replace('Z', '+00:00'))
                except ValueError:
                    updated_at = datetime.strptime(row['updated_at'], '%Y-%m-%d %H:%M:%S')
            
            indexed_at = None
            if row['indexed_at']:
                try:
                    indexed_at = datetime.fromisoformat(row['indexed_at'].replace('Z', '+00:00'))
                except ValueError:
                    indexed_at = datetime.strptime(row['indexed_at'], '%Y-%m-%d %H:%M:%S')
            
            return FabricImage(
                id=row['id'],
                file_path=row['file_path'],
                file_name=row['file_name'],
                file_size=row['file_size'],
                width=row['width'],
                height=row['height'],
                channels=row['channels'] if 'channels' in row.keys() else 3,
                format=row['format'],
                hash_md5=row['hash_md5'],
                features=row['features'],
                thumbnail_path=row['thumbnail_path'],
                tags=tags,
                category=row['category'],
                color_info=color_info,
                texture_info=texture_info,
                pattern_info=pattern_info,
                material_info=material_info,
                description=row['description'],
                metadata=metadata,
                created_at=created_at,
                updated_at=updated_at,
                indexed_at=indexed_at,
                is_active=bool(row['is_active'])
            )
            
        except Exception as e:
            self.logger.error(f"转换数据库行为FabricImage对象失败: {e}")
            raise RepositoryError(f"转换数据库行为FabricImage对象失败: {e}")
    
    def search_by_tags(self, tags: List[str], match_all: bool = False,
                      include_inactive: bool = False) -> List[FabricImage]:
        """根据标签搜索布料图片
        
        Args:
            tags: 标签列表
            match_all: 是否匹配所有标签（True为AND，False为OR）
            include_inactive: 是否包含非活跃记录
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        try:
            if not tags:
                return []
            
            # 构建标签搜索条件
            tag_conditions = []
            params = []
            
            for tag in tags:
                # 使用LIKE进行模糊匹配
                # 这样可以匹配逗号分隔的标签字符串
                tag_conditions.append("tags LIKE ?")
                params.append(f'%{tag}%')
            
            if match_all:
                tag_condition = " AND ".join(tag_conditions)
            else:
                tag_condition = " OR ".join(tag_conditions)
            
            sql = f"SELECT * FROM fabric_images WHERE ({tag_condition})"
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " ORDER BY created_at DESC"
            
            self.logger.debug(f"标签搜索SQL: {sql}, 参数: {params}")
            rows = self.db_manager.execute_query(sql, tuple(params))
            result = [self._row_to_fabric_image(row) for row in rows]
            self.logger.debug(f"标签搜索结果数量: {len(result)}")
            return result
            
        except Exception as e:
            self.logger.error(f"根据标签搜索布料图片失败: {e}")
            raise RepositoryError(f"根据标签搜索布料图片失败: {e}")
    
    def get_images_by_tag(self, tag: str, 
                        include_inactive: bool = False) -> List[FabricImage]:
        """根据单个标签获取布料图片
        
        Args:
            tag: 标签
            include_inactive: 是否包含非活跃记录
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        return self.search_by_tags([tag], match_all=False, include_inactive=include_inactive)
    
    def search_by_text(self, text: str, 
                      include_inactive: bool = False) -> List[FabricImage]:
        """根据文本搜索布料图片
        
        Args:
            text: 搜索文本
            include_inactive: 是否包含非活跃记录
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        try:
            sql = """
                SELECT * FROM fabric_images 
                WHERE (file_name LIKE ? OR description LIKE ? OR tags LIKE ? OR category LIKE ?)
            """
            
            search_pattern = f"%{text}%"
            params = [search_pattern, search_pattern, search_pattern, search_pattern]
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " ORDER BY created_at DESC"
            
            rows = self.db_manager.execute_query(sql, tuple(params))
            return [self._row_to_fabric_image(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"根据文本搜索布料图片失败: {e}")
            raise RepositoryError(f"根据文本搜索布料图片失败: {e}")
    
    def get_by_date_range(self, start_date: datetime, 
                         end_date: datetime,
                         include_inactive: bool = False) -> List[FabricImage]:
        """根据日期范围获取布料图片
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            include_inactive: 是否包含非活跃记录
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        try:
            sql = "SELECT * FROM fabric_images WHERE created_at BETWEEN ? AND ?"
            params = [start_date, end_date]
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " ORDER BY created_at DESC"
            
            rows = self.db_manager.execute_query(sql, tuple(params))
            return [self._row_to_fabric_image(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"根据日期范围获取布料图片失败: {e}")
            raise RepositoryError(f"根据日期范围获取布料图片失败: {e}")
    
    def search_by_category(self, category: str,
                          include_inactive: bool = False) -> List[FabricImage]:
        """根据类别搜索布料图片
        
        Args:
            category: 类别
            include_inactive: 是否包含非活跃记录
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        try:
            sql = "SELECT * FROM fabric_images WHERE category = ?"
            params = [category]
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " ORDER BY created_at DESC"
            
            rows = self.db_manager.execute_query(sql, tuple(params))
            return [self._row_to_fabric_image(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"根据类别搜索布料图片失败: {e}")
            raise RepositoryError(f"根据类别搜索布料图片失败: {e}")
    
    def search_by_format(self, format_type: str,
                        include_inactive: bool = False) -> List[FabricImage]:
        """根据格式搜索布料图片
        
        Args:
            format_type: 格式类型
            include_inactive: 是否包含非活跃记录
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        try:
            sql = "SELECT * FROM fabric_images WHERE format = ?"
            params = [format_type]
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " ORDER BY created_at DESC"
            
            rows = self.db_manager.execute_query(sql, tuple(params))
            return [self._row_to_fabric_image(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"根据格式搜索布料图片失败: {e}")
            raise RepositoryError(f"根据格式搜索布料图片失败: {e}")
    
    def search_by_size_range(self, min_width: int = None, max_width: int = None,
                           min_height: int = None, max_height: int = None,
                           include_inactive: bool = False) -> List[FabricImage]:
        """根据尺寸范围搜索布料图片
        
        Args:
            min_width: 最小宽度
            max_width: 最大宽度
            min_height: 最小高度
            max_height: 最大高度
            include_inactive: 是否包含非活跃记录
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        try:
            conditions = []
            params = []
            
            if min_width is not None:
                conditions.append("width >= ?")
                params.append(min_width)
            
            if max_width is not None:
                conditions.append("width <= ?")
                params.append(max_width)
            
            if min_height is not None:
                conditions.append("height >= ?")
                params.append(min_height)
            
            if max_height is not None:
                conditions.append("height <= ?")
                params.append(max_height)
            
            if not conditions:
                # 如果没有条件，返回所有记录
                sql = "SELECT * FROM fabric_images"
                if not include_inactive:
                    sql += " WHERE is_active = 1"
            else:
                where_clause = " AND ".join(conditions)
                sql = f"SELECT * FROM fabric_images WHERE {where_clause}"
                
                if not include_inactive:
                    sql += " AND is_active = 1"
            
            sql += " ORDER BY created_at DESC"
            
            rows = self.db_manager.execute_query(sql, tuple(params))
            return [self._row_to_fabric_image(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"根据尺寸范围搜索布料图片失败: {e}")
            raise RepositoryError(f"根据尺寸范围搜索布料图片失败: {e}")
    
    def search_by_file_size_range(self, min_size: int = None, max_size: int = None,
                                 include_inactive: bool = False) -> List[FabricImage]:
        """根据文件大小范围搜索布料图片
        
        Args:
            min_size: 最小文件大小（字节）
            max_size: 最大文件大小（字节）
            include_inactive: 是否包含非活跃记录
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        try:
            conditions = []
            params = []
            
            if min_size is not None:
                conditions.append("file_size >= ?")
                params.append(min_size)
            
            if max_size is not None:
                conditions.append("file_size <= ?")
                params.append(max_size)
            
            if not conditions:
                # 如果没有条件，返回所有记录
                sql = "SELECT * FROM fabric_images"
                if not include_inactive:
                    sql += " WHERE is_active = 1"
            else:
                where_clause = " AND ".join(conditions)
                sql = f"SELECT * FROM fabric_images WHERE {where_clause}"
                
                if not include_inactive:
                    sql += " AND is_active = 1"
            
            sql += " ORDER BY created_at DESC"
            
            rows = self.db_manager.execute_query(sql, tuple(params))
            return [self._row_to_fabric_image(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"根据文件大小范围搜索布料图片失败: {e}")
            raise RepositoryError(f"根据文件大小范围搜索布料图片失败: {e}")
    
    def advanced_search(self, filters: Dict[str, Any],
                       order_by: str = "created_at",
                       order_direction: str = "DESC",
                       limit: int = None,
                       offset: int = None,
                       include_inactive: bool = False) -> List[FabricImage]:
        """高级搜索
        
        Args:
            filters: 搜索过滤条件字典
            order_by: 排序字段
            order_direction: 排序方向（ASC/DESC）
            limit: 限制结果数量
            offset: 偏移量
            include_inactive: 是否包含非活跃记录
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        try:
            conditions = []
            params = []
            
            # 处理各种过滤条件
            if 'text' in filters and filters['text']:
                text = filters['text']
                conditions.append("(file_name LIKE ? OR description LIKE ? OR tags LIKE ? OR category LIKE ?)")
                search_pattern = f"%{text}%"
                params.extend([search_pattern, search_pattern, search_pattern, search_pattern])
            
            if 'category' in filters and filters['category']:
                conditions.append("category = ?")
                params.append(filters['category'])
            
            if 'format' in filters and filters['format']:
                conditions.append("format = ?")
                params.append(filters['format'])
            
            if 'tags' in filters and filters['tags']:
                tags = filters['tags']
                if isinstance(tags, list):
                    tag_conditions = []
                    for tag in tags:
                        tag_conditions.append("tags LIKE ?")
                        params.append(f'%{tag}%')
                    
                    match_all = filters.get('match_all_tags', False)
                    if match_all:
                        tag_condition = " AND ".join(tag_conditions)
                    else:
                        tag_condition = " OR ".join(tag_conditions)
                    
                    conditions.append(f"({tag_condition})")
            
            if 'start_date' in filters and filters['start_date']:
                conditions.append("created_at >= ?")
                params.append(filters['start_date'])
            
            if 'end_date' in filters and filters['end_date']:
                conditions.append("created_at <= ?")
                params.append(filters['end_date'])
            
            if 'min_width' in filters and filters['min_width'] is not None:
                conditions.append("width >= ?")
                params.append(filters['min_width'])
            
            if 'max_width' in filters and filters['max_width'] is not None:
                conditions.append("width <= ?")
                params.append(filters['max_width'])
            
            if 'min_height' in filters and filters['min_height'] is not None:
                conditions.append("height >= ?")
                params.append(filters['min_height'])
            
            if 'max_height' in filters and filters['max_height'] is not None:
                conditions.append("height <= ?")
                params.append(filters['max_height'])
            
            if 'min_file_size' in filters and filters['min_file_size'] is not None:
                conditions.append("file_size >= ?")
                params.append(filters['min_file_size'])
            
            if 'max_file_size' in filters and filters['max_file_size'] is not None:
                conditions.append("file_size <= ?")
                params.append(filters['max_file_size'])
            
            # 构建SQL查询
            sql = "SELECT * FROM fabric_images"
            
            if conditions:
                where_clause = " AND ".join(conditions)
                sql += f" WHERE {where_clause}"
                
                if not include_inactive:
                    sql += " AND is_active = 1"
            else:
                if not include_inactive:
                    sql += " WHERE is_active = 1"
            
            # 添加排序
            if order_by:
                sql += f" ORDER BY {order_by} {order_direction}"
            
            # 添加限制和偏移
            if limit is not None:
                sql += f" LIMIT {limit}"
                if offset is not None:
                    sql += f" OFFSET {offset}"
            
            self.logger.debug(f"高级搜索SQL: {sql}, 参数: {params}")
            rows = self.db_manager.execute_query(sql, tuple(params))
            result = [self._row_to_fabric_image(row) for row in rows]
            self.logger.debug(f"高级搜索结果数量: {len(result)}")
            return result
            
        except Exception as e:
            self.logger.error(f"高级搜索失败: {e}")
            raise RepositoryError(f"高级搜索失败: {e}")
    
    def get_categories(self) -> List[str]:
        """获取所有类别
        
        Returns:
            List[str]: 类别列表
        """
        try:
            sql = """
                SELECT DISTINCT category 
                FROM fabric_images 
                WHERE category IS NOT NULL AND category != '' AND is_active = 1
                ORDER BY category
            """
            result = self.db_manager.execute_query(sql)
            return [row['category'] for row in result]
            
        except Exception as e:
            self.logger.error(f"获取类别列表失败: {e}")
            raise RepositoryError(f"获取类别列表失败: {e}")
    
    def get_formats(self) -> List[str]:
        """获取所有格式
        
        Returns:
            List[str]: 格式列表
        """
        try:
            sql = """
                SELECT DISTINCT format 
                FROM fabric_images 
                WHERE format IS NOT NULL AND format != '' AND is_active = 1
                ORDER BY format
            """
            result = self.db_manager.execute_query(sql)
            return [row['format'] for row in result]
            
        except Exception as e:
            self.logger.error(f"获取格式列表失败: {e}")
            raise RepositoryError(f"获取格式列表失败: {e}")
    
    def get_all_tags(self) -> List[str]:
        """获取所有标签
        
        Returns:
            List[str]: 标签列表
        """
        try:
            sql = """
                SELECT DISTINCT tags 
                FROM fabric_images 
                WHERE tags IS NOT NULL AND tags != '' AND tags != '[]' AND is_active = 1
            """
            result = self.db_manager.execute_query(sql)
            
            # 解析标签
            all_tags = set()
            for row in result:
                tags_data = row['tags']
                if tags_data:
                    try:
                        # 尝试解析JSON格式的标签
                        tags = self._deserialize_field(tags_data)
                        if isinstance(tags, list):
                            all_tags.update(tags)
                        elif isinstance(tags, str):
                            # 如果是字符串，按逗号分割
                            all_tags.update([tag.strip() for tag in tags.split(',') if tag.strip()])
                    except:
                        # 如果解析失败，尝试按逗号分割
                        if isinstance(tags_data, str):
                            all_tags.update([tag.strip() for tag in tags_data.split(',') if tag.strip()])
            
            return sorted(list(all_tags))
            
        except Exception as e:
            self.logger.error(f"获取标签列表失败: {e}")
            raise RepositoryError(f"获取标签列表失败: {e}")
    
    def count_by_filters(self, filters: Dict[str, Any],
                        include_inactive: bool = False) -> int:
        """根据过滤条件统计数量
        
        Args:
            filters: 搜索过滤条件字典
            include_inactive: 是否包含非活跃记录
            
        Returns:
            int: 符合条件的记录数量
        """
        try:
            conditions = []
            params = []
            
            # 处理各种过滤条件（与advanced_search相同的逻辑）
            if 'text' in filters and filters['text']:
                text = filters['text']
                conditions.append("(file_name LIKE ? OR description LIKE ? OR tags LIKE ? OR category LIKE ?)")
                search_pattern = f"%{text}%"
                params.extend([search_pattern, search_pattern, search_pattern, search_pattern])
            
            if 'category' in filters and filters['category']:
                conditions.append("category = ?")
                params.append(filters['category'])
            
            if 'format' in filters and filters['format']:
                conditions.append("format = ?")
                params.append(filters['format'])
            
            if 'tags' in filters and filters['tags']:
                tags = filters['tags']
                if isinstance(tags, list):
                    tag_conditions = []
                    for tag in tags:
                        tag_conditions.append("tags LIKE ?")
                        params.append(f'%{tag}%')
                    
                    match_all = filters.get('match_all_tags', False)
                    if match_all:
                        tag_condition = " AND ".join(tag_conditions)
                    else:
                        tag_condition = " OR ".join(tag_conditions)
                    
                    conditions.append(f"({tag_condition})")
            
            # 构建SQL查询
            sql = "SELECT COUNT(*) as count FROM fabric_images"
            
            if conditions:
                where_clause = " AND ".join(conditions)
                sql += f" WHERE {where_clause}"
                
                if not include_inactive:
                    sql += " AND is_active = 1"
            else:
                if not include_inactive:
                    sql += " WHERE is_active = 1"
            
            result = self.db_manager.execute_query(sql, tuple(params))
            return result[0]['count'] if result else 0
            
        except Exception as e:
            self.logger.error(f"根据过滤条件统计数量失败: {e}")
            raise RepositoryError(f"根据过滤条件统计数量失败: {e}")