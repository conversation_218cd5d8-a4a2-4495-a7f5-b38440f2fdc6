"""任务表格组件

该模块定义了任务进度对话框中的任务表格组件。
"""

from typing import Dict, Set, Optional, Callable
from datetime import datetime, timedelta

from PyQt6.QtWidgets import (
    QTableWidget, QTableWidgetItem, QProgressBar, QHeaderView
)
from PyQt6.QtCore import Qt, pyqtSignal, QObject
from PyQt6.QtGui import QColor

from utils.task_manager import TaskInfo
from .task_config import TaskDisplayConfig, TaskStatusInfo


class TaskTable(QTableWidget):
    """任务表格组件"""
    
    # 信号
    taskSelected = pyqtSignal(str)  # 任务选择信号
    
    def __init__(self, config: TaskDisplayConfig, parent=None):
        super().__init__(parent)
        self.config = config
        self.status_map = TaskStatusInfo.get_status_map()
        self.task_rows: Dict[str, int] = {}  # 任务ID到行号的映射
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self) -> None:
        """设置UI"""
        # 设置列数和标题
        self.setColumnCount(len(self.config.table_headers))
        self.setHorizontalHeaderLabels(self.config.table_headers)
        
        # 设置表格属性
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(False)
        
        # 设置列宽
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # 任务名称
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 状态
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 进度
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 开始时间
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 耗时
        
        # 设置进度列宽度
        self.setColumnWidth(2, 120)
    
    def _connect_signals(self) -> None:
        """连接信号"""
        self.itemSelectionChanged.connect(self._on_selection_changed)
    
    def add_task(self, task_info: TaskInfo) -> None:
        """添加任务到表格
        
        Args:
            task_info: 任务信息
        """
        row = self.rowCount()
        self.insertRow(row)
        
        # 记录任务ID到行号的映射
        self.task_rows[task_info.task_id] = row
        
        # 设置任务名称和ID
        name_item = QTableWidgetItem(task_info.name)
        name_item.setData(Qt.ItemDataRole.UserRole, task_info.task_id)
        self.setItem(row, 0, name_item)
        
        # 设置状态
        self._update_status(row, task_info.status)
        
        # 设置进度条
        self._create_progress_bar(row, task_info.progress)
        
        # 设置开始时间
        start_time = task_info.started_at.strftime("%H:%M:%S") if task_info.started_at else "-"
        self.setItem(row, 3, QTableWidgetItem(start_time))
        
        # 设置耗时
        self.setItem(row, 4, QTableWidgetItem("-"))
    
    def update_task(self, task_info: TaskInfo) -> None:
        """更新任务信息
        
        Args:
            task_info: 任务信息
        """
        if task_info.task_id not in self.task_rows:
            return
        
        row = self.task_rows[task_info.task_id]
        
        # 更新状态
        self._update_status(row, task_info.status)
        
        # 更新进度
        progress_bar = self.cellWidget(row, 2)
        if progress_bar and isinstance(progress_bar, QProgressBar):
            progress_bar.setValue(int(task_info.progress * 100))
        
        # 更新开始时间
        if task_info.started_at:
            start_time = task_info.started_at.strftime("%H:%M:%S")
            self.setItem(row, 3, QTableWidgetItem(start_time))
        
        # 更新耗时
        duration = self._calculate_duration(task_info)
        self.setItem(row, 4, QTableWidgetItem(duration))
    
    def remove_task(self, task_id: str) -> None:
        """移除任务
        
        Args:
            task_id: 任务ID
        """
        if task_id not in self.task_rows:
            return
        
        row = self.task_rows[task_id]
        self.removeRow(row)
        
        # 更新行号映射
        del self.task_rows[task_id]
        for tid, r in self.task_rows.items():
            if r > row:
                self.task_rows[tid] = r - 1
    
    def clear_tasks(self) -> None:
        """清空所有任务"""
        self.setRowCount(0)
        self.task_rows.clear()
    
    def get_selected_task_id(self) -> Optional[str]:
        """获取选中的任务ID
        
        Returns:
            Optional[str]: 任务ID，如果没有选中则返回None
        """
        selected_items = self.selectedItems()
        if not selected_items:
            return None
        
        item = self.item(selected_items[0].row(), 0)
        if not item:
            return None
        
        return item.data(Qt.ItemDataRole.UserRole)
    
    def _update_status(self, row: int, status: str) -> None:
        """更新状态
        
        Args:
            row: 行号
            status: 状态代码
        """
        status_info = self.status_map.get(status)
        if not status_info:
            return
        
        status_item = QTableWidgetItem(status_info.display_text)
        status_item.setForeground(QColor(*status_info.color))
        self.setItem(row, 1, status_item)
    
    def _create_progress_bar(self, row: int, progress: float) -> None:
        """创建进度条
        
        Args:
            row: 行号
            progress: 进度值（0-1）
        """
        progress_bar = QProgressBar()
        progress_bar.setMinimum(0)
        progress_bar.setMaximum(100)
        progress_bar.setValue(int(progress * 100))
        progress_bar.setTextVisible(True)
        progress_bar.setFormat("%p%")
        self.setCellWidget(row, 2, progress_bar)
    
    def _calculate_duration(self, task_info: TaskInfo) -> str:
        """计算耗时
        
        Args:
            task_info: 任务信息
            
        Returns:
            str: 耗时文本
        """
        if not task_info.started_at:
            return "-"
        
        end_time = task_info.completed_at or datetime.now()
        delta = end_time - task_info.started_at
        return str(timedelta(seconds=int(delta.total_seconds())))
    
    def _on_selection_changed(self) -> None:
        """选择变更处理"""
        task_id = self.get_selected_task_id()
        if task_id:
            self.taskSelected.emit(task_id)