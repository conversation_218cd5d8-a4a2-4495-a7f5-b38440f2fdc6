#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度特征提取器核心模块

整合模型管理、图像预处理和特征处理的核心特征提取器。
"""

import logging
from typing import Optional, Dict, Any, List
import numpy as np
import torch
from PIL import Image

from ..config.feature_config import FeatureExtractorConfig
from ..data_models.feature_models import FeatureExtractionResult
from .deep_model_manager import DeepModelManager
from .deep_image_preprocessor import DeepImagePreprocessor
from .deep_feature_processor import DeepFeatureProcessor

logger = logging.getLogger(__name__)


class DeepFeatureExtractor:
    """深度特征提取器核心类"""
    
    def __init__(self, config: FeatureExtractorConfig):
        """初始化深度特征提取器
        
        Args:
            config: 特征提取器配置
        """
        self.config = config
        
        # 设备管理
        self.device = self._setup_device()
        
        # 初始化组件
        self.model_manager = DeepModelManager(
            device=self.device,
            use_gpu=self.config.use_gpu
        )
        
        # 加载模型
        self.model_manager.load_model(config.model_name)
        
        self.image_preprocessor = DeepImagePreprocessor(
            input_size=getattr(config, 'input_size', 224),
            mean=self._get_preprocessing_param('mean', [0.485, 0.456, 0.406]),
            std=self._get_preprocessing_param('std', [0.229, 0.224, 0.225]),
            device=self.device
        )
        
        self.feature_processor = DeepFeatureProcessor(
            normalize_method=getattr(config, 'normalize_method', 'l2'),
            reduce_dimension=getattr(config, 'reduce_dimension', False),
            target_dimension=getattr(config, 'target_dimension', None),
            device=self.device
        )
        
        logger.info(f"深度特征提取器初始化完成，模型: {config.model_name}，设备: {self.device}")
    
    def _setup_device(self) -> str:
        """设置计算设备"""
        try:
            # 如果明确指定了设备，直接使用
            if hasattr(self.config, 'device') and self.config.device and self.config.device != 'auto':
                device = self.config.device
                logger.info(f"使用指定设备: {device}")
                return device

            # 自动选择最优设备
            if self.config.use_gpu and torch.cuda.is_available():
                try:
                    # 选择显存最多的GPU
                    best_gpu = 0
                    max_memory = 0

                    for i in range(torch.cuda.device_count()):
                        memory = torch.cuda.get_device_properties(i).total_memory
                        if memory > max_memory:
                            max_memory = memory
                            best_gpu = i

                    # 设置GPU设备
                    torch.cuda.set_device(best_gpu)
                    device = f'cuda:{best_gpu}'

                    # 优化GPU设置
                    if hasattr(torch.backends.cudnn, 'benchmark'):
                        torch.backends.cudnn.benchmark = True  # 优化卷积性能
                    if hasattr(torch.backends.cudnn, 'deterministic'):
                        torch.backends.cudnn.deterministic = False  # 允许非确定性以提高性能

                    gpu_name = torch.cuda.get_device_name(best_gpu)
                    gpu_memory = max_memory / (1024**3)  # 转换为GB
                    logger.info(f"使用GPU设备: {gpu_name} (设备{best_gpu}, {gpu_memory:.1f}GB)")

                    # 设置内存管理
                    if hasattr(self.config, 'gpu_memory_fraction'):
                        memory_fraction = getattr(self.config, 'gpu_memory_fraction', 0.8)
                        torch.cuda.set_per_process_memory_fraction(memory_fraction, best_gpu)
                        logger.info(f"设置GPU内存使用比例: {memory_fraction}")

                    return device

                except Exception as e:
                    logger.warning(f"GPU设备设置失败，切换到CPU: {e}")

            logger.info("使用CPU设备")
            return 'cpu'

        except Exception as e:
            logger.error(f"设备设置失败: {str(e)}")
            return 'cpu'
    
    def _get_preprocessing_param(self, param_name: str, default_value: Any) -> Any:
        """获取预处理参数"""
        try:
            if hasattr(self.config, 'preprocessing_params') and self.config.preprocessing_params:
                return self.config.preprocessing_params.get(param_name, default_value)
            return default_value
        except Exception:
            return default_value
    
    def _load_model(self, model_name=None):
        """加载模型
        
        Args:
            model_name: 模型名称，如果为None则使用配置中的模型名称
        """
        try:
            model_name = model_name or self.config.model_name
            # 检查是否需要重新加载模型
            if self.model_manager.current_model_name != model_name:
                self.model_manager.load_model(model_name)
                logger.info(f"模型加载成功，特征维度: {self.model_manager.get_feature_dimension()}")
            else:
                logger.debug(f"模型 {model_name} 已加载，跳过重复加载")
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise
    
    def extract_features_from_image(self, image: Image.Image) -> np.ndarray:
        """从单个图像提取特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 特征向量
        """
        try:
            # 验证模型状态
            if not self.model_manager.is_model_loaded():
                raise RuntimeError("模型未正确加载")
            
            # 验证输入图像
            if image is None:
                raise ValueError("输入图像为空")
            
            # 图像预处理
            input_tensor = self.image_preprocessor.preprocess_single_image(image)
            
            # 移动到设备
            input_tensor = self.image_preprocessor.move_to_device(input_tensor, self.device)
            
            # 验证张量
            if not self.image_preprocessor.validate_tensor(input_tensor):
                raise RuntimeError("图像预处理失败")
            
            # 提取特征
            with torch.no_grad():
                try:
                    model = self.model_manager.get_model()

                    # 使用混合精度加速（如果支持）
                    use_amp = (self.device.startswith('cuda') and
                              hasattr(self.config, 'use_mixed_precision') and
                              self.config.use_mixed_precision and
                              hasattr(torch.cuda, 'amp'))

                    if use_amp:
                        # 使用新的PyTorch API，兼容旧版本
                        try:
                            # PyTorch 1.10+ 新API
                            with torch.amp.autocast('cuda'):
                                features = model(input_tensor)
                        except (AttributeError, TypeError):
                            # 回退到旧API以保持兼容性
                            with torch.cuda.amp.autocast():
                                features = model(input_tensor)
                    else:
                        features = model(input_tensor)

                    # 处理不同模型的输出格式
                    features = self._process_model_output(features)
                    
                    # 转换为numpy数组
                    features = features.cpu().numpy().flatten()
                    
                    # 验证特征
                    if len(features) == 0:
                        raise RuntimeError("提取的特征为空")
                    
                    # 特征后处理 - 将1维特征reshape为2维
                    features = features.reshape(1, -1)
                    features = self.feature_processor.process_features(features)
                    # 返回时再flatten为1维
                    features = features.flatten()
                    
                    return features
                    
                except RuntimeError as e:
                    if "out of memory" in str(e).lower():
                        logger.error("GPU内存不足，尝试使用CPU")
                        return self._extract_features_cpu_fallback(image)
                    else:
                        raise
                
        except Exception as e:
            logger.error(f"从图像提取特征失败: {str(e)}")
            # 返回零向量而不是抛出异常
            feature_dim = self.model_manager.get_feature_dimension()
            return np.zeros(feature_dim if feature_dim > 0 else 2048, dtype=np.float32)
    
    def _extract_features_cpu_fallback(self, image: Image.Image) -> np.ndarray:
        """CPU回退特征提取"""
        try:
            # 切换到CPU
            self.device = 'cpu'
            self.model_manager.change_model(self.config.model_name)
            self.image_preprocessor.device = 'cpu'
            
            # 重新提取特征
            input_tensor = self.image_preprocessor.preprocess_single_image(image)
            input_tensor = input_tensor.to('cpu')
            
            with torch.no_grad():
                model = self.model_manager.get_model()
                features = model(input_tensor)
                features = self._process_model_output(features)
                features = features.cpu().numpy().flatten()
                
                # 特征后处理 - 将1维特征reshape为2维
                features = features.reshape(1, -1)
                features = self.feature_processor.process_features(features)
                # 返回时再flatten为1维
                features = features.flatten()
                
                return features
                
        except Exception as e:
            logger.error(f"CPU回退特征提取失败: {str(e)}")
            feature_dim = self.model_manager.get_feature_dimension()
            return np.zeros(feature_dim if feature_dim > 0 else 2048, dtype=np.float32)
    
    def extract_features_batch(self, images: List[Image.Image]) -> List[np.ndarray]:
        """批量提取特征
        
        Args:
            images: 图像列表
            
        Returns:
            List[np.ndarray]: 特征向量列表
        """
        try:
            # 输入验证
            if not images:
                logger.warning("图像列表为空")
                return []
            
            if not self.model_manager.is_model_loaded():
                logger.error("模型未正确加载")
                feature_dim = self.model_manager.get_feature_dimension()
                return [np.zeros(feature_dim if feature_dim > 0 else 2048, dtype=np.float32) for _ in images]
            
            # 过滤有效图像
            valid_images, valid_indices = self._filter_valid_images(images)
            
            if not valid_images:
                logger.warning("没有有效的图像")
                feature_dim = self.model_manager.get_feature_dimension()
                return [np.zeros(feature_dim if feature_dim > 0 else 2048, dtype=np.float32) for _ in images]
            
            # 批量预处理
            try:
                batch_tensor = self.image_preprocessor.preprocess_batch_images(valid_images)
                batch_tensor = self.image_preprocessor.move_to_device(batch_tensor, self.device)
                
                if not self.image_preprocessor.validate_tensor(batch_tensor):
                    raise RuntimeError("批量图像预处理失败")
                
            except Exception as e:
                logger.error(f"批量预处理失败: {str(e)}")
                feature_dim = self.model_manager.get_feature_dimension()
                return [np.zeros(feature_dim if feature_dim > 0 else 2048, dtype=np.float32) for _ in images]
            
            # 批量特征提取
            try:
                with torch.no_grad():
                    model = self.model_manager.get_model()

                    # 使用混合精度加速（如果支持）
                    use_amp = (self.device.startswith('cuda') and
                              hasattr(self.config, 'use_mixed_precision') and
                              self.config.use_mixed_precision and
                              hasattr(torch.cuda, 'amp'))

                    if use_amp:
                        # 使用新的PyTorch API，兼容旧版本
                        try:
                            # PyTorch 1.10+ 新API
                            with torch.amp.autocast('cuda'):
                                batch_features = model(batch_tensor)
                        except (AttributeError, TypeError):
                            # 回退到旧API以保持兼容性
                            with torch.cuda.amp.autocast():
                                batch_features = model(batch_tensor)
                    else:
                        batch_features = model(batch_tensor)

                    # 处理输出格式
                    batch_features = self._process_model_output(batch_features)

                    if batch_features is None or batch_features.size(0) == 0:
                        logger.error("模型输出为空")
                        feature_dim = self.model_manager.get_feature_dimension()
                        return [np.zeros(feature_dim if feature_dim > 0 else 2048, dtype=np.float32) for _ in images]
                    
                    # 转换为numpy数组列表
                    features_list = []
                    for i in range(batch_features.shape[0]):
                        try:
                            features = batch_features[i].cpu().numpy().flatten()
                            
                            if features.size == 0:
                                logger.warning(f"批次中第 {i} 个特征为空")
                                feature_dim = self.model_manager.get_feature_dimension()
                                features = np.zeros(feature_dim if feature_dim > 0 else 2048, dtype=np.float32)
                            else:
                                # 特征后处理 - 将1维特征reshape为2维
                                features = features.reshape(1, -1)
                                features = self.feature_processor.process_features(features)
                                # 返回时再flatten为1维
                                features = features.flatten()
                            
                            features_list.append(features)
                            
                        except Exception as e:
                            logger.warning(f"处理批次中第 {i} 个特征失败: {str(e)}")
                            feature_dim = self.model_manager.get_feature_dimension()
                            features_list.append(np.zeros(feature_dim if feature_dim > 0 else 2048, dtype=np.float32))
                    
                    # 构建完整的结果列表
                    return self._build_full_results(images, valid_indices, features_list)
                    
            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    logger.error("GPU内存不足，尝试使用CPU进行批量处理")
                    return self._extract_features_batch_cpu_fallback(valid_images, images, valid_indices)
                else:
                    raise
                    
        except Exception as e:
            logger.error(f"批量特征提取失败: {str(e)}")
            feature_dim = self.model_manager.get_feature_dimension()
            return [np.zeros(feature_dim if feature_dim > 0 else 2048, dtype=np.float32) for _ in images]
    
    def _filter_valid_images(self, images: List[Image.Image]) -> tuple:
        """过滤有效图像"""
        valid_images = []
        valid_indices = []
        
        for i, image in enumerate(images):
            if image is not None and isinstance(image, Image.Image):
                try:
                    if hasattr(image, 'size') and image.size:
                        width, height = image.size
                        if 32 <= width <= 10000 and 32 <= height <= 10000:
                            valid_images.append(image)
                            valid_indices.append(i)
                        else:
                            logger.warning(f"图像 {i} 尺寸无效: {width}x{height}")
                    else:
                        logger.warning(f"图像 {i} 无效")
                except Exception as e:
                    logger.warning(f"验证图像 {i} 失败: {str(e)}")
            else:
                logger.warning(f"图像 {i} 不是有效的PIL Image对象")
        
        return valid_images, valid_indices
    
    def _extract_features_batch_cpu_fallback(self, valid_images: List[Image.Image], 
                                           original_images: List[Image.Image],
                                           valid_indices: List[int]) -> List[np.ndarray]:
        """CPU回退批量特征提取"""
        try:
            # 切换到CPU
            self.device = 'cpu'
            self.model_manager.change_model(self.config.model_name)
            self.image_preprocessor.device = 'cpu'
            
            # 重新批量处理
            batch_tensor = self.image_preprocessor.preprocess_batch_images(valid_images)
            batch_tensor = batch_tensor.to('cpu')
            
            with torch.no_grad():
                model = self.model_manager.get_model()
                batch_features = model(batch_tensor)
                batch_features = self._process_model_output(batch_features)
                
                features_list = []
                for i in range(batch_features.shape[0]):
                    features = batch_features[i].cpu().numpy().flatten()
                    # 特征后处理 - 将1维特征reshape为2维
                    features = features.reshape(1, -1)
                    features = self.feature_processor.process_features(features)
                    # 返回时再flatten为1维
                    features = features.flatten()
                    features_list.append(features.astype(np.float32))
                
                return self._build_full_results(original_images, valid_indices, features_list)
                
        except Exception as e:
            logger.error(f"CPU回退批量特征提取失败: {str(e)}")
            feature_dim = self.model_manager.get_feature_dimension()
            return [np.zeros(feature_dim if feature_dim > 0 else 2048, dtype=np.float32) for _ in original_images]
    
    def _build_full_results(self, original_images: List[Image.Image], 
                           valid_indices: List[int], 
                           features_list: List[np.ndarray]) -> List[np.ndarray]:
        """构建完整的结果列表"""
        full_results = []
        valid_idx = 0
        feature_dim = self.model_manager.get_feature_dimension()
        
        for i in range(len(original_images)):
            if i in valid_indices and valid_idx < len(features_list):
                full_results.append(features_list[valid_idx])
                valid_idx += 1
            else:
                full_results.append(np.zeros(feature_dim if feature_dim > 0 else 2048, dtype=np.float32))
        
        return full_results
    
    def _process_model_output(self, features: torch.Tensor) -> torch.Tensor:
        """处理模型输出格式"""
        try:
            # 处理不同模型的输出格式
            if isinstance(features, (list, tuple)):
                # 某些模型返回多个输出，取第一个
                features = features[0]
            
            # 确保是tensor
            if not isinstance(features, torch.Tensor):
                raise ValueError(f"模型输出不是tensor: {type(features)}")
            
            # 处理维度
            if len(features.shape) > 2:
                # 如果有空间维度，进行全局平均池化
                features = torch.mean(features, dim=tuple(range(2, len(features.shape))))
            
            return features
            
        except Exception as e:
            logger.error(f"处理模型输出失败: {str(e)}")
            raise
    
    def extract_features(self, input_data) -> FeatureExtractionResult:
        """提取特征的统一接口
        
        Args:
            input_data: 输入数据（图像或图像列表）
            
        Returns:
            FeatureExtractionResult: 特征提取结果
        """
        try:
            if isinstance(input_data, Image.Image):
                # 单个图像
                features = self.extract_features_from_image(input_data)
                return FeatureExtractionResult(
                    features=features,
                    success=True,
                    feature_dim=len(features),
                    extraction_time=0.0  # 可以添加时间统计
                )
            elif isinstance(input_data, (list, tuple)):
                # 图像列表
                features_list = self.extract_features_batch(input_data)
                return FeatureExtractionResult(
                    features=features_list,
                    success=True,
                    feature_dim=len(features_list[0]) if features_list else 0,
                    extraction_time=0.0
                )
            else:
                raise ValueError(f"不支持的输入类型: {type(input_data)}")
                
        except Exception as e:
            logger.error(f"特征提取失败: {str(e)}")
            return FeatureExtractionResult(
                features=None,
                success=False,
                error_message=str(e),
                feature_dim=0,
                extraction_time=0.0
            )
    
    def change_model(self, model_name: str) -> bool:
        """更换模型
        
        Args:
            model_name: 新模型名称
            
        Returns:
            bool: 是否成功
        """
        try:
            self.model_manager.change_model(model_name)
            self.config.model_name = model_name
            logger.info(f"模型已更换为: {model_name}")
            return True
        except Exception as e:
            logger.error(f"更换模型失败: {str(e)}")
            return False
    
    def get_feature_dimension(self) -> int:
        """获取特征维度"""
        return self.model_manager.get_feature_dimension()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return self.model_manager.get_model_info()
    
    def cleanup(self):
        """清理资源"""
        # 添加静态类变量来跟踪清理状态
        if not hasattr(DeepFeatureExtractor, '_cleanup_called'):
            DeepFeatureExtractor._cleanup_called = False
            
        # 如果已经清理过，则跳过
        if DeepFeatureExtractor._cleanup_called:
            logger.debug("深度特征提取器资源已清理，跳过重复清理")
            return
            
        try:
            logger.info("开始清理深度特征提取器资源")
            
            # 清理模型管理器
            if self.model_manager:
                self.model_manager.cleanup()
            
            # 标记为已清理
            DeepFeatureExtractor._cleanup_called = True
            logger.info("深度特征提取器资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {str(e)}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except Exception:
            pass