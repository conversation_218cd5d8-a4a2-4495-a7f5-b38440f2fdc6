#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析搜索配置和特征权重设置的脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def analyze_search_config():
    """分析搜索配置"""
    print("=== 搜索配置分析 ===\n")
    
    try:
        # 1. 检查配置文件
        print("1. 配置文件检查:")
        config_files = [
            "config.yaml",
            "config/feature_extraction_example.yaml",
            "config/app_config.py",
            "config/system_config.py"
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"  ✓ {config_file} 存在")
            else:
                print(f"  ✗ {config_file} 不存在")
        
        # 2. 检查默认特征权重配置
        print("\n2. 默认特征权重配置:")
        
        # 从配置文件读取
        try:
            import yaml
            with open("config.yaml", 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            feature_config = config.get('feature_extraction', {})
            print(f"  extract_color: {feature_config.get('extract_color', 'N/A')}")
            print(f"  extract_texture: {feature_config.get('extract_texture', 'N/A')}")
            print(f"  extract_shape: {feature_config.get('extract_shape', 'N/A')}")
            
        except Exception as e:
            print(f"  读取配置文件失败: {e}")
        
        # 3. 检查GUI默认权重
        print("\n3. GUI默认权重设置:")
        try:
            from gui.search.feature_weights_widget import FeatureWeightsWidget
            from PyQt6.QtWidgets import QApplication
            
            # 创建临时应用程序
            app = QApplication([])
            widget = FeatureWeightsWidget()
            weights = widget.get_weights()
            print(f"  默认权重: {weights}")
            app.quit()
            
        except Exception as e:
            print(f"  获取GUI默认权重失败: {e}")
        
        # 4. 检查搜索策略判断逻辑
        print("\n4. 搜索策略判断逻辑:")
        try:
            from search.search_methods import SearchMethods
            
            # 模拟不同的权重配置
            test_weights = [
                {},  # 空权重
                {'deep_learning': 1.0},  # 只有深度学习
                {'deep_learning': 0.7, 'color': 0.3},  # 混合权重
                {'color': 0.5, 'texture': 0.5},  # 无深度学习
                {'deep_learning': 0.25, 'color': 0.25, 'texture': 0.25, 'shape': 0.25}  # 均匀权重
            ]
            
            # 创建搜索方法实例（需要模拟依赖）
            class MockFeatureManager:
                pass
            
            class MockFabricRepository:
                pass
            
            search_methods = SearchMethods(MockFeatureManager(), MockFabricRepository())
            
            for i, weights in enumerate(test_weights):
                use_multi = search_methods._should_use_multi_feature_search(weights)
                print(f"  权重 {i+1}: {weights} -> 多特征搜索: {use_multi}")
                
        except Exception as e:
            print(f"  搜索策略分析失败: {e}")
        
        # 5. 检查数据库中的特征类型
        print("\n5. 数据库特征类型:")
        try:
            import sqlite3
            db_path = 'data/fabric_search.db'
            
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 检查image_features表中的特征类型
                cursor.execute('SELECT DISTINCT feature_type FROM image_features;')
                feature_types = cursor.fetchall()
                print(f"  数据库中的特征类型: {[ft[0] for ft in feature_types]}")
                
                # 检查每种特征类型的数量
                for feature_type in feature_types:
                    cursor.execute('SELECT COUNT(*) FROM image_features WHERE feature_type = ?;', feature_type)
                    count = cursor.fetchone()[0]
                    print(f"    {feature_type[0]}: {count} 条记录")
                
                conn.close()
            else:
                print("  数据库文件不存在")
                
        except Exception as e:
            print(f"  数据库检查失败: {e}")
        
        # 6. 分析搜索历史
        print("\n6. 搜索历史分析:")
        try:
            import sqlite3
            import json
            db_path = 'data/fabric_search.db'
            
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 获取最近的搜索记录
                cursor.execute('''
                    SELECT search_type, search_params, result_count 
                    FROM search_history 
                    ORDER BY created_at DESC 
                    LIMIT 5
                ''')
                
                recent_searches = cursor.fetchall()
                print(f"  最近 {len(recent_searches)} 次搜索:")
                
                for i, (search_type, search_params, result_count) in enumerate(recent_searches):
                    print(f"    搜索 {i+1}: 类型={search_type}, 结果数={result_count}")
                    if search_params:
                        try:
                            params = json.loads(search_params)
                            if 'feature_weights' in params:
                                print(f"      特征权重: {params['feature_weights']}")
                        except:
                            pass
                
                conn.close()
            else:
                print("  数据库文件不存在")
                
        except Exception as e:
            print(f"  搜索历史分析失败: {e}")
        
    except Exception as e:
        print(f"分析失败: {e}")

if __name__ == '__main__':
    analyze_search_config()
