#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存管理模块

提供内存使用监控、优化和管理功能。
"""

import gc
import threading
import time
import psutil
import numpy as np
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from pathlib import Path
import logging


@dataclass
class MemoryStats:
    """内存统计信息"""
    total_memory: int           # 总内存(字节)
    available_memory: int       # 可用内存(字节)
    used_memory: int           # 已用内存(字节)
    memory_percent: float      # 内存使用百分比
    process_memory: int        # 进程内存(字节)
    cache_memory: int          # 缓存内存(字节)
    
    @property
    def total_memory_mb(self) -> float:
        return self.total_memory / 1024 / 1024
    
    @property
    def available_memory_mb(self) -> float:
        return self.available_memory / 1024 / 1024
    
    @property
    def used_memory_mb(self) -> float:
        return self.used_memory / 1024 / 1024
    
    @property
    def process_memory_mb(self) -> float:
        return self.process_memory / 1024 / 1024
    
    @property
    def cache_memory_mb(self) -> float:
        return self.cache_memory / 1024 / 1024


class MemoryPool:
    """内存池管理器"""
    
    def __init__(self, pool_size_mb: int = 512):
        """初始化内存池
        
        Args:
            pool_size_mb: 内存池大小(MB)
        """
        self.pool_size_bytes = pool_size_mb * 1024 * 1024
        self._pool: List[np.ndarray] = []
        self._lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
        
    def get_array(self, shape: tuple, dtype=np.float32) -> np.ndarray:
        """从内存池获取数组
        
        Args:
            shape: 数组形状
            dtype: 数据类型
            
        Returns:
            np.ndarray: 数组对象
        """
        size = np.prod(shape) * np.dtype(dtype).itemsize
        
        with self._lock:
            # 查找合适大小的数组
            for i, arr in enumerate(self._pool):
                if arr.nbytes >= size and arr.dtype == dtype:
                    # 移除并返回
                    arr = self._pool.pop(i)
                    return arr.reshape(shape)[:np.prod(shape)].reshape(shape)
        
        # 没有找到合适的，创建新数组
        return np.empty(shape, dtype=dtype)
    
    def return_array(self, arr: np.ndarray) -> None:
        """归还数组到内存池
        
        Args:
            arr: 要归还的数组
        """
        if not isinstance(arr, np.ndarray):
            return
            
        with self._lock:
            # 检查内存池大小限制
            current_size = sum(a.nbytes for a in self._pool)
            if current_size + arr.nbytes <= self.pool_size_bytes:
                self._pool.append(arr.copy())
    
    def clear(self) -> None:
        """清空内存池"""
        with self._lock:
            self._pool.clear()
            gc.collect()


class MemoryManager:
    """内存管理器"""
    
    def __init__(self, 
                 max_memory_percent: float = 80.0,
                 cleanup_interval: int = 300,
                 enable_monitoring: bool = True):
        """初始化内存管理器
        
        Args:
            max_memory_percent: 最大内存使用百分比
            cleanup_interval: 清理间隔(秒)
            enable_monitoring: 是否启用监控
        """
        self.max_memory_percent = max_memory_percent
        self.cleanup_interval = cleanup_interval
        self.enable_monitoring = enable_monitoring
        
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        self._last_cleanup = time.time()
        self._cleanup_callbacks: List[Callable[[], None]] = []
        
        # 内存池
        self.memory_pool = MemoryPool()
        
        # 监控线程
        self._monitoring_thread = None
        self._stop_monitoring = threading.Event()
        
        if enable_monitoring:
            self.start_monitoring()
    
    def get_memory_stats(self) -> MemoryStats:
        """获取内存统计信息
        
        Returns:
            MemoryStats: 内存统计信息
        """
        try:
            # 系统内存信息
            memory = psutil.virtual_memory()
            
            # 进程内存信息
            process = psutil.Process()
            process_memory = process.memory_info().rss
            
            return MemoryStats(
                total_memory=memory.total,
                available_memory=memory.available,
                used_memory=memory.used,
                memory_percent=memory.percent,
                process_memory=process_memory,
                cache_memory=0  # 需要从缓存管理器获取
            )
        except Exception as e:
            self.logger.error(f"获取内存统计失败: {e}")
            return MemoryStats(0, 0, 0, 0.0, 0, 0)
    
    def check_memory_pressure(self) -> bool:
        """检查内存压力
        
        Returns:
            bool: 是否存在内存压力
        """
        stats = self.get_memory_stats()
        return stats.memory_percent > self.max_memory_percent
    
    def register_cleanup_callback(self, callback: Callable[[], None]) -> None:
        """注册清理回调函数
        
        Args:
            callback: 清理回调函数
        """
        with self._lock:
            self._cleanup_callbacks.append(callback)
    
    def force_cleanup(self) -> None:
        """强制执行内存清理"""
        self.logger.info("开始强制内存清理")
        
        # 执行注册的清理回调
        with self._lock:
            for callback in self._cleanup_callbacks:
                try:
                    callback()
                except Exception as e:
                    self.logger.error(f"执行清理回调失败: {e}")
        
        # 清理内存池
        self.memory_pool.clear()
        
        # 强制垃圾回收
        gc.collect()
        
        self.logger.info("内存清理完成")
    
    def periodic_cleanup(self) -> None:
        """定期内存清理"""
        current_time = time.time()
        if current_time - self._last_cleanup > self.cleanup_interval:
            self._last_cleanup = current_time
            
            if self.check_memory_pressure():
                self.force_cleanup()
    
    def start_monitoring(self) -> None:
        """启动内存监控"""
        if self._monitoring_thread is None or not self._monitoring_thread.is_alive():
            self._stop_monitoring.clear()
            self._monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self._monitoring_thread.start()
            self.logger.info("内存监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止内存监控"""
        self._stop_monitoring.set()
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            self._monitoring_thread.join(timeout=5)
        self.logger.info("内存监控已停止")
    
    def _monitoring_loop(self) -> None:
        """监控循环"""
        while not self._stop_monitoring.wait(60):  # 每分钟检查一次
            try:
                self.periodic_cleanup()
                
                # 记录内存使用情况
                stats = self.get_memory_stats()
                if stats.memory_percent > 70:  # 内存使用超过70%时记录
                    self.logger.warning(
                        f"内存使用率较高: {stats.memory_percent:.1f}%, "
                        f"进程内存: {stats.process_memory_mb:.1f}MB"
                    )
                    
            except Exception as e:
                self.logger.error(f"内存监控循环错误: {e}")
    
    def __del__(self):
        """析构函数"""
        self.stop_monitoring()


# 全局内存管理器实例
_memory_manager: Optional[MemoryManager] = None


def get_memory_manager() -> MemoryManager:
    """获取全局内存管理器实例
    
    Returns:
        MemoryManager: 内存管理器实例
    """
    global _memory_manager
    if _memory_manager is None:
        _memory_manager = MemoryManager()
    return _memory_manager


def optimize_memory() -> None:
    """优化内存使用"""
    manager = get_memory_manager()
    manager.force_cleanup()


def get_memory_stats() -> MemoryStats:
    """获取内存统计信息
    
    Returns:
        MemoryStats: 内存统计信息
    """
    manager = get_memory_manager()
    return manager.get_memory_stats()
