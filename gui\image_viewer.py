#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像查看器模块 - 兼容性导入文件

该文件保持向后兼容性，导入新的模块化图像查看器组件。
原始功能已拆分为多个模块以提高代码可维护性。

作者: Assistant
日期: 2024
"""

# 导入新的模块化组件
from gui.image_viewer.viewer import ImageViewer
from gui.image_viewer.models import ViewMode, ZoomMode, RotationAngle, ViewerState
from gui.image_viewer.widgets import ImageLabel
from gui.image_viewer.controls import ZoomWidget, ToolsWidget
from gui.image_viewer.config import ImageViewerConfig

# 保持向后兼容性的导出
__all__ = [
    'ImageViewer',
    'ViewMode',
    'ZoomMode', 
    'RotationAngle',
    'ViewerState',
    'ImageLabel',
    'ZoomWidget',
    'ToolsWidget',
    'ImageViewerConfig'
]


# 这些类现在从新的模块化组件中导入
# 保持向后兼容性，但实际实现已移至单独的模块