#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索设置页面

该模块提供应用程序搜索设置的用户界面。
"""

from typing import Dict, Any

from PyQt6.QtWidgets import QVBoxLayout, QHBoxLayout
from PyQt6.QtCore import Qt

from .settings_base import BaseSettingsWidget


class SearchSettingsWidget(BaseSettingsWidget):
    """搜索设置组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # 搜索参数
        search_group = self.widget_factory.create_group_box(title="搜索参数", layout_type="form")
        search_layout = search_group.layout()
        
        # 相似度阈值 - 从配置管理器读取默认值
        try:
            from config.config_manager import ConfigManager
            config_manager = ConfigManager()
            default_threshold = int(config_manager.config.search.similarity_threshold * 100)
        except Exception:
            default_threshold = 0
            
        self.similarity_slider = self.widget_factory.create_slider(
            minimum=0, maximum=100, value=default_threshold,
            orientation=Qt.Orientation.Horizontal,
            tick_position=self.widget_factory.get_slider_tick_position("below")
        )
        self.similarity_label = self.widget_factory.create_label(f"{default_threshold}%")
        
        similarity_layout = QHBoxLayout()
        similarity_layout.addWidget(self.similarity_slider)
        similarity_layout.addWidget(self.similarity_label)
        
        search_layout.addRow("默认相似度阈值:", similarity_layout)
        
        # 最大结果数 - 从配置管理器读取默认值
        try:
            from config.config_manager import ConfigManager
            config_manager = ConfigManager()
            default_max_results = config_manager.config.search.top_k
        except Exception:
            default_max_results = 10
            
        self.max_results_spin = self.widget_factory.create_spin_box(
            minimum=10, maximum=10000, value=default_max_results
        )
        search_layout.addRow("最大搜索结果数:", self.max_results_spin)
        
        # 搜索超时
        self.timeout_spin = self.widget_factory.create_spin_box(
            minimum=5, maximum=300, value=30, suffix=" 秒"
        )
        search_layout.addRow("搜索超时时间:", self.timeout_spin)
        
        layout.addWidget(search_group)
        
        # 搜索历史
        history_group = self.widget_factory.create_group_box(title="搜索历史", layout_type="vbox")
        history_layout = history_group.layout()
        
        self.enable_history_cb = self.widget_factory.create_checkbox(
            "启用搜索历史", checked=True
        )
        history_layout.addWidget(self.enable_history_cb)
        
        # 历史记录数量
        history_count_layout = QHBoxLayout()
        history_count_layout.addWidget(self.widget_factory.create_label("最大历史记录数:"))
        self.max_history_spin = self.widget_factory.create_spin_box(
            minimum=100, maximum=10000, value=1000
        )
        history_count_layout.addWidget(self.max_history_spin)
        history_count_layout.addStretch()
        
        history_layout.addLayout(history_count_layout)
        
        layout.addWidget(history_group)
        
        layout.addStretch()
        
        # 连接信号
        self.similarity_slider.valueChanged.connect(self.update_similarity_label)
    
    def update_similarity_label(self, value: int):
        """更新相似度标签"""
        self.similarity_label.setText(f"{value}%")
    
    def get_settings(self) -> Dict[str, Any]:
        """获取设置"""
        return {
            "default_similarity_threshold": self.similarity_slider.value() / 100.0,
            "max_search_results": self.max_results_spin.value(),
            "search_timeout": self.timeout_spin.value(),
            "enable_search_history": self.enable_history_cb.isChecked(),
            "max_history_items": self.max_history_spin.value()
        }
    
    def set_settings(self, settings: Dict[str, Any]):
        """设置设置"""
        threshold = int(settings.get("default_similarity_threshold", 0.0) * 100)
        self.similarity_slider.setValue(threshold)
        self.update_similarity_label(threshold)
        
        self.max_results_spin.setValue(settings.get("max_search_results", 10))
        self.timeout_spin.setValue(settings.get("search_timeout", 30))
        self.enable_history_cb.setChecked(settings.get("enable_search_history", True))
        self.max_history_spin.setValue(settings.get("max_history_items", 1000))