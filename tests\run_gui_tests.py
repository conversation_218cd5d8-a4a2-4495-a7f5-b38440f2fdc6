#!/usr/bin/env python3
"""
GUI测试运行器
生成测试报告和覆盖率报告
"""

import os
import sys
import subprocess
import pytest
from datetime import datetime

def run_tests():
    """运行GUI测试并生成报告"""
    
    # 确保在项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    os.chdir(project_root)
    
    # 测试命令
    test_cmd = [
        'python', '-m', 'pytest',
        'tests/gui/',
        '--cov=ui',
        '--cov=models', 
        '--cov=database',
        '--cov=search',
        '--cov-report=html:htmlcov',
        '--cov-report=xml:coverage.xml',
        '--cov-report=term-missing',
        '--html=test_report.html',
        '--self-contained-html',
        '-v',
        '--tb=short'
    ]
    
    print("开始运行GUI测试...")
    print(f"命令: {' '.join(test_cmd)}")
    
    # 运行测试
    result = subprocess.run(test_cmd, capture_output=True, text=True)
    
    print("测试输出:")
    print(result.stdout)
    
    if result.stderr:
        print("错误输出:")
        print(result.stderr)
    
    print(f"测试完成，退出码: {result.returncode}")
    
    # 生成自定义测试报告
    generate_custom_report()
    
    return result.returncode

def generate_custom_report():
    """生成自定义测试报告"""
    
    report_content = f"""
# GUI测试报告

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 测试概述

本报告涵盖了布料图片相似度搜索系统GUI的全面测试，包括：

### 测试模块

1. **主窗口测试** (`test_main_window.py`)
   - 窗口初始化
   - 窗口显示/隐藏
   - 查询图像加载
   - 搜索配置

2. **搜索面板测试** (`test_search_panel.py`)
   - 面板初始化
   - 搜索模式选择
   - 权重控制
   - 图像加载功能
   - 搜索按钮状态

3. **结果显示测试** (`test_result_display.py`)
   - 显示面板初始化
   - 视图模式切换
   - 排序功能
   - 结果设置
   - 分页功能
   - 导出功能

4. **数据库管理测试** (`test_database_management.py`)
   - 数据库面板初始化
   - 文件夹选择
   - 数据库路径选择
   - 数据库创建
   - 数据库同步
   - 进度对话框
   - 模型选择
   - GPU选项

5. **集成测试** (`test_integration.py`)
   - 完整搜索工作流
   - 数据库创建工作流
   - 错误处理
   - UI响应性
   - 内存管理

### 测试覆盖率目标

- **目标覆盖率**: 95%以上
- **重点测试区域**:
  - 用户交互功能
  - 数据处理流程
  - 错误处理机制
  - 性能关键路径

### 测试环境

- **测试框架**: pytest + pytest-qt
- **模拟工具**: unittest.mock
- **GUI框架**: PyQt5
- **覆盖率工具**: pytest-cov

## 测试结果

详细的测试结果请查看：
- HTML测试报告: `test_report.html`
- 覆盖率报告: `htmlcov/index.html`
- XML覆盖率报告: `coverage.xml`

## 测试数据

测试使用了以下模拟数据：
- 模拟图像文件
- 模拟特征数据
- 模拟搜索结果
- 模拟数据库操作

## 注意事项

1. 所有测试都使用模拟对象，不依赖真实的文件系统或网络
2. GUI测试需要QApplication实例，已在conftest.py中配置
3. 测试覆盖了正常流程和异常情况
4. 内存泄漏测试确保长时间运行的稳定性
"""

    with open('GUI_TEST_REPORT.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("自定义测试报告已生成: GUI_TEST_REPORT.md")

if __name__ == '__main__':
    exit_code = run_tests()
    sys.exit(exit_code)