#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本信息工具

该模块提供获取应用程序版本信息的功能。
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional


def get_version_info() -> Dict[str, Any]:
    """
    获取应用程序版本信息
    
    返回:
        Dict[str, Any]: 包含版本信息的字典
    """
    # 默认版本信息
    version_info = {
        "version": "2.0.0",
        "build": "development",
        "date": "2024-06-26",
        "commit": "unknown"
    }
    
    # 尝试从version.json文件中读取版本信息
    try:
        version_file = Path(__file__).parent.parent / "version.json"
        if version_file.exists():
            with open(version_file, "r", encoding="utf-8") as f:
                file_version_info = json.load(f)
                version_info.update(file_version_info)
    except Exception as e:
        print(f"读取版本信息失败: {e}")
    
    return version_info