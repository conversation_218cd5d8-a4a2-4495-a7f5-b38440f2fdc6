#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习模型管理器

负责深度学习模型的加载、管理和设备配置。
"""

import logging
import torch
from typing import Optional, Tuple, Dict, Any

from ..models.model_loader import ModelLoader

logger = logging.getLogger(__name__)


class DeepModelManager:
    """深度学习模型管理器"""
    
    def __init__(self, device: Optional[str] = None, use_gpu: bool = True):
        """初始化模型管理器
        
        Args:
            device: 指定设备
            use_gpu: 是否使用GPU
        """
        self.device = self._setup_device(device, use_gpu)
        self.model_loader = ModelLoader(self.device)
        self.model = None
        self.feature_dim = 0
        self._model_loaded = False
        self.current_model_name = None
        
        logger.info(f"初始化深度模型管理器，设备: {self.device}")
    
    def _setup_device(self, device: Optional[str], use_gpu: bool) -> str:
        """设置计算设备
        
        Args:
            device: 指定设备
            use_gpu: 是否使用GPU
            
        Returns:
            str: 设备名称
        """
        if device:
            return device
        
        if use_gpu and torch.cuda.is_available():
            try:
                torch.cuda.current_device()
                device_name = torch.cuda.get_device_name()
                logger.info(f"使用GPU设备: {device_name}")
                return 'cuda'
            except Exception as e:
                logger.warning(f"CUDA设备不可用，切换到CPU: {e}")
                return 'cpu'
        else:
            logger.info("使用CPU设备")
            return 'cpu'
    
    def load_model(self, model_name: str, pretrained: bool = True) -> bool:
        """加载模型
        
        Args:
            model_name: 模型名称
            pretrained: 是否使用预训练模型
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if not model_name or not isinstance(model_name, str):
                raise ValueError("模型名称必须是非空字符串")
            
            # 如果已经加载了相同的模型，直接返回
            if self._model_loaded and self.current_model_name == model_name:
                logger.debug(f"模型 {model_name} 已加载，跳过重复加载")
                return True
            
            # 清理旧模型
            self._cleanup_model()
            
            # 加载新模型
            self.model, self.feature_dim = self.model_loader.load_model(
                model_name, pretrained=pretrained
            )
            
            if self.model is not None:
                self.model = self.model.to(self.device)
                self.model.eval()  # 设置为评估模式
                self._model_loaded = True
                self.current_model_name = model_name
                
                logger.info(f"成功加载模型 {model_name}，特征维度: {self.feature_dim}")
                return True
            else:
                logger.error(f"模型 {model_name} 加载失败")
                return False
                
        except Exception as e:
            logger.error(f"加载模型 {model_name} 失败: {str(e)}")
            self._model_loaded = False
            return False
    
    def change_model(self, model_name: str) -> bool:
        """更换模型
        
        Args:
            model_name: 新模型名称
            
        Returns:
            bool: 是否更换成功
        """
        try:
            if not model_name or not isinstance(model_name, str):
                raise ValueError("模型名称必须是非空字符串")
            
            old_model = self.current_model_name
            success = self.load_model(model_name)
            
            if success:
                logger.info(f"模型更换成功: {old_model} -> {model_name}")
            else:
                logger.error(f"模型更换失败: {old_model} -> {model_name}")
            
            return success
            
        except Exception as e:
            logger.error(f"模型更换失败: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        info = {
            'model_name': self.current_model_name,
            'feature_dimension': self.feature_dim,
            'device': self.device,
            'model_loaded': self._model_loaded,
            'cuda_available': torch.cuda.is_available()
        }
        
        if torch.cuda.is_available() and self.device == 'cuda':
            try:
                info.update({
                    'gpu_name': torch.cuda.get_device_name(),
                    'gpu_memory_allocated': torch.cuda.memory_allocated(),
                    'gpu_memory_cached': torch.cuda.memory_reserved()
                })
            except Exception as e:
                logger.warning(f"获取GPU信息失败: {e}")
        
        return info
    
    def get_feature_dimension(self) -> int:
        """获取特征维度
        
        Returns:
            int: 特征维度
        """
        if self._model_loaded and self.feature_dim > 0:
            return self.feature_dim
        
        # 默认维度
        default_dims = {
            'resnet50': 2048,
            'resnet101': 2048,
            'resnet152': 2048,
            'vgg16': 4096,
            'vgg19': 4096,
            'densenet121': 1024,
            'densenet169': 1664,
            'densenet201': 1920,
            'efficientnet_b0': 1280,
            'efficientnet_b7': 2560
        }
        
        return default_dims.get(self.current_model_name, 512)
    
    def is_model_loaded(self) -> bool:
        """检查模型是否已加载
        
        Returns:
            bool: 模型是否已加载
        """
        return self._model_loaded and self.model is not None
    
    def get_model(self):
        """获取模型实例
        
        Returns:
            模型实例或None
        """
        if self.is_model_loaded():
            return self.model
        return None
    
    def _cleanup_model(self):
        """清理当前模型"""
        try:
            if hasattr(self, 'model') and self.model is not None:
                # 将模型移到CPU以释放GPU内存
                if hasattr(self.model, 'cpu'):
                    self.model.cpu()
                
                del self.model
                self.model = None
                logger.debug("旧模型已清理")
                
        except Exception as e:
            logger.warning(f"清理模型失败: {str(e)}")
        
        self._model_loaded = False
        self.current_model_name = None
        self.feature_dim = 0
    
    def cleanup(self):
        """清理所有资源"""
        # 添加静态类变量来跟踪清理状态
        if not hasattr(DeepModelManager, '_cleanup_called'):
            DeepModelManager._cleanup_called = False
            
        # 如果已经清理过，则跳过
        if DeepModelManager._cleanup_called:
            logger.debug("深度模型管理器资源已清理，跳过重复清理")
            return
            
        try:
            logger.info("开始清理深度模型管理器资源")
            
            # 清理模型
            self._cleanup_model()
            
            # 清理GPU缓存
            if torch.cuda.is_available():
                try:
                    torch.cuda.empty_cache()
                    logger.debug("GPU缓存已清理")
                except Exception as e:
                    logger.warning(f"清理GPU缓存失败: {str(e)}")
            
            # 标记为已清理
            DeepModelManager._cleanup_called = True
            logger.info("深度模型管理器资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源时出错: {str(e)}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except Exception:
            pass