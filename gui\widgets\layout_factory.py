#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布局工厂模块

该模块提供各种布局的创建功能。
"""

from typing import List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QSizePolicy, QSpacerItem
)
from PyQt6.QtCore import Qt

from utils.log_utils import LoggerMixin
from ..helpers import LayoutHelper


class LayoutFactory(LoggerMixin):
    """布局工厂"""
    
    def __init__(self):
        super().__init__()
    
    def create_vbox_layout(self, widgets: List[QWidget] = None, 
                          spacing: int = 6, margins: tuple = None) -> QVBoxLayout:
        """创建垂直布局
        
        Args:
            widgets: 控件列表
            spacing: 间距
            margins: 边距 (left, top, right, bottom)
            
        Returns:
            QVBoxLayout: 垂直布局
        """
        try:
            layout = LayoutHelper.create_vbox_layout(spacing)
            
            # 设置边距
            if margins:
                layout.setContentsMargins(*margins)
            
            # 添加控件
            if widgets:
                for widget in widgets:
                    if widget:
                        layout.addWidget(widget)
            
            return layout
            
        except Exception as e:
            self.logger.error(f"创建垂直布局失败: {e}")
            return QVBoxLayout()
    
    def create_hbox_layout(self, widgets: List[QWidget] = None,
                          spacing: int = 6, margins: tuple = None) -> QHBoxLayout:
        """创建水平布局
        
        Args:
            widgets: 控件列表
            spacing: 间距
            margins: 边距 (left, top, right, bottom)
            
        Returns:
            QHBoxLayout: 水平布局
        """
        try:
            layout = LayoutHelper.create_hbox_layout(spacing)
            
            # 设置边距
            if margins:
                layout.setContentsMargins(*margins)
            
            # 添加控件
            if widgets:
                for widget in widgets:
                    if widget:
                        layout.addWidget(widget)
            
            return layout
            
        except Exception as e:
            self.logger.error(f"创建水平布局失败: {e}")
            return QHBoxLayout()
    
    def create_grid_layout(self, spacing: int = 6, margins: tuple = None) -> QGridLayout:
        """创建网格布局
        
        Args:
            spacing: 间距
            margins: 边距 (left, top, right, bottom)
            
        Returns:
            QGridLayout: 网格布局
        """
        try:
            layout = LayoutHelper.create_grid_layout(spacing)
            
            # 设置边距
            if margins:
                layout.setContentsMargins(*margins)
            
            return layout
            
        except Exception as e:
            self.logger.error(f"创建网格布局失败: {e}")
            return QGridLayout()
    
    def create_form_layout(self, spacing: int = 6, margins: tuple = None) -> QFormLayout:
        """创建表单布局
        
        Args:
            spacing: 间距
            margins: 边距 (left, top, right, bottom)
            
        Returns:
            QFormLayout: 表单布局
        """
        try:
            layout = QFormLayout()
            layout.setSpacing(spacing)
            
            # 设置边距
            if margins:
                layout.setContentsMargins(*margins)
            
            return layout
            
        except Exception as e:
            self.logger.error(f"创建表单布局失败: {e}")
            return QFormLayout()
    
    def create_spacer(self, width: int = 0, height: int = 0,
                     h_policy: QSizePolicy.Policy = QSizePolicy.Policy.Expanding,
                     v_policy: QSizePolicy.Policy = QSizePolicy.Policy.Minimum) -> QSpacerItem:
        """创建空白间隔
        
        Args:
            width: 宽度
            height: 高度
            h_policy: 水平策略
            v_policy: 垂直策略
            
        Returns:
            QSpacerItem: 空白间隔项
        """
        return QSpacerItem(width, height, h_policy, v_policy)
    
    def add_stretch(self, layout, stretch: int = 1):
        """添加弹性空间
        
        Args:
            layout: 布局对象
            stretch: 拉伸因子
        """
        try:
            if hasattr(layout, 'addStretch'):
                layout.addStretch(stretch)
        except Exception as e:
            self.logger.error(f"添加弹性空间失败: {e}")
    
    def clear_layout(self, layout):
        """清空布局
        
        Args:
            layout: 布局对象
        """
        try:
            LayoutHelper.clear_layout(layout)
        except Exception as e:
            self.logger.error(f"清空布局失败: {e}")