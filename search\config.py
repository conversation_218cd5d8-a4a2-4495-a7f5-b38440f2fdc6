#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索引擎配置模块

该模块集中管理搜索引擎的配置参数。
"""

import os
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from utils.log_utils import LoggerMixin


@dataclass
class SearchConfig:
    """搜索引擎配置类"""
    
    # 缓存配置
    cache_enabled: bool = True
    cache_ttl: int = 3600  # 缓存生存时间（秒）
    max_cache_size: int = 100  # 最大缓存条目数
    
    # 搜索结果配置
    default_page_size: int = 20  # 默认分页大小
    max_page_size: int = 100  # 最大分页大小
    default_sort_by: str = "similarity"  # 默认排序字段
    default_sort_order: str = "desc"  # 默认排序顺序
    
    # 相似度搜索配置
    similarity_threshold: float = 0.5  # 相似度阈值
    max_results: int = 1000  # 最大结果数
    
    # 文本搜索配置
    text_search_fields: List[str] = field(default_factory=lambda: [
        "file_name", "description", "tags", "category", "metadata"
    ])
    field_weights: Dict[str, float] = field(default_factory=lambda: {
        "file_name": 1.0,
        "description": 0.8,
        "tags": 0.9,
        "category": 0.7,
        "metadata": 0.5
    })
    
    # 搜索历史配置
    max_history_size: int = 100  # 最大历史记录数
    max_suggestions: int = 10  # 最大建议数
    
    # 高级配置
    enable_jieba: bool = True  # 启用结巴分词
    enable_statistics: bool = True  # 启用统计信息
    log_searches: bool = True  # 记录搜索日志
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'SearchConfig':
        """从字典创建配置
        
        Args:
            config_dict: 配置字典
            
        Returns:
            SearchConfig: 配置对象
        """
        # 过滤掉不在类定义中的字段
        valid_fields = {f.name for f in cls.__dataclass_fields__.values()}
        filtered_dict = {k: v for k, v in config_dict.items() if k in valid_fields}
        
        return cls(**filtered_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        return {
            'cache_enabled': self.cache_enabled,
            'cache_ttl': self.cache_ttl,
            'max_cache_size': self.max_cache_size,
            'default_page_size': self.default_page_size,
            'max_page_size': self.max_page_size,
            'default_sort_by': self.default_sort_by,
            'default_sort_order': self.default_sort_order,
            'similarity_threshold': self.similarity_threshold,
            'max_results': self.max_results,
            'text_search_fields': self.text_search_fields,
            'field_weights': self.field_weights,
            'max_history_size': self.max_history_size,
            'max_suggestions': self.max_suggestions,
            'enable_jieba': self.enable_jieba,
            'enable_statistics': self.enable_statistics,
            'log_searches': self.log_searches
        }


class ConfigManager(LoggerMixin):
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化配置管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        super().__init__()
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self) -> SearchConfig:
        """加载配置
        
        Returns:
            SearchConfig: 配置对象
        """
        # 默认配置
        config = SearchConfig()
        
        # 如果指定了配置文件路径，尝试加载
        if self.config_path and os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                    config = SearchConfig.from_dict(config_dict)
                    self.logger.info(f"已从 {self.config_path} 加载配置")
            except Exception as e:
                self.logger.error(f"加载配置文件失败: {e}")
                self.logger.info("使用默认配置")
        else:
            self.logger.info("使用默认配置")
        
        return config
    
    def save_config(self, config_path: Optional[str] = None) -> bool:
        """保存配置
        
        Args:
            config_path: 配置文件路径，如果为None则使用初始化时的路径
            
        Returns:
            bool: 是否保存成功
        """
        save_path = config_path or self.config_path
        
        if not save_path:
            self.logger.error("未指定配置文件路径")
            return False
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 保存配置
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self.config.to_dict(), f, indent=4, ensure_ascii=False)
                
            self.logger.info(f"配置已保存到 {save_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get_config(self) -> SearchConfig:
        """获取配置
        
        Returns:
            SearchConfig: 配置对象
        """
        return self.config
    
    def update_config(self, config_dict: Dict[str, Any]) -> None:
        """更新配置
        
        Args:
            config_dict: 配置字典
        """
        # 更新配置
        for key, value in config_dict.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
            else:
                self.logger.warning(f"未知配置项: {key}")
        
        self.logger.info("配置已更新")
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self.config = SearchConfig()
        self.logger.info("配置已重置为默认值")