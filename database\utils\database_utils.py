#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库工具函数

该模块提供数据库相关的工具函数。
"""

import json
import sqlite3
import logging
from typing import Any, Optional, Dict, List
from datetime import datetime


def serialize_json(data: Any) -> Optional[str]:
    """序列化数据为JSON字符串
    
    Args:
        data: 要序列化的数据
        
    Returns:
        Optional[str]: JSON字符串，如果数据为None则返回None
    """
    if data is None:
        return None
    
    try:
        return json.dumps(data, ensure_ascii=False, default=str)
    except (TypeError, ValueError) as e:
        logging.warning(f"JSON序列化失败: {e}")
        return None


def deserialize_json(json_str: Optional[str]) -> Any:
    """反序列化JSON字符串
    
    Args:
        json_str: JSON字符串
        
    Returns:
        Any: 反序列化后的数据，如果字符串为None或空则返回None
    """
    if not json_str:
        return None
    
    try:
        return json.loads(json_str)
    except (TypeError, ValueError) as e:
        logging.warning(f"JSON反序列化失败: {e}")
        return None


def format_timestamp(timestamp: Optional[str]) -> Optional[datetime]:
    """格式化时间戳
    
    Args:
        timestamp: 时间戳字符串
        
    Returns:
        Optional[datetime]: 格式化后的时间对象
    """
    if not timestamp:
        return None
    
    try:
        return datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
    except ValueError:
        try:
            return datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
        except ValueError as e:
            logging.warning(f"时间戳格式化失败: {e}")
            return None


def build_where_clause(conditions: Dict[str, Any]) -> tuple[str, List[Any]]:
    """构建WHERE子句
    
    Args:
        conditions: 条件字典
        
    Returns:
        tuple[str, List[Any]]: WHERE子句和参数列表
    """
    if not conditions:
        return "", []
    
    where_parts = []
    params = []
    
    for key, value in conditions.items():
        if value is None:
            where_parts.append(f"{key} IS NULL")
        elif isinstance(value, (list, tuple)):
            placeholders = ','.join(['?' for _ in value])
            where_parts.append(f"{key} IN ({placeholders})")
            params.extend(value)
        elif isinstance(value, dict):
            # 支持操作符，如 {'age': {'>=': 18}}
            for op, val in value.items():
                where_parts.append(f"{key} {op} ?")
                params.append(val)
        else:
            where_parts.append(f"{key} = ?")
            params.append(value)
    
    where_clause = " AND ".join(where_parts)
    return f"WHERE {where_clause}" if where_clause else "", params


def build_order_clause(order_by: Optional[str], order_direction: str = "ASC") -> str:
    """构建ORDER BY子句
    
    Args:
        order_by: 排序字段
        order_direction: 排序方向
        
    Returns:
        str: ORDER BY子句
    """
    if not order_by:
        return ""
    
    direction = order_direction.upper()
    if direction not in ["ASC", "DESC"]:
        direction = "ASC"
    
    return f"ORDER BY {order_by} {direction}"


def build_limit_clause(limit: Optional[int], offset: Optional[int] = None) -> str:
    """构建LIMIT子句
    
    Args:
        limit: 限制数量
        offset: 偏移量
        
    Returns:
        str: LIMIT子句
    """
    if limit is None:
        return ""
    
    if offset is not None:
        return f"LIMIT {limit} OFFSET {offset}"
    else:
        return f"LIMIT {limit}"


def row_to_dict(row: sqlite3.Row) -> Dict[str, Any]:
    """将sqlite3.Row转换为字典
    
    Args:
        row: sqlite3.Row对象
        
    Returns:
        Dict[str, Any]: 字典
    """
    return dict(row) if row else {}


def rows_to_dicts(rows: List[sqlite3.Row]) -> List[Dict[str, Any]]:
    """将sqlite3.Row列表转换为字典列表
    
    Args:
        rows: sqlite3.Row列表
        
    Returns:
        List[Dict[str, Any]]: 字典列表
    """
    return [row_to_dict(row) for row in rows]


def escape_like_pattern(pattern: str) -> str:
    """转义LIKE模式中的特殊字符
    
    Args:
        pattern: 原始模式
        
    Returns:
        str: 转义后的模式
    """
    # 转义LIKE模式中的特殊字符
    pattern = pattern.replace('\\', '\\\\')
    pattern = pattern.replace('%', '\\%')
    pattern = pattern.replace('_', '\\_')
    return pattern


def validate_table_name(table_name: str) -> bool:
    """验证表名是否安全
    
    Args:
        table_name: 表名
        
    Returns:
        bool: 是否安全
    """
    # 简单的表名验证，只允许字母、数字和下划线
    import re
    return bool(re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', table_name))


def validate_column_name(column_name: str) -> bool:
    """验证列名是否安全
    
    Args:
        column_name: 列名
        
    Returns:
        bool: 是否安全
    """
    # 简单的列名验证，只允许字母、数字和下划线
    import re
    return bool(re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', column_name))