#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像验证器

提供图像内容和格式验证功能。
"""

import logging
from PIL import Image
from typing import Optional

from ..data_models.feature_models import FeatureExtractionResult
from ..utils.image_utils import validate_image_path

logger = logging.getLogger(__name__)


class ImageValidator:
    """图像验证器"""
    
    # 图像尺寸限制
    MIN_SIZE = 32
    MAX_SIZE = 10000
    
    @staticmethod
    def validate_image_content(image_path: str) -> FeatureExtractionResult:
        """验证图像内容
        
        Args:
            image_path: 图像路径
            
        Returns:
            FeatureExtractionResult: 验证结果
        """
        try:
            # 使用统一的图像验证逻辑
            try:
                from utils.image_validation import validate_image_file, get_image_info
                
                # 获取图像信息并验证
                image_info = get_image_info(image_path)
                if not image_info.get('valid_content', False):
                    error_msg = image_info.get('error', 'Unknown validation error')
                    return FeatureExtractionResult(
                        image_path=image_path,
                        success=False,
                        error_message=f"图像验证失败: {error_msg}"
                    )
            except ImportError:
                # 如果验证模块不可用，使用基础验证
                if not validate_image_path(image_path):
                    return FeatureExtractionResult(
                        image_path=image_path,
                        success=False,
                        error_message=f"图像格式不支持: {image_path}"
                    )
            except Exception as e:
                logger.warning(f"图像验证模块异常: {str(e)}")
                # 继续使用基础验证
                if not validate_image_path(image_path):
                    return FeatureExtractionResult(
                        image_path=image_path,
                        success=False,
                        error_message=f"图像格式不支持: {image_path}"
                    )
            
            # 验证通过
            return FeatureExtractionResult(
                image_path=image_path,
                success=True
            )
            
        except Exception as e:
            logger.error(f"图像内容验证异常: {str(e)}")
            return FeatureExtractionResult(
                image_path=image_path,
                success=False,
                error_message=f"图像内容验证异常: {str(e)}"
            )
    
    @staticmethod
    def validate_image_object(image: Image.Image) -> bool:
        """验证图像对象
        
        Args:
            image: PIL图像对象
            
        Returns:
            bool: 是否有效
        """
        try:
            if image is None:
                logger.error("图像对象不能为None")
                return False
            
            if not isinstance(image, Image.Image):
                logger.error("输入必须是PIL Image对象")
                return False
            
            # 验证图像对象
            if not hasattr(image, 'size') or not image.size:
                logger.error("图像对象无效")
                return False
            
            # 验证图像尺寸
            width, height = image.size
            if width < ImageValidator.MIN_SIZE or height < ImageValidator.MIN_SIZE:
                logger.error(f"图像尺寸过小: {width}x{height} < {ImageValidator.MIN_SIZE}x{ImageValidator.MIN_SIZE}")
                return False
            
            if width > ImageValidator.MAX_SIZE or height > ImageValidator.MAX_SIZE:
                logger.error(f"图像尺寸过大: {width}x{height} > {ImageValidator.MAX_SIZE}x{ImageValidator.MAX_SIZE}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"图像对象验证异常: {str(e)}")
            return False
    
    @staticmethod
    def get_image_dimensions(image: Image.Image) -> Optional[tuple]:
        """获取图像尺寸
        
        Args:
            image: PIL图像对象
            
        Returns:
            Optional[tuple]: (width, height) 或 None
        """
        try:
            if ImageValidator.validate_image_object(image):
                return image.size
            return None
            
        except Exception as e:
            logger.error(f"获取图像尺寸异常: {str(e)}")
            return None