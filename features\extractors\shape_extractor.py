"""
形状特征提取器模块

该模块负责提取图像的形状特征，包括：
- 轮廓特征（面积、周长、圆形度等）
- 傅里叶描述子
"""

import numpy as np
import cv2
from PIL import Image
import logging

from ..config import TraditionalFeatureConfig

logger = logging.getLogger(__name__)


class ShapeFeatureExtractor:
    """形状特征提取器"""
    
    def __init__(self, config: TraditionalFeatureConfig):
        """初始化形状特征提取器
        
        Args:
            config: 传统特征配置
        """
        if config is None:
            logger.error("Config is None, using default values")
            from ..config.feature_config import TraditionalFeatureConfig
            self.config = TraditionalFeatureConfig()
        else:
            self.config = config
            
        # 验证配置参数
        if not hasattr(self.config, 'n_fourier_descriptors') or self.config.n_fourier_descriptors <= 0:
            logger.warning(f"Invalid n_fourier_descriptors: {getattr(self.config, 'n_fourier_descriptors', None)}, using default: 32")
            self.config.n_fourier_descriptors = 32
    
    def extract_contour_features(self, image: Image.Image) -> np.ndarray:
        """提取轮廓特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 轮廓特征
        """
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return np.zeros(7, dtype=np.float32)  # 默认特征数量
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return np.zeros(7, dtype=np.float32)
            
            # 检查图像尺寸
            if image.size[0] == 0 or image.size[1] == 0:
                logger.error("Image has zero dimensions")
                return np.zeros(7, dtype=np.float32)
            
            # 转换为灰度图像
            try:
                if image.mode != 'L':
                    gray_image = image.convert('L')
                else:
                    gray_image = image
            except Exception as e:
                logger.error(f"Failed to convert image to grayscale: {str(e)}")
                return np.zeros(7, dtype=np.float32)
            
            # 转换为numpy数组
            try:
                img_array = np.array(gray_image)
            except Exception as e:
                logger.error(f"Failed to convert image to numpy array: {str(e)}")
                return np.zeros(7, dtype=np.float32)
            
            # 验证数组
            if img_array.size == 0:
                logger.error("Image array is empty")
                return np.zeros(7, dtype=np.float32)
            
            # 检查图像尺寸是否足够大
            if img_array.shape[0] < 3 or img_array.shape[1] < 3:
                logger.warning("Image too small for contour extraction")
                return np.zeros(7, dtype=np.float32)
            
            # 二值化
            try:
                _, binary = cv2.threshold(img_array, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                
                # 验证二值化结果
                if binary is None or binary.size == 0:
                    logger.error("Image binarization failed")
                    return np.zeros(7, dtype=np.float32)
                
            except Exception as e:
                logger.error(f"Error in image binarization: {str(e)}")
                return np.zeros(7, dtype=np.float32)
            
            # 查找轮廓
            try:
                contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                if not contours or len(contours) == 0:
                    logger.warning("No contours found in image")
                    return np.zeros(7, dtype=np.float32)
                
            except Exception as e:
                logger.error(f"Error finding contours: {str(e)}")
                return np.zeros(7, dtype=np.float32)
            
            # 选择最大轮廓
            try:
                largest_contour = max(contours, key=cv2.contourArea)
                
                if largest_contour is None or len(largest_contour) < 3:
                    logger.warning("Largest contour is too small")
                    return np.zeros(7, dtype=np.float32)
                
            except Exception as e:
                logger.error(f"Error selecting largest contour: {str(e)}")
                return np.zeros(7, dtype=np.float32)
            
            # 计算轮廓特征
            try:
                # 面积
                area = cv2.contourArea(largest_contour)
                if area <= 0:
                    logger.warning("Contour area is zero or negative")
                    return np.zeros(7, dtype=np.float32)
                
                # 周长
                perimeter = cv2.arcLength(largest_contour, True)
                if perimeter <= 0:
                    logger.warning("Contour perimeter is zero or negative")
                    return np.zeros(7, dtype=np.float32)
                
                # 圆形度
                circularity = 4 * np.pi * area / (perimeter * perimeter)
                circularity = np.clip(circularity, 0, 1)  # 限制在合理范围内
                
                # 边界矩形
                x, y, w, h = cv2.boundingRect(largest_contour)
                if w <= 0 or h <= 0:
                    logger.warning("Invalid bounding rectangle")
                    return np.zeros(7, dtype=np.float32)
                
                # 矩形度
                rect_area = w * h
                rectangularity = area / rect_area if rect_area > 0 else 0
                rectangularity = np.clip(rectangularity, 0, 1)
                
                # 长宽比
                aspect_ratio = w / h if h > 0 else 0
                aspect_ratio = np.clip(aspect_ratio, 0, 100)  # 限制在合理范围内
                
                # 凸包
                hull = cv2.convexHull(largest_contour)
                hull_area = cv2.contourArea(hull)
                solidity = area / hull_area if hull_area > 0 else 0
                solidity = np.clip(solidity, 0, 1)
                
                # 紧凑度
                compactness = area / (perimeter * perimeter) if perimeter > 0 else 0
                compactness = np.clip(compactness, 0, 1)
                
                # 组合特征
                features = np.array([
                    area, perimeter, circularity, rectangularity, 
                    aspect_ratio, solidity, compactness
                ], dtype=np.float32)
                
                # 最终验证和清理
                features = np.nan_to_num(features, nan=0.0, posinf=1e6, neginf=0.0)
                
                # 确保特征值在合理范围内
                features = np.clip(features, 0, 1e6)
                
                if features.size != 7:
                    logger.error(f"Unexpected features size: {features.size}, expected: 7")
                    return np.zeros(7, dtype=np.float32)
                
                return features
                
            except Exception as e:
                logger.error(f"Error computing contour features: {str(e)}")
                return np.zeros(7, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error extracting contour features: {str(e)}")
            return np.zeros(7, dtype=np.float32)
    
    def extract_fourier_descriptors(self, image: Image.Image) -> np.ndarray:
        """提取傅里叶描述子
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 傅里叶描述子
        """
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
            # 检查图像尺寸
            if image.size[0] == 0 or image.size[1] == 0:
                logger.error("Image has zero dimensions")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
            # 转换为灰度图像
            try:
                if image.mode != 'L':
                    gray_image = image.convert('L')
                else:
                    gray_image = image
            except Exception as e:
                logger.error(f"Failed to convert image to grayscale: {str(e)}")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
            # 转换为numpy数组
            try:
                img_array = np.array(gray_image)
            except Exception as e:
                logger.error(f"Failed to convert image to numpy array: {str(e)}")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
            # 验证数组
            if img_array.size == 0:
                logger.error("Image array is empty")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
            # 检查图像尺寸是否足够大
            if img_array.shape[0] < 3 or img_array.shape[1] < 3:
                logger.warning("Image too small for Fourier descriptor extraction")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
            # 二值化
            try:
                _, binary = cv2.threshold(img_array, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                
                # 验证二值化结果
                if binary is None or binary.size == 0:
                    logger.error("Image binarization failed")
                    return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
                
            except Exception as e:
                logger.error(f"Error in image binarization: {str(e)}")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
            # 查找轮廓
            try:
                contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                if not contours or len(contours) == 0:
                    logger.warning("No contours found in image")
                    return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
                
            except Exception as e:
                logger.error(f"Error finding contours: {str(e)}")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
            # 选择最大轮廓
            try:
                largest_contour = max(contours, key=cv2.contourArea)
                
                if largest_contour is None or len(largest_contour) < 3:
                    logger.warning("Largest contour is too small")
                    return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
                
            except Exception as e:
                logger.error(f"Error selecting largest contour: {str(e)}")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
            # 提取轮廓点
            try:
                contour_points = largest_contour.reshape(-1, 2)
                
                if len(contour_points) < 3:
                    logger.warning("Not enough contour points for Fourier descriptors")
                    return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
                
                # 验证轮廓点
                if np.any(np.isnan(contour_points)) or np.any(np.isinf(contour_points)):
                    logger.warning("Invalid contour points detected")
                    contour_points = np.nan_to_num(contour_points, nan=0.0, posinf=1e6, neginf=0.0)
                
            except Exception as e:
                logger.error(f"Error extracting contour points: {str(e)}")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
            # 转换为复数形式并计算傅里叶变换
            try:
                complex_contour = contour_points[:, 0] + 1j * contour_points[:, 1]
                
                # 验证复数轮廓
                if np.any(np.isnan(complex_contour)) or np.any(np.isinf(complex_contour)):
                    logger.warning("Invalid complex contour detected")
                    complex_contour = np.nan_to_num(complex_contour, nan=0.0, posinf=1e6, neginf=0.0)
                
                # 计算傅里叶变换
                fourier_coeffs = np.fft.fft(complex_contour)
                
                # 验证傅里叶系数
                if fourier_coeffs is None or len(fourier_coeffs) == 0:
                    logger.error("Fourier transform failed")
                    return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
                
                # 检查并处理异常值
                fourier_coeffs = np.nan_to_num(fourier_coeffs, nan=0.0, posinf=1e6, neginf=-1e6)
                
            except Exception as e:
                logger.error(f"Error computing Fourier transform: {str(e)}")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
            # 提取描述子
            try:
                # 取前n个系数的幅值作为描述子
                n_coeffs = min(self.config.n_fourier_descriptors, len(fourier_coeffs))
                descriptors = np.abs(fourier_coeffs[:n_coeffs])
                
                # 验证描述子
                if descriptors is None or len(descriptors) == 0:
                    logger.error("Fourier descriptors extraction failed")
                    return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
                
                # 检查并处理异常值
                descriptors = np.nan_to_num(descriptors, nan=0.0, posinf=1e6, neginf=0.0)
                
                # 归一化（除以第一个系数，使其平移不变）
                if len(descriptors) > 0 and descriptors[0] > 1e-10:  # 避免除零
                    descriptors = descriptors / descriptors[0]
                else:
                    logger.warning("First Fourier coefficient is too small for normalization")
                
                # 再次检查异常值
                descriptors = np.nan_to_num(descriptors, nan=0.0, posinf=1.0, neginf=0.0)
                
                # 限制值在合理范围内
                descriptors = np.clip(descriptors, 0, 100)
                
                # 填充到指定长度
                if len(descriptors) < self.config.n_fourier_descriptors:
                    padding = np.zeros(self.config.n_fourier_descriptors - len(descriptors), dtype=np.float32)
                    descriptors = np.concatenate([descriptors, padding])
                elif len(descriptors) > self.config.n_fourier_descriptors:
                    descriptors = descriptors[:self.config.n_fourier_descriptors]
                
                result = descriptors.astype(np.float32)
                
                # 最终验证
                if result.size != self.config.n_fourier_descriptors:
                    logger.error(f"Unexpected result size: {result.size}, expected: {self.config.n_fourier_descriptors}")
                    return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
                
                return result
                
            except Exception as e:
                logger.error(f"Error processing Fourier descriptors: {str(e)}")
                return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error extracting Fourier descriptors: {str(e)}")
            return np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
    
    def extract_features(self, image: Image.Image) -> np.ndarray:
        """提取所有形状特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 形状特征向量
        """
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return np.zeros(7 + self.config.n_fourier_descriptors, dtype=np.float32)
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return np.zeros(7 + self.config.n_fourier_descriptors, dtype=np.float32)
            
            features = []
            
            # 轮廓特征
            try:
                contour_features = self.extract_contour_features(image)
                
                # 验证轮廓特征
                if contour_features is None or contour_features.size == 0:
                    logger.warning("Contour features extraction failed, using zeros")
                    contour_features = np.zeros(7, dtype=np.float32)
                
                # 检查并处理异常值
                contour_features = np.nan_to_num(contour_features, nan=0.0, posinf=1e6, neginf=0.0)
                
                features.extend(contour_features)
                
            except Exception as e:
                logger.error(f"Error extracting contour features: {str(e)}")
                features.extend(np.zeros(7, dtype=np.float32))
            
            # 傅里叶描述子
            try:
                fourier_features = self.extract_fourier_descriptors(image)
                
                # 验证傅里叶特征
                if fourier_features is None or fourier_features.size == 0:
                    logger.warning("Fourier features extraction failed, using zeros")
                    fourier_features = np.zeros(self.config.n_fourier_descriptors, dtype=np.float32)
                
                # 检查并处理异常值
                fourier_features = np.nan_to_num(fourier_features, nan=0.0, posinf=1.0, neginf=0.0)
                
                features.extend(fourier_features)
                
            except Exception as e:
                logger.error(f"Error extracting Fourier features: {str(e)}")
                features.extend(np.zeros(self.config.n_fourier_descriptors, dtype=np.float32))
            
            # 转换为numpy数组
            try:
                result = np.array(features, dtype=np.float32)
                
                # 最终验证
                if result.size == 0:
                    logger.error("Final shape features array is empty")
                    return np.zeros(7 + self.config.n_fourier_descriptors, dtype=np.float32)
                
                # 最终清理异常值
                result = np.nan_to_num(result, nan=0.0, posinf=1e6, neginf=0.0)
                
                return result
                
            except Exception as e:
                logger.error(f"Error creating final shape features array: {str(e)}")
                return np.zeros(7 + self.config.n_fourier_descriptors, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error extracting shape features: {str(e)}")
            return np.zeros(7 + self.config.n_fourier_descriptors, dtype=np.float32)