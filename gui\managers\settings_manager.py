#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置管理器

该模块负责管理应用程序的设置加载、保存和应用。
"""

import os
from typing import Dict, Any, Optional
from PyQt6.QtCore import QObject, pyqtSignal, QSettings, QSize, QPoint
from PyQt6.QtWidgets import QMainWindow, QApplication
from PyQt6.QtGui import QFont

from utils.logger_mixin import LoggerMixin
from config.app_config import AppConfig
from config.ui_config import UIConfig
from gui.themes import ThemeManager, ThemeType


class SettingsManager(QObject, LoggerMixin):
    """设置管理器"""
    
    # 信号
    settingsLoaded = pyqtSignal(dict)
    settingsSaved = pyqtSignal()
    settingsChanged = pyqtSignal(str, object)  # 设置键, 新值
    fontChanged = pyqtSignal(QFont)
    themeChanged = pyqtSignal(ThemeType)
    
    def __init__(self, main_window: QMainWindow):
        super().__init__()
        self.main_window = main_window
        self.app_config = AppConfig()
        self.ui_config = UIConfig()
        self.theme_manager = ThemeManager(self.ui_config)
        
        # QSettings 实例
        self.settings = QSettings(
            "FabricSearch",
            "FabricSearchV2"
        )
        
        # 当前设置缓存
        self._current_settings = {}
        
    def load_settings(self) -> Dict[str, Any]:
        """加载设置
        
        Returns:
            Dict[str, Any]: 设置字典
        """
        try:
            settings = {}
            
            # 窗口设置
            settings['window'] = self._load_window_settings()
            
            # 界面设置
            settings['ui'] = self._load_ui_settings()
            
            # 搜索设置
            settings['search'] = self._load_search_settings()
            
            # 数据库设置
            settings['database'] = self._load_database_settings()
            
            # 模型设置
            settings['model'] = self._load_model_settings()
            
            # 缓存当前设置
            self._current_settings = settings.copy()
            
            self.settingsLoaded.emit(settings)
            self.logger.info("设置加载完成")
            
            return settings
            
        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
            return self._get_default_settings()
    
    def save_settings(self, settings: Optional[Dict[str, Any]] = None):
        """保存设置
        
        Args:
            settings: 要保存的设置，如果为None则保存当前设置
        """
        try:
            if settings is None:
                settings = self._current_settings
            
            # 保存窗口设置
            self._save_window_settings(settings.get('window', {}))
            
            # 保存界面设置
            self._save_ui_settings(settings.get('ui', {}))
            
            # 保存搜索设置
            self._save_search_settings(settings.get('search', {}))
            
            # 保存数据库设置
            self._save_database_settings(settings.get('database', {}))
            
            # 保存模型设置
            self._save_model_settings(settings.get('model', {}))
            
            # 同步到磁盘
            self.settings.sync()
            
            # 更新缓存
            self._current_settings = settings.copy()
            
            self.settingsSaved.emit()
            self.logger.info("设置保存完成")
            
        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
    
    def apply_settings(self, settings: Optional[Dict[str, Any]] = None):
        """应用设置
        
        Args:
            settings: 要应用的设置，如果为None则应用当前设置
        """
        try:
            if settings is None:
                settings = self._current_settings
            
            # 应用窗口设置
            self._apply_window_settings(settings.get('window', {}))
            
            # 应用界面设置
            self._apply_ui_settings(settings.get('ui', {}))
            
            self.logger.info("设置应用完成")
            
        except Exception as e:
            self.logger.error(f"应用设置失败: {e}")
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """获取设置值
        
        Args:
            key: 设置键（支持点分隔的嵌套键）
            default: 默认值
            
        Returns:
            Any: 设置值
        """
        try:
            keys = key.split('.')
            value = self._current_settings
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception:
            return default
    
    def set_setting(self, key: str, value: Any):
        """设置值
        
        Args:
            key: 设置键（支持点分隔的嵌套键）
            value: 设置值
        """
        try:
            keys = key.split('.')
            target = self._current_settings
            
            # 导航到目标位置
            for k in keys[:-1]:
                if k not in target:
                    target[k] = {}
                target = target[k]
            
            # 设置值
            target[keys[-1]] = value
            
            self.settingsChanged.emit(key, value)
            
        except Exception as e:
            self.logger.error(f"设置值失败: {e}")
    
    def _load_window_settings(self) -> Dict[str, Any]:
        """加载窗口设置"""
        self.settings.beginGroup("window")
        
        settings = {
            'size': self.settings.value('size', QSize(1200, 800)),
            'position': self.settings.value('position', QPoint(100, 100)),
            'maximized': self.settings.value('maximized', False, type=bool),
            'fullscreen': self.settings.value('fullscreen', False, type=bool),
            'toolbar_visible': self.settings.value('toolbar_visible', True, type=bool)
        }
        
        self.settings.endGroup()
        return settings
    
    def _load_ui_settings(self) -> Dict[str, Any]:
        """加载界面设置"""
        self.settings.beginGroup("ui")
        
        # 字体设置
        font_family = self.settings.value('font_family', 'Microsoft YaHei')
        font_size = self.settings.value('font_size', 9, type=int)
        
        # 主题设置
        theme_name = self.settings.value('theme', 'light')
        theme_type = ThemeType.LIGHT if theme_name == 'light' else ThemeType.DARK
        
        settings = {
            'font': QFont(font_family, font_size),
            'theme': theme_type,
            'language': self.settings.value('language', 'zh_CN')
        }
        
        self.settings.endGroup()
        return settings
    
    def _load_search_settings(self) -> Dict[str, Any]:
        """加载搜索设置"""
        self.settings.beginGroup("search")
        
        settings = {
            'max_results': self.settings.value('max_results', 100, type=int),
            'similarity_threshold': self.settings.value('similarity_threshold', 0.5, type=float),
            'auto_search': self.settings.value('auto_search', False, type=bool),
            'search_timeout': self.settings.value('search_timeout', 30, type=int)
        }
        
        self.settings.endGroup()
        return settings
    
    def _load_database_settings(self) -> Dict[str, Any]:
        """加载数据库设置"""
        self.settings.beginGroup("database")
        
        settings = {
            'path': self.settings.value('path', ''),
            'auto_backup': self.settings.value('auto_backup', True, type=bool),
            'backup_interval': self.settings.value('backup_interval', 24, type=int)
        }
        
        self.settings.endGroup()
        return settings
    
    def _load_model_settings(self) -> Dict[str, Any]:
        """加载模型设置"""
        self.settings.beginGroup("model")
        
        settings = {
            'current_model': self.settings.value('current_model', ''),
            'model_path': self.settings.value('model_path', ''),
            'use_gpu': self.settings.value('use_gpu', True, type=bool),
            'batch_size': self.settings.value('batch_size', 32, type=int)
        }
        
        self.settings.endGroup()
        return settings
    
    def _save_window_settings(self, settings: Dict[str, Any]):
        """保存窗口设置"""
        self.settings.beginGroup("window")
        
        # 获取当前窗口状态
        if not self.main_window.isMaximized() and not self.main_window.isFullScreen():
            self.settings.setValue('size', self.main_window.size())
            self.settings.setValue('position', self.main_window.pos())
        
        self.settings.setValue('maximized', self.main_window.isMaximized())
        self.settings.setValue('fullscreen', self.main_window.isFullScreen())
        
        # 保存其他设置
        for key, value in settings.items():
            if key not in ['size', 'position', 'maximized', 'fullscreen']:
                self.settings.setValue(key, value)
        
        self.settings.endGroup()
    
    def _save_ui_settings(self, settings: Dict[str, Any]):
        """保存界面设置"""
        self.settings.beginGroup("ui")
        
        if 'font' in settings:
            font = settings['font']
            self.settings.setValue('font_family', font.family())
            self.settings.setValue('font_size', font.pointSize())
        
        if 'theme' in settings:
            theme_name = 'light' if settings['theme'] == ThemeType.LIGHT else 'dark'
            self.settings.setValue('theme', theme_name)
        
        if 'language' in settings:
            self.settings.setValue('language', settings['language'])
        
        self.settings.endGroup()
    
    def _save_search_settings(self, settings: Dict[str, Any]):
        """保存搜索设置"""
        self.settings.beginGroup("search")
        
        for key, value in settings.items():
            self.settings.setValue(key, value)
        
        self.settings.endGroup()
    
    def _save_database_settings(self, settings: Dict[str, Any]):
        """保存数据库设置"""
        self.settings.beginGroup("database")
        
        for key, value in settings.items():
            self.settings.setValue(key, value)
        
        self.settings.endGroup()
    
    def _save_model_settings(self, settings: Dict[str, Any]):
        """保存模型设置"""
        self.settings.beginGroup("model")
        
        for key, value in settings.items():
            self.settings.setValue(key, value)
        
        self.settings.endGroup()
    
    def _apply_window_settings(self, settings: Dict[str, Any]):
        """应用窗口设置"""
        # 设置窗口大小和位置
        if 'size' in settings and 'position' in settings:
            self.main_window.resize(settings['size'])
            self.main_window.move(settings['position'])
        
        # 设置窗口状态
        if settings.get('maximized', False):
            self.main_window.showMaximized()
        elif settings.get('fullscreen', False):
            self.main_window.showFullScreen()
    
    def _apply_ui_settings(self, settings: Dict[str, Any]):
        """应用界面设置"""
        # 应用字体
        if 'font' in settings:
            font = settings['font']
            QApplication.instance().setFont(font)
            self.fontChanged.emit(font)
        
        # 应用主题
        if 'theme' in settings:
            theme_str = settings['theme']
            # 将字符串转换为 ThemeType 枚举
            theme_type_map = {
                'light': ThemeType.LIGHT,
                'dark': ThemeType.DARK,
                'auto': ThemeType.AUTO,
                'custom': ThemeType.CUSTOM
            }
            theme_type = theme_type_map.get(theme_str, ThemeType.LIGHT)
            
            theme_name = 'light' if theme_type == ThemeType.LIGHT else 'dark'
            self.theme_manager.apply_theme(theme_name)
            self.themeChanged.emit(theme_type)
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """获取默认设置"""
        return {
            'window': {
                'size': QSize(1200, 800),
                'position': QPoint(100, 100),
                'maximized': False,
                'fullscreen': False,
                'toolbar_visible': True
            },
            'ui': {
                'font': QFont('Microsoft YaHei', 9),
                'theme': ThemeType.LIGHT,
                'language': 'zh_CN'
            },
            'search': {
                'max_results': 100,
                'similarity_threshold': 0.5,
                'auto_search': False,
                'search_timeout': 30
            },
            'database': {
                'path': '',
                'auto_backup': True,
                'backup_interval': 24
            },
            'model': {
                'current_model': '',
                'model_path': '',
                'use_gpu': True,
                'batch_size': 32
            }
        }
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        try:
            # 清除所有设置
            self.settings.clear()
            
            # 加载默认设置
            default_settings = self._get_default_settings()
            self._current_settings = default_settings
            
            # 应用默认设置
            self.apply_settings(default_settings)
            
            # 保存默认设置
            self.save_settings(default_settings)
            
            self.logger.info("设置已重置为默认值")
            
        except Exception as e:
            self.logger.error(f"重置设置失败: {e}")
    
    def export_settings(self, file_path: str):
        """导出设置到文件
        
        Args:
            file_path: 导出文件路径
        """
        try:
            import json
            
            # 转换设置为可序列化的格式
            exportable_settings = self._make_settings_exportable(
                self._current_settings
            )
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(exportable_settings, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"设置已导出到: {file_path}")
            
        except Exception as e:
            self.logger.error(f"导出设置失败: {e}")
    
    def import_settings(self, file_path: str):
        """从文件导入设置
        
        Args:
            file_path: 导入文件路径
        """
        try:
            import json
            
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # 转换设置为内部格式
            settings = self._make_settings_importable(imported_settings)
            
            # 应用和保存设置
            self._current_settings = settings
            self.apply_settings(settings)
            self.save_settings(settings)
            
            self.logger.info(f"设置已从文件导入: {file_path}")
            
        except Exception as e:
            self.logger.error(f"导入设置失败: {e}")
    
    def _make_settings_exportable(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """将设置转换为可导出的格式"""
        # 这里需要处理QFont、QSize、QPoint等Qt对象
        # 简化实现，实际应该递归处理所有Qt对象
        return settings
    
    def _make_settings_importable(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """将导入的设置转换为内部格式"""
        # 这里需要将字典转换回Qt对象
        # 简化实现，实际应该递归处理所有需要转换的对象
        return settings