#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级设置页面

该模块提供应用程序高级设置的用户界面。
"""

from typing import Dict, Any

from PyQt6.QtWidgets import QVBoxLayout, QHBoxLayout, QFormLayout
from PyQt6.QtCore import Qt

from .settings_base import BaseSettingsWidget


class AdvancedSettingsWidget(BaseSettingsWidget):
    """高级设置组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # 日志设置
        log_group = self.widget_factory.create_group_box(title="日志设置", layout_type="form")
        log_layout = log_group.layout()
        
        self.log_level_combo = self.widget_factory.create_combo_box(
            items=["DEBUG", "INFO", "WARNING", "ERROR"]
        )
        self.log_level_combo.setCurrentText("INFO")
        log_layout.addRow("日志级别:", self.log_level_combo)
        
        layout.addWidget(log_group)
        
        # 调试设置
        debug_group = self.widget_factory.create_group_box(title="调试设置", layout_type="vbox")
        debug_layout = debug_group.layout()
        
        self.debug_mode_cb = self.widget_factory.create_checkbox(
            "启用调试模式", checked=False
        )
        debug_layout.addWidget(self.debug_mode_cb)
        
        debug_warning = self.widget_factory.create_label(
            "警告: 启用调试模式可能会降低应用程序性能"
        )
        debug_warning.setStyleSheet("color: #FF5555; font-size: 9pt;")
        debug_layout.addWidget(debug_warning)
        
        layout.addWidget(debug_group)
        
        # 更新设置
        update_group = self.widget_factory.create_group_box(title="更新设置", layout_type="vbox")
        update_layout = update_group.layout()
        
        self.auto_update_cb = self.widget_factory.create_checkbox(
            "自动检查更新", checked=True
        )
        update_layout.addWidget(self.auto_update_cb)
        
        layout.addWidget(update_group)
        
        # 遥测设置
        telemetry_group = self.widget_factory.create_group_box(title="遥测设置", layout_type="vbox")
        telemetry_layout = telemetry_group.layout()
        
        self.telemetry_cb = self.widget_factory.create_checkbox(
            "允许匿名统计数据收集", checked=True
        )
        telemetry_layout.addWidget(self.telemetry_cb)
        
        telemetry_note = self.widget_factory.create_label(
            "注: 我们只收集匿名使用数据，帮助改进应用程序。不会收集任何个人信息。"
        )
        telemetry_note.setStyleSheet("color: #666; font-size: 9pt;")
        telemetry_layout.addWidget(telemetry_note)
        
        layout.addWidget(telemetry_group)
        
        # 实验性功能
        experimental_group = self.widget_factory.create_group_box(title="实验性功能", layout_type="vbox")
        experimental_layout = experimental_group.layout()
        
        self.experimental_features_cb = self.widget_factory.create_checkbox(
            "启用实验性功能", checked=False
        )
        experimental_layout.addWidget(self.experimental_features_cb)
        
        experimental_warning = self.widget_factory.create_label(
            "警告: 实验性功能可能不稳定，使用风险自负"
        )
        experimental_warning.setStyleSheet("color: #FF5555; font-size: 9pt;")
        experimental_layout.addWidget(experimental_warning)
        
        layout.addWidget(experimental_group)
        
        layout.addStretch()
    
    def get_settings(self) -> Dict[str, Any]:
        """获取设置"""
        return {
            "log_level": self.log_level_combo.currentText(),
            "debug_mode": self.debug_mode_cb.isChecked(),
            "auto_update": self.auto_update_cb.isChecked(),
            "telemetry_enabled": self.telemetry_cb.isChecked(),
            "experimental_features": self.experimental_features_cb.isChecked()
        }
    
    def set_settings(self, settings: Dict[str, Any]):
        """设置设置"""
        self.log_level_combo.setCurrentText(settings.get("log_level", "INFO"))
        self.debug_mode_cb.setChecked(settings.get("debug_mode", False))
        self.auto_update_cb.setChecked(settings.get("auto_update", True))
        self.telemetry_cb.setChecked(settings.get("telemetry_enabled", True))
        self.experimental_features_cb.setChecked(settings.get("experimental_features", False))