#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征索引模块

提供特征索引的数据结构和操作。
"""

import time
import logging
import numpy as np
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime

from features.config.search_config import FeatureIndexInfo
from features.utils.feature_utils import validate_features

logger = logging.getLogger(__name__)


@dataclass
class IndexEntry:
    """索引条目"""
    item_id: Union[int, str]
    features: np.ndarray
    metadata: Dict[str, Any] = field(default_factory=dict)
    feature_type: str = "deep"
    model_name: str = "unknown"
    extraction_time: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            'item_id': self.item_id,
            'features': self.features.tolist() if isinstance(self.features, np.ndarray) else self.features,
            'metadata': self.metadata,
            'feature_type': self.feature_type,
            'model_name': self.model_name,
            'extraction_time': self.extraction_time,
            'created_at': self.created_at.isoformat() if isinstance(self.created_at, datetime) else self.created_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'IndexEntry':
        """从字典创建实例
        
        Args:
            data: 字典数据
            
        Returns:
            IndexEntry: 索引条目实例
        """
        # 处理特征数组
        if 'features' in data and isinstance(data['features'], list):
            data['features'] = np.array(data['features'])
        
        # 处理时间
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        
        return cls(**data)


class FeatureIndex:
    """特征索引"""
    
    def __init__(self, index_name: str = "default"):
        """初始化特征索引
        
        Args:
            index_name: 索引名称
        """
        self.index_name = index_name
        self.entries: Dict[Union[int, str], IndexEntry] = {}
        self.created_at = datetime.now()
        self.last_updated = datetime.now()
        
        logger.info(f"Initialized FeatureIndex: {index_name}")
    
    def add_entry(self, entry: IndexEntry) -> bool:
        """添加索引条目
        
        Args:
            entry: 索引条目
            
        Returns:
            bool: 添加是否成功
        """
        try:
            # 验证特征
            if not validate_features(entry.features):
                logger.error(f"Invalid features for item {entry.item_id}")
                return False
            
            self.entries[entry.item_id] = entry
            self.last_updated = datetime.now()
            
            logger.debug(f"Added entry for item {entry.item_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding entry for item {entry.item_id}: {str(e)}")
            return False
    
    def get_entry(self, item_id: Union[int, str]) -> Optional[IndexEntry]:
        """获取索引条目
        
        Args:
            item_id: 项目ID
            
        Returns:
            Optional[IndexEntry]: 索引条目
        """
        return self.entries.get(item_id)
    
    def remove_entry(self, item_id: Union[int, str]) -> bool:
        """移除索引条目
        
        Args:
            item_id: 项目ID
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if item_id in self.entries:
                del self.entries[item_id]
                self.last_updated = datetime.now()
                logger.debug(f"Removed entry for item {item_id}")
                return True
            else:
                logger.warning(f"Entry not found for item {item_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error removing entry for item {item_id}: {str(e)}")
            return False
    
    def update_entry(self, item_id: Union[int, str], entry: IndexEntry) -> bool:
        """更新索引条目
        
        Args:
            item_id: 项目ID
            entry: 新的索引条目
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if item_id not in self.entries:
                logger.warning(f"Entry not found for item {item_id}, adding new entry")
            
            return self.add_entry(entry)
            
        except Exception as e:
            logger.error(f"Error updating entry for item {item_id}: {str(e)}")
            return False
    
    def get_features_matrix(self) -> tuple[np.ndarray, List[Union[int, str]]]:
        """获取特征矩阵
        
        Returns:
            tuple: (特征矩阵, 项目ID列表)
        """
        try:
            if not self.entries:
                return np.array([]), []
            
            features_list = []
            item_ids = []
            
            for item_id, entry in self.entries.items():
                if validate_features(entry.features):
                    features_list.append(entry.features)
                    item_ids.append(item_id)
            
            if not features_list:
                return np.array([]), []
            
            features_matrix = np.vstack(features_list)
            return features_matrix, item_ids
            
        except Exception as e:
            logger.error(f"Error getting features matrix: {str(e)}")
            return np.array([]), []
    
    def filter_by_metadata(self, filters: Dict[str, Any]) -> List[IndexEntry]:
        """根据元数据过滤条目
        
        Args:
            filters: 过滤条件
            
        Returns:
            List[IndexEntry]: 过滤后的条目列表
        """
        try:
            filtered_entries = []
            
            for entry in self.entries.values():
                match = True
                for key, value in filters.items():
                    if key not in entry.metadata or entry.metadata[key] != value:
                        match = False
                        break
                
                if match:
                    filtered_entries.append(entry)
            
            return filtered_entries
            
        except Exception as e:
            logger.error(f"Error filtering by metadata: {str(e)}")
            return []
    
    def filter_by_feature_type(self, feature_type: str) -> List[IndexEntry]:
        """根据特征类型过滤条目
        
        Args:
            feature_type: 特征类型
            
        Returns:
            List[IndexEntry]: 过滤后的条目列表
        """
        try:
            return [
                entry for entry in self.entries.values() 
                if entry.feature_type == feature_type
            ]
            
        except Exception as e:
            logger.error(f"Error filtering by feature type: {str(e)}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            total_entries = len(self.entries)
            
            if total_entries == 0:
                return {
                    'index_name': self.index_name,
                    'total_entries': 0,
                    'feature_types': {},
                    'models_used': {},
                    'feature_dimensions': {},
                    'created_at': self.created_at,
                    'last_updated': self.last_updated
                }
            
            # 统计特征类型
            feature_types = {}
            models_used = {}
            feature_dimensions = {}
            
            for entry in self.entries.values():
                # 特征类型统计
                feature_type = entry.feature_type
                feature_types[feature_type] = feature_types.get(feature_type, 0) + 1
                
                # 模型统计
                model = entry.model_name
                models_used[model] = models_used.get(model, 0) + 1
                
                # 特征维度统计
                if isinstance(entry.features, np.ndarray):
                    dim = entry.features.shape[0] if entry.features.ndim == 1 else entry.features.shape[1]
                    feature_dimensions[feature_type] = dim
            
            return {
                'index_name': self.index_name,
                'total_entries': total_entries,
                'feature_types': feature_types,
                'models_used': models_used,
                'feature_dimensions': feature_dimensions,
                'created_at': self.created_at,
                'last_updated': self.last_updated
            }
            
        except Exception as e:
            logger.error(f"Error getting statistics: {str(e)}")
            return {'error': str(e)}
    
    def clear(self):
        """清空索引"""
        self.entries.clear()
        self.last_updated = datetime.now()
        logger.info(f"Cleared index: {self.index_name}")
    
    def merge_with(self, other_index: 'FeatureIndex', 
                   overwrite_existing: bool = False) -> bool:
        """与另一个索引合并
        
        Args:
            other_index: 另一个特征索引
            overwrite_existing: 是否覆盖已存在的条目
            
        Returns:
            bool: 合并是否成功
        """
        try:
            merged_count = 0
            skipped_count = 0
            
            for item_id, entry in other_index.entries.items():
                if item_id in self.entries and not overwrite_existing:
                    skipped_count += 1
                    continue
                
                if self.add_entry(entry):
                    merged_count += 1
            
            logger.info(
                f"Merged {merged_count} entries from {other_index.index_name}, "
                f"skipped {skipped_count} existing entries"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error merging with index {other_index.index_name}: {str(e)}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            'index_name': self.index_name,
            'entries': {str(k): v.to_dict() for k, v in self.entries.items()},
            'created_at': self.created_at.isoformat(),
            'last_updated': self.last_updated.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FeatureIndex':
        """从字典创建实例
        
        Args:
            data: 字典数据
            
        Returns:
            FeatureIndex: 特征索引实例
        """
        index = cls(data.get('index_name', 'default'))
        
        # 处理时间
        if 'created_at' in data:
            index.created_at = datetime.fromisoformat(data['created_at'])
        if 'last_updated' in data:
            index.last_updated = datetime.fromisoformat(data['last_updated'])
        
        # 处理条目
        if 'entries' in data:
            for item_id, entry_data in data['entries'].items():
                try:
                    # 尝试转换item_id为原始类型
                    if item_id.isdigit():
                        item_id = int(item_id)
                    
                    entry = IndexEntry.from_dict(entry_data)
                    entry.item_id = item_id  # 确保ID一致
                    index.entries[item_id] = entry
                    
                except Exception as e:
                    logger.warning(f"Error loading entry {item_id}: {str(e)}")
                    continue
        
        return index
    
    def __len__(self) -> int:
        """返回索引条目数量"""
        return len(self.entries)
    
    def __contains__(self, item_id: Union[int, str]) -> bool:
        """检查是否包含指定项目"""
        return item_id in self.entries
    
    def __iter__(self):
        """迭代索引条目"""
        return iter(self.entries.values())