#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础仓库类

该模块提供数据库仓库的基础功能。
"""

import logging
from typing import Optional, Dict, Any, List
from abc import ABC, abstractmethod

from ..database_manager import DatabaseManager
from ..utils.database_utils import serialize_json, deserialize_json, row_to_dict, rows_to_dicts
from ..exceptions.database_exceptions import RepositoryError


class BaseRepository(ABC):
    """基础仓库类"""
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化基础仓库
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @property
    @abstractmethod
    def table_name(self) -> str:
        """表名"""
        pass
    
    def _serialize_field(self, value: Any) -> Optional[str]:
        """序列化字段值
        
        Args:
            value: 字段值
            
        Returns:
            Optional[str]: 序列化后的字符串
        """
        return serialize_json(value)
    
    def _deserialize_field(self, value: Optional[str]) -> Any:
        """反序列化字段值
        
        Args:
            value: 序列化的字符串
            
        Returns:
            Any: 反序列化后的值
        """
        return deserialize_json(value)
    
    def _row_to_dict(self, row) -> Dict[str, Any]:
        """将数据库行转换为字典
        
        Args:
            row: 数据库行
            
        Returns:
            Dict[str, Any]: 字典
        """
        return row_to_dict(row)
    
    def _rows_to_dicts(self, rows: List) -> List[Dict[str, Any]]:
        """将数据库行列表转换为字典列表
        
        Args:
            rows: 数据库行列表
            
        Returns:
            List[Dict[str, Any]]: 字典列表
        """
        return rows_to_dicts(rows)
    
    def exists(self, record_id: int) -> bool:
        """检查记录是否存在
        
        Args:
            record_id: 记录ID
            
        Returns:
            bool: 记录是否存在
        """
        try:
            sql = f"SELECT 1 FROM {self.table_name} WHERE id = ?"
            result = self.db_manager.execute_query(sql, (record_id,))
            return len(result) > 0
        except Exception as e:
            self.logger.error(f"检查记录存在性失败: {e}")
            raise RepositoryError(f"检查记录存在性失败: {e}")
    
    def count(self, conditions: Optional[Dict[str, Any]] = None) -> int:
        """统计记录数量
        
        Args:
            conditions: 查询条件
            
        Returns:
            int: 记录数量
        """
        try:
            sql = f"SELECT COUNT(*) FROM {self.table_name}"
            params = []
            
            if conditions:
                from ..utils.database_utils import build_where_clause
                where_clause, where_params = build_where_clause(conditions)
                if where_clause:
                    sql += f" {where_clause}"
                    params.extend(where_params)
            
            result = self.db_manager.execute_query(sql, tuple(params))
            return result[0][0] if result else 0
            
        except Exception as e:
            self.logger.error(f"统计记录数量失败: {e}")
            raise RepositoryError(f"统计记录数量失败: {e}")
    
    def delete_by_id(self, record_id: int) -> bool:
        """根据ID删除记录
        
        Args:
            record_id: 记录ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            sql = f"DELETE FROM {self.table_name} WHERE id = ?"
            affected_rows = self.db_manager.execute_update(sql, (record_id,))
            return affected_rows > 0
        except Exception as e:
            self.logger.error(f"删除记录失败: {e}")
            raise RepositoryError(f"删除记录失败: {e}")
    
    def get_by_id(self, record_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取记录
        
        Args:
            record_id: 记录ID
            
        Returns:
            Optional[Dict[str, Any]]: 记录字典，如果不存在则返回None
        """
        try:
            sql = f"SELECT * FROM {self.table_name} WHERE id = ?"
            result = self.db_manager.execute_query(sql, (record_id,))
            return self._row_to_dict(result[0]) if result else None
        except Exception as e:
            self.logger.error(f"根据ID获取记录失败: {e}")
            raise RepositoryError(f"根据ID获取记录失败: {e}")