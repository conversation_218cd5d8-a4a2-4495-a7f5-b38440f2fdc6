#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型配置模块

该模块定义了深度学习模型的配置类，管理各种模型的参数和设置。
"""

import logging
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass, field


@dataclass
class ModelConfig:
    """模型配置数据类"""
    name: str
    architecture: str
    input_size: Tuple[int, int, int]
    feature_dim: int
    pretrained: bool = True
    requires_grad: bool = False
    dropout_rate: float = 0.0
    batch_norm: bool = True
    activation: str = 'relu'
    pooling: str = 'adaptive_avg'
    normalization: Dict[str, List[float]] = field(default_factory=lambda: {
        'mean': [0.485, 0.456, 0.406],
        'std': [0.229, 0.224, 0.225]
    })
    model_url: Optional[str] = None
    description: str = ''


class ModelConfigManager:
    """模型配置管理器"""
    
    def __init__(self):
        """初始化模型配置管理器"""
        self._configs = self._initialize_configs()
        self._current_model = 'resnet50'
    
    def _initialize_configs(self) -> Dict[str, ModelConfig]:
        """初始化模型配置
        
        Returns:
            Dict[str, ModelConfig]: 模型配置字典
        """
        configs = {}
        
        # ResNet50配置
        configs['resnet50'] = ModelConfig(
            name='resnet50',
            architecture='ResNet',
            input_size=(3, 224, 224),
            feature_dim=2048,
            pretrained=True,
            description='ResNet-50深度残差网络，适合通用图像特征提取'
        )
        
        # VGG16配置
        configs['vgg16'] = ModelConfig(
            name='vgg16',
            architecture='VGG',
            input_size=(3, 224, 224),
            feature_dim=4096,
            pretrained=True,
            description='VGG-16卷积神经网络，适合细节丰富的图像'
        )
        
        # EfficientNet配置
        configs['efficientnet'] = ModelConfig(
            name='efficientnet',
            architecture='EfficientNet',
            input_size=(3, 224, 224),
            feature_dim=1280,
            pretrained=True,
            description='EfficientNet-B0，高效的轻量级模型'
        )
        
        # EfficientNetV2配置
        configs['efficientnetv2'] = ModelConfig(
            name='efficientnetv2',
            architecture='EfficientNetV2',
            input_size=(3, 224, 224),
            feature_dim=1280,
            pretrained=True,
            description='EfficientNet-V2，改进的高效模型'
        )
        
        # ConvNeXt配置
        configs['convnext'] = ModelConfig(
            name='convnext',
            architecture='ConvNeXt',
            input_size=(3, 224, 224),
            feature_dim=768,
            pretrained=True,
            description='ConvNeXt，现代化的卷积网络架构'
        )
        
        # ConvNeXtV2配置
        configs['convnextv2'] = ModelConfig(
            name='convnextv2',
            architecture='ConvNeXtV2',
            input_size=(3, 224, 224),
            feature_dim=768,
            pretrained=True,
            description='ConvNeXt-V2，改进的现代卷积网络'
        )
        
        # Vision Transformer配置
        configs['vit'] = ModelConfig(
            name='vit',
            architecture='ViT',
            input_size=(3, 224, 224),
            feature_dim=768,
            pretrained=True,
            description='Vision Transformer，基于注意力机制的模型'
        )
        
        # Swin Transformer配置
        configs['swin'] = ModelConfig(
            name='swin',
            architecture='SwinTransformer',
            input_size=(3, 224, 224),
            feature_dim=768,
            pretrained=True,
            description='Swin Transformer，分层的视觉Transformer'
        )
        
        return configs
    
    def get_config(self, model_name: str) -> Optional[ModelConfig]:
        """获取模型配置
        
        Args:
            model_name: 模型名称
            
        Returns:
            Optional[ModelConfig]: 模型配置，如果不存在则返回None
        """
        return self._configs.get(model_name)
    
    def get_all_configs(self) -> Dict[str, ModelConfig]:
        """获取所有模型配置
        
        Returns:
            Dict[str, ModelConfig]: 所有模型配置
        """
        return self._configs.copy()
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表
        
        Returns:
            List[str]: 可用模型名称列表
        """
        return list(self._configs.keys())
    
    def add_config(self, config: ModelConfig) -> bool:
        """添加模型配置
        
        Args:
            config: 模型配置
            
        Returns:
            bool: 添加是否成功
        """
        try:
            if config.name in self._configs:
                logging.warning(f"模型配置 {config.name} 已存在，将被覆盖")
            
            self._configs[config.name] = config
            logging.info(f"模型配置 {config.name} 添加成功")
            return True
            
        except Exception as e:
            logging.error(f"添加模型配置失败: {e}")
            return False
    
    def remove_config(self, model_name: str) -> bool:
        """移除模型配置
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if model_name not in self._configs:
                logging.warning(f"模型配置 {model_name} 不存在")
                return False
            
            del self._configs[model_name]
            logging.info(f"模型配置 {model_name} 移除成功")
            return True
            
        except Exception as e:
            logging.error(f"移除模型配置失败: {e}")
            return False
    
    def validate_config(self, model_name: str) -> bool:
        """验证模型配置
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 配置是否有效
        """
        config = self.get_config(model_name)
        if config is None:
            logging.error(f"模型配置 {model_name} 不存在")
            return False
        
        try:
            # 验证输入尺寸
            if len(config.input_size) != 3:
                logging.error(f"输入尺寸格式错误: {config.input_size}")
                return False
            
            # 验证特征维度
            if config.feature_dim <= 0:
                logging.error(f"特征维度必须大于0: {config.feature_dim}")
                return False
            
            # 验证dropout率
            if not 0.0 <= config.dropout_rate <= 1.0:
                logging.error(f"Dropout率必须在0-1之间: {config.dropout_rate}")
                return False
            
            # 验证归一化参数
            if 'mean' not in config.normalization or 'std' not in config.normalization:
                logging.error("归一化参数缺失")
                return False
            
            if len(config.normalization['mean']) != 3 or len(config.normalization['std']) != 3:
                logging.error("归一化参数长度错误")
                return False
            
            logging.info(f"模型配置 {model_name} 验证通过")
            return True
            
        except Exception as e:
            logging.error(f"验证模型配置失败: {e}")
            return False
    
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """获取模型信息
        
        Args:
            model_name: 模型名称
            
        Returns:
            Dict[str, Any]: 模型信息字典
        """
        config = self.get_config(model_name)
        if config is None:
            return {}
        
        return {
            'name': config.name,
            'architecture': config.architecture,
            'input_size': config.input_size,
            'feature_dim': config.feature_dim,
            'pretrained': config.pretrained,
            'description': config.description,
            'parameters': {
                'requires_grad': config.requires_grad,
                'dropout_rate': config.dropout_rate,
                'batch_norm': config.batch_norm,
                'activation': config.activation,
                'pooling': config.pooling
            },
            'normalization': config.normalization
        }
    
    def set_current_model(self, model_name: str) -> bool:
        """设置当前模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 设置是否成功
        """
        if model_name not in self._configs:
            logging.error(f"模型 {model_name} 不存在")
            return False
        
        self._current_model = model_name
        logging.info(f"当前模型设置为: {model_name}")
        return True
    
    def get_current_model(self) -> str:
        """获取当前模型名称
        
        Returns:
            str: 当前模型名称
        """
        return self._current_model
    
    def get_current_config(self) -> Optional[ModelConfig]:
        """获取当前模型配置
        
        Returns:
            Optional[ModelConfig]: 当前模型配置
        """
        return self.get_config(self._current_model)
    
    def compare_models(self, model1: str, model2: str) -> Dict[str, Any]:
        """比较两个模型
        
        Args:
            model1: 第一个模型名称
            model2: 第二个模型名称
            
        Returns:
            Dict[str, Any]: 比较结果
        """
        config1 = self.get_config(model1)
        config2 = self.get_config(model2)
        
        if config1 is None or config2 is None:
            return {'error': '模型不存在'}
        
        return {
            'models': [model1, model2],
            'architectures': [config1.architecture, config2.architecture],
            'input_sizes': [config1.input_size, config2.input_size],
            'feature_dims': [config1.feature_dim, config2.feature_dim],
            'pretrained': [config1.pretrained, config2.pretrained],
            'descriptions': [config1.description, config2.description]
        }
    
    def get_recommended_model(self, use_case: str = 'general') -> str:
        """获取推荐模型
        
        Args:
            use_case: 使用场景，可选值：'general', 'detail', 'speed', 'accuracy'
            
        Returns:
            str: 推荐的模型名称
        """
        recommendations = {
            'general': 'resnet50',
            'detail': 'vgg16',
            'speed': 'efficientnet',
            'accuracy': 'swin',
            'lightweight': 'efficientnet',
            'modern': 'convnext'
        }
        
        return recommendations.get(use_case, 'resnet50')
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ModelConfigManager(models={len(self._configs)}, current={self._current_model})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"ModelConfigManager(configs={list(self._configs.keys())}, current={self._current_model})"


# 全局模型配置管理器实例
model_config_manager = ModelConfigManager()