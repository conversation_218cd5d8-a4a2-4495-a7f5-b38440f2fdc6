#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输入控件工厂模块

该模块提供各种输入控件的创建功能。
"""

from typing import Callable, List
from PyQt6.QtWidgets import (
    QLabel, QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QSlider, QCheckBox, QRadioButton, QButtonGroup
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QValidator, QIntValidator

from utils.log_utils import LoggerMixin
from ..helpers import FontHelper
from .base import WidgetConfig, InputType


class InputFactory(LoggerMixin):
    """输入控件工厂"""
    
    def __init__(self):
        super().__init__()
    
    def create_label(self, text: str = "", bold: bool = False,
                    alignment: Qt.AlignmentFlag = Qt.AlignmentFlag.AlignLeft,
                    tooltip: str = None, config: WidgetConfig = None) -> QLabel:
        """创建标签
        
        Args:
            text: 标签文本
            bold: 是否粗体
            alignment: 对齐方式
            tooltip: 工具提示
            config: 控件配置
            
        Returns:
            QLabel: 标签控件
        """
        try:
            label = QLabel(text)
            label.setAlignment(alignment)
            
            # 设置字体
            if bold:
                font = FontHelper.get_bold_font()
                label.setFont(font)
            
            # 设置工具提示
            if tooltip:
                label.setToolTip(tooltip)
            
            # 应用配置
            if config:
                config.apply_to_widget(label)
            
            return label
            
        except Exception as e:
            self.logger.error(f"创建标签失败: {e}")
            return QLabel(text)
    
    def create_line_edit(
        self, 
        placeholder: str = "", 
        input_type: InputType = InputType.TEXT,
        validator: QValidator = None, 
        config: WidgetConfig = None,
        text_changed_handler: Callable = None
    ) -> QLineEdit:
        """创建单行输入框
        
        Args:
            placeholder: 占位符文本
            input_type: 输入类型
            validator: 验证器
            config: 控件配置
            text_changed_handler: 文本变更事件处理器
            
        Returns:
            QLineEdit: 输入框控件
        """
        try:
            line_edit = QLineEdit()
            
            # 设置占位符
            if placeholder:
                line_edit.setPlaceholderText(placeholder)
            
            # 设置输入类型
            if input_type == InputType.PASSWORD:
                line_edit.setEchoMode(QLineEdit.EchoMode.Password)
            
            # 设置验证器
            if validator:
                line_edit.setValidator(validator)
            elif input_type == InputType.NUMBER:
                line_edit.setValidator(QIntValidator())
            elif input_type == InputType.EMAIL:
                # 简单的邮箱验证
                from PyQt6.QtGui import QRegularExpressionValidator
                from PyQt6.QtCore import QRegularExpression
                email_regex = QRegularExpression(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}")
                line_edit.setValidator(QRegularExpressionValidator(email_regex))
            
            # 应用配置
            if config:
                config.apply_to_widget(line_edit)
            
            # 连接事件
            if text_changed_handler:
                line_edit.textChanged.connect(text_changed_handler)
            
            return line_edit
            
        except Exception as e:
            self.logger.error(f"创建单行输入框失败: {e}")
            return QLineEdit()
    
    def create_text_edit(self, placeholder: str = "", read_only: bool = False,
                        config: WidgetConfig = None,
                        text_changed_handler: Callable = None) -> QTextEdit:
        """创建多行文本编辑器
        
        Args:
            placeholder: 占位符文本
            read_only: 是否只读
            config: 控件配置
            text_changed_handler: 文本变更事件处理器
            
        Returns:
            QTextEdit: 文本编辑器控件
        """
        try:
            text_edit = QTextEdit()
            
            # 设置占位符
            if placeholder:
                text_edit.setPlaceholderText(placeholder)
            
            # 设置只读
            text_edit.setReadOnly(read_only)
            
            # 应用配置
            if config:
                config.apply_to_widget(text_edit)
            
            # 连接事件
            if text_changed_handler:
                text_edit.textChanged.connect(text_changed_handler)
            
            return text_edit
            
        except Exception as e:
            self.logger.error(f"创建多行文本编辑器失败: {e}")
            return QTextEdit()
    
    def create_combo_box(self, items: List[str] = None, editable: bool = False,
                        config: WidgetConfig = None,
                        selection_changed_handler: Callable = None) -> QComboBox:
        """创建组合框
        
        Args:
            items: 选项列表
            editable: 是否可编辑
            config: 控件配置
            selection_changed_handler: 选择变更事件处理器
            
        Returns:
            QComboBox: 组合框控件
        """
        try:
            combo_box = QComboBox()
            
            # 添加选项
            if items:
                combo_box.addItems(items)
            
            # 设置可编辑
            combo_box.setEditable(editable)
            
            # 应用配置
            if config:
                config.apply_to_widget(combo_box)
            
            # 连接事件
            if selection_changed_handler:
                combo_box.currentTextChanged.connect(selection_changed_handler)
            
            return combo_box
            
        except Exception as e:
            self.logger.error(f"创建组合框失败: {e}")
            return QComboBox()
    
    def create_spin_box(self, minimum: int = 0, maximum: int = 100, value: int = 0,
                       step: int = 1, suffix: str = "", config: WidgetConfig = None,
                       value_changed_handler: Callable = None) -> QSpinBox:
        """创建整数调节框
        
        Args:
            minimum: 最小值
            maximum: 最大值
            value: 当前值
            step: 步长
            suffix: 后缀
            config: 控件配置
            value_changed_handler: 值变更事件处理器
            
        Returns:
            QSpinBox: 整数调节框控件
        """
        try:
            spin_box = QSpinBox()
            
            spin_box.setMinimum(minimum)
            spin_box.setMaximum(maximum)
            spin_box.setValue(value)
            spin_box.setSingleStep(step)
            
            if suffix:
                spin_box.setSuffix(suffix)
            
            # 应用配置
            if config:
                config.apply_to_widget(spin_box)
            
            # 连接事件
            if value_changed_handler:
                spin_box.valueChanged.connect(value_changed_handler)
            
            return spin_box
            
        except Exception as e:
            self.logger.error(f"创建整数调节框失败: {e}")
            return QSpinBox()
    
    def create_double_spin_box(self, minimum: float = 0.0, maximum: float = 100.0,
                              value: float = 0.0, step: float = 1.0, decimals: int = 2,
                              suffix: str = "", config: WidgetConfig = None,
                              value_changed_handler: Callable = None) -> QDoubleSpinBox:
        """创建浮点数调节框
        
        Args:
            minimum: 最小值
            maximum: 最大值
            value: 当前值
            step: 步长
            decimals: 小数位数
            suffix: 后缀
            config: 控件配置
            value_changed_handler: 值变更事件处理器
            
        Returns:
            QDoubleSpinBox: 浮点数调节框控件
        """
        try:
            spin_box = QDoubleSpinBox()
            
            spin_box.setMinimum(minimum)
            spin_box.setMaximum(maximum)
            spin_box.setValue(value)
            spin_box.setSingleStep(step)
            spin_box.setDecimals(decimals)
            
            if suffix:
                spin_box.setSuffix(suffix)
            
            # 应用配置
            if config:
                config.apply_to_widget(spin_box)
            
            # 连接事件
            if value_changed_handler:
                spin_box.valueChanged.connect(value_changed_handler)
            
            return spin_box
            
        except Exception as e:
            self.logger.error(f"创建浮点数调节框失败: {e}")
            return QDoubleSpinBox()
    
    def create_slider(self, orientation: Qt.Orientation = Qt.Orientation.Horizontal,
                     minimum: int = 0, maximum: int = 100, value: int = 0,
                     tick_position = None, config: WidgetConfig = None,
                     value_changed_handler: Callable = None) -> QSlider:
        """创建滑块
        
        Args:
            orientation: 方向
            minimum: 最小值
            maximum: 最大值
            value: 当前值
            tick_position: 刻度位置，例如QSlider.TickPosition.TicksBelow
            config: 控件配置
            value_changed_handler: 值变更事件处理器
            
        Returns:
            QSlider: 滑块控件
        """
        try:
            slider = QSlider(orientation)
            
            slider.setMinimum(minimum)
            slider.setMaximum(maximum)
            slider.setValue(value)
            
            # 设置刻度位置
            if tick_position is not None:
                slider.setTickPosition(tick_position)
            
            # 应用配置
            if config:
                config.apply_to_widget(slider)
            
            # 连接事件
            if value_changed_handler:
                slider.valueChanged.connect(value_changed_handler)
            
            return slider
            
        except Exception as e:
            self.logger.error(f"创建滑块失败: {e}")
            return QSlider(orientation)
    
    def create_checkbox(self, text: str = "", checked: bool = False,
                       config: WidgetConfig = None,
                       state_changed_handler: Callable = None) -> QCheckBox:
        """创建复选框
        
        Args:
            text: 复选框文本
            checked: 是否选中
            config: 控件配置
            state_changed_handler: 状态变更事件处理器
            
        Returns:
            QCheckBox: 复选框控件
        """
        try:
            checkbox = QCheckBox(text)
            checkbox.setChecked(checked)
            
            # 应用配置
            if config:
                config.apply_to_widget(checkbox)
            
            # 连接事件
            if state_changed_handler:
                checkbox.stateChanged.connect(state_changed_handler)
            
            return checkbox
            
        except Exception as e:
            self.logger.error(f"创建复选框失败: {e}")
            return QCheckBox(text)
    
    def create_radio_button(self, text: str = "", checked: bool = False,
                           config: WidgetConfig = None,
                           toggled_handler: Callable = None) -> QRadioButton:
        """创建单选按钮
        
        Args:
            text: 单选按钮文本
            checked: 是否选中
            config: 控件配置
            toggled_handler: 切换事件处理器
            
        Returns:
            QRadioButton: 单选按钮控件
        """
        try:
            radio_button = QRadioButton(text)
            radio_button.setChecked(checked)
            
            # 应用配置
            if config:
                config.apply_to_widget(radio_button)
            
            # 连接事件
            if toggled_handler:
                radio_button.toggled.connect(toggled_handler)
            
            return radio_button
            
        except Exception as e:
            self.logger.error(f"创建单选按钮失败: {e}")
            return QRadioButton(text)
    
    def create_button_group(self, buttons: List[QRadioButton] = None,
                           exclusive: bool = True) -> QButtonGroup:
        """创建按钮组
        
        Args:
            buttons: 按钮列表
            exclusive: 是否互斥
            
        Returns:
            QButtonGroup: 按钮组
        """
        try:
            button_group = QButtonGroup()
            button_group.setExclusive(exclusive)
            
            # 添加按钮
            if buttons:
                for button in buttons:
                    button_group.addButton(button)
            
            return button_group
            
        except Exception as e:
            self.logger.error(f"创建按钮组失败: {e}")
            return QButtonGroup()