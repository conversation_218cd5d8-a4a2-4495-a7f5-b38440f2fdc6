#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置模块

管理数据库连接和配置信息
"""

import os
from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from pathlib import Path

from utils.log_utils import get_logger


@dataclass
class DatabaseConfig:
    """数据库配置类"""
    
    # 数据库类型
    db_type: str = "sqlite"
    
    # SQLite配置
    sqlite_path: str = "data/fabric_search.db"
    
    # PostgreSQL配置
    postgres_host: str = "localhost"
    postgres_port: int = 5432
    postgres_database: str = "fabric_search"
    postgres_username: str = "postgres"
    postgres_password: str = ""
    
    # 连接池配置
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    
    # 查询配置
    query_timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # 缓存配置
    enable_cache: bool = True
    cache_size: int = 1000
    cache_ttl: int = 3600
    
    # 批处理配置
    batch_size: int = 1000
    commit_interval: int = 100
    
    # 备份配置
    enable_backup: bool = True
    backup_interval: int = 86400  # 24小时
    backup_keep_days: int = 30
    backup_path: str = "data/backups"
    
    # 其他配置
    extra_config: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        self.logger = get_logger(self.__class__.__name__)
        
        # 确保路径是绝对路径
        if not os.path.isabs(self.sqlite_path):
            self.sqlite_path = os.path.abspath(self.sqlite_path)
        
        if not os.path.isabs(self.backup_path):
            self.backup_path = os.path.abspath(self.backup_path)
        
        # 创建必要的目录
        self._create_directories()
    
    @property
    def db_path(self) -> str:
        """获取数据库路径（兼容性属性）"""
        return self.sqlite_path
    
    @db_path.setter
    def db_path(self, value: str):
        """设置数据库路径（兼容性属性）"""
        self.sqlite_path = value
    
    @property
    def timeout(self) -> int:
        """获取超时时间（兼容性属性）"""
        return self.query_timeout
    
    @timeout.setter
    def timeout(self, value: int):
        """设置超时时间（兼容性属性）"""
        self.query_timeout = value
    
    @property
    def check_same_thread(self) -> bool:
        """获取检查同线程设置（兼容性属性）"""
        return False
    
    @property
    def isolation_level(self) -> Optional[str]:
        """获取隔离级别（兼容性属性）"""
        return None
    
    @property
    def enable_foreign_keys(self) -> bool:
        """获取外键约束设置（兼容性属性）"""
        return True
    
    @property
    def enable_wal_mode(self) -> bool:
        """获取WAL模式设置（兼容性属性）"""
        return True
    
    @property
    def synchronous(self) -> str:
        """获取同步模式设置（兼容性属性）"""
        return "NORMAL"
    
    def _create_directories(self):
        """创建必要的目录"""
        try:
            # 创建数据库目录
            if self.db_type == "sqlite":
                db_dir = Path(self.sqlite_path).parent
                db_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建备份目录
            if self.enable_backup:
                backup_dir = Path(self.backup_path)
                backup_dir.mkdir(parents=True, exist_ok=True)
                
        except Exception as e:
            self.logger.error(f"创建目录失败: {e}")
    
    def get_connection_string(self) -> str:
        """获取数据库连接字符串"""
        if self.db_type == "sqlite":
            return f"sqlite:///{self.sqlite_path}"
        elif self.db_type == "postgresql":
            return (
                f"postgresql://{self.postgres_username}:{self.postgres_password}@"
                f"{self.postgres_host}:{self.postgres_port}/{self.postgres_database}"
            )
        else:
            raise ValueError(f"不支持的数据库类型: {self.db_type}")
    
    def get_connection_params(self) -> Dict[str, Any]:
        """获取数据库连接参数"""
        if self.db_type == "sqlite":
            return {
                "database": self.sqlite_path,
                "timeout": self.query_timeout,
                "check_same_thread": False
            }
        elif self.db_type == "postgresql":
            return {
                "host": self.postgres_host,
                "port": self.postgres_port,
                "database": self.postgres_database,
                "user": self.postgres_username,
                "password": self.postgres_password,
                "connect_timeout": self.query_timeout
            }
        else:
            raise ValueError(f"不支持的数据库类型: {self.db_type}")
    
    def validate(self) -> bool:
        """验证配置"""
        try:
            # 验证数据库类型
            if self.db_type not in ["sqlite", "postgresql"]:
                self.logger.error(f"不支持的数据库类型: {self.db_type}")
                return False
            
            # 验证SQLite配置
            if self.db_type == "sqlite":
                if not self.sqlite_path:
                    self.logger.error("SQLite数据库路径不能为空")
                    return False
                
                # 检查目录是否可写
                db_dir = Path(self.sqlite_path).parent
                if not db_dir.exists():
                    try:
                        db_dir.mkdir(parents=True, exist_ok=True)
                    except Exception as e:
                        self.logger.error(f"无法创建数据库目录: {e}")
                        return False
            
            # 验证PostgreSQL配置
            elif self.db_type == "postgresql":
                if not all([self.postgres_host, self.postgres_database, self.postgres_username]):
                    self.logger.error("PostgreSQL配置不完整")
                    return False
                
                if not (1 <= self.postgres_port <= 65535):
                    self.logger.error(f"PostgreSQL端口无效: {self.postgres_port}")
                    return False
            
            # 验证数值配置
            if self.pool_size <= 0:
                self.logger.error(f"连接池大小必须大于0: {self.pool_size}")
                return False
            
            if self.query_timeout <= 0:
                self.logger.error(f"查询超时时间必须大于0: {self.query_timeout}")
                return False
            
            if self.batch_size <= 0:
                self.logger.error(f"批处理大小必须大于0: {self.batch_size}")
                return False
            
            self.logger.info("数据库配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "db_type": self.db_type,
            "sqlite_path": self.sqlite_path,
            "postgres_host": self.postgres_host,
            "postgres_port": self.postgres_port,
            "postgres_database": self.postgres_database,
            "postgres_username": self.postgres_username,
            "postgres_password": self.postgres_password,
            "pool_size": self.pool_size,
            "max_overflow": self.max_overflow,
            "pool_timeout": self.pool_timeout,
            "pool_recycle": self.pool_recycle,
            "query_timeout": self.query_timeout,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "enable_cache": self.enable_cache,
            "cache_size": self.cache_size,
            "cache_ttl": self.cache_ttl,
            "batch_size": self.batch_size,
            "commit_interval": self.commit_interval,
            "enable_backup": self.enable_backup,
            "backup_interval": self.backup_interval,
            "backup_keep_days": self.backup_keep_days,
            "backup_path": self.backup_path,
            "extra_config": self.extra_config
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DatabaseConfig':
        """从字典创建配置"""
        return cls(**data)
    
    @classmethod
    def load_from_file(cls, config_file: str) -> 'DatabaseConfig':
        """从文件加载配置"""
        import yaml
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            # 提取数据库配置部分
            db_config = data.get('database', {})
            return cls.from_dict(db_config)
            
        except Exception as e:
            logger = get_logger(cls.__name__)
            logger.error(f"加载数据库配置文件失败: {e}")
            return cls()  # 返回默认配置
    
    def save_to_file(self, config_file: str):
        """保存配置到文件"""
        import yaml
        
        try:
            # 读取现有配置
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f) or {}
            else:
                data = {}
            
            # 更新数据库配置
            data['database'] = self.to_dict()
            
            # 保存配置
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
            
            self.logger.info(f"数据库配置已保存到: {config_file}")
            
        except Exception as e:
            self.logger.error(f"保存数据库配置失败: {e}")
    
    def get_backup_file_path(self) -> str:
        """获取备份文件路径"""
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"fabric_search_backup_{timestamp}.db"
        return os.path.join(self.backup_path, backup_name)
    
    def cleanup_old_backups(self):
        """清理旧备份文件"""
        try:
            from datetime import datetime, timedelta
            
            backup_dir = Path(self.backup_path)
            if not backup_dir.exists():
                return
            
            cutoff_date = datetime.now() - timedelta(days=self.backup_keep_days)
            
            for backup_file in backup_dir.glob("fabric_search_backup_*.db"):
                try:
                    file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        backup_file.unlink()
                        self.logger.info(f"删除旧备份文件: {backup_file}")
                except Exception as e:
                    self.logger.warning(f"删除备份文件失败 {backup_file}: {e}")
                    
        except Exception as e:
            self.logger.error(f"清理旧备份失败: {e}")


# 默认数据库配置实例
default_database_config = DatabaseConfig()