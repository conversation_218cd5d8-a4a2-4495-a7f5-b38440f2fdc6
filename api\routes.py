#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API路由模块

定义所有API端点和路由处理函数。
"""

import os
import time
import uuid
import base64
from typing import Dict, Any, List, Optional
from flask import Flask, request, jsonify, g, send_file, current_app
from werkzeug.utils import secure_filename
from PIL import Image
import io
import json
from datetime import datetime

from config.config_manager import get_config, get_config_manager
from database.models import FabricImage
from database.fabric_repository import FabricRepository
from database.database_manager import get_database_manager
from features.feature_extractor import FeatureExtractor
from models.batch_processing import BatchProcessor, BatchConfig
import sys
import os
# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 直接导入根目录下search模块的内容
import importlib.util
search_module_path = os.path.join(project_root, 'search', 'image_search.py')
spec = importlib.util.spec_from_file_location("image_search_module", search_module_path)
image_search_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(image_search_module)
ImageSearchEngine = image_search_module.ImageSearchEngine
SearchConfig = image_search_module.SearchConfig
from export.result_exporter import ResultExporter, ExportConfig
from utils.logger_mixin import LoggerMixin
from .middleware import require_api_key, validate_json, validate_file_upload, log_performance
from .error_handlers import (
    success_response, paginated_response, ValidationError, ResourceNotFoundError,
    ProcessingError, AuthenticationError
)
from .schemas import (
    validate_request, validate_response,
    IMAGE_SEARCH_REQUEST_SCHEMA, IMAGE_SEARCH_RESPONSE_SCHEMA,
    BATCH_PROCESSING_REQUEST_SCHEMA, BATCH_PROCESSING_RESPONSE_SCHEMA,
    CONFIG_UPDATE_REQUEST_SCHEMA, EXPORT_REQUEST_SCHEMA,
    PAGINATION_SCHEMA
)


class APIRoutes(LoggerMixin):
    """API路由处理器"""
    
    def __init__(self):
        super().__init__()
        self.config = get_config()
        self.config_manager = get_config_manager()
        
        # 初始化组件（延迟加载）
        self._feature_extractor = None
        self._repository = None
        self._search_engine = None
        self._batch_processor = None
        self._result_exporter = None
        
        # 批处理任务存储
        self._batch_jobs = {}
    
    @property
    def feature_extractor(self) -> FeatureExtractor:
        """获取特征提取器（延迟加载）"""
        if self._feature_extractor is None:
            from features.config.feature_config import FeatureExtractorConfig
            config = FeatureExtractorConfig(
                model_name=self.config.feature_extraction.model_name,
                device="cpu"  # 明确设置为CPU设备
            )
            self._feature_extractor = FeatureExtractor(config)
        return self._feature_extractor
    
    @property
    def repository(self) -> FabricRepository:
        """获取数据仓库（延迟加载）"""
        if self._repository is None:
            db_manager = get_database_manager()
            self._repository = FabricRepository(db_manager)
        return self._repository
    
    @property
    def search_engine(self) -> ImageSearchEngine:
        """获取搜索引擎（延迟加载）"""
        if self._search_engine is None:
            search_config = SearchConfig(
                index_type=self.config.search.index_type,
                search_algorithm=self.config.search.similarity_metric,  # 使用search_algorithm替代similarity_metric
                top_k=self.config.search.top_k
            )
            self._search_engine = ImageSearchEngine(
                feature_extractor=self.feature_extractor,
                fabric_repository=self.repository,
                config=search_config
            )
        return self._search_engine
    
    @property
    def batch_processor(self) -> BatchProcessor:
        """获取批处理器（延迟加载）"""
        if self._batch_processor is None:
            batch_config = BatchConfig(
                max_workers=self.config.batch_processing.max_workers,
                batch_size=self.config.batch_processing.batch_size,
                queue_size=self.config.batch_processing.queue_size
            )
            self._batch_processor = BatchProcessor(
                feature_extractor=self.feature_extractor,
                repository=self.repository,
                config=batch_config
            )
        return self._batch_processor
    
    @property
    def result_exporter(self) -> ResultExporter:
        """获取结果导出器（延迟加载）"""
        if self._result_exporter is None:
            export_config = ExportConfig()
            self._result_exporter = ResultExporter(export_config)
        return self._result_exporter
    
    # 健康检查和信息端点
    def health_check(self):
        """健康检查"""
        try:
            # 检查数据库连接
            db_status = self.repository.health_check()
            
            # 检查GPU状态
            gpu_status = self.feature_extractor.check_gpu_status()
            
            health_info = {
                'status': 'healthy',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'version': self.config.version,
                'components': {
                    'database': 'healthy' if db_status else 'unhealthy',
                    'feature_extractor': 'healthy',
                    'gpu': gpu_status
                }
            }
            
            return success_response(health_info)
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return {
                'status': 'unhealthy',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'error': str(e)
            }, 503
    
    def api_info(self):
        """API信息"""
        api_info = {
            'name': self.config.app_name,
            'version': self.config.version,
            'description': 'Fabric Search API - 面料图像搜索系统',
            'endpoints': {
                'health': '/api/health',
                'search': '/api/search',
                'batch': '/api/batch',
                'config': '/api/config',
                'export': '/api/export',
                'images': '/api/images'
            },
            'features': [
                '图像相似性搜索',
                '批量图像处理',
                '特征提取',
                '结果导出',
                '配置管理'
            ],
            'supported_formats': self.config.security.allowed_extensions
        }
        
        return success_response(api_info)
    
    # 图像搜索端点
    @validate_request(IMAGE_SEARCH_REQUEST_SCHEMA)
    @validate_response(IMAGE_SEARCH_RESPONSE_SCHEMA)
    @log_performance
    def search_images(self):
        """图像搜索"""
        try:
            data = g.validated_data
            start_time = time.time()
            
            # 根据查询类型处理
            query_type = data.get('query_type', 'image')
            
            if query_type == 'image':
                results = self._search_by_image(data)
            elif query_type == 'text':
                results = self._search_by_text(data)
            elif query_type == 'features':
                results = self._search_by_features(data)
            else:
                raise ValidationError(f"不支持的查询类型: {query_type}")
            
            processing_time = time.time() - start_time
            
            # 构建响应
            response_data = {
                'results': results,
                'query_info': {
                    'query_type': query_type,
                    'processing_time': round(processing_time, 3),
                    'total_candidates': len(results),
                    'filters_applied': bool(data.get('filters'))
                }
            }
            
            return success_response(response_data)
            
        except Exception as e:
            self.logger.error(f"图像搜索失败: {e}")
            raise ProcessingError(f"搜索处理失败: {str(e)}")
    
    def _search_by_image(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于图像搜索"""
        query_image = data['query_image']
        
        # 处理图像数据
        if query_image.startswith('data:image/'):
            # Base64编码的图像
            image_data = query_image.split(',')[1]
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))
        elif query_image.startswith('http'):
            # URL图像
            import requests
            response = requests.get(query_image)
            image = Image.open(io.BytesIO(response.content))
        else:
            # 本地文件路径
            if not os.path.exists(query_image):
                raise ResourceNotFoundError(f"图像文件不存在: {query_image}")
            image = Image.open(query_image)
        
        # 将图像保存为临时文件，以便特征提取器处理
        temp_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_search_image.jpg")
        image.save(temp_path)
        
        try:
            # 使用特征提取器提取特征
            from features.feature_extractor import create_feature_extractor
            import numpy as np
            
            extractor = create_feature_extractor()
            result = extractor.extract_features(temp_path)
            
            if not result.success:
                raise ProcessingError(f"特征提取失败: {result.error}")
            
            # 获取深度特征向量
            features = result.get_feature_vector("deep")
            
            # 确保特征向量是numpy数组并且数据类型正确
            if features is None:
                raise ProcessingError("无法获取深度特征向量")
                
            # 确保特征向量是numpy数组并且数据类型正确
            if not isinstance(features, np.ndarray):
                features = np.array(features, dtype=np.float32)
            else:
                features = features.astype(np.float32)
                
            # 检查特征向量是否包含异常值
            if np.isnan(features).any() or np.isinf(features).any():
                self.logger.warning("特征向量包含NaN或无穷大值，已自动修正")
                features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
            
            # 使用提取的特征进行搜索
            search_results = self.search_engine.search(
                query_image=features,  # 传递处理后的特征向量
                top_k=data.get('top_k', 50),
                similarity_threshold=data.get('similarity_threshold', 0.0),
                filters=data.get('filters')
            )
        finally:
            # 删除临时文件
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except Exception as e:
                    self.logger.warning(f"删除临时文件失败: {e}")
        
        return self._format_search_results(search_results, data)
    
    def _search_by_text(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于文本搜索"""
        # 当前版本的ImageSearchEngine不支持文本搜索
        self.logger.warning("当前版本不支持文本搜索")
        return []
        
        # 以下是原始实现，暂时注释掉
        '''
        try:
            query_text = data.get('query_text', '')
            search_fields = data.get('search_fields', ['file_name', 'description', 'tags', 'category'])
            top_k = data.get('top_k', 20)
            
            if not query_text:
                return []
            
            # 创建搜索查询
            from search.search_engine import SearchQuery
            from search.search_history import SearchType
            
            query = SearchQuery(
                query_type=SearchType.TEXT_SEARCH,
                text_query=query_text,
                search_fields=search_fields,
                top_k=top_k
            )
            
            # 执行搜索
            search_results = self.search_engine.search(query)
            
            # 转换结果格式
            results = []
            for result in search_results.results:
                fabric_image = result.fabric_image
                result_dict = {
                    'id': fabric_image.id,
                    'file_path': fabric_image.file_path,
                    'file_name': fabric_image.file_name,
                    'description': fabric_image.description,
                    'category': fabric_image.category,
                    'tags': fabric_image.tags,
                    'similarity_score': result.similarity_score,
                    'created_at': fabric_image.created_at.isoformat() if fabric_image.created_at else None,
                    'updated_at': fabric_image.updated_at.isoformat() if fabric_image.updated_at else None
                }
                results.append(result_dict)
            
            return results
            
        except Exception as e:
            self.logger.error(f"文本搜索失败: {e}")
            return []
        '''

    
    def _search_by_features(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于特征向量搜索"""
        query_features = data['query_features']
        
        # 确保特征向量是numpy数组并且数据类型正确
        try:
            import numpy as np
            
            # 如果是列表，转换为numpy数组
            if isinstance(query_features, list):
                query_features = np.array(query_features, dtype=np.float32)
            # 如果已经是numpy数组，确保数据类型正确
            elif isinstance(query_features, np.ndarray):
                query_features = query_features.astype(np.float32)
            else:
                raise ProcessingError(f"不支持的特征向量类型: {type(query_features)}")
                
            # 检查特征向量是否包含异常值
            if np.isnan(query_features).any() or np.isinf(query_features).any():
                self.logger.warning("特征向量包含NaN或无穷大值，已自动修正")
                query_features = np.nan_to_num(query_features, nan=0.0, posinf=1.0, neginf=-1.0)
                
            # 执行搜索
            search_results = self.search_engine.search(
                query_image=query_features,  # 传递处理后的特征向量
                top_k=data.get('top_k', 50),
                similarity_threshold=data.get('similarity_threshold', 0.0),
                filters=data.get('filters')
            )
            
            return self._format_search_results(search_results, data)
            
        except Exception as e:
            self.logger.error(f"特征向量搜索失败: {e}")
            raise ProcessingError(f"特征向量处理失败: {str(e)}")
    
    def _format_search_results(self, search_results, request_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """格式化搜索结果"""
        results = []
        
        for i, result in enumerate(search_results):
            formatted_result = {
                'id': result.image_id,
                'image_path': result.image_path,
                'similarity_score': round(result.similarity_score, 4),
                'rank': i + 1
            }
            
            # 添加元数据
            if request_data.get('include_metadata', True) and result.metadata:
                formatted_result['metadata'] = result.metadata
            
            # 添加特征向量
            if request_data.get('include_features', False) and result.features is not None:
                formatted_result['features'] = result.features.tolist()
            
            results.append(formatted_result)
        
        return results
    
    # 图像上传和管理端点
    @validate_file_upload(['.jpg', '.jpeg', '.png', '.bmp', '.tiff'])
    @log_performance
    def upload_image(self):
        """上传图像"""
        try:
            file = g.uploaded_file
            
            # 生成安全的文件名
            filename = secure_filename(file.filename)
            file_ext = os.path.splitext(filename)[1].lower()
            unique_filename = f"{uuid.uuid4().hex}{file_ext}"
            
            # 保存文件
            upload_dir = os.path.join(self.config.data_dir, 'uploads')
            os.makedirs(upload_dir, exist_ok=True)
            file_path = os.path.join(upload_dir, unique_filename)
            file.save(file_path)
            
            # 提取特征并保存到数据库
            try:
                features = self.feature_extractor.extract_features(file_path)
                
                # 获取图像信息
                with Image.open(file_path) as img:
                    width, height = img.size
                
                file_size = os.path.getsize(file_path)
                
                # 创建FabricImage对象
                fabric_image = FabricImage(
                    file_path=file_path,
                    file_name=filename,
                    features=features,
                    width=width,
                    height=height,
                    file_size=file_size,
                    hash_md5=self._calculate_file_hash(file_path),
                    metadata={
                        'original_filename': filename,
                        'upload_time': datetime.utcnow().isoformat(),
                        'content_type': file.content_type
                    }
                )
                
                # 保存到数据库
                image_id = self.repository.create(fabric_image)
                
                response_data = {
                    'image_id': image_id,
                    'file_path': file_path,
                    'original_filename': filename,
                    'file_size': file_size,
                    'dimensions': {'width': width, 'height': height},
                    'features_extracted': True
                }
                
                return success_response(response_data, "图像上传成功")
                
            except Exception as e:
                # 如果处理失败，删除已上传的文件
                if os.path.exists(file_path):
                    os.remove(file_path)
                raise ProcessingError(f"图像处理失败: {str(e)}")
            
        except Exception as e:
            self.logger.error(f"图像上传失败: {e}")
            raise ProcessingError(f"上传处理失败: {str(e)}")
    
    def get_image(self, image_id: int):
        """获取图像信息"""
        try:
            fabric_image = self.repository.get_image_by_id(image_id)
            if not fabric_image:
                raise ResourceNotFoundError(f"图像不存在: {image_id}")
            
            image_info = {
                'id': fabric_image.id,
                'file_path': fabric_image.file_path,
                'file_name': fabric_image.file_name,
                'width': fabric_image.width,
                'height': fabric_image.height,
                'file_size': fabric_image.file_size,
                'hash_md5': fabric_image.hash_md5,
                'created_at': fabric_image.created_at.isoformat() if fabric_image.created_at else None,
                'updated_at': fabric_image.updated_at.isoformat() if fabric_image.updated_at else None,
                'metadata': fabric_image.metadata
            }
            
            return success_response(image_info)
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"获取图像信息失败: {e}")
            raise ProcessingError(f"获取图像失败: {str(e)}")
    
    def delete_image(self, image_id: int):
        """删除图像"""
        try:
            fabric_image = self.repository.get_image_by_id(image_id)
            if not fabric_image:
                raise ResourceNotFoundError(f"图像不存在: {image_id}")
            
            # 删除文件
            if os.path.exists(fabric_image.file_path):
                os.remove(fabric_image.file_path)
            
            # 从数据库删除
            self.repository.delete_image(image_id)
            
            return success_response(message=f"图像 {image_id} 已删除")
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"删除图像失败: {e}")
            raise ProcessingError(f"删除图像失败: {str(e)}")
    
    @validate_request(PAGINATION_SCHEMA)
    def list_images(self):
        """列出图像"""
        try:
            data = g.validated_data
            page = data.get('page', 1)
            per_page = data.get('per_page', 20)
            
            # 获取图像列表
            images, total = self.repository.get_images_paginated(page, per_page)
            
            # 格式化结果
            image_list = []
            for img in images:
                image_info = {
                    'id': img.id,
                    'image_path': img.image_path,
                    'width': img.width,
                    'height': img.height,
                    'file_size': img.file_size,
                    'created_at': img.created_at.isoformat() if img.created_at else None,
                    'metadata': img.metadata
                }
                image_list.append(image_info)
            
            return paginated_response(image_list, page, per_page, total)
            
        except Exception as e:
            self.logger.error(f"列出图像失败: {e}")
            raise ProcessingError(f"获取图像列表失败: {str(e)}")
    
    def serve_image(self, image_id: int):
        """提供图像文件"""
        try:
            fabric_image = self.repository.get_image_by_id(image_id)
            if not fabric_image:
                raise ResourceNotFoundError(f"图像不存在: {image_id}")
            
            if not os.path.exists(fabric_image.image_path):
                raise ResourceNotFoundError(f"图像文件不存在: {fabric_image.image_path}")
            
            return send_file(fabric_image.image_path)
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"提供图像文件失败: {e}")
            raise ProcessingError(f"获取图像文件失败: {str(e)}")
    
    # 批处理端点
    @validate_request(BATCH_PROCESSING_REQUEST_SCHEMA)
    @validate_response(BATCH_PROCESSING_RESPONSE_SCHEMA)
    @log_performance
    def start_batch_processing(self):
        """开始批处理"""
        try:
            data = g.validated_data
            
            # 生成任务ID
            job_id = str(uuid.uuid4())
            
            # 创建批处理配置
            batch_config = BatchConfig(
                max_workers=data.get('max_workers', 4),
                batch_size=data.get('batch_size', 32),
                queue_size=1000,
                timeout=300
            )
            
            # 启动批处理任务
            self.batch_processor.start_batch_job(
                job_id=job_id,
                source_path=data['source_path'],
                recursive=data.get('recursive', True),
                skip_existing=data.get('skip_existing', True),
                update_existing=data.get('update_existing', False),
                supported_formats=data.get('supported_formats'),
                filters=data.get('filters'),
                metadata=data.get('metadata')
            )
            
            # 存储任务信息
            self._batch_jobs[job_id] = {
                'id': job_id,
                'status': 'pending',
                'start_time': datetime.utcnow(),
                'config': data
            }
            
            response_data = {
                'job_id': job_id,
                'status': 'pending',
                'message': '批处理任务已启动'
            }
            
            return success_response(response_data, "批处理任务创建成功")
            
        except Exception as e:
            self.logger.error(f"启动批处理失败: {e}")
            raise ProcessingError(f"批处理启动失败: {str(e)}")
    
    def get_batch_status(self, job_id: str):
        """获取批处理状态"""
        try:
            if job_id not in self._batch_jobs:
                raise ResourceNotFoundError(f"批处理任务不存在: {job_id}")
            
            # 获取任务状态
            job_status = self.batch_processor.get_job_status(job_id)
            
            return success_response(job_status)
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"获取批处理状态失败: {e}")
            raise ProcessingError(f"获取状态失败: {str(e)}")
    
    def cancel_batch_processing(self, job_id: str):
        """取消批处理"""
        try:
            if job_id not in self._batch_jobs:
                raise ResourceNotFoundError(f"批处理任务不存在: {job_id}")
            
            # 取消任务
            self.batch_processor.cancel_job(job_id)
            
            # 更新任务状态
            self._batch_jobs[job_id]['status'] = 'cancelled'
            
            return success_response(message=f"批处理任务 {job_id} 已取消")
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"取消批处理失败: {e}")
            raise ProcessingError(f"取消任务失败: {str(e)}")
    
    def list_batch_jobs(self):
        """列出批处理任务"""
        try:
            jobs = []
            for job_id, job_info in self._batch_jobs.items():
                job_data = {
                    'job_id': job_id,
                    'status': job_info['status'],
                    'start_time': job_info['start_time'].isoformat(),
                    'config': job_info['config']
                }
                jobs.append(job_data)
            
            return success_response({'jobs': jobs})
            
        except Exception as e:
            self.logger.error(f"列出批处理任务失败: {e}")
            raise ProcessingError(f"获取任务列表失败: {str(e)}")
    
    # 配置管理端点
    def get_config(self):
        """获取配置"""
        try:
            # 返回配置的安全副本（隐藏敏感信息）
            config_dict = {
                'app_name': self.config.app_name,
                'version': self.config.version,
                'debug': self.config.debug,
                'feature_extraction': {
                    'model_name': self.config.feature_extraction.model_name,
                    'device': self.config.feature_extraction.device,
                    'batch_size': self.config.feature_extraction.batch_size,
                    'image_size': self.config.feature_extraction.image_size
                },
                'search': {
                    'index_type': self.config.search.index_type,
                    'similarity_metric': self.config.search.similarity_metric,
                    'top_k': self.config.search.top_k
                },
                'logging': {
                    'level': self.config.logging.level,
                    'log_to_file': self.config.logging.log_to_file,
                    'log_to_console': self.config.logging.log_to_console
                }
            }
            
            return success_response(config_dict)
            
        except Exception as e:
            self.logger.error(f"获取配置失败: {e}")
            raise ProcessingError(f"获取配置失败: {str(e)}")
    
    @require_api_key
    @validate_request(CONFIG_UPDATE_REQUEST_SCHEMA)
    def update_config(self):
        """更新配置"""
        try:
            data = g.validated_data
            
            # 更新配置
            updated_fields = []
            for section, values in data.items():
                for key, value in values.items():
                    config_key = f"{section}.{key}"
                    if self.config_manager.set(config_key, value):
                        updated_fields.append(config_key)
            
            # 保存配置
            if updated_fields:
                self.config_manager.save_config()
            
            return success_response(
                {'updated_fields': updated_fields},
                f"配置已更新，共 {len(updated_fields)} 个字段"
            )
            
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
            raise ProcessingError(f"配置更新失败: {str(e)}")
    
    # 导出端点
    @validate_request(EXPORT_REQUEST_SCHEMA)
    @log_performance
    def export_results(self):
        """导出搜索结果"""
        try:
            data = g.validated_data
            
            # 这里需要从请求中获取搜索结果
            # 实际实现中可能需要先执行搜索或从缓存中获取结果
            search_results = []  # 占位符
            
            # 创建导出配置
            export_config = ExportConfig(
                format=data['format'],
                include_images=data.get('include_images', False),
                include_features=data.get('include_features', False),
                include_metadata=data.get('include_metadata', True),
                max_results=data.get('max_results'),
                image_size=tuple(data['image_size']) if data.get('image_size') else None,
                compression_quality=data.get('compression_quality', 85)
            )
            
            # 执行导出
            export_result = self.result_exporter.export_results(
                results=search_results,
                output_path=data['output_path'],
                config=export_config
            )
            
            return success_response(export_result, "结果导出成功")
            
        except Exception as e:
            self.logger.error(f"导出结果失败: {e}")
            raise ProcessingError(f"导出失败: {str(e)}")
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希"""
        import hashlib
        
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()


# 全局路由处理器实例
api_routes = APIRoutes()


def register_routes(app: Flask):
    """注册所有路由
    
    Args:
        app: Flask应用实例
    """
    
    # 健康检查和信息
    app.add_url_rule('/api/health', 'health_check', api_routes.health_check, methods=['GET'])
    app.add_url_rule('/api/info', 'api_info', api_routes.api_info, methods=['GET'])
    
    # 图像搜索
    app.add_url_rule('/api/search', 'search_images', api_routes.search_images, methods=['POST'])
    
    # 图像管理
    app.add_url_rule('/api/images', 'upload_image', api_routes.upload_image, methods=['POST'])
    app.add_url_rule('/api/images', 'list_images', api_routes.list_images, methods=['GET'])
    app.add_url_rule('/api/images/<int:image_id>', 'get_image', api_routes.get_image, methods=['GET'])
    app.add_url_rule('/api/images/<int:image_id>', 'delete_image', api_routes.delete_image, methods=['DELETE'])
    app.add_url_rule('/api/images/<int:image_id>/file', 'serve_image', api_routes.serve_image, methods=['GET'])
    
    # 批处理
    app.add_url_rule('/api/batch', 'start_batch_processing', api_routes.start_batch_processing, methods=['POST'])
    app.add_url_rule('/api/batch', 'list_batch_jobs', api_routes.list_batch_jobs, methods=['GET'])
    app.add_url_rule('/api/batch/<string:job_id>', 'get_batch_status', api_routes.get_batch_status, methods=['GET'])
    app.add_url_rule('/api/batch/<string:job_id>', 'cancel_batch_processing', api_routes.cancel_batch_processing, methods=['DELETE'])
    
    # 配置管理
    app.add_url_rule('/api/config', 'get_config', api_routes.get_config, methods=['GET'])
    app.add_url_rule('/api/config', 'update_config', api_routes.update_config, methods=['PUT'])
    
    # 导出
    app.add_url_rule('/api/export', 'export_results', api_routes.export_results, methods=['POST'])
    
    # 静态文件（如果需要）
    @app.route('/api/static/<path:filename>')
    def serve_static(filename):
        """提供静态文件"""
        from flask import send_from_directory
        static_dir = os.path.join(app.root_path, 'static')
        return send_from_directory(static_dir, filename)