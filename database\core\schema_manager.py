#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库架构管理器

该模块提供数据库表结构的创建、更新和管理功能。
"""

import sqlite3
import logging
from typing import List

from .connection_manager import ConnectionManager


class SchemaManager:
    """数据库架构管理器"""
    
    def __init__(self, connection_manager: ConnectionManager):
        """初始化架构管理器
        
        Args:
            connection_manager: 连接管理器
        """
        self.connection_manager = connection_manager
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def initialize_database(self):
        """初始化数据库"""
        try:
            with self.connection_manager.connection_context() as conn:
                cursor = conn.cursor()
                
                # 检查数据库版本
                cursor.execute("PRAGMA user_version")
                version = cursor.fetchone()[0]
                
                if version == 0:
                    self.logger.info("初始化新数据库")
                    self._create_initial_schema(conn)
                    cursor.execute("PRAGMA user_version = 1")
                    conn.commit()
                else:
                    # 检查并更新现有数据库结构
                    self._update_database_schema(conn)
                
                cursor.close()
                
        except Exception as e:
            self.logger.error(f"初始化数据库失败: {e}")
            raise
    
    def _create_initial_schema(self, conn: sqlite3.Connection):
        """创建初始数据库架构
        
        Args:
            conn: 数据库连接
        """
        cursor = conn.cursor()
        
        try:
            # 创建布料图片表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS fabric_images (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT NOT NULL UNIQUE,
                    file_name TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    width INTEGER NOT NULL,
                    height INTEGER NOT NULL,
                    channels INTEGER DEFAULT 3,
                    format TEXT NOT NULL,
                    hash_md5 TEXT NOT NULL,
                    features BLOB,
                    thumbnail_path TEXT,
                    tags TEXT,
                    category TEXT,
                    color_info TEXT,
                    texture_info TEXT,
                    pattern_info TEXT,
                    material_info TEXT,
                    description TEXT,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    indexed_at TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            """)
            
            # 创建特征表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS image_features (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    image_id INTEGER NOT NULL,
                    feature_type TEXT NOT NULL,
                    feature_data BLOB NOT NULL,
                    model_name TEXT,
                    feature_dim INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (image_id) REFERENCES fabric_images (id) ON DELETE CASCADE
                )
            """)
            
            # 创建搜索历史表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS search_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    session_id TEXT,
                    search_type TEXT NOT NULL,
                    query_text TEXT,
                    query_image_id INTEGER,
                    query_image_path TEXT,
                    query_features BLOB,
                    search_params TEXT,
                    filters TEXT,
                    result_count INTEGER,
                    execution_time REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (query_image_id) REFERENCES fabric_images (id) ON DELETE SET NULL
                )
            """)
            
            # 创建用户偏好表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_preferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT NOT NULL UNIQUE,
                    value TEXT NOT NULL,
                    data_type TEXT DEFAULT 'string',
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建索引
            self._create_indexes(cursor)
            
            # 创建触发器
            self._create_triggers(cursor)
            
        except Exception as e:
            self.logger.error(f"创建数据库架构失败: {e}")
            raise
        finally:
            cursor.close()
    
    def _update_database_schema(self, conn: sqlite3.Connection):
        """更新数据库结构
        
        Args:
            conn: 数据库连接
        """
        cursor = conn.cursor()
        
        try:
            # 检查并更新search_history表
            self._update_search_history_table(cursor, conn)
            
            # 检查并更新fabric_images表
            self._update_fabric_images_table(cursor, conn)
            
            # 检查并更新image_features表
            self._update_image_features_table(cursor, conn)
            
            # 检查并更新user_preferences表
            self._update_user_preferences_table(cursor, conn)
            
            # 重新创建触发器（如果不存在）
            self._create_triggers(cursor)
            
        except Exception as e:
            self.logger.error(f"更新数据库结构失败: {e}")
            raise
        finally:
            cursor.close()
    
    def _update_search_history_table(self, cursor: sqlite3.Cursor, conn: sqlite3.Connection):
        """更新search_history表结构"""
        # 检查search_history表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='search_history'")
        if not cursor.fetchone():
            self.logger.info("创建search_history表")
            cursor.execute("""
                CREATE TABLE search_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    session_id TEXT,
                    search_type TEXT NOT NULL,
                    query_text TEXT,
                    query_image_id INTEGER,
                    query_image_path TEXT,
                    query_features BLOB,
                    search_params TEXT,
                    filters TEXT,
                    result_count INTEGER,
                    execution_time REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (query_image_id) REFERENCES fabric_images (id) ON DELETE SET NULL
                )
            """)
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_search_history_created_at ON search_history(created_at)")
            conn.commit()
            return
        
        # 检查search_history表是否存在必要的列
        cursor.execute("PRAGMA table_info(search_history)")
        search_columns = [row[1] for row in cursor.fetchall()]
        
        columns_to_add = [
            ('user_id', "ALTER TABLE search_history ADD COLUMN user_id TEXT NOT NULL DEFAULT 'default_user'"),
            ('session_id', "ALTER TABLE search_history ADD COLUMN session_id TEXT"),
            ('query_text', "ALTER TABLE search_history ADD COLUMN query_text TEXT"),
            ('search_type', "ALTER TABLE search_history ADD COLUMN search_type TEXT NOT NULL DEFAULT 'image'"),
            ('query_image_id', "ALTER TABLE search_history ADD COLUMN query_image_id INTEGER"),
            ('query_image_path', "ALTER TABLE search_history ADD COLUMN query_image_path TEXT"),
            ('query_features', "ALTER TABLE search_history ADD COLUMN query_features BLOB"),
            ('search_params', "ALTER TABLE search_history ADD COLUMN search_params TEXT"),
            ('filters', "ALTER TABLE search_history ADD COLUMN filters TEXT"),
            ('result_count', "ALTER TABLE search_history ADD COLUMN result_count INTEGER"),
            ('execution_time', "ALTER TABLE search_history ADD COLUMN execution_time REAL"),
        ]
        
        for column_name, alter_sql in columns_to_add:
            if column_name not in search_columns:
                self.logger.info(f"添加search_history表的{column_name}列")
                cursor.execute(alter_sql)
                conn.commit()
    
    def _update_fabric_images_table(self, cursor: sqlite3.Cursor, conn: sqlite3.Connection):
        """更新fabric_images表结构"""
        # 检查fabric_images表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='fabric_images'")
        if not cursor.fetchone():
            self.logger.info("创建fabric_images表")
            cursor.execute("""
                CREATE TABLE fabric_images (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT NOT NULL UNIQUE,
                    file_name TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    width INTEGER NOT NULL,
                    height INTEGER NOT NULL,
                    channels INTEGER DEFAULT 3,
                    format TEXT NOT NULL,
                    hash_md5 TEXT NOT NULL,
                    features BLOB,
                    thumbnail_path TEXT,
                    tags TEXT,
                    category TEXT,
                    color_info TEXT,
                    texture_info TEXT,
                    pattern_info TEXT,
                    material_info TEXT,
                    description TEXT,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    indexed_at TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            """)
            # 创建相关索引
            self._create_fabric_images_indexes(cursor)
            conn.commit()
            return
        
        # 检查fabric_images表是否存在必要的列
        cursor.execute("PRAGMA table_info(fabric_images)")
        fabric_columns = [row[1] for row in cursor.fetchall()]
        
        columns_to_add = [
            ('channels', "ALTER TABLE fabric_images ADD COLUMN channels INTEGER DEFAULT 3"),
            ('description', "ALTER TABLE fabric_images ADD COLUMN description TEXT"),
            ('metadata', "ALTER TABLE fabric_images ADD COLUMN metadata TEXT"),
        ]
        
        for column_name, alter_sql in columns_to_add:
            if column_name not in fabric_columns:
                self.logger.info(f"添加fabric_images表的{column_name}列")
                cursor.execute(alter_sql)
                conn.commit()
    
    def _update_image_features_table(self, cursor: sqlite3.Cursor, conn: sqlite3.Connection):
        """更新image_features表结构"""
        # 检查image_features表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='image_features'")
        if not cursor.fetchone():
            self.logger.info("创建image_features表")
            cursor.execute("""
                CREATE TABLE image_features (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    image_id INTEGER NOT NULL,
                    feature_type TEXT NOT NULL,
                    feature_data BLOB NOT NULL,
                    model_name TEXT,
                    feature_dim INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (image_id) REFERENCES fabric_images (id) ON DELETE CASCADE
                )
            """)
            # 创建相关索引
            self._create_image_features_indexes(cursor)
            conn.commit()
            return
        
        # 检查image_features表是否存在必要的列
        cursor.execute("PRAGMA table_info(image_features)")
        feature_columns = [row[1] for row in cursor.fetchall()]
        
        if 'feature_dim' not in feature_columns:
            self.logger.info("添加image_features表的feature_dim列")
            cursor.execute("ALTER TABLE image_features ADD COLUMN feature_dim INTEGER DEFAULT 0")
            conn.commit()
        
        if 'updated_at' not in feature_columns:
            self.logger.info("添加image_features表的updated_at列")
            cursor.execute("ALTER TABLE image_features ADD COLUMN updated_at TIMESTAMP DEFAULT NULL")
            cursor.execute("UPDATE image_features SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL")
            conn.commit()
    
    def _update_user_preferences_table(self, cursor: sqlite3.Cursor, conn: sqlite3.Connection):
        """更新user_preferences表结构"""
        # 检查user_preferences表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_preferences'")
        if not cursor.fetchone():
            self.logger.info("创建user_preferences表")
            cursor.execute("""
                CREATE TABLE user_preferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT NOT NULL UNIQUE,
                    value TEXT NOT NULL,
                    data_type TEXT DEFAULT 'string',
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_preferences_key ON user_preferences(key)")
            conn.commit()
    
    def _create_indexes(self, cursor: sqlite3.Cursor):
        """创建数据库索引
        
        Args:
            cursor: 数据库游标
        """
        self._create_fabric_images_indexes(cursor)
        self._create_image_features_indexes(cursor)
        self._create_search_history_indexes(cursor)
        self._create_user_preferences_indexes(cursor)
    
    def _create_fabric_images_indexes(self, cursor: sqlite3.Cursor):
        """创建fabric_images表索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_fabric_images_file_path ON fabric_images(file_path)",
            "CREATE INDEX IF NOT EXISTS idx_fabric_images_hash_md5 ON fabric_images(hash_md5)",
            "CREATE INDEX IF NOT EXISTS idx_fabric_images_category ON fabric_images(category)",
            "CREATE INDEX IF NOT EXISTS idx_fabric_images_created_at ON fabric_images(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_fabric_images_is_active ON fabric_images(is_active)",
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                self.logger.warning(f"创建fabric_images索引失败: {e}")
    
    def _create_image_features_indexes(self, cursor: sqlite3.Cursor):
        """创建image_features表索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_image_features_image_id ON image_features(image_id)",
            "CREATE INDEX IF NOT EXISTS idx_image_features_feature_type ON image_features(feature_type)",
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                self.logger.warning(f"创建image_features索引失败: {e}")
    
    def _create_search_history_indexes(self, cursor: sqlite3.Cursor):
        """创建search_history表索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_search_history_created_at ON search_history(created_at)",
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                self.logger.warning(f"创建search_history索引失败: {e}")
    
    def _create_user_preferences_indexes(self, cursor: sqlite3.Cursor):
        """创建user_preferences表索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_user_preferences_key ON user_preferences(key)",
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                self.logger.warning(f"创建user_preferences索引失败: {e}")
    
    def _create_triggers(self, cursor: sqlite3.Cursor):
        """创建数据库触发器
        
        Args:
            cursor: 数据库游标
        """
        triggers = [
            # 更新fabric_images的updated_at字段
            """
            CREATE TRIGGER IF NOT EXISTS update_fabric_images_timestamp 
            AFTER UPDATE ON fabric_images
            BEGIN
                UPDATE fabric_images SET updated_at = CURRENT_TIMESTAMP 
                WHERE id = NEW.id;
            END
            """,
            
            # 更新image_features的updated_at字段
            """
            CREATE TRIGGER IF NOT EXISTS update_image_features_timestamp 
            AFTER UPDATE ON image_features
            BEGIN
                UPDATE image_features SET updated_at = CURRENT_TIMESTAMP 
                WHERE id = NEW.id;
            END
            """,
            
            # 更新user_preferences的updated_at字段
            """
            CREATE TRIGGER IF NOT EXISTS update_user_preferences_timestamp 
            AFTER UPDATE ON user_preferences
            BEGIN
                UPDATE user_preferences SET updated_at = CURRENT_TIMESTAMP 
                WHERE id = NEW.id;
            END
            """
        ]
        
        for trigger_sql in triggers:
            try:
                cursor.execute(trigger_sql)
            except Exception as e:
                self.logger.warning(f"创建触发器失败: {e}")
    
    def get_table_names(self) -> List[str]:
        """获取数据库中的所有表名
        
        Returns:
            List[str]: 表名列表
        """
        try:
            with self.connection_manager.connection_context() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                tables = [row[0] for row in cursor.fetchall()]
                cursor.close()
                return tables
        except Exception as e:
            self.logger.error(f"获取表名失败: {e}")
            return []