#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习特征处理器

负责特征的后处理、归一化和优化操作。
"""

import logging
import torch
import numpy as np
from typing import Optional, List, Union, Dict, Any
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA

logger = logging.getLogger(__name__)


class DeepFeatureProcessor:
    """深度学习特征处理器"""
    
    def __init__(self, normalize_method: str = 'l2',
                 reduce_dimension: bool = False,
                 target_dimension: Optional[int] = None,
                 device: str = 'cpu'):
        """初始化特征处理器
        
        Args:
            normalize_method: 归一化方法 ('l2', 'l1', 'standard', 'minmax', 'none')
            reduce_dimension: 是否降维
            target_dimension: 目标维度
            device: 计算设备
        """
        self.normalize_method = normalize_method
        self.reduce_dimension = reduce_dimension
        self.target_dimension = target_dimension
        self.device = device
        
        # 初始化处理器
        self.scaler = None
        self.pca = None
        self._setup_processors()
        
        logger.debug(f"初始化特征处理器，归一化方法: {normalize_method}")
    
    def _setup_processors(self):
        """设置特征处理器"""
        try:
            # 设置标准化器
            if self.normalize_method == 'standard':
                self.scaler = StandardScaler()
            elif self.normalize_method == 'minmax':
                self.scaler = MinMaxScaler()
            
            # 设置降维器
            if self.reduce_dimension and self.target_dimension:
                self.pca = PCA(n_components=self.target_dimension)
                
            logger.debug("特征处理器设置完成")
            
        except Exception as e:
            logger.error(f"设置特征处理器失败: {str(e)}")
            raise
    
    def process_features(self, features: Union[torch.Tensor, np.ndarray],
                        fit_transform: bool = False) -> np.ndarray:
        """处理特征
        
        Args:
            features: 输入特征
            fit_transform: 是否拟合并转换（用于训练时）
            
        Returns:
            np.ndarray: 处理后的特征
        """
        try:
            # 转换为numpy数组
            if isinstance(features, torch.Tensor):
                features = features.detach().cpu().numpy()
            
            # 验证特征
            if not self._validate_features(features):
                raise ValueError("无效的特征数据")
            
            processed_features = features.copy()
            
            # 归一化处理
            processed_features = self._normalize_features(processed_features, fit_transform)
            
            # 降维处理
            if self.reduce_dimension:
                processed_features = self._reduce_dimensions(processed_features, fit_transform)
            
            # 最终验证
            if not self._validate_features(processed_features):
                raise RuntimeError("特征处理后数据无效")
            
            logger.debug(f"特征处理完成，输入形状: {features.shape}, 输出形状: {processed_features.shape}")
            
            return processed_features
            
        except Exception as e:
            logger.error(f"特征处理失败: {str(e)}")
            raise
    
    def _normalize_features(self, features: np.ndarray, fit_transform: bool = False) -> np.ndarray:
        """归一化特征
        
        Args:
            features: 输入特征
            fit_transform: 是否拟合并转换
            
        Returns:
            np.ndarray: 归一化后的特征
        """
        try:
            if self.normalize_method == 'none':
                return features
            
            # L2归一化
            if self.normalize_method == 'l2':
                return self._l2_normalize(features)
            
            # L1归一化
            elif self.normalize_method == 'l1':
                return self._l1_normalize(features)
            
            # 标准化
            elif self.normalize_method == 'standard':
                if self.scaler is None:
                    self.scaler = StandardScaler()
                
                if fit_transform:
                    return self.scaler.fit_transform(features)
                else:
                    if hasattr(self.scaler, 'mean_'):
                        return self.scaler.transform(features)
                    else:
                        logger.warning("标准化器未拟合，使用fit_transform")
                        return self.scaler.fit_transform(features)
            
            # 最小-最大归一化
            elif self.normalize_method == 'minmax':
                if self.scaler is None:
                    self.scaler = MinMaxScaler()
                
                if fit_transform:
                    return self.scaler.fit_transform(features)
                else:
                    if hasattr(self.scaler, 'min_'):
                        return self.scaler.transform(features)
                    else:
                        logger.warning("最小-最大归一化器未拟合，使用fit_transform")
                        return self.scaler.fit_transform(features)
            
            else:
                logger.warning(f"未知的归一化方法: {self.normalize_method}，使用L2归一化")
                return self._l2_normalize(features)
                
        except Exception as e:
            logger.error(f"特征归一化失败: {str(e)}")
            raise
    
    def _l2_normalize(self, features: np.ndarray) -> np.ndarray:
        """L2归一化"""
        try:
            # 计算L2范数
            norms = np.linalg.norm(features, axis=1, keepdims=True)
            
            # 避免除零
            norms = np.where(norms == 0, 1, norms)
            
            return features / norms
            
        except Exception as e:
            logger.error(f"L2归一化失败: {str(e)}")
            raise
    
    def _l1_normalize(self, features: np.ndarray) -> np.ndarray:
        """L1归一化"""
        try:
            # 计算L1范数
            norms = np.sum(np.abs(features), axis=1, keepdims=True)
            
            # 避免除零
            norms = np.where(norms == 0, 1, norms)
            
            return features / norms
            
        except Exception as e:
            logger.error(f"L1归一化失败: {str(e)}")
            raise
    
    def _reduce_dimensions(self, features: np.ndarray, fit_transform: bool = False) -> np.ndarray:
        """降维处理
        
        Args:
            features: 输入特征
            fit_transform: 是否拟合并转换
            
        Returns:
            np.ndarray: 降维后的特征
        """
        try:
            if not self.reduce_dimension or not self.target_dimension:
                return features
            
            # 检查是否需要降维
            if features.shape[1] <= self.target_dimension:
                logger.debug(f"特征维度 {features.shape[1]} 已小于等于目标维度 {self.target_dimension}，跳过降维")
                return features
            
            if self.pca is None:
                self.pca = PCA(n_components=self.target_dimension)
            
            if fit_transform:
                reduced_features = self.pca.fit_transform(features)
                logger.info(f"PCA降维完成，解释方差比: {self.pca.explained_variance_ratio_.sum():.4f}")
            else:
                if hasattr(self.pca, 'components_'):
                    reduced_features = self.pca.transform(features)
                else:
                    logger.warning("PCA未拟合，使用fit_transform")
                    reduced_features = self.pca.fit_transform(features)
            
            return reduced_features
            
        except Exception as e:
            logger.error(f"特征降维失败: {str(e)}")
            raise
    
    def _validate_features(self, features: np.ndarray) -> bool:
        """验证特征的有效性
        
        Args:
            features: 特征数组
            
        Returns:
            bool: 特征是否有效
        """
        try:
            if features is None:
                return False
            
            if not isinstance(features, np.ndarray):
                return False
            
            if features.size == 0:
                return False
            
            # 检查维度
            if len(features.shape) != 2:
                logger.warning(f"特征维度不正确: {features.shape}")
                return False
            
            # 检查异常值
            if np.isnan(features).any():
                logger.warning("特征包含NaN值")
                return False
            
            if np.isinf(features).any():
                logger.warning("特征包含无穷大值")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证特征失败: {str(e)}")
            return False
    
    def batch_process_features(self, features_list: List[Union[torch.Tensor, np.ndarray]],
                              fit_transform: bool = False) -> List[np.ndarray]:
        """批量处理特征
        
        Args:
            features_list: 特征列表
            fit_transform: 是否拟合并转换
            
        Returns:
            List[np.ndarray]: 处理后的特征列表
        """
        try:
            if not features_list:
                return []
            
            processed_features = []
            
            # 如果需要拟合，先合并所有特征进行拟合
            if fit_transform and (self.normalize_method in ['standard', 'minmax'] or self.reduce_dimension):
                # 合并所有特征
                all_features = []
                for features in features_list:
                    if isinstance(features, torch.Tensor):
                        features = features.detach().cpu().numpy()
                    all_features.append(features)
                
                combined_features = np.vstack(all_features)
                
                # 拟合处理器
                self.process_features(combined_features, fit_transform=True)
                
                # 现在处理每个特征（不再拟合）
                for features in features_list:
                    processed = self.process_features(features, fit_transform=False)
                    processed_features.append(processed)
            else:
                # 逐个处理
                for features in features_list:
                    processed = self.process_features(features, fit_transform=fit_transform)
                    processed_features.append(processed)
            
            logger.debug(f"批量特征处理完成，处理了 {len(processed_features)} 个特征")
            
            return processed_features
            
        except Exception as e:
            logger.error(f"批量特征处理失败: {str(e)}")
            raise
    
    def get_feature_stats(self, features: np.ndarray) -> Dict[str, Any]:
        """获取特征统计信息
        
        Args:
            features: 特征数组
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            if not self._validate_features(features):
                return {}
            
            stats = {
                'shape': features.shape,
                'mean': np.mean(features, axis=0),
                'std': np.std(features, axis=0),
                'min': np.min(features, axis=0),
                'max': np.max(features, axis=0),
                'norm_l2': np.linalg.norm(features, axis=1),
                'sparsity': np.mean(features == 0)
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取特征统计信息失败: {str(e)}")
            return {}
    
    def save_processors(self, filepath: str):
        """保存处理器状态
        
        Args:
            filepath: 保存路径
        """
        try:
            import pickle
            
            state = {
                'normalize_method': self.normalize_method,
                'reduce_dimension': self.reduce_dimension,
                'target_dimension': self.target_dimension,
                'scaler': self.scaler,
                'pca': self.pca
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(state, f)
            
            logger.info(f"处理器状态已保存到: {filepath}")
            
        except Exception as e:
            logger.error(f"保存处理器状态失败: {str(e)}")
            raise
    
    def load_processors(self, filepath: str):
        """加载处理器状态
        
        Args:
            filepath: 加载路径
        """
        try:
            import pickle
            
            with open(filepath, 'rb') as f:
                state = pickle.load(f)
            
            self.normalize_method = state.get('normalize_method', 'l2')
            self.reduce_dimension = state.get('reduce_dimension', False)
            self.target_dimension = state.get('target_dimension', None)
            self.scaler = state.get('scaler', None)
            self.pca = state.get('pca', None)
            
            logger.info(f"处理器状态已从 {filepath} 加载")
            
        except Exception as e:
            logger.error(f"加载处理器状态失败: {str(e)}")
            raise