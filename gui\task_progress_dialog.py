"""任务进度对话框模块

该模块提供任务进度对话框的导入转发，保持向后兼容性。
"""

# 导入重构后的组件
from gui.dialogs.task_progress_dialog import TaskProgressDialog
from gui.dialogs.task_config import TaskDisplayConfig, TaskStatusInfo, TaskProgressInfo
from gui.dialogs.task_table import TaskTable
from gui.dialogs.task_details import TaskDetailsWidget

# 导出所有组件
__all__ = [
    'TaskProgressDialog',
    'TaskDisplayConfig',
    'TaskStatusInfo', 
    'TaskProgressInfo',
    'TaskTable',
    'TaskDetailsWidget'
]