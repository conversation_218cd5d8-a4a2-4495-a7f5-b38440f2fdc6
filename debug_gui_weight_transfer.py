#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI权重传递调试脚本

该脚本用于调试GUI中权重变化是否正确传递到搜索引擎，以及缓存机制是否影响结果。
"""

import sys
import os
import logging
import json
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from search.models import SearchQuery, SearchType
from search.cache_manager import CacheManager
from gui.search.models import SearchConfig, SearchMode

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_cache_key_generation():
    """测试缓存键生成是否包含特征权重"""
    logger.info("=" * 50)
    logger.info("测试缓存键生成")
    logger.info("=" * 50)
    
    cache_manager = CacheManager()
    
    # 创建两个具有不同特征权重的查询
    query1 = SearchQuery(
        query_type=SearchType.IMAGE_SIMILARITY,
        query_image_path="/test/image.jpg",
        feature_weights={
            'color': 1.0,
            'texture': 0.0,
            'shape': 0.0,
            'deep_learning': 0.0
        }
    )
    
    query2 = SearchQuery(
        query_type=SearchType.IMAGE_SIMILARITY,
        query_image_path="/test/image.jpg",
        feature_weights={
            'color': 0.0,
            'texture': 1.0,
            'shape': 0.0,
            'deep_learning': 0.0
        }
    )
    
    # 生成缓存键
    key1 = cache_manager.generate_cache_key(query1)
    key2 = cache_manager.generate_cache_key(query2)
    
    logger.info(f"查询1 (仅颜色特征) 缓存键: {key1}")
    logger.info(f"查询2 (仅纹理特征) 缓存键: {key2}")
    
    if key1 != key2:
        logger.info("✅ 缓存键生成正确 - 不同权重产生不同缓存键")
        return True
    else:
        logger.error("❌ 缓存键生成错误 - 相同权重产生相同缓存键")
        return False


def test_search_config_to_query():
    """测试SearchConfig到SearchQuery的转换"""
    logger.info("=" * 50)
    logger.info("测试SearchConfig到SearchQuery的转换")
    logger.info("=" * 50)
    
    # 模拟GUI中的搜索配置
    config = SearchConfig(
        mode=SearchMode.SIMILARITY,
        feature_weights={
            'color': 0.8,
            'texture': 0.2,
            'shape': 0.0,
            'deep_learning': 0.0
        },
        similarity_threshold=0.7,
        max_results=10
    )
    
    logger.info(f"原始配置特征权重: {config.feature_weights}")
    
    # 模拟search_handler中的转换过程
    query = SearchQuery(query_type=SearchType.IMAGE_SIMILARITY)
    
    # 设置特征权重
    if hasattr(config, 'feature_weights') and config.feature_weights:
        query.feature_weights = config.feature_weights
        logger.info(f"转换后查询特征权重: {query.feature_weights}")
    
    # 设置相似度阈值
    if hasattr(config, 'similarity_threshold'):
        query.similarity_threshold = config.similarity_threshold
        logger.info(f"转换后相似度阈值: {query.similarity_threshold}")
    
    return query


def test_weight_normalization():
    """测试权重归一化"""
    logger.info("=" * 50)
    logger.info("测试权重归一化")
    logger.info("=" * 50)
    
    # 模拟GUI中的权重设置（百分比形式）
    raw_weights = {
        'deep_learning': 25,  # 25%
        'color': 75,          # 75%
        'texture': 0,         # 0%
        'shape': 0            # 0%
    }
    
    logger.info(f"原始权重（百分比）: {raw_weights}")
    
    # 计算总权重
    total_weight = sum(raw_weights.values())
    logger.info(f"总权重: {total_weight}")
    
    # 归一化权重
    normalized_weights = {}
    if total_weight > 0:
        for feature, weight in raw_weights.items():
            normalized_weights[feature] = weight / total_weight
    
    logger.info(f"归一化权重: {normalized_weights}")
    
    # 验证权重和为1
    weight_sum = sum(normalized_weights.values())
    logger.info(f"权重和: {weight_sum}")
    
    if abs(weight_sum - 1.0) < 1e-6:
        logger.info("✅ 权重归一化正确")
        return True
    else:
        logger.error("❌ 权重归一化错误")
        return False


def test_cache_invalidation():
    """测试缓存失效机制"""
    logger.info("=" * 50)
    logger.info("测试缓存失效机制")
    logger.info("=" * 50)
    
    cache_manager = CacheManager(cache_ttl=1)  # 1秒TTL用于测试
    
    # 创建查询
    query = SearchQuery(
        query_type=SearchType.IMAGE_SIMILARITY,
        query_image_path="/test/image.jpg",
        feature_weights={'color': 1.0, 'texture': 0.0, 'shape': 0.0, 'deep_learning': 0.0}
    )
    
    # 模拟搜索结果
    from search.models import SearchResult
    mock_result = SearchResult(
        query=query,
        results=[],
        total_results=0
    )
    
    # 缓存结果
    cache_manager.cache_result(query, mock_result)
    logger.info("结果已缓存")
    
    # 立即获取缓存
    cached = cache_manager.get_cached_result(query)
    if cached:
        logger.info("✅ 立即获取缓存成功")
    else:
        logger.error("❌ 立即获取缓存失败")
        return False
    
    # 等待缓存过期
    import time
    time.sleep(2)
    
    # 再次获取缓存
    cached = cache_manager.get_cached_result(query)
    if cached is None:
        logger.info("✅ 缓存正确过期")
        return True
    else:
        logger.error("❌ 缓存未正确过期")
        return False


def main():
    """主函数"""
    logger.info("开始GUI权重传递调试测试")
    logger.info("=" * 80)
    
    results = []
    
    # 测试1: 缓存键生成
    results.append(test_cache_key_generation())
    
    # 测试2: 配置转换
    query = test_search_config_to_query()
    results.append(query is not None)
    
    # 测试3: 权重归一化
    results.append(test_weight_normalization())
    
    # 测试4: 缓存失效
    results.append(test_cache_invalidation())
    
    # 总结
    logger.info("=" * 80)
    logger.info("测试总结")
    logger.info("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    logger.info(f"通过测试: {passed}/{total}")
    
    if passed == total:
        logger.info("✅ 所有测试通过")
    else:
        logger.error("❌ 部分测试失败")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
