"""搜索配置模块

定义图像搜索相关的配置参数。
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from enum import Enum
from pathlib import Path


class SimilarityMetric(Enum):
    """相似度度量枚举"""
    COSINE = "cosine"
    EUCLIDEAN = "euclidean"
    MANHATTAN = "manhattan"
    CHEBYSHEV = "chebyshev"
    CORRELATION = "correlation"
    CHI_SQUARE = "chi_square"
    INTERSECTION = "intersection"
    HELLINGER = "hellinger"


class IndexType(Enum):
    """索引类型枚举"""
    FLAT = "flat"  # 暴力搜索
    IVF_FLAT = "ivf_flat"  # 倒排文件索引
    IVF_PQ = "ivf_pq"  # 倒排文件 + 乘积量化
    HNSW = "hnsw"  # 分层导航小世界图
    LSH = "lsh"  # 局部敏感哈希


class WeightStrategy(Enum):
    """权重策略枚举"""
    UNIFORM = "uniform"  # 均匀权重
    ADAPTIVE = "adaptive"  # 自适应权重
    LEARNED = "learned"  # 学习权重
    MANUAL = "manual"  # 手动权重


@dataclass
class FaissConfig:
    """FAISS索引配置"""
    index_type: IndexType = IndexType.IVF_FLAT
    nlist: int = 100  # IVF聚类中心数
    nprobe: int = 10  # 搜索时探测的聚类数
    
    # PQ参数
    m: int = 8  # 子向量数量
    nbits: int = 8  # 每个子向量的位数
    
    # HNSW参数
    M: int = 16  # 每个节点的连接数
    efConstruction: int = 200  # 构建时的搜索深度
    efSearch: int = 50  # 搜索时的搜索深度
    
    # 训练参数
    train_size: int = 10000  # 训练样本数
    use_gpu: bool = False  # 是否使用GPU
    gpu_id: int = 0  # GPU设备ID
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'index_type': self.index_type.value,
            'nlist': self.nlist,
            'nprobe': self.nprobe,
            'm': self.m,
            'nbits': self.nbits,
            'M': self.M,
            'efConstruction': self.efConstruction,
            'efSearch': self.efSearch,
            'train_size': self.train_size,
            'use_gpu': self.use_gpu,
            'gpu_id': self.gpu_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FaissConfig':
        """从字典创建"""
        return cls(
            index_type=IndexType(data.get('index_type', 'ivf_flat')),
            nlist=data.get('nlist', 100),
            nprobe=data.get('nprobe', 10),
            m=data.get('m', 8),
            nbits=data.get('nbits', 8),
            M=data.get('M', 16),
            efConstruction=data.get('efConstruction', 200),
            efSearch=data.get('efSearch', 50),
            train_size=data.get('train_size', 10000),
            use_gpu=data.get('use_gpu', False),
            gpu_id=data.get('gpu_id', 0)
        )


@dataclass
class FeatureIndexInfo:
    """特征索引信息"""
    total_features: int = 0
    index_size_mb: float = 0.0
    last_updated: Optional[str] = None
    model_name: Optional[str] = None
    feature_dim: Optional[int] = None


@dataclass
class FilterConfig:
    """过滤配置"""
    enable_category_filter: bool = True
    enable_tag_filter: bool = True
    enable_metadata_filter: bool = True
    
    # 类别过滤
    allowed_categories: Optional[List[str]] = None
    excluded_categories: Optional[List[str]] = None
    
    # 标签过滤
    required_tags: Optional[List[str]] = None
    excluded_tags: Optional[List[str]] = None
    
    # 元数据过滤
    metadata_filters: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'enable_category_filter': self.enable_category_filter,
            'enable_tag_filter': self.enable_tag_filter,
            'enable_metadata_filter': self.enable_metadata_filter,
            'allowed_categories': self.allowed_categories,
            'excluded_categories': self.excluded_categories,
            'required_tags': self.required_tags,
            'excluded_tags': self.excluded_tags,
            'metadata_filters': self.metadata_filters
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FilterConfig':
        """从字典创建"""
        return cls(
            enable_category_filter=data.get('enable_category_filter', True),
            enable_tag_filter=data.get('enable_tag_filter', True),
            enable_metadata_filter=data.get('enable_metadata_filter', True),
            allowed_categories=data.get('allowed_categories'),
            excluded_categories=data.get('excluded_categories'),
            required_tags=data.get('required_tags'),
            excluded_tags=data.get('excluded_tags'),
            metadata_filters=data.get('metadata_filters', {})
        )


@dataclass
class RerankConfig:
    """重排序配置"""
    enable_rerank: bool = False
    rerank_top_k: int = 100  # 重排序前K个结果
    
    # 重排序策略
    use_semantic_similarity: bool = True
    use_visual_similarity: bool = True
    use_metadata_similarity: bool = False
    
    # 权重配置
    semantic_weight: float = 0.5
    visual_weight: float = 0.4
    metadata_weight: float = 0.1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'enable_rerank': self.enable_rerank,
            'rerank_top_k': self.rerank_top_k,
            'use_semantic_similarity': self.use_semantic_similarity,
            'use_visual_similarity': self.use_visual_similarity,
            'use_metadata_similarity': self.use_metadata_similarity,
            'semantic_weight': self.semantic_weight,
            'visual_weight': self.visual_weight,
            'metadata_weight': self.metadata_weight
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RerankConfig':
        """从字典创建"""
        return cls(
            enable_rerank=data.get('enable_rerank', False),
            rerank_top_k=data.get('rerank_top_k', 100),
            use_semantic_similarity=data.get('use_semantic_similarity', True),
            use_visual_similarity=data.get('use_visual_similarity', True),
            use_metadata_similarity=data.get('use_metadata_similarity', False),
            semantic_weight=data.get('semantic_weight', 0.5),
            visual_weight=data.get('visual_weight', 0.4),
            metadata_weight=data.get('metadata_weight', 0.1)
        )


@dataclass
class SearchConfig:
    """搜索配置主类"""
    # 基本搜索参数
    default_top_k: int = 20
    max_top_k: int = 1000
    similarity_metric: SimilarityMetric = SimilarityMetric.COSINE
    similarity_threshold: float = 0.0
    
    # 路径配置
    data_dir: str = field(default_factory=lambda: str(Path(__file__).parent.parent.parent / 'data'))
    
    # 索引配置
    use_faiss: bool = True
    faiss_config: FaissConfig = field(default_factory=FaissConfig)
    
    # 权重配置
    weight_strategy: WeightStrategy = WeightStrategy.UNIFORM
    feature_weights: Dict[str, float] = field(default_factory=lambda: {
        'color': 0.25,
        'texture': 0.25,
        'shape': 0.25,
        'deep': 0.25
    })
    
    # 过滤配置
    filter_config: FilterConfig = field(default_factory=FilterConfig)
    
    # 重排序配置
    rerank_config: RerankConfig = field(default_factory=RerankConfig)
    
    # 性能配置
    enable_parallel_search: bool = True
    max_search_threads: int = 4
    search_timeout_seconds: int = 30
    
    # 缓存配置
    enable_result_cache: bool = True
    result_cache_size: int = 1000
    result_cache_ttl_seconds: int = 3600
    
    # 调试配置
    enable_search_logging: bool = False
    log_search_details: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'default_top_k': self.default_top_k,
            'max_top_k': self.max_top_k,
            'similarity_metric': self.similarity_metric.value,
            'similarity_threshold': self.similarity_threshold,
            'data_dir': self.data_dir,
            'use_faiss': self.use_faiss,
            'faiss_config': self.faiss_config.to_dict(),
            'weight_strategy': self.weight_strategy.value,
            'feature_weights': self.feature_weights,
            'filter_config': self.filter_config.to_dict(),
            'rerank_config': self.rerank_config.to_dict(),
            'enable_parallel_search': self.enable_parallel_search,
            'max_search_threads': self.max_search_threads,
            'search_timeout_seconds': self.search_timeout_seconds,
            'enable_result_cache': self.enable_result_cache,
            'result_cache_size': self.result_cache_size,
            'result_cache_ttl_seconds': self.result_cache_ttl_seconds,
            'enable_search_logging': self.enable_search_logging,
            'log_search_details': self.log_search_details
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchConfig':
        """从字典创建"""
        return cls(
            default_top_k=data.get('default_top_k', 20),
            max_top_k=data.get('max_top_k', 1000),
            similarity_metric=SimilarityMetric(data.get('similarity_metric', 'cosine')),
            similarity_threshold=data.get('similarity_threshold', 0.0),
            data_dir=data.get('data_dir', str(Path(__file__).parent.parent.parent / 'data')),
            use_faiss=data.get('use_faiss', True),
            faiss_config=FaissConfig.from_dict(data.get('faiss_config', {})),
            weight_strategy=WeightStrategy(data.get('weight_strategy', 'uniform')),
            feature_weights=data.get('feature_weights', {
                'color': 0.25,
                'texture': 0.25,
                'shape': 0.25,
                'deep': 0.25
            }),
            filter_config=FilterConfig.from_dict(data.get('filter_config', {})),
            rerank_config=RerankConfig.from_dict(data.get('rerank_config', {})),
            enable_parallel_search=data.get('enable_parallel_search', True),
            max_search_threads=data.get('max_search_threads', 4),
            search_timeout_seconds=data.get('search_timeout_seconds', 30),
            enable_result_cache=data.get('enable_result_cache', True),
            result_cache_size=data.get('result_cache_size', 1000),
            result_cache_ttl_seconds=data.get('result_cache_ttl_seconds', 3600),
            enable_search_logging=data.get('enable_search_logging', False),
            log_search_details=data.get('log_search_details', False)
        )
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            # 检查基本参数
            if self.default_top_k <= 0 or self.max_top_k <= 0:
                return False
            if self.default_top_k > self.max_top_k:
                return False
            if not (0.0 <= self.similarity_threshold <= 1.0):
                return False
                
            # 检查权重
            if self.weight_strategy == WeightStrategy.MANUAL:
                total_weight = sum(self.feature_weights.values())
                if abs(total_weight - 1.0) > 0.01:
                    return False
                    
            # 检查性能参数
            if self.max_search_threads <= 0:
                return False
            if self.search_timeout_seconds <= 0:
                return False
                
            # 检查缓存参数
            if self.result_cache_size <= 0:
                return False
            if self.result_cache_ttl_seconds <= 0:
                return False
                
            return True
            
        except Exception:
            return False
    
    def get_effective_top_k(self, requested_k: Optional[int] = None) -> int:
        """获取有效的top_k值"""
        if requested_k is None:
            return self.default_top_k
        return min(max(1, requested_k), self.max_top_k)
    
    def should_use_faiss(self, feature_count: int) -> bool:
        """判断是否应该使用FAISS"""
        # 当特征数量较大时使用FAISS
        return self.use_faiss and feature_count > 1000