#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口状态管理器

该模块负责管理主窗口的状态，如全屏、最大化、工具栏显示等。
"""

from typing import Optional, Dict, Any
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtWidgets import QMainWindow, QStatusBar, QLabel, QProgressBar
from PyQt6.QtGui import QFont

from utils.log_utils import LoggerMixin
from config.ui_config import UIConfig
from gui.themes import ThemeManager, ThemeType


class WindowStateManager(QObject, LoggerMixin):
    """窗口状态管理器"""
    
    # 信号
    fullscreenChanged = pyqtSignal(bool)
    toolbarVisibilityChanged = pyqtSignal(bool)
    themeChanged = pyqtSignal(ThemeType)
    fontChanged = pyqtSignal(QFont)
    statusUpdated = pyqtSignal(str)
    
    def __init__(self, main_window: QMainWindow):
        super().__init__()
        self.main_window = main_window
        self.ui_config = UIConfig()
        self.theme_manager = ThemeManager(self.ui_config)
        
        # 状态栏组件
        self.status_bar: Optional[QStatusBar] = None
        self.status_label: Optional[QLabel] = None
        self.progress_bar: Optional[QProgressBar] = None
        
        # 当前状态
        self._is_fullscreen = False
        self._toolbar_visible = True
        self._current_theme = ThemeType.LIGHT
        self._current_font = QFont()
        
        self.setup_status_bar()
    
    def setup_status_bar(self):
        """设置状态栏"""
        try:
            self.status_bar = self.main_window.statusBar()
            
            # 状态标签
            self.status_label = QLabel("就绪")
            self.status_bar.addWidget(self.status_label)
            
            # 进度条
            self.progress_bar = QProgressBar()
            self.progress_bar.setVisible(False)
            self.progress_bar.setMaximumWidth(200)
            self.status_bar.addPermanentWidget(self.progress_bar)
            
            self.logger.info("状态栏设置完成")
            
        except Exception as e:
            self.logger.error(f"设置状态栏失败: {e}")
    
    def toggle_fullscreen(self):
        """切换全屏模式"""
        try:
            if self._is_fullscreen:
                self.exit_fullscreen()
            else:
                self.enter_fullscreen()
                
        except Exception as e:
            self.logger.error(f"切换全屏模式失败: {e}")
    
    def enter_fullscreen(self):
        """进入全屏模式"""
        try:
            if not self._is_fullscreen:
                self.main_window.showFullScreen()
                self._is_fullscreen = True
                self.fullscreenChanged.emit(True)
                self.update_status("已进入全屏模式")
                self.logger.info("进入全屏模式")
                
        except Exception as e:
            self.logger.error(f"进入全屏模式失败: {e}")
    
    def exit_fullscreen(self):
        """退出全屏模式"""
        try:
            if self._is_fullscreen:
                self.main_window.showNormal()
                self._is_fullscreen = False
                self.fullscreenChanged.emit(False)
                self.update_status("已退出全屏模式")
                self.logger.info("退出全屏模式")
                
        except Exception as e:
            self.logger.error(f"退出全屏模式失败: {e}")
    
    def is_fullscreen(self) -> bool:
        """获取是否全屏
        
        Returns:
            bool: 是否全屏
        """
        return self._is_fullscreen
    
    def toggle_toolbar(self):
        """切换工具栏显示"""
        try:
            self.set_toolbar_visible(not self._toolbar_visible)
            
        except Exception as e:
            self.logger.error(f"切换工具栏显示失败: {e}")
    
    def set_toolbar_visible(self, visible: bool):
        """设置工具栏可见性
        
        Args:
            visible: 是否可见
        """
        try:
            # 查找工具栏并设置可见性
            toolbars = self.main_window.findChildren(type(self.main_window.addToolBar("")))
            for toolbar in toolbars:
                toolbar.setVisible(visible)
            
            self._toolbar_visible = visible
            self.toolbarVisibilityChanged.emit(visible)
            
            status_msg = "工具栏已显示" if visible else "工具栏已隐藏"
            self.update_status(status_msg)
            self.logger.info(status_msg)
            
        except Exception as e:
            self.logger.error(f"设置工具栏可见性失败: {e}")
    
    def is_toolbar_visible(self) -> bool:
        """获取工具栏是否可见
        
        Returns:
            bool: 工具栏是否可见
        """
        return self._toolbar_visible
    
    def change_theme(self, theme_type: ThemeType):
        """更改主题
        
        Args:
            theme_type: 主题类型
        """
        try:
            if theme_type != self._current_theme:
                theme_name = 'light' if theme_type == ThemeType.LIGHT else 'dark'
                self.theme_manager.apply_theme(theme_name)
                self._current_theme = theme_type
                self.themeChanged.emit(theme_type)
                
                theme_name = "浅色" if theme_type == ThemeType.LIGHT else "深色"
                self.update_status(f"已切换到{theme_name}主题")
                self.logger.info(f"主题已更改为: {theme_name}")
                
        except Exception as e:
            self.logger.error(f"更改主题失败: {e}")
    
    def get_current_theme(self) -> ThemeType:
        """获取当前主题
        
        Returns:
            ThemeType: 当前主题类型
        """
        return self._current_theme
    
    def apply_font(self, font: QFont):
        """应用字体
        
        Args:
            font: 字体对象
        """
        try:
            self.main_window.setFont(font)
            self._current_font = font
            self.fontChanged.emit(font)
            
            self.update_status(f"字体已更改为: {font.family()} {font.pointSize()}pt")
            self.logger.info(f"字体已应用: {font.family()} {font.pointSize()}pt")
            
        except Exception as e:
            self.logger.error(f"应用字体失败: {e}")
    
    def get_current_font(self) -> QFont:
        """获取当前字体
        
        Returns:
            QFont: 当前字体
        """
        return self._current_font
    
    def update_status(self, message: str, timeout: int = 3000):
        """更新状态栏消息
        
        Args:
            message: 状态消息
            timeout: 显示超时时间（毫秒），0表示永久显示
        """
        try:
            if self.status_label:
                self.status_label.setText(message)
            
            if self.status_bar:
                if timeout > 0:
                    self.status_bar.showMessage(message, timeout)
                else:
                    self.status_bar.showMessage(message)
            
            self.statusUpdated.emit(message)
            self.logger.debug(f"状态更新: {message}")
            
        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")
    
    def show_progress(self, value: int = 0, maximum: int = 100, text: str = ""):
        """显示进度条
        
        Args:
            value: 当前进度值
            maximum: 最大进度值
            text: 进度文本
        """
        try:
            if self.progress_bar:
                self.progress_bar.setMaximum(maximum)
                self.progress_bar.setValue(value)
                
                if text:
                    self.progress_bar.setFormat(text)
                
                self.progress_bar.setVisible(True)
            
            self.logger.debug(f"显示进度: {value}/{maximum} - {text}")
            
        except Exception as e:
            self.logger.error(f"显示进度失败: {e}")
    
    def update_progress(self, value: int, text: str = ""):
        """更新进度
        
        Args:
            value: 进度值
            text: 进度文本
        """
        try:
            if self.progress_bar and self.progress_bar.isVisible():
                self.progress_bar.setValue(value)
                
                if text:
                    self.progress_bar.setFormat(text)
            
            self.logger.debug(f"更新进度: {value} - {text}")
            
        except Exception as e:
            self.logger.error(f"更新进度失败: {e}")
    
    def hide_progress(self):
        """隐藏进度条"""
        try:
            if self.progress_bar:
                self.progress_bar.setVisible(False)
                self.progress_bar.setValue(0)
            
            self.logger.debug("隐藏进度条")
            
        except Exception as e:
            self.logger.error(f"隐藏进度条失败: {e}")
    
    def get_window_state(self) -> Dict[str, Any]:
        """获取窗口状态
        
        Returns:
            Dict[str, Any]: 窗口状态字典
        """
        try:
            return {
                'size': self.main_window.size(),
                'position': self.main_window.pos(),
                'maximized': self.main_window.isMaximized(),
                'fullscreen': self._is_fullscreen,
                'toolbar_visible': self._toolbar_visible,
                'theme': self._current_theme,
                'font': self._current_font
            }
            
        except Exception as e:
            self.logger.error(f"获取窗口状态失败: {e}")
            return {}
    
    def restore_window_state(self, state: Dict[str, Any]):
        """恢复窗口状态
        
        Args:
            state: 窗口状态字典
        """
        try:
            # 恢复窗口大小和位置
            if 'size' in state and 'position' in state:
                self.main_window.resize(state['size'])
                self.main_window.move(state['position'])
            
            # 恢复窗口状态
            if state.get('maximized', False):
                self.main_window.showMaximized()
            elif state.get('fullscreen', False):
                self.enter_fullscreen()
            
            # 恢复工具栏可见性
            if 'toolbar_visible' in state:
                self.set_toolbar_visible(state['toolbar_visible'])
            
            # 恢复主题
            if 'theme' in state:
                self.change_theme(state['theme'])
            
            # 恢复字体
            if 'font' in state:
                self.apply_font(state['font'])
            
            self.logger.info("窗口状态已恢复")
            
        except Exception as e:
            self.logger.error(f"恢复窗口状态失败: {e}")
    
    def center_window(self):
        """将窗口居中显示"""
        try:
            # 获取屏幕几何信息
            screen = self.main_window.screen()
            if screen:
                screen_geometry = screen.availableGeometry()
                window_geometry = self.main_window.frameGeometry()
                
                # 计算居中位置
                center_point = screen_geometry.center()
                window_geometry.moveCenter(center_point)
                
                # 移动窗口
                self.main_window.move(window_geometry.topLeft())
                
                self.logger.info("窗口已居中")
            
        except Exception as e:
            self.logger.error(f"居中窗口失败: {e}")
    
    def minimize_window(self):
        """最小化窗口"""
        try:
            self.main_window.showMinimized()
            self.logger.info("窗口已最小化")
            
        except Exception as e:
            self.logger.error(f"最小化窗口失败: {e}")
    
    def maximize_window(self):
        """最大化窗口"""
        try:
            if self.main_window.isMaximized():
                self.main_window.showNormal()
                self.update_status("窗口已还原")
            else:
                self.main_window.showMaximized()
                self.update_status("窗口已最大化")
            
            self.logger.info("窗口最大化状态已切换")
            
        except Exception as e:
            self.logger.error(f"切换窗口最大化状态失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理状态栏组件
            if self.progress_bar:
                self.progress_bar.setParent(None)
                self.progress_bar = None
            
            if self.status_label:
                self.status_label.setParent(None)
                self.status_label = None
            
            self.status_bar = None
            
            self.logger.info("窗口状态管理器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理窗口状态管理器资源失败: {e}")