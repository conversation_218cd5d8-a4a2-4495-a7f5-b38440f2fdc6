#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话框模块

该模块包含各种对话框组件。
"""

from gui.dialogs.task_manager_dialog import TaskManagerDialog
from gui.dialogs.settings_dialog import SettingsDialog
from gui.dialogs.settings_base import SettingsCategory, SettingsData, BaseSettingsWidget
from gui.dialogs.settings_general import GeneralSettingsWidget
from gui.dialogs.settings_search import SearchSettingsWidget
from gui.dialogs.settings_display import DisplaySettingsWidget
from gui.dialogs.settings_performance import PerformanceSettingsWidget
from gui.dialogs.settings_advanced import AdvancedSettingsWidget
from gui.dialogs.about_dialog import AboutDialog, AppInfo
from gui.dialogs.model_selector_dialog import ModelSelectorDialog
from gui.dialogs.model_config import ModelType, ModelDisplayInfo, ModelSelectorConfig
from gui.dialogs.model_selector import ModelSelector
from gui.dialogs.model_details import ModelDetailsWidget
from gui.dialogs.task_progress_dialog import TaskProgressDialog
from gui.dialogs.task_config import TaskDisplayConfig, TaskStatusInfo, TaskProgressInfo
from gui.dialogs.task_table import TaskTable
from gui.dialogs.task_details import TaskDetailsWidget as TaskDetailsWidget_

__all__ = [
    'TaskManagerDialog',
    'SettingsDialog',
    'SettingsCategory',
    'SettingsData',
    'BaseSettingsWidget',
    'GeneralSettingsWidget',
    'SearchSettingsWidget',
    'DisplaySettingsWidget',
    'PerformanceSettingsWidget',
    'AdvancedSettingsWidget',
    'AboutDialog',
    'AppInfo',
    'ModelSelectorDialog',
    'ModelType',
    'ModelDisplayInfo',
    'ModelSelectorConfig',
    'ModelSelector',
    'ModelDetailsWidget',
    'TaskProgressDialog',
    'TaskDisplayConfig',
    'TaskStatusInfo',
    'TaskProgressInfo',
    'TaskTable',
    'TaskDetailsWidget_'
]