#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
几何信息处理模块

该模块提供窗口几何信息和屏幕信息的处理功能。
"""

from dataclasses import dataclass
from typing import Optional

from PyQt6.QtCore import QRect
from PyQt6.QtGui import QGuiApplication


@dataclass
class WindowGeometry:
    """窗口几何信息"""
    x: int
    y: int
    width: int
    height: int
    maximized: bool = False
    
    def to_rect(self) -> QRect:
        """转换为QRect"""
        return QRect(self.x, self.y, self.width, self.height)
    
    @classmethod
    def from_rect(cls, rect: QRect, maximized: bool = False) -> 'WindowGeometry':
        """从QRect创建"""
        return cls(
            x=rect.x(),
            y=rect.y(),
            width=rect.width(),
            height=rect.height(),
            maximized=maximized
        )


@dataclass
class ScreenInfo:
    """屏幕信息"""
    width: int
    height: int
    dpi: float
    scale_factor: float
    available_geometry: QRect
    
    @classmethod
    def get_primary_screen_info(cls) -> 'ScreenInfo':
        """获取主屏幕信息"""
        screen = QGuiApplication.primaryScreen()
        geometry = screen.geometry()
        available_geometry = screen.availableGeometry()
        
        return cls(
            width=geometry.width(),
            height=geometry.height(),
            dpi=screen.logicalDotsPerInch(),
            scale_factor=screen.devicePixelRatio(),
            available_geometry=available_geometry
        )