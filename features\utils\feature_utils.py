#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征处理工具模块

该模块提供特征归一化、相似度计算、特征保存加载等功能。
"""

import os
import pickle
import logging
from typing import Union, Dict, Any, Optional
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import normalize

logger = logging.getLogger(__name__)


def normalize_features(features: np.ndarray, method: str = "l2") -> np.ndarray:
    """归一化特征向量
    
    Args:
        features: 特征向量
        method: 归一化方法 (l2, l1, max)
        
    Returns:
        np.ndarray: 归一化后的特征向量
    """
    if features is None:
        return features
    
    if isinstance(features, np.ndarray) and features.size == 0:
        return features
    
    if not isinstance(features, np.ndarray) and len(features) == 0:
        return features
    
    try:
        if method == "l2":
            # L2归一化
            norm = np.linalg.norm(features)
            if norm > 0:
                return features / norm
            else:
                return features
        elif method == "l1":
            # L1归一化
            norm = np.sum(np.abs(features))
            if norm > 0:
                return features / norm
            else:
                return features
        elif method == "max":
            # 最大值归一化
            max_val = np.max(np.abs(features))
            if max_val > 0:
                return features / max_val
            else:
                return features
        elif method == "sklearn_l2":
            # 使用sklearn的L2归一化
            return normalize(features.reshape(1, -1), norm='l2')[0]
        else:
            logger.warning(f"Unknown normalization method: {method}, using l2")
            return normalize_features(features, "l2")
            
    except Exception as e:
        logger.error(f"Error normalizing features: {str(e)}")
        return features


def compute_similarity(features1: np.ndarray, features2: np.ndarray, 
                      method: str = "cosine") -> float:
    """计算两个特征向量的相似度
    
    Args:
        features1: 第一个特征向量
        features2: 第二个特征向量
        method: 相似度计算方法 (cosine, euclidean, dot_product)
        
    Returns:
        float: 相似度分数
    """
    try:
        # 验证输入
        if features1 is None or features2 is None:
            return 0.0
        
        # 检查特征维度是否匹配
        if isinstance(features1, np.ndarray) and isinstance(features2, np.ndarray):
            if features1.shape != features2.shape:
                logger.warning(f"Feature dimension mismatch: {features1.shape} vs {features2.shape}")
                return 0.0
        else:
            if len(features1) != len(features2):
                logger.warning(f"Feature dimension mismatch: {len(features1)} vs {len(features2)}")
                return 0.0
        
        if method == "cosine":
            # 余弦相似度
            similarity = cosine_similarity(
                features1.reshape(1, -1), 
                features2.reshape(1, -1)
            )[0, 0]
            return float(similarity)
            
        elif method == "euclidean":
            # 欧氏距离转相似度
            distance = np.linalg.norm(features1 - features2)
            # 转换为相似度 (0-1范围)
            similarity = 1.0 / (1.0 + distance)
            return float(similarity)
            
        elif method == "dot_product":
            # 点积相似度
            similarity = np.dot(features1, features2)
            return float(similarity)
            
        elif method == "manhattan":
            # 曼哈顿距离转相似度
            distance = np.sum(np.abs(features1 - features2))
            similarity = 1.0 / (1.0 + distance)
            return float(similarity)
            
        else:
            logger.warning(f"Unknown similarity method: {method}, using cosine")
            return compute_similarity(features1, features2, "cosine")
            
    except Exception as e:
        logger.error(f"Error computing similarity: {str(e)}")
        return 0.0


def validate_features(features: np.ndarray) -> bool:
    """验证特征向量
    
    Args:
        features: 特征向量
        
    Returns:
        bool: 是否有效
    """
    if features is None:
        return False
    
    if not isinstance(features, np.ndarray):
        return False
    
    if features.size == 0:
        return False
    
    if np.any(np.isnan(features)) or np.any(np.isinf(features)):
        return False
    
    return True


def save_features(features: Union[np.ndarray, Dict[str, Any]], 
                 file_path: str, format: str = "pickle") -> bool:
    """保存特征到文件
    
    Args:
        features: 特征数据
        file_path: 保存路径
        format: 保存格式 (pickle, npy)
        
    Returns:
        bool: 是否保存成功
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        if format == "pickle":
            with open(file_path, 'wb') as f:
                pickle.dump(features, f)
        elif format == "npy":
            if isinstance(features, np.ndarray):
                np.save(file_path, features)
            else:
                logger.error("NPY format only supports numpy arrays")
                return False
        else:
            logger.error(f"Unsupported format: {format}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error saving features to {file_path}: {str(e)}")
        return False


def load_features(file_path: str, format: str = "auto") -> Optional[Union[np.ndarray, Dict[str, Any]]]:
    """从文件加载特征
    
    Args:
        file_path: 文件路径
        format: 文件格式 (auto, pickle, npy)
        
    Returns:
        Optional[Union[np.ndarray, Dict[str, Any]]]: 加载的特征数据
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"Feature file not found: {file_path}")
            return None
        
        # 自动检测格式
        if format == "auto":
            _, ext = os.path.splitext(file_path)
            if ext == ".npy":
                format = "npy"
            else:
                format = "pickle"
        
        if format == "pickle":
            with open(file_path, 'rb') as f:
                return pickle.load(f)
        elif format == "npy":
            return np.load(file_path)
        else:
            logger.error(f"Unsupported format: {format}")
            return None
            
    except Exception as e:
        logger.error(f"Error loading features from {file_path}: {str(e)}")
        return None


def batch_normalize_features(features_list: list, method: str = "l2") -> list:
    """批量归一化特征
    
    Args:
        features_list: 特征列表
        method: 归一化方法
        
    Returns:
        list: 归一化后的特征列表
    """
    normalized_features = []
    for features in features_list:
        if validate_features(features):
            normalized = normalize_features(features, method)
            normalized_features.append(normalized)
        else:
            logger.warning("Invalid features found in batch, skipping")
            normalized_features.append(features)
    
    return normalized_features


def compute_batch_similarity(query_features: np.ndarray,
                           features_matrix: np.ndarray,
                           method: str = "cosine") -> np.ndarray:
    """批量计算相似度

    Args:
        query_features: 查询特征向量
        features_matrix: 特征矩阵 (每行一个特征向量)
        method: 相似度计算方法

    Returns:
        np.ndarray: 相似度分数数组，形状为 (1, n)，与FAISS返回格式一致
    """
    try:
        # 确保输入是有效的
        if query_features is None or features_matrix is None:
            logger.error("Invalid input for batch similarity computation")
            return np.array([[0.0]])

        # 确保特征矩阵不为空
        if isinstance(features_matrix, np.ndarray) and features_matrix.size == 0:
            logger.warning("Empty features matrix")
            return np.array([[0.0]])

        # 确保查询特征不为空
        if isinstance(query_features, np.ndarray) and query_features.size == 0:
            logger.warning("Empty query features")
            return np.array([[0.0]])

        # 确保查询特征是正确的形状
        if query_features.ndim == 1:
            query_features = query_features.reshape(1, -1)

        # 确保特征维度匹配
        if query_features.shape[1] != features_matrix.shape[1]:
            logger.error(f"Feature dimension mismatch: {query_features.shape[1]} vs {features_matrix.shape[1]}")
            return np.zeros((1, features_matrix.shape[0]))

        if method == "cosine":
            # 使用余弦相似度
            try:
                similarities = cosine_similarity(
                    query_features,
                    features_matrix
                )
                # 确保返回的是(1, n)形状的数组
                if similarities.ndim == 2 and similarities.shape[0] == 1:
                    return similarities
                else:
                    # 如果形状不对，重新整形
                    return similarities.reshape(1, -1)
            except Exception as inner_e:
                logger.error(f"Error in cosine similarity: {str(inner_e)}")
                return np.zeros((1, features_matrix.shape[0]))

        elif method == "euclidean":
            # 批量计算欧氏距离
            try:
                # 计算欧氏距离
                distances = np.linalg.norm(features_matrix - query_features, axis=1)
                # 转换为相似度 (0-1范围)
                similarities = 1.0 / (1.0 + distances)
                return similarities.reshape(1, -1)
            except Exception as inner_e:
                logger.error(f"Error in euclidean similarity: {str(inner_e)}")
                return np.zeros((1, features_matrix.shape[0]))

        elif method == "dot_product":
            # 批量计算点积
            try:
                similarities = np.dot(features_matrix, query_features.T).flatten()
                return similarities.reshape(1, -1)
            except Exception as inner_e:
                logger.error(f"Error in dot product similarity: {str(inner_e)}")
                return np.zeros((1, features_matrix.shape[0]))

        elif method == "manhattan":
            # 批量计算曼哈顿距离
            try:
                # 计算曼哈顿距离
                distances = np.sum(np.abs(features_matrix - query_features), axis=1)
                # 转换为相似度
                similarities = 1.0 / (1.0 + distances)
                return similarities.reshape(1, -1)
            except Exception as inner_e:
                logger.error(f"Error in manhattan similarity: {str(inner_e)}")
                return np.zeros((1, features_matrix.shape[0]))
        else:
            # 对于未知方法，回退到逐个计算
            logger.warning(f"Unknown similarity method: {method}, falling back to individual computation")
            try:
                similarities = []
                query_flat = query_features.flatten()
                for features in features_matrix:
                    sim = compute_similarity(query_flat, features, method)
                    # 确保sim是标量
                    if isinstance(sim, np.ndarray):
                        sim = float(sim.item()) if sim.size == 1 else float(sim[0])
                    similarities.append(sim)
                return np.array(similarities).reshape(1, -1)
            except Exception as inner_e:
                logger.error(f"Error in fallback similarity method: {str(inner_e)}")
                return np.zeros((1, features_matrix.shape[0]))

    except Exception as e:
        logger.error(f"Error computing batch similarity: {str(e)}")
        # 返回一个形状为(1, n)的零数组
        if isinstance(features_matrix, np.ndarray):
            return np.zeros((1, features_matrix.shape[0]))
        else:
            return np.array([[0.0]])


def merge_features(features_dict: Dict[str, np.ndarray], 
                  weights: Dict[str, float] = None) -> np.ndarray:
    """合并多种类型的特征
    
    Args:
        features_dict: 特征字典
        weights: 权重字典
        
    Returns:
        np.ndarray: 合并后的特征向量
    """
    if not features_dict:
        return np.array([])
    
    if weights is None:
        weights = {k: 1.0 for k in features_dict.keys()}
    
    try:
        weighted_features = []
        for feature_type, features in features_dict.items():
            weight = weights.get(feature_type, 1.0)
            if validate_features(features):
                weighted_features.append(features * weight)
        
        if weighted_features:
            return np.concatenate(weighted_features)
        else:
            return np.array([])
            
    except Exception as e:
        logger.error(f"Error merging features: {str(e)}")
        return np.array([])