#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样式构建器模块

该模块负责根据主题方案构建样式表。
"""

from typing import Dict, Any

from PyQt6.QtGui import QPalette, QColor

from utils.log_utils import LoggerMixin
from gui.themes.schemes import ColorScheme, FontScheme


class StyleBuilder(LoggerMixin):
    """样式构建器
    
    负责根据颜色方案和字体方案构建样式表。
    """
    
    def __init__(self):
        """初始化样式构建器"""
        super().__init__()
    
    def generate_stylesheet(self, color_scheme: ColorScheme, font_scheme: FontScheme) -> str:
        """生成样式表
        
        Args:
            color_scheme: 颜色方案
            font_scheme: 字体方案
            
        Returns:
            str: 样式表字符串
        """
        try:
            stylesheet = f"""
/* 全局样式 */
QWidget {{
    font-family: {font_scheme.family};
    font-size: {font_scheme.size_normal}px;
    color: {color_scheme.text_primary};
    background-color: {color_scheme.background};
}}

/* 主窗口 */
QMainWindow {{
    background-color: {color_scheme.background};
}}

/* 按钮 */
QPushButton {{
    background-color: {color_scheme.primary};
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: {font_scheme.weight_medium};
}}

QPushButton:hover {{
    background-color: {self._darken_color(color_scheme.primary, 0.1)};
}}

QPushButton:pressed {{
    background-color: {self._darken_color(color_scheme.primary, 0.2)};
}}

QPushButton:disabled {{
    background-color: {color_scheme.text_disabled};
    color: {color_scheme.text_secondary};
}}

/* 输入框 */
QLineEdit, QTextEdit, QPlainTextEdit {{
    background-color: {color_scheme.card};
    border: 1px solid {color_scheme.border};
    border-radius: 4px;
    padding: 6px;
    selection-background-color: {color_scheme.selected};
}}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
    border-color: {color_scheme.primary};
}}

/* 列表和树 */
QListWidget, QTreeWidget, QTableWidget {{
    background-color: {color_scheme.card};
    border: 1px solid {color_scheme.border};
    selection-background-color: {color_scheme.selected};
    alternate-background-color: {color_scheme.surface};
}}

QListWidget::item, QTreeWidget::item, QTableWidget::item {{
    padding: 4px;
    border-bottom: 1px solid {color_scheme.divider};
}}

QListWidget::item:hover, QTreeWidget::item:hover, QTableWidget::item:hover {{
    background-color: {color_scheme.hover};
}}

QListWidget::item:selected, QTreeWidget::item:selected, QTableWidget::item:selected {{
    background-color: {color_scheme.selected};
}}

/* 标签页 */
QTabWidget::pane {{
    border: 1px solid {color_scheme.border};
    background-color: {color_scheme.card};
}}

QTabBar::tab {{
    background-color: {color_scheme.surface};
    border: 1px solid {color_scheme.border};
    padding: 6px 12px;
    margin-right: 2px;
}}

QTabBar::tab:selected {{
    background-color: {color_scheme.card};
    border-bottom-color: {color_scheme.card};
}}

QTabBar::tab:hover {{
    background-color: {color_scheme.hover};
}}

/* 滚动条 */
QScrollBar:vertical {{
    background-color: {color_scheme.surface};
    width: 12px;
    border-radius: 6px;
}}

QScrollBar::handle:vertical {{
    background-color: {color_scheme.text_disabled};
    border-radius: 6px;
    min-height: 20px;
}}

QScrollBar::handle:vertical:hover {{
    background-color: {color_scheme.text_secondary};
}}

QScrollBar:horizontal {{
    background-color: {color_scheme.surface};
    height: 12px;
    border-radius: 6px;
}}

QScrollBar::handle:horizontal {{
    background-color: {color_scheme.text_disabled};
    border-radius: 6px;
    min-width: 20px;
}}

QScrollBar::handle:horizontal:hover {{
    background-color: {color_scheme.text_secondary};
}}

/* 菜单 */
QMenuBar {{
    background-color: {color_scheme.surface};
    border-bottom: 1px solid {color_scheme.border};
}}

QMenuBar::item {{
    padding: 6px 12px;
}}

QMenuBar::item:selected {{
    background-color: {color_scheme.hover};
}}

QMenu {{
    background-color: {color_scheme.card};
    border: 1px solid {color_scheme.border};
}}

QMenu::item {{
    padding: 6px 12px;
}}

QMenu::item:selected {{
    background-color: {color_scheme.selected};
}}

/* 工具栏 */
QToolBar {{
    background-color: {color_scheme.surface};
    border: 1px solid {color_scheme.border};
    spacing: 2px;
}}

QToolButton {{
    background-color: transparent;
    border: none;
    padding: 4px;
    border-radius: 4px;
}}

QToolButton:hover {{
    background-color: {color_scheme.hover};
}}

QToolButton:pressed {{
    background-color: {color_scheme.selected};
}}

/* 状态栏 */
QStatusBar {{
    background-color: {color_scheme.surface};
    border-top: 1px solid {color_scheme.border};
}}

/* 分组框 */
QGroupBox {{
    font-weight: {font_scheme.weight_medium};
    border: 1px solid {color_scheme.border};
    border-radius: 4px;
    margin-top: 8px;
    padding-top: 8px;
}}

QGroupBox::title {{
    subcontrol-origin: margin;
    left: 8px;
    padding: 0 4px 0 4px;
}}

/* 进度条 */
QProgressBar {{
    border: 1px solid {color_scheme.border};
    border-radius: 4px;
    text-align: center;
    background-color: {color_scheme.surface};
}}

QProgressBar::chunk {{
    background-color: {color_scheme.primary};
    border-radius: 3px;
}}

/* 滑块 */
QSlider::groove:horizontal {{
    border: 1px solid {color_scheme.border};
    height: 6px;
    background-color: {color_scheme.surface};
    border-radius: 3px;
}}

QSlider::handle:horizontal {{
    background-color: {color_scheme.primary};
    border: 1px solid {color_scheme.border};
    width: 16px;
    margin: -6px 0;
    border-radius: 8px;
}}

/* 复选框和单选框 */
QCheckBox, QRadioButton {{
    spacing: 6px;
}}

QCheckBox::indicator, QRadioButton::indicator {{
    width: 16px;
    height: 16px;
}}

QCheckBox::indicator:unchecked {{
    border: 1px solid {color_scheme.border};
    background-color: {color_scheme.card};
    border-radius: 2px;
}}

QCheckBox::indicator:checked {{
    border: 1px solid {color_scheme.primary};
    background-color: {color_scheme.primary};
    border-radius: 2px;
}}

QRadioButton::indicator:unchecked {{
    border: 1px solid {color_scheme.border};
    background-color: {color_scheme.card};
    border-radius: 8px;
}}

QRadioButton::indicator:checked {{
    border: 1px solid {color_scheme.primary};
    background-color: {color_scheme.primary};
    border-radius: 8px;
}}

/* 组合框 */
QComboBox {{
    background-color: {color_scheme.card};
    border: 1px solid {color_scheme.border};
    border-radius: 4px;
    padding: 6px;
    min-width: 100px;
}}

QComboBox:hover {{
    border-color: {color_scheme.primary};
}}

QComboBox::drop-down {{
    border: none;
    width: 20px;
}}

QComboBox::down-arrow {{
    width: 12px;
    height: 12px;
}}

QComboBox QAbstractItemView {{
    background-color: {color_scheme.card};
    border: 1px solid {color_scheme.border};
    selection-background-color: {color_scheme.selected};
}}

/* 分隔线 */
QFrame[frameShape="4"], QFrame[frameShape="5"] {{
    color: {color_scheme.divider};
}}
"""
            return stylesheet
            
        except Exception as e:
            self.logger.error(f"生成样式表失败: {e}")
            return ""
    
    def create_palette(self, color_scheme: ColorScheme) -> QPalette:
        """创建调色板
        
        Args:
            color_scheme: 颜色方案
            
        Returns:
            QPalette: 调色板
        """
        palette = QPalette()
        
        # 设置颜色角色
        palette.setColor(QPalette.ColorRole.Window, QColor(color_scheme.background))
        palette.setColor(QPalette.ColorRole.WindowText, QColor(color_scheme.text_primary))
        palette.setColor(QPalette.ColorRole.Base, QColor(color_scheme.card))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(color_scheme.surface))
        palette.setColor(QPalette.ColorRole.Text, QColor(color_scheme.text_primary))
        palette.setColor(QPalette.ColorRole.Button, QColor(color_scheme.surface))
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(color_scheme.text_primary))
        palette.setColor(QPalette.ColorRole.Highlight, QColor(color_scheme.selected))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor(color_scheme.text_primary))
        
        return palette
    
    def _darken_color(self, color_hex: str, factor: float) -> str:
        """加深颜色
        
        Args:
            color_hex: 十六进制颜色值
            factor: 加深因子 (0-1)
            
        Returns:
            str: 加深后的颜色值
        """
        try:
            # 移除 # 符号
            color_hex = color_hex.lstrip('#')
            
            # 转换为RGB
            r = int(color_hex[0:2], 16)
            g = int(color_hex[2:4], 16)
            b = int(color_hex[4:6], 16)
            
            # 加深颜色
            r = max(0, int(r * (1 - factor)))
            g = max(0, int(g * (1 - factor)))
            b = max(0, int(b * (1 - factor)))
            
            # 转换回十六进制
            return f"#{r:02x}{g:02x}{b:02x}"
            
        except Exception:
            return color_hex
    
    def lighten_color(self, color_hex: str, factor: float) -> str:
        """减淡颜色
        
        Args:
            color_hex: 十六进制颜色值
            factor: 减淡因子 (0-1)
            
        Returns:
            str: 减淡后的颜色值
        """
        try:
            # 移除 # 符号
            color_hex = color_hex.lstrip('#')
            
            # 转换为RGB
            r = int(color_hex[0:2], 16)
            g = int(color_hex[2:4], 16)
            b = int(color_hex[4:6], 16)
            
            # 减淡颜色
            r = min(255, int(r + (255 - r) * factor))
            g = min(255, int(g + (255 - g) * factor))
            b = min(255, int(b + (255 - b) * factor))
            
            # 转换回十六进制
            return f"#{r:02x}{g:02x}{b:02x}"
            
        except Exception:
            return color_hex