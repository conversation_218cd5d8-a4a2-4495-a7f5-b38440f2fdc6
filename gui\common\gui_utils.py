#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI工具类模块

该模块提供GUI相关的核心工具类。
"""

from typing import Optional, Dict, Any
from PyQt6.QtWidgets import QWidget, QApplication
from PyQt6.QtCore import QSettings, QSize, QPoint
from PyQt6.QtGui import QScreen

from utils.log_utils import get_logger
from .geometry import WindowGeometry, ScreenInfo

logger = get_logger(__name__)


class GUIUtils:
    """GUI工具类"""
    
    @staticmethod
    def center_window(widget: QWidget, parent: Optional[QWidget] = None) -> None:
        """
        居中窗口
        
        Args:
            widget: 要居中的窗口
            parent: 父窗口，如果为None则相对于屏幕居中
        """
        try:
            if parent:
                # 相对于父窗口居中
                parent_geometry = parent.geometry()
                widget_size = widget.size()
                
                x = parent_geometry.x() + (parent_geometry.width() - widget_size.width()) // 2
                y = parent_geometry.y() + (parent_geometry.height() - widget_size.height()) // 2
                
                widget.move(x, y)
            else:
                # 相对于屏幕居中
                screen = QApplication.primaryScreen()
                if screen:
                    screen_geometry = screen.availableGeometry()
                    widget_size = widget.size()
                    
                    x = (screen_geometry.width() - widget_size.width()) // 2
                    y = (screen_geometry.height() - widget_size.height()) // 2
                    
                    widget.move(x, y)
        except Exception as e:
            logger.error(f"居中窗口失败: {e}")
    
    @staticmethod
    def save_window_geometry(widget: QWidget, key: str) -> None:
        """
        保存窗口几何信息
        
        Args:
            widget: 窗口对象
            key: 保存的键名
        """
        try:
            settings = QSettings()
            geometry = WindowGeometry.from_widget(widget)
            settings.setValue(f"geometry/{key}", geometry.to_dict())
            logger.debug(f"保存窗口几何信息: {key}")
        except Exception as e:
            logger.error(f"保存窗口几何信息失败: {e}")
    
    @staticmethod
    def restore_window_geometry(widget: QWidget, key: str, 
                              default_size: Optional[QSize] = None) -> bool:
        """
        恢复窗口几何信息
        
        Args:
            widget: 窗口对象
            key: 保存的键名
            default_size: 默认尺寸
            
        Returns:
            是否成功恢复
        """
        try:
            settings = QSettings()
            geometry_data = settings.value(f"geometry/{key}")
            
            if geometry_data:
                geometry = WindowGeometry.from_dict(geometry_data)
                geometry.apply_to_widget(widget)
                logger.debug(f"恢复窗口几何信息: {key}")
                return True
            else:
                # 使用默认几何信息
                GUIUtils._set_default_geometry(widget, default_size)
                return False
        except Exception as e:
            logger.error(f"恢复窗口几何信息失败: {e}")
            GUIUtils._set_default_geometry(widget, default_size)
            return False
    
    @staticmethod
    def _set_default_geometry(widget: QWidget, default_size: Optional[QSize] = None) -> None:
        """
        设置默认几何信息
        
        Args:
            widget: 窗口对象
            default_size: 默认尺寸
        """
        try:
            if default_size:
                widget.resize(default_size)
            
            # 居中窗口
            GUIUtils.center_window(widget)
            logger.debug("设置默认窗口几何信息")
        except Exception as e:
            logger.error(f"设置默认几何信息失败: {e}")
    
    @staticmethod
    def get_system_theme() -> str:
        """
        获取系统主题
        
        Returns:
            系统主题名称 ('light' 或 'dark')
        """
        try:
            # 尝试检测系统主题
            app = QApplication.instance()
            if app:
                palette = app.palette()
                # 通过背景色判断主题
                bg_color = palette.color(palette.ColorRole.Window)
                if bg_color.lightness() < 128:
                    return "dark"
                else:
                    return "light"
            return "light"
        except Exception as e:
            logger.error(f"获取系统主题失败: {e}")
            return "light"
    
    @staticmethod
    def get_available_fonts() -> list:
        """
        获取可用字体列表
        
        Returns:
            字体名称列表
        """
        try:
            from PyQt6.QtGui import QFontDatabase
            font_db = QFontDatabase()
            return font_db.families()
        except Exception as e:
            logger.error(f"获取可用字体失败: {e}")
            return ["Microsoft YaHei", "SimHei", "Arial"]
    
    @staticmethod
    def validate_image_file(file_path: str) -> bool:
        """
        验证图片文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为有效的图片文件
        """
        try:
            import os
            from PyQt6.QtGui import QPixmap
            
            if not os.path.exists(file_path):
                return False
            
            # 检查文件扩展名
            valid_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp'}
            _, ext = os.path.splitext(file_path.lower())
            if ext not in valid_extensions:
                return False
            
            # 尝试加载图片
            pixmap = QPixmap(file_path)
            return not pixmap.isNull()
        except Exception as e:
            logger.error(f"验证图片文件失败: {e}")
            return False
    
    @staticmethod
    def cleanup() -> None:
        """清理资源"""
        try:
            # 这里可以添加清理逻辑
            logger.debug("GUI工具类清理完成")
        except Exception as e:
            logger.error(f"GUI工具类清理失败: {e}")