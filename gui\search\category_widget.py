#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
类别搜索组件

该模块提供基于类别的搜索功能界面。
"""

from typing import Dict, Any, List, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QComboBox, QCheckBox,
    QGroupBox, QSpinBox, QListWidget, QListWidgetItem, QFormLayout
)
from PyQt6.QtCore import pyqtSignal, Qt

from utils.log_utils import LoggerMixin
from gui.helpers.message_helper import MessageHelper
from gui.widget_factory import WidgetFactory, ButtonStyle, ButtonSize


class CategorySearchWidget(QWidget, LoggerMixin):
    """类别搜索组件"""
    
    # 信号
    searchRequested = pyqtSignal(str, dict)  # 搜索请求
    categoryRefreshRequested = pyqtSignal()  # 类别刷新请求
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.widget_factory = WidgetFactory()
        self.categories = []  # 可用类别列表
        
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 类别选择
        self._setup_category_selection(layout)
        
        # 搜索选项
        self._setup_search_options(layout)
        
        # 搜索参数
        self._setup_search_params(layout)
        
        # 按钮组
        self._setup_buttons(layout)
        
        layout.addStretch()
    
    def _setup_category_selection(self, layout):
        """设置类别选择"""
        category_group = self.widget_factory.create_group_box("选择类别")
        category_layout = QVBoxLayout(category_group)
        
        # 类别选择方式
        selection_layout = QHBoxLayout()
        
        # 单选模式
        self.single_mode_rb = self.widget_factory.create_radio_button("单选模式", checked=True)
        selection_layout.addWidget(self.single_mode_rb)
        
        # 多选模式
        self.multi_mode_rb = self.widget_factory.create_radio_button("多选模式")
        selection_layout.addWidget(self.multi_mode_rb)
        
        selection_layout.addStretch()
        category_layout.addLayout(selection_layout)
        
        # 单选下拉框
        self.category_combo = QComboBox()
        self.category_combo.setMinimumHeight(30)
        self.category_combo.addItem("请选择类别...", "")
        category_layout.addWidget(self.category_combo)
        
        # 多选列表
        self.category_list = QListWidget()
        self.category_list.setMaximumHeight(150)
        self.category_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        self.category_list.hide()  # 默认隐藏
        category_layout.addWidget(self.category_list)
        
        # 刷新按钮
        refresh_layout = QHBoxLayout()
        refresh_layout.addStretch()
        self.refresh_btn = self.widget_factory.create_button(
            "刷新类别", "refresh", None, ButtonStyle.SECONDARY, ButtonSize.MEDIUM,
            click_handler=self.refresh_categories
        )
        refresh_layout.addWidget(self.refresh_btn)
        category_layout.addLayout(refresh_layout)
        
        layout.addWidget(category_group)
    
    def _setup_search_options(self, layout):
        """设置搜索选项"""
        options_group = self.widget_factory.create_group_box("搜索选项")
        options_layout = QVBoxLayout(options_group)
        
        # 搜索选项
        options_row_layout = QHBoxLayout()
        
        self.exact_match_cb = self.widget_factory.create_checkbox("精确匹配", checked=True)
        self.include_subcategories_cb = self.widget_factory.create_checkbox("包含子类别")
        
        options_row_layout.addWidget(self.exact_match_cb)
        options_row_layout.addWidget(self.include_subcategories_cb)
        options_row_layout.addStretch()
        
        options_layout.addLayout(options_row_layout)
        
        layout.addWidget(options_group)
    
    def _setup_search_params(self, layout):
        """设置搜索参数"""
        params_group = self.widget_factory.create_group_box("搜索参数")
        params_layout = QFormLayout(params_group)
        
        # 最大结果数
        try:
            from config.config_manager import ConfigManager
            config_manager = ConfigManager()
            default_max_results = config_manager.config.search.top_k
        except Exception:
            default_max_results = 50
            
        self.max_results_spin = self.widget_factory.create_spin_box(
            minimum=1, maximum=1000, value=default_max_results
        )
        params_layout.addRow("最大结果数:", self.max_results_spin)
        
        layout.addWidget(params_group)
    
    def _setup_buttons(self, layout):
        """设置按钮"""
        button_layout = QHBoxLayout()
        
        # 清除选择按钮
        self.clear_btn = self.widget_factory.create_button(
            "清除选择", "clear", None, ButtonStyle.WARNING, ButtonSize.MEDIUM,
            click_handler=self.clear_selection
        )
        button_layout.addWidget(self.clear_btn)
        
        button_layout.addStretch()
        
        # 搜索按钮
        self.search_btn = self.widget_factory.create_button(
            "开始搜索", "search", None, ButtonStyle.SUCCESS, ButtonSize.LARGE,
            click_handler=self.start_search
        )
        button_layout.addWidget(self.search_btn)
        
        layout.addLayout(button_layout)
    
    def connect_signals(self):
        """连接信号"""
        # 选择模式切换
        self.single_mode_rb.toggled.connect(self._on_selection_mode_changed)
        self.multi_mode_rb.toggled.connect(self._on_selection_mode_changed)
        
        # 类别选择变化
        self.category_combo.currentTextChanged.connect(self._on_category_changed)
        self.category_list.itemSelectionChanged.connect(self._on_category_changed)
    
    def _on_selection_mode_changed(self):
        """选择模式变化"""
        if self.single_mode_rb.isChecked():
            self.category_combo.show()
            self.category_list.hide()
        else:
            self.category_combo.hide()
            self.category_list.show()
        
        self._update_search_button_state()
    
    def _on_category_changed(self):
        """类别选择变化"""
        self._update_search_button_state()
    
    def _update_search_button_state(self):
        """更新搜索按钮状态"""
        has_selection = False
        
        if self.single_mode_rb.isChecked():
            # 单选模式
            current_data = self.category_combo.currentData()
            has_selection = current_data and current_data != ""
        else:
            # 多选模式
            has_selection = len(self.category_list.selectedItems()) > 0
        
        self.search_btn.setEnabled(has_selection)
    
    def refresh_categories(self):
        """刷新类别列表"""
        try:
            self.categoryRefreshRequested.emit()
            
        except Exception as e:
            self.logger.error(f"刷新类别失败: {e}")
            MessageHelper.show_error(self, "错误", f"刷新类别失败: {e}")
    
    def update_categories(self, categories: List[str]):
        """更新类别列表
        
        Args:
            categories: 类别列表
        """
        try:
            self.categories = categories
            
            # 更新下拉框
            self.category_combo.clear()
            self.category_combo.addItem("请选择类别...", "")
            for category in categories:
                self.category_combo.addItem(category, category)
            
            # 更新列表
            self.category_list.clear()
            for category in categories:
                item = QListWidgetItem(category)
                item.setData(Qt.ItemDataRole.UserRole, category)
                self.category_list.addItem(item)
            
            self.logger.info(f"类别列表已更新，共 {len(categories)} 个类别")
            
        except Exception as e:
            self.logger.error(f"更新类别列表失败: {e}")
            MessageHelper.show_error(self, "错误", f"更新类别列表失败: {e}")
    
    def clear_selection(self):
        """清除选择"""
        try:
            # 清除下拉框选择
            self.category_combo.setCurrentIndex(0)
            
            # 清除列表选择
            self.category_list.clearSelection()
            
            self._update_search_button_state()
            
        except Exception as e:
            self.logger.error(f"清除选择失败: {e}")
    
    def start_search(self):
        """开始搜索"""
        try:
            selected_categories = self.get_selected_categories()
            if not selected_categories:
                MessageHelper.show_warning(self, "警告", "请选择至少一个类别")
                return
            
            # 构建搜索参数
            params = self.get_search_params()
            
            # 使用第一个类别作为查询字符串（对于多选，在参数中处理）
            query = selected_categories[0] if selected_categories else ""
            
            # 发送搜索请求
            self.searchRequested.emit(query, params)
            
        except Exception as e:
            self.logger.error(f"开始类别搜索失败: {e}")
            MessageHelper.show_error(self, "错误", f"开始搜索失败: {e}")
    
    def get_selected_categories(self) -> List[str]:
        """获取选中的类别"""
        selected = []
        
        if self.single_mode_rb.isChecked():
            # 单选模式
            current_data = self.category_combo.currentData()
            if current_data and current_data != "":
                selected.append(current_data)
        else:
            # 多选模式
            for item in self.category_list.selectedItems():
                category = item.data(Qt.ItemDataRole.UserRole)
                if category:
                    selected.append(category)
        
        return selected
    
    def get_search_params(self) -> Dict[str, Any]:
        """获取搜索参数"""
        selected_categories = self.get_selected_categories()
        
        return {
            "categories": selected_categories,
            "exact_match": self.exact_match_cb.isChecked(),
            "include_subcategories": self.include_subcategories_cb.isChecked(),
            "max_results": self.max_results_spin.value(),
            "multi_category": self.multi_mode_rb.isChecked()
        }
    
    def set_categories(self, categories: List[str]):
        """设置类别列表（外部调用）"""
        self.update_categories(categories)
    
    def get_categories(self) -> List[str]:
        """获取当前类别列表"""
        return self.categories.copy()
        
    def set_searching(self, is_searching: bool):
        """设置搜索状态
        
        Args:
            is_searching: 是否正在搜索
        """
        try:
            # 更新搜索按钮状态
            if hasattr(self, 'search_btn'):
                self.search_btn.setEnabled(not is_searching)
                self.search_btn.setText("停止搜索" if is_searching else "搜索")
            
            # 更新类别选择控件状态
            if hasattr(self, 'category_combo'):
                self.category_combo.setEnabled(not is_searching)
            if hasattr(self, 'category_list'):
                self.category_list.setEnabled(not is_searching)
            
            # 记录日志
            self.logger.debug(f"类别搜索组件状态已更新: {'搜索中' if is_searching else '空闲'}")
            
        except Exception as e:
            self.logger.error(f"设置搜索状态失败: {e}")