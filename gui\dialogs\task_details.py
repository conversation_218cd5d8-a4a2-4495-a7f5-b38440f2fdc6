"""任务详情组件

该模块定义了任务进度对话框中的任务详情显示组件。
"""

from typing import Optional, Dict, Any
from datetime import datetime

from PyQt6.QtWidgets import QTextEdit, QWidget, QVBoxLayout, QLabel
from PyQt6.QtCore import Qt

from utils.task_manager import TaskInfo
from .task_config import TaskStatusInfo


class TaskDetailsWidget(QWidget):
    """任务详情组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.status_map = TaskStatusInfo.get_status_map()
        self._setup_ui()
    
    def _setup_ui(self) -> None:
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 标题
        self.title_label = QLabel("任务详情")
        self.title_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addWidget(self.title_label)
        
        # 详情文本
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(200)
        self.details_text.setText("选择一个任务以查看详情")
        layout.addWidget(self.details_text)
    
    def update_details(self, task_info: TaskInfo) -> None:
        """更新任务详情
        
        Args:
            task_info: 任务信息
        """
        if not task_info:
            self.clear_details()
            return
        
        details = self._format_task_details(task_info)
        self.details_text.setText(details)
    
    def clear_details(self) -> None:
        """清空详情"""
        self.details_text.setText("选择一个任务以查看详情")
    
    def _format_task_details(self, task_info: TaskInfo) -> str:
        """格式化任务详情
        
        Args:
            task_info: 任务信息
            
        Returns:
            str: 格式化的详情文本
        """
        details = []
        
        # 基本信息
        details.append(f"任务ID: {task_info.task_id}")
        details.append(f"名称: {task_info.name}")
        
        # 状态信息
        status_info = self.status_map.get(task_info.status)
        status_text = status_info.display_text if status_info else task_info.status
        details.append(f"状态: {status_text}")
        
        # 进度信息
        progress_percent = int(task_info.progress * 100)
        details.append(f"进度: {progress_percent}%")
        
        # 时间信息
        details.append(f"创建时间: {self._format_datetime(task_info.created_at)}")
        
        if task_info.started_at:
            details.append(f"开始时间: {self._format_datetime(task_info.started_at)}")
        
        if task_info.completed_at:
            details.append(f"完成时间: {self._format_datetime(task_info.completed_at)}")
            
            # 计算耗时
            if task_info.started_at:
                duration = task_info.completed_at - task_info.started_at
                details.append(f"耗时: {duration.total_seconds():.2f} 秒")
        
        # 错误信息
        if task_info.error:
            details.append("")
            details.append("错误信息:")
            details.append(str(task_info.error))
        
        # 结果信息
        if task_info.result is not None and task_info.status == "completed":
            details.append("")
            details.append("结果:")
            result_str = self._format_result(task_info.result)
            details.append(result_str)
        
        # 元数据
        if task_info.metadata:
            details.append("")
            details.append("元数据:")
            for key, value in task_info.metadata.items():
                details.append(f"{key}: {value}")
        
        return "\n".join(details)
    
    def _format_datetime(self, dt: datetime) -> str:
        """格式化日期时间
        
        Args:
            dt: 日期时间对象
            
        Returns:
            str: 格式化的日期时间字符串
        """
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    
    def _format_result(self, result: Any) -> str:
        """格式化结果
        
        Args:
            result: 结果对象
            
        Returns:
            str: 格式化的结果字符串
        """
        if isinstance(result, (str, int, float, bool)):
            return str(result)
        elif isinstance(result, dict):
            return self._format_dict(result)
        elif isinstance(result, (list, tuple)):
            return self._format_list(result)
        else:
            return str(type(result).__name__)
    
    def _format_dict(self, data: Dict[str, Any], indent: int = 0) -> str:
        """格式化字典
        
        Args:
            data: 字典数据
            indent: 缩进级别
            
        Returns:
            str: 格式化的字典字符串
        """
        lines = []
        prefix = "  " * indent
        
        for key, value in data.items():
            if isinstance(value, dict):
                lines.append(f"{prefix}{key}:")
                lines.append(self._format_dict(value, indent + 1))
            elif isinstance(value, (list, tuple)):
                lines.append(f"{prefix}{key}: {self._format_list(value)}")
            else:
                lines.append(f"{prefix}{key}: {value}")
        
        return "\n".join(lines)
    
    def _format_list(self, data: list) -> str:
        """格式化列表
        
        Args:
            data: 列表数据
            
        Returns:
            str: 格式化的列表字符串
        """
        if len(data) <= 3:
            return str(data)
        else:
            return f"[{len(data)} items: {data[0]}, {data[1]}, ...]"