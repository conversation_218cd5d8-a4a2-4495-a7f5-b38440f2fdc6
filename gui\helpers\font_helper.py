#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体辅助模块

该模块提供字体创建和管理的辅助功能。
"""

from typing import List

from PyQt6.QtGui import QFont, QFontMetrics, QFontDatabase
from PyQt6.QtCore import QSize, Qt

from utils.log_utils import LoggerMixin


class FontHelper(LoggerMixin):
    """字体辅助类"""
    
    @staticmethod
    def get_default_font(size: int = 9) -> QFont:
        """获取默认字体
        
        Args:
            size: 字体大小
            
        Returns:
            QFont: 字体对象
        """
        font = QFont()
        font.setPointSize(size)
        return font
    
    @staticmethod
    def get_bold_font(size: int = 9) -> QFont:
        """获取粗体字体
        
        Args:
            size: 字体大小
            
        Returns:
            QFont: 字体对象
        """
        font = QFont()
        font.setPointSize(size)
        font.setBold(True)
        return font
    
    @staticmethod
    def get_title_font(size: int = 12) -> QFont:
        """获取标题字体
        
        Args:
            size: 字体大小
            
        Returns:
            QFont: 字体对象
        """
        font = QFont()
        font.setPointSize(size)
        font.setBold(True)
        return font
    
    @staticmethod
    def get_monospace_font(size: int = 9) -> QFont:
        """获取等宽字体
        
        Args:
            size: 字体大小
            
        Returns:
            QFont: 字体对象
        """
        font = QFont("Consolas, Monaco, 'Courier New', monospace")
        font.setPointSize(size)
        return font
    
    @staticmethod
    def calculate_text_size(text: str, font: QFont) -> QSize:
        """计算文本尺寸
        
        Args:
            text: 文本内容
            font: 字体
            
        Returns:
            QSize: 文本尺寸
        """
        metrics = QFontMetrics(font)
        return metrics.size(Qt.TextFlag.TextSingleLine, text)
    
    def get_available_fonts(self) -> List[str]:
        """获取可用字体列表
        
        Returns:
            List[str]: 字体名称列表
        """
        try:
            font_db = QFontDatabase()
            return font_db.families()
        except Exception as e:
            self.logger.error(f"获取可用字体失败: {e}")
            return []