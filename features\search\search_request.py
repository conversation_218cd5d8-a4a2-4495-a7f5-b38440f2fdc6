#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索请求模块

定义搜索请求相关的数据结构。
"""

import numpy as np
from typing import Optional, List, Dict, Any, Union
from dataclasses import dataclass, field
from enum import Enum


class SimilarityMetric(Enum):
    """相似度度量枚举"""
    COSINE = "cosine"
    EUCLIDEAN = "euclidean"
    DOT_PRODUCT = "dot_product"
    MANHATTAN = "manhattan"


@dataclass
class SearchRequest:
    """搜索请求"""
    query_image_path: Optional[str] = None
    query_features: Optional[np.ndarray] = None
    query_id: Optional[Union[int, str]] = None
    top_k: int = 10
    similarity_metric: SimilarityMetric = SimilarityMetric.COSINE
    filter_category: Optional[str] = None
    filter_tags: Optional[List[str]] = None
    use_faiss: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    feature_extraction_params: Optional[Dict[str, Any]] = None
    similarity_threshold: float = 0.0
    include_metadata: bool = True
    feature_types: List[str] = field(default_factory=lambda: ['deep'])
    weights: Dict[str, float] = field(default_factory=dict)
    filters: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """后处理验证"""
        # 验证查询参数
        query_params = [self.query_image_path, self.query_features, self.query_id]
        valid_params = [p for p in query_params if p is not None]
        
        if len(valid_params) == 0:
            raise ValueError("Must provide at least one query parameter")
        
        if len(valid_params) > 1:
            raise ValueError("Only one query parameter should be provided")
        
        # 验证top_k
        if self.top_k <= 0:
            raise ValueError("top_k must be positive")
        
        # 验证相似度阈值
        if not 0.0 <= self.similarity_threshold <= 1.0:
            raise ValueError("similarity_threshold must be between 0.0 and 1.0")
        
        # 设置默认权重
        if not self.weights:
            for feature_type in self.feature_types:
                self.weights[feature_type] = 1.0
    
    def validate(self) -> bool:
        """验证请求参数
        
        Returns:
            bool: 验证是否通过
        """
        try:
            self.__post_init__()
            return True
        except ValueError:
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        result = {
            'query_image_path': self.query_image_path,
            'query_id': self.query_id,
            'top_k': self.top_k,
            'similarity_metric': self.similarity_metric.value,
            'filter_category': self.filter_category,
            'filter_tags': self.filter_tags,
            'use_faiss': self.use_faiss,
            'metadata': self.metadata,
            'feature_extraction_params': self.feature_extraction_params,
            'similarity_threshold': self.similarity_threshold,
            'include_metadata': self.include_metadata,
            'feature_types': self.feature_types,
            'weights': self.weights,
            'filters': self.filters
        }
        
        # 处理numpy数组
        if self.query_features is not None:
            result['query_features'] = self.query_features.tolist()
        else:
            result['query_features'] = None
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchRequest':
        """从字典创建实例
        
        Args:
            data: 字典数据
            
        Returns:
            SearchRequest: 搜索请求实例
        """
        # 处理相似度度量
        if 'similarity_metric' in data and isinstance(data['similarity_metric'], str):
            data['similarity_metric'] = SimilarityMetric(data['similarity_metric'])
        
        # 处理numpy数组
        if 'query_features' in data and data['query_features'] is not None:
            data['query_features'] = np.array(data['query_features'])
        
        return cls(**data)