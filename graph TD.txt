graph TD
    A[用户界面层] --> B[搜索参数设置]
    
    B --> C[相似度搜索参数]
    B --> D[高级搜索参数]
    B --> E[文本搜索参数]
    
    C --> F[FeatureWeightsWidget<br/>特征权重设置]
    C --> G[FeatureParamsWidget<br/>传统特征参数]
    C --> H[SimilaritySearchWidget<br/>相似度阈值等]
    
    D --> I[AdvancedSearchWidget<br/>高级过滤参数]
    
    F --> J[get_weights方法<br/>归一化权重]
    G --> K[get_params方法<br/>特征提取参数]
    H --> L[get_search_params方法<br/>搜索参数]
    I --> M[get_search_params方法<br/>过滤参数]
    
    J --> N[特征权重字典<br/>deep_learning: 0.4<br/>color: 0.3<br/>texture: 0.2<br/>shape: 0.1]
    K --> O[传统特征参数<br/>hist_bins: 32<br/>lbp_radius: 3<br/>fourier_descriptors: 64]
    L --> P[搜索参数<br/>similarity_threshold: 0.7<br/>max_results: 50]
    M --> Q[过滤参数<br/>file_filters<br/>date_filter<br/>content_filters]
    
    N --> R[SearchPanel.get_search_config]
    O --> R
    P --> R
    Q --> R
    
    R --> S[SearchConfig对象<br/>mode: SearchMode<br/>feature_weights: Dict<br/>feature_extraction_params: Dict<br/>max_results: int]
    
    S --> T[SearchHandler.start_search]
    T --> U[SearchHandler._config_to_query]
    
    U --> V[SearchQuery对象创建]
    V --> W[参数映射与转换]
    
    W --> X[query_type: SearchType]
    W --> Y[feature_weights: Dict]
    W --> Z[feature_extraction_params: Dict]
    W --> AA[search_strategy: str]
    W --> BB[similarity_threshold: float]
    W --> CC[top_k: int]
    
    X --> DD[SearchEngine.search]
    Y --> DD
    Z --> DD
    AA --> DD
    BB --> DD
    CC --> DD
    
    DD --> EE[SearchMethods分发]
    EE --> FF[search_by_image_similarity]
    
    FF --> GG[FeatureManager特征管理]
    GG --> HH[特征提取]
    GG --> II[搜索策略选择]
    
    II --> JJ{搜索策略类型}
    
    JJ -->|weighted| KK[WeightedSearchStrategy<br/>加权搜索策略]
    JJ -->|adaptive| LL[AdaptiveSearchStrategy<br/>自适应搜索策略]
    JJ -->|query_expansion| MM[QueryExpansionStrategy<br/>查询扩展策略]
    JJ -->|hybrid| NN[HybridSearchStrategy<br/>混合搜索策略]
    JJ -->|single_feature| OO[SingleFeatureSearchStrategy<br/>单特征搜索策略]
    
    KK --> PP[加权融合计算<br/>final_score = Σ(weight_i × similarity_i)]
    LL --> QQ[自适应权重分析<br/>根据特征方差调整权重]
    MM --> RR[查询扩展<br/>1.初步搜索<br/>2.扩展查询特征<br/>3.二次搜索]
    NN --> SS[多策略融合<br/>综合多种策略结果]
    OO --> TT[单特征计算<br/>仅使用指定特征类型]
    
    PP --> UU[相似度计算]
    QQ --> UU
    RR --> UU
    SS --> UU
    TT --> UU
    
    UU --> VV[FAISS索引搜索]
    UU --> WW[NumPy相似度计算]
    
    VV --> XX[搜索结果]
    WW --> XX
    
    XX --> YY[ResultProcessor结果处理]
    YY --> ZZ[过滤器应用]
    YY --> AAA[结果排序]
    YY --> BBB[分页处理]
    
    ZZ --> CCC[SearchResult对象]
    AAA --> CCC
    BBB --> CCC
    
    CCC --> DDD[返回GUI显示]
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style G fill:#f3e5f5
    style II fill:#e8f5e8
    style KK fill:#fff3e0
    style LL fill:#fff3e0
    style MM fill:#fff3e0
    style NN fill:#fff3e0
    style OO fill:#fff3e0
    style UU fill:#fce4ec