#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户偏好管理模块

该模块提供用户偏好设置的管理功能，包括偏好的增删改查操作。
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any, Union

from .base_repository import BaseRepository
from ..database_manager import DatabaseManager
from ..exceptions.database_exceptions import RepositoryError
from ..utils.database_utils import serialize_json, deserialize_json


class UserPreferencesRepository(BaseRepository):
    """用户偏好仓库类"""
    
    @property
    def table_name(self) -> str:
        """表名"""
        return "user_preferences"
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化用户偏好仓库
        
        Args:
            db_manager: 数据库管理器
        """
        super().__init__(db_manager)
    
    def _row_to_dict(self, row) -> Dict[str, Any]:
        """将数据库行转换为字典
        
        Args:
            row: 数据库行
            
        Returns:
            Dict[str, Any]: 用户偏好字典
        """
        try:
            # 处理时间字段
            created_at = None
            if row['created_at']:
                try:
                    created_at = datetime.fromisoformat(row['created_at'].replace('Z', '+00:00'))
                except ValueError:
                    created_at = datetime.strptime(row['created_at'], '%Y-%m-%d %H:%M:%S')
            
            updated_at = None
            if row.get('updated_at'):
                try:
                    updated_at = datetime.fromisoformat(row['updated_at'].replace('Z', '+00:00'))
                except ValueError:
                    updated_at = datetime.strptime(row['updated_at'], '%Y-%m-%d %H:%M:%S')
            
            # 处理值字段
            value = row['value']
            if row['value_type'] == 'json' and value:
                value = deserialize_json(value)
            elif row['value_type'] == 'int' and value:
                value = int(value)
            elif row['value_type'] == 'float' and value:
                value = float(value)
            elif row['value_type'] == 'bool' and value:
                value = value.lower() in ('true', '1', 'yes', 'on')
            
            return {
                'id': row['id'],
                'key': row['key'],
                'value': value,
                'value_type': row['value_type'],
                'description': row.get('description'),
                'category': row.get('category'),
                'created_at': created_at,
                'updated_at': updated_at
            }
            
        except Exception as e:
            self.logger.error(f"转换数据库行为字典失败: {e}")
            raise RepositoryError(f"转换数据库行为字典失败: {e}")
    
    def _serialize_value(self, value: Any) -> tuple:
        """序列化值
        
        Args:
            value: 要序列化的值
            
        Returns:
            tuple: (序列化后的值, 值类型)
        """
        if value is None:
            return None, 'string'
        elif isinstance(value, bool):
            return str(value).lower(), 'bool'
        elif isinstance(value, int):
            return str(value), 'int'
        elif isinstance(value, float):
            return str(value), 'float'
        elif isinstance(value, (dict, list)):
            return serialize_json(value), 'json'
        else:
            return str(value), 'string'
    
    def set_preference(self, key: str, value: Any, 
                      description: str = None, category: str = None) -> bool:
        """设置用户偏好
        
        Args:
            key: 偏好键
            value: 偏好值
            description: 描述
            category: 分类
            
        Returns:
            bool: 设置是否成功
        """
        try:
            serialized_value, value_type = self._serialize_value(value)
            
            # 检查是否已存在
            existing = self.get_preference(key)
            
            if existing:
                # 更新现有偏好
                sql = """
                    UPDATE user_preferences 
                    SET value = ?, value_type = ?, description = ?, 
                        category = ?, updated_at = ?
                    WHERE key = ?
                """
                params = (
                    serialized_value,
                    value_type,
                    description,
                    category,
                    datetime.now().isoformat(),
                    key
                )
                affected_rows = self.db_manager.execute_update(sql, params)
                success = affected_rows > 0
            else:
                # 创建新偏好
                sql = """
                    INSERT INTO user_preferences (
                        key, value, value_type, description, category, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """
                params = (
                    key,
                    serialized_value,
                    value_type,
                    description,
                    category,
                    datetime.now().isoformat()
                )
                preference_id = self.db_manager.execute_insert(sql, params)
                success = preference_id is not None
            
            if success:
                self.logger.debug(f"用户偏好设置成功: {key} = {value}")
            else:
                self.logger.error(f"用户偏好设置失败: {key}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"设置用户偏好失败: {e}")
            raise RepositoryError(f"设置用户偏好失败: {e}")
    
    def get_preference(self, key: str, default: Any = None) -> Any:
        """获取用户偏好
        
        Args:
            key: 偏好键
            default: 默认值
            
        Returns:
            Any: 偏好值，如果不存在则返回默认值
        """
        try:
            sql = "SELECT * FROM user_preferences WHERE key = ?"
            rows = self.db_manager.execute_query(sql, (key,))
            
            if rows:
                preference = self._row_to_dict(rows[0])
                return preference['value']
            
            return default
            
        except Exception as e:
            self.logger.error(f"获取用户偏好失败: {e}")
            return default
    
    def get_preference_info(self, key: str) -> Optional[Dict[str, Any]]:
        """获取用户偏好详细信息
        
        Args:
            key: 偏好键
            
        Returns:
            Optional[Dict[str, Any]]: 偏好信息，如果不存在则返回None
        """
        try:
            sql = "SELECT * FROM user_preferences WHERE key = ?"
            rows = self.db_manager.execute_query(sql, (key,))
            
            if rows:
                return self._row_to_dict(rows[0])
            return None
            
        except Exception as e:
            self.logger.error(f"获取用户偏好信息失败: {e}")
            raise RepositoryError(f"获取用户偏好信息失败: {e}")
    
    def get_all_preferences(self, category: str = None) -> Dict[str, Any]:
        """获取所有用户偏好
        
        Args:
            category: 分类过滤
            
        Returns:
            Dict[str, Any]: 偏好字典
        """
        try:
            sql = "SELECT * FROM user_preferences"
            params = []
            
            if category:
                sql += " WHERE category = ?"
                params.append(category)
            
            sql += " ORDER BY key"
            
            rows = self.db_manager.execute_query(sql, tuple(params))
            
            preferences = {}
            for row in rows:
                preference = self._row_to_dict(row)
                preferences[preference['key']] = preference['value']
            
            return preferences
            
        except Exception as e:
            self.logger.error(f"获取所有用户偏好失败: {e}")
            raise RepositoryError(f"获取所有用户偏好失败: {e}")
    
    def get_preferences_by_category(self, category: str) -> List[Dict[str, Any]]:
        """根据分类获取用户偏好
        
        Args:
            category: 分类
            
        Returns:
            List[Dict[str, Any]]: 偏好列表
        """
        try:
            sql = "SELECT * FROM user_preferences WHERE category = ? ORDER BY key"
            rows = self.db_manager.execute_query(sql, (category,))
            
            return [self._row_to_dict(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"根据分类获取用户偏好失败: {e}")
            raise RepositoryError(f"根据分类获取用户偏好失败: {e}")
    
    def delete_preference(self, key: str) -> bool:
        """删除用户偏好
        
        Args:
            key: 偏好键
            
        Returns:
            bool: 删除是否成功
        """
        try:
            sql = "DELETE FROM user_preferences WHERE key = ?"
            affected_rows = self.db_manager.execute_update(sql, (key,))
            success = affected_rows > 0
            
            if success:
                self.logger.debug(f"用户偏好删除成功: {key}")
            else:
                self.logger.warning(f"用户偏好删除失败，未找到记录: {key}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"删除用户偏好失败: {e}")
            raise RepositoryError(f"删除用户偏好失败: {e}")
    
    def delete_preferences_by_category(self, category: str) -> int:
        """根据分类删除用户偏好
        
        Args:
            category: 分类
            
        Returns:
            int: 删除的记录数量
        """
        try:
            sql = "DELETE FROM user_preferences WHERE category = ?"
            affected_rows = self.db_manager.execute_update(sql, (category,))
            
            self.logger.info(f"根据分类删除用户偏好成功: category={category}, count={affected_rows}")
            return affected_rows
            
        except Exception as e:
            self.logger.error(f"根据分类删除用户偏好失败: {e}")
            raise RepositoryError(f"根据分类删除用户偏好失败: {e}")
    
    def batch_set_preferences(self, preferences: Dict[str, Any], 
                             category: str = None) -> bool:
        """批量设置用户偏好
        
        Args:
            preferences: 偏好字典
            category: 分类
            
        Returns:
            bool: 设置是否成功
        """
        try:
            success_count = 0
            
            for key, value in preferences.items():
                try:
                    if self.set_preference(key, value, category=category):
                        success_count += 1
                except Exception as e:
                    self.logger.error(f"批量设置偏好失败: {key} = {value}, error: {e}")
            
            success = success_count == len(preferences)
            
            self.logger.info(f"批量设置用户偏好: 成功 {success_count}/{len(preferences)}")
            return success
            
        except Exception as e:
            self.logger.error(f"批量设置用户偏好失败: {e}")
            raise RepositoryError(f"批量设置用户偏好失败: {e}")
    
    def get_categories(self) -> List[str]:
        """获取所有分类
        
        Returns:
            List[str]: 分类列表
        """
        try:
            sql = """
                SELECT DISTINCT category 
                FROM user_preferences 
                WHERE category IS NOT NULL 
                ORDER BY category
            """
            result = self.db_manager.execute_query(sql)
            return [row['category'] for row in result]
            
        except Exception as e:
            self.logger.error(f"获取分类列表失败: {e}")
            raise RepositoryError(f"获取分类列表失败: {e}")
    
    def search_preferences(self, query: str) -> List[Dict[str, Any]]:
        """搜索用户偏好
        
        Args:
            query: 搜索关键词
            
        Returns:
            List[Dict[str, Any]]: 偏好列表
        """
        try:
            sql = """
                SELECT * FROM user_preferences 
                WHERE key LIKE ? OR description LIKE ?
                ORDER BY key
            """
            params = (f"%{query}%", f"%{query}%")
            rows = self.db_manager.execute_query(sql, params)
            
            return [self._row_to_dict(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"搜索用户偏好失败: {e}")
            raise RepositoryError(f"搜索用户偏好失败: {e}")
    
    def export_preferences(self, category: str = None) -> Dict[str, Any]:
        """导出用户偏好
        
        Args:
            category: 分类过滤
            
        Returns:
            Dict[str, Any]: 导出的偏好数据
        """
        try:
            sql = "SELECT * FROM user_preferences"
            params = []
            
            if category:
                sql += " WHERE category = ?"
                params.append(category)
            
            sql += " ORDER BY category, key"
            
            rows = self.db_manager.execute_query(sql, tuple(params))
            
            export_data = {
                'export_time': datetime.now().isoformat(),
                'category': category,
                'preferences': []
            }
            
            for row in rows:
                preference = self._row_to_dict(row)
                export_data['preferences'].append(preference)
            
            return export_data
            
        except Exception as e:
            self.logger.error(f"导出用户偏好失败: {e}")
            raise RepositoryError(f"导出用户偏好失败: {e}")
    
    def import_preferences(self, preferences_data: Dict[str, Any], 
                          overwrite: bool = False) -> Dict[str, int]:
        """导入用户偏好
        
        Args:
            preferences_data: 偏好数据
            overwrite: 是否覆盖现有偏好
            
        Returns:
            Dict[str, int]: 导入结果统计
        """
        try:
            stats = {'imported': 0, 'skipped': 0, 'errors': 0}
            
            preferences = preferences_data.get('preferences', [])
            
            for preference in preferences:
                try:
                    key = preference['key']
                    value = preference['value']
                    description = preference.get('description')
                    category = preference.get('category')
                    
                    # 检查是否已存在
                    existing = self.get_preference_info(key)
                    
                    if existing and not overwrite:
                        stats['skipped'] += 1
                        continue
                    
                    if self.set_preference(key, value, description, category):
                        stats['imported'] += 1
                    else:
                        stats['errors'] += 1
                        
                except Exception as e:
                    self.logger.error(f"导入偏好失败: {preference}, error: {e}")
                    stats['errors'] += 1
            
            self.logger.info(f"导入用户偏好完成: {stats}")
            return stats
            
        except Exception as e:
            self.logger.error(f"导入用户偏好失败: {e}")
            raise RepositoryError(f"导入用户偏好失败: {e}")
    
    def reset_preferences(self, category: str = None) -> int:
        """重置用户偏好
        
        Args:
            category: 分类，如果为None则重置所有偏好
            
        Returns:
            int: 重置的记录数量
        """
        try:
            if category:
                sql = "DELETE FROM user_preferences WHERE category = ?"
                params = (category,)
            else:
                sql = "DELETE FROM user_preferences"
                params = ()
            
            affected_rows = self.db_manager.execute_update(sql, params)
            
            self.logger.info(f"重置用户偏好成功: category={category}, count={affected_rows}")
            return affected_rows
            
        except Exception as e:
            self.logger.error(f"重置用户偏好失败: {e}")
            raise RepositoryError(f"重置用户偏好失败: {e}")
    
    def get_preference_statistics(self) -> Dict[str, Any]:
        """获取偏好统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {}
            
            # 总数统计
            sql = "SELECT COUNT(*) as total FROM user_preferences"
            result = self.db_manager.execute_query(sql)
            stats['total_count'] = result[0]['total'] if result else 0
            
            # 按分类统计
            sql = """
                SELECT category, COUNT(*) as count 
                FROM user_preferences 
                WHERE category IS NOT NULL
                GROUP BY category
                ORDER BY count DESC
            """
            result = self.db_manager.execute_query(sql)
            stats['by_category'] = {row['category']: row['count'] for row in result}
            
            # 按类型统计
            sql = """
                SELECT value_type, COUNT(*) as count 
                FROM user_preferences 
                GROUP BY value_type
                ORDER BY count DESC
            """
            result = self.db_manager.execute_query(sql)
            stats['by_type'] = {row['value_type']: row['count'] for row in result}
            
            # 最近更新统计
            sql = """
                SELECT COUNT(*) as count 
                FROM user_preferences 
                WHERE updated_at >= datetime('now', '-7 days')
            """
            result = self.db_manager.execute_query(sql)
            stats['recent_updates'] = result[0]['count'] if result else 0
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取偏好统计信息失败: {e}")
            raise RepositoryError(f"获取偏好统计信息失败: {e}")
    
    # 常用偏好的便捷方法
    def get_string_preference(self, key: str, default: str = "") -> str:
        """获取字符串偏好"""
        return str(self.get_preference(key, default))
    
    def get_int_preference(self, key: str, default: int = 0) -> int:
        """获取整数偏好"""
        try:
            return int(self.get_preference(key, default))
        except (ValueError, TypeError):
            return default
    
    def get_float_preference(self, key: str, default: float = 0.0) -> float:
        """获取浮点数偏好"""
        try:
            return float(self.get_preference(key, default))
        except (ValueError, TypeError):
            return default
    
    def get_bool_preference(self, key: str, default: bool = False) -> bool:
        """获取布尔偏好"""
        value = self.get_preference(key, default)
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        return bool(value)
    
    def get_list_preference(self, key: str, default: List = None) -> List:
        """获取列表偏好"""
        if default is None:
            default = []
        value = self.get_preference(key, default)
        return value if isinstance(value, list) else default
    
    def get_dict_preference(self, key: str, default: Dict = None) -> Dict:
        """获取字典偏好"""
        if default is None:
            default = {}
        value = self.get_preference(key, default)
        return value if isinstance(value, dict) else default