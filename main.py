#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fabric Search v2 - 主程序入口

面料图像搜索系统的主程序入口文件。
支持多种运行模式：API服务器、GUI界面、批处理、配置管理等。
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.config_manager import get_config, get_config_manager, ConfigManager
from config.app_config import AppConfig
from utils.log_utils import setup_logging
from utils.logger_mixin import LoggerMixin
from database.database_manager import DatabaseManager, DatabaseConfig
from database.repositories.search_history_repository import SearchHistoryRepository

# 尝试导入GUI相关模块（可选）
use_tkinter = False
use_pyqt = False
try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QIcon
    use_pyqt = True
    print("检测到PyQt6，GUI模式可用")
except ImportError:
    try:
        import tkinter
        use_tkinter = True
        print("检测到tkinter，GUI模式可用")
    except ImportError:
        print("未检测到GUI框架，仅支持命令行模式")

# 根据可用的GUI框架导入相应的窗口类
if use_pyqt:
    try:
        from gui.main_window import MainWindow
        print("MainWindow导入成功")
    except ImportError as e:
        print(f"MainWindow导入失败: {e}")
        use_pyqt = False
elif use_tkinter:
    try:
        from gui.tkinter_main_window import TkinterMainWindow as MainWindow
        print("TkinterMainWindow导入成功")
    except ImportError as e:
        print(f"TkinterMainWindow导入失败: {e}")
        use_tkinter = False


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='Fabric Search v2 - 面料图像搜索系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 运行API服务器
  python main.py server
  python main.py server --host 0.0.0.0 --port 8080 --debug
  
  # 运行GUI界面
  python main.py gui
  
  # 批量处理图像
  python main.py batch /path/to/images
  python main.py batch /path/to/images --max-workers 8 --recursive
  
  # 配置管理
  python main.py config list
  python main.py config get feature_extraction.model_name
  python main.py config set search.top_k 100
  python main.py config export config_backup.json
  
  # 显示系统信息
  python main.py info
  
  # 设置环境
  python main.py setup
"""
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 服务器命令
    server_parser = subparsers.add_parser('server', help='运行API服务器')
    server_parser.add_argument('--host', default=None, help='服务器主机地址')
    server_parser.add_argument('--port', type=int, default=None, help='服务器端口')
    server_parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    # GUI命令
    gui_parser = subparsers.add_parser('gui', help='运行GUI界面')
    gui_parser.add_argument('--theme', choices=['light', 'dark', 'auto'], default='auto', help='界面主题')
    
    # 批处理命令
    batch_parser = subparsers.add_parser('batch', help='批量处理图像')
    batch_parser.add_argument('source_path', help='源图像目录路径')
    batch_parser.add_argument('--max-workers', type=int, help='最大工作线程数')
    batch_parser.add_argument('--batch-size', type=int, help='批处理大小')
    batch_parser.add_argument('--recursive', action='store_true', default=True, help='递归处理子目录')
    batch_parser.add_argument('--no-recursive', action='store_false', dest='recursive', help='不递归处理子目录')
    batch_parser.add_argument('--skip-existing', action='store_true', default=True, help='跳过已存在的图像')
    batch_parser.add_argument('--no-skip-existing', action='store_false', dest='skip_existing', help='不跳过已存在的图像')
    batch_parser.add_argument('--update-existing', action='store_true', help='更新已存在的图像')
    
    # 配置管理命令
    config_parser = subparsers.add_parser('config', help='配置管理')
    config_subparsers = config_parser.add_subparsers(dest='config_action', help='配置操作')
    
    config_get_parser = config_subparsers.add_parser('get', help='获取配置值')
    config_get_parser.add_argument('key', help='配置键')
    
    config_set_parser = config_subparsers.add_parser('set', help='设置配置值')
    config_set_parser.add_argument('key', help='配置键')
    config_set_parser.add_argument('value', help='配置值')
    
    config_subparsers.add_parser('list', help='列出所有配置')
    
    config_export_parser = config_subparsers.add_parser('export', help='导出配置')
    config_export_parser.add_argument('output_file', help='输出文件路径')
    config_export_parser.add_argument('--format', choices=['json', 'yaml', 'toml'], default='json', help='导出格式')
    
    config_import_parser = config_subparsers.add_parser('import', help='导入配置')
    config_import_parser.add_argument('input_file', help='输入文件路径')
    
    # 系统信息命令
    subparsers.add_parser('info', help='显示系统信息')
    
    # 环境设置命令
    subparsers.add_parser('setup', help='设置环境')
    
    # 兼容旧版本的参数（用于向后兼容）
    parser.add_argument('--mode', choices=['gui', 'create_db', 'sync_db', 'search'], help='运行模式（已弃用，请使用子命令）')
    parser.add_argument('--folder', type=str, help='图像文件夹路径（已弃用）')
    parser.add_argument('--db', type=str, help='数据库路径（已弃用）')
    parser.add_argument('--query', type=str, help='查询图像路径（已弃用）')
    parser.add_argument('--model', type=str, default='resnet50', help='特征提取模型（已弃用）')
    parser.add_argument('--use-gpu', action='store_true', help='使用GPU加速（已弃用）')
    parser.add_argument('--top-n', type=int, default=20, help='返回的搜索结果数量（已弃用）')
    parser.add_argument('--db-type', choices=['standard', 'chunked', 'vector'], default='standard', help='数据库类型（已弃用）')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='日志级别')
    
    return parser.parse_args()


def setup_environment():
    """设置环境"""
    # 确保必要的目录存在
    if not setup_directories():
        print("警告: 创建目录失败，将使用默认目录")
    
    config_manager = get_config_manager()
    config = config_manager.get_config()
    
    # 设置日志
    setup_logging(
        level=config.logging.level,
        log_dir=config.log_dir,
        force=True  # 强制重新配置日志系统，确保配置正确应用
    )
    
    # 初始化数据库
    db_config = DatabaseConfig(db_path=os.path.join(config.data_dir, 'fabric_search.db'))
    db_manager = DatabaseManager(config=db_config)
    # 数据库已在构造函数中初始化
    
    print(f"环境设置完成")
    print(f"数据目录: {config.data_dir}")
    print(f"日志目录: {config.log_dir}")
    print(f"数据库: {config.database.database}")


def setup_application():
    """设置GUI应用程序"""
    if use_tkinter:
        # 使用tkinter时不需要特殊设置
        return None
    elif use_pyqt:
        # 创建PyQt6应用程序
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setApplicationName("Fabric Search")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("Fabric Search Team")
        app.setOrganizationDomain("fabric-search.com")
        
        # 设置应用程序图标
        icon_path = project_root / "resources" / "icons" / "app_icon.png"
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
        
        return app
    else:
        return None


def run_api_server(host: str = None, port: int = None, debug: bool = None):
    """运行API服务器
    
    Args:
        host: 服务器主机地址
        port: 服务器端口
        debug: 是否启用调试模式
    """
    logger = logging.getLogger(__name__)
    
    try:
        # 初始化组件管理器
        from utils.component_manager import ComponentManager
        component_manager = ComponentManager()
        
        # 获取配置
        config = component_manager.config.api
        
        # 使用命令行参数覆盖配置
        host = host or config.host or '127.0.0.1'  # 默认使用localhost
        port = port or config.port or 5000  # 默认使用5000端口
        debug = debug if debug is not None else config.debug
        
        print(f"启动Fabric Search API服务器...")
        print(f"地址: http://{host}:{port}")
        print(f"调试模式: {debug}")
        print(f"API文档: http://{host}:{port}/api/info")
        
        # 创建Flask应用
        from api.app import create_app
        app = create_app()
        
        # 运行服务器
        try:
            app.run(
                host=host,
                port=port,
                debug=debug,
                threaded=True
            )
        finally:
            # 清理组件管理器资源
            try:
                component_manager.cleanup()
                logger.info("API服务器组件管理器资源清理完成")
            except Exception as e:
                logger.warning(f"清理API服务器组件管理器资源失败: {e}")
                
    except Exception as e:
        logger.error(f"API服务器启动失败: {e}", exc_info=True)
        print(f"错误: API服务器启动失败: {e}")
        
        # 确保清理资源
        try:
            # 使用已有的组件管理器实例进行清理
            component_manager.cleanup()
        except Exception as cleanup_e:
            logger.warning(f"异常情况下清理API服务器资源失败: {cleanup_e}")
            
        return 1


def run_gui_mode(args):
    """运行GUI模式"""
    logger = logging.getLogger(__name__)
    logger.info("开始启动GUI模式")
    print("开始启动GUI模式...")
    
    # 检查GUI框架可用性
    if not use_pyqt and not use_tkinter:
        print("错误: 未检测到可用的GUI框架")
        print("请安装PyQt6或确保tkinter可用")
        logger.error("未检测到可用的GUI框架")
        return 1
    
    try:
        # 设置环境，确保所有必要的目录存在
        logger.info("设置基本环境")
        # 使用项目根目录的绝对路径创建必要的目录
        if not setup_directories():
            logger.error("创建目录失败，无法继续")
            print("错误: 创建目录失败，无法继续")
            return 1
        
        logger.info("目录创建成功")
            
        # 使用组件管理器初始化核心组件
        try:
            from utils.component_manager import ComponentManager
            
            # 获取组件管理器实例
            component_manager = ComponentManager()
            
            # 通过属性访问器触发核心组件的初始化
            logger.info("初始化GUI模式核心组件")
            # 访问config属性会触发initialize_components
            _ = component_manager.config
            
            logger.info("核心组件初始化成功")
        except Exception as e:
            logger.error(f"初始化核心组件失败: {e}", exc_info=True)
            print(f"错误: 初始化核心组件失败: {e}")
            return 1
        
        if use_tkinter:
            # 使用tkinter版本的主窗口
            logger.info("使用Tkinter创建主窗口")
            main_window = MainWindow()
            logger.info("使用Tkinter GUI界面已启动")
            main_window.run()
            return 0
        elif use_pyqt:
            # 使用PyQt6版本的主窗口
            logger.info("使用PyQt6创建应用程序")
            print("正在创建PyQt6应用程序...")
            try:
                app = setup_application()
                if app is None:
                    print("错误: 无法创建PyQt6应用程序")
                    logger.error("无法创建PyQt6应用程序")
                    return 1
                print("PyQt6应用程序创建成功")
            except Exception as e:
                print(f"创建PyQt6应用程序失败: {e}")
                logger.error(f"创建PyQt6应用程序失败: {e}", exc_info=True)
                return 1
            
            # 创建主窗口，使用组件管理器提供的组件
            logger.info("创建PyQt6主窗口")
            print("正在创建主窗口...")
            try:
                # 获取特征管理器（统一使用组件管理器的属性访问器）
                logger.info("main.py - 获取feature_manager")
                
                # 统一使用属性访问器获取FeatureManager实例
                feature_manager = component_manager.feature_manager
                
                logger.info(f"main.py - feature_manager获取完成: {feature_manager is not None}")
                if feature_manager is not None:
                    logger.info(f"main.py - feature_manager类型: {type(feature_manager)}")
                    logger.info(f"main.py - feature_manager ID: {id(feature_manager)}")
                    
                    main_window = MainWindow(
                        feature_manager=feature_manager,
                        fabric_repository=component_manager.fabric_repository,
                        search_repository=component_manager.search_repository,
                        search_history_manager=component_manager.search_history_manager,
                        statistics_manager=component_manager.statistics_manager,
                        gpu_manager=component_manager.gpu_manager,
                        model_config_manager=component_manager.model_config_manager
                    )
                    logger.info("主窗口创建成功")
                    print("主窗口创建成功")
            except Exception as e:
                print(f"创建主窗口失败: {e}")
                logger.error(f"创建主窗口失败: {e}", exc_info=True)
                return 1
            
            # 显示主窗口
            logger.info("显示主窗口")
            print("正在显示主窗口...")
            try:
                main_window.show()
                print("主窗口显示成功")
            except Exception as e:
                print(f"显示主窗口失败: {e}")
                logger.error(f"显示主窗口失败: {e}", exc_info=True)
                return 1
            
            logger.info("PyQt6 GUI界面已启动")
            print("PyQt6 GUI界面已启动，请等待窗口显示...")
            
            # 运行应用程序
            logger.info("开始事件循环")
            print("开始事件循环...")
            try:
                exit_code = app.exec()
                logger.info(f"应用程序退出，退出码: {exit_code}")
                print(f"应用程序退出，退出码: {exit_code}")
                
                # 清理组件管理器资源
                try:
                    component_manager.cleanup()
                    logger.info("组件管理器资源清理完成")
                except Exception as e:
                    logger.warning(f"清理组件管理器资源失败: {e}")
                
                return exit_code
            except Exception as e:
                print(f"事件循环异常: {e}")
                logger.error(f"事件循环异常: {e}", exc_info=True)
                
                # 确保清理资源
                try:
                    component_manager.cleanup()
                except Exception as cleanup_e:
                    logger.warning(f"异常情况下清理资源失败: {cleanup_e}")
                
                return 1
        
    except Exception as e:
        logger.error(f"GUI模式运行失败: {e}")
        return 1


def run_batch_processing(source_path: str, **kwargs):
    """运行批处理
    
    Args:
        source_path: 源图像目录路径
        **kwargs: 其他批处理参数
    """
    logger = logging.getLogger(__name__)
    
    print(f"开始批量处理图像...")
    print(f"源路径: {source_path}")
    
    # 检查源路径
    if not os.path.exists(source_path):
        print(f"错误: 源路径不存在: {source_path}")
        return False
    
    try:
        # 初始化组件管理器
        from utils.component_manager import ComponentManager
        component_manager = ComponentManager()
        
        # 获取配置
        config = component_manager.config
        
        # 初始化批处理组件
        from models.batch_processing import BatchProcessor, BatchConfig
        
        # 获取特征提取器（通过特征管理器属性访问器）
        feature_extractor = component_manager.feature_manager.feature_extractor
        
        batch_config = BatchConfig(
            max_workers=kwargs.get('max_workers', config.batch_processing.max_workers),
            batch_size=kwargs.get('batch_size', config.batch_processing.batch_size),
            queue_size=config.batch_processing.queue_size
        )
        
        batch_processor = BatchProcessor(
            feature_extractor=feature_extractor,
            repository=component_manager.fabric_repository,
            config=batch_config
        )
        
        # 启动批处理
        job_id = f"batch_{int(time.time())}"
        
        result = batch_processor.process_directory(
            source_path=source_path,
            recursive=kwargs.get('recursive', True),
            skip_existing=kwargs.get('skip_existing', True),
            update_existing=kwargs.get('update_existing', False),
            supported_formats=kwargs.get('supported_formats'),
            filters=kwargs.get('filters')
        )
        
        print(f"批处理完成!")
        print(f"处理图像数: {result.total_processed}")
        print(f"成功数: {result.successful}")
        print(f"失败数: {result.failed}")
        print(f"跳过数: {result.skipped}")
        print(f"处理时间: {result.processing_time:.2f}秒")
        
        if result.errors:
            print(f"\n错误详情:")
            for error in result.errors[:10]:  # 只显示前10个错误
                print(f"  - {error}")
            if len(result.errors) > 10:
                print(f"  ... 还有 {len(result.errors) - 10} 个错误")
        
        # 清理组件管理器资源
        try:
            component_manager.cleanup()
            logger.info("批处理组件管理器资源清理完成")
        except Exception as e:
            logger.warning(f"清理批处理组件管理器资源失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"批处理失败: {e}")
        logger.error(f"批处理异常: {e}", exc_info=True)
        
        # 确保清理资源
        try:
            # 使用已有的组件管理器实例进行清理
            component_manager.cleanup()
        except Exception as cleanup_e:
            logger.warning(f"异常情况下清理批处理资源失败: {cleanup_e}")
        
        return False


def run_config_management(action: str, **kwargs):
    """运行配置管理
    
    Args:
        action: 配置操作类型 (get, set, list, export, import)
        **kwargs: 其他参数
    """
    logger = logging.getLogger(__name__)
    
    try:
        # 使用组件管理器获取配置管理器
        from utils.component_manager import ComponentManager
        component_manager = ComponentManager()
        
        # 获取配置管理器（通过属性访问器）
        config_manager = component_manager.config_manager
        
        if action == 'get':
            key = kwargs.get('key')
            if not key:
                print("错误: 需要指定配置键")
                return False
            
            value = config_manager.get(key)
            if value is not None:
                print(f"{key}: {value}")
            else:
                print(f"配置键 '{key}' 不存在")
                
        elif action == 'set':
            key = kwargs.get('key')
            value = kwargs.get('value')
            if not key or value is None:
                print("错误: 需要指定配置键和值")
                return False
            
            # 尝试解析值类型
            if value.lower() in ('true', 'false'):
                value = value.lower() == 'true'
            elif value.isdigit():
                value = int(value)
            elif '.' in value and value.replace('.', '').isdigit():
                value = float(value)
            
            config_manager.set(key, value)
            config_manager.save()
            print(f"已设置 {key} = {value}")
            
        elif action == 'list':
            config = config_manager.get_config()
            config_dict = {
                'app_name': config.app_name,
                'version': config.version,
                'debug': config.debug,
                'data_dir': config.data_dir,
                'model_dir': config.model_dir,
                'cache_dir': config.cache_dir,
                'log_dir': config.log_dir,
                'temp_dir': config.temp_dir,
                'database': {
                    'host': config.database.host,
                    'port': config.database.port,
                    'database': config.database.database,
                    'username': config.database.username
                },
                'feature_extraction': {
                    'model_name': config.feature_extraction.model_name,
                    'device': config.feature_extraction.device,
                    'batch_size': config.feature_extraction.batch_size
                },
                'search': {
                    'index_type': config.search.index_type,
                    'similarity_metric': config.search.similarity_metric,
                    'top_k': config.search.top_k,
                    'similarity_threshold': config.search.similarity_threshold
                },
                'logging': {
                    'level': config.logging.level,
                    'log_to_file': config.logging.log_to_file,
                    'log_to_console': config.logging.log_to_console,
                    'log_file': config.logging.log_file
                },
                'security': {
                    'api_key_required': config.security.api_key_required,
                    'rate_limit_enabled': config.security.rate_limit_enabled,
                    'max_requests_per_minute': config.security.max_requests_per_minute
                }
            }
            print("当前配置:")
            _print_config_dict(config_dict)
            
        elif action == 'export':
            output_path = kwargs.get('output_path', 'config_export.json')
            config_manager.export_config(output_path)
            print(f"配置已导出到: {output_path}")
            
        elif action == 'import':
            input_path = kwargs.get('input_path')
            if not input_path or not os.path.exists(input_path):
                print("错误: 需要指定有效的配置文件路径")
                return False
            
            config_manager.import_config(input_path)
            print(f"配置已从 {input_path} 导入")
            
        else:
            print(f"未知的配置操作: {action}")
            return False
            
        return True
        
    except Exception as e:
        print(f"配置管理失败: {e}")
        logger.error(f"配置管理异常: {e}", exc_info=True)
        return False


def _print_config_dict(config_dict, prefix='', indent=0):
    """递归打印配置字典"""
    for key, value in config_dict.items():
        full_key = f"{prefix}.{key}" if prefix else key
        if isinstance(value, dict):
            print("  " * indent + f"{key}:")
            _print_config_dict(value, full_key, indent + 1)
        else:
            print("  " * indent + f"{key}: {value}")


def show_system_info():
    """显示系统信息"""
    import platform
    import psutil
    
    print("=" * 60)
    print("Fabric Search v2 - 系统信息")
    print("=" * 60)
    
    # 基本系统信息
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {platform.python_version()}")
    print(f"架构: {platform.machine()}")
    print(f"处理器: {platform.processor()}")
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total / (1024**3):.1f} GB")
    print(f"可用内存: {memory.available / (1024**3):.1f} GB")
    print(f"内存使用率: {memory.percent}%")
    
    # 磁盘信息
    disk = psutil.disk_usage('/')
    print(f"磁盘总空间: {disk.total / (1024**3):.1f} GB")
    print(f"磁盘可用空间: {disk.free / (1024**3):.1f} GB")
    print(f"磁盘使用率: {(disk.used / disk.total) * 100:.1f}%")
    
    # GPU信息
    try:
        import torch
        if torch.cuda.is_available():
            print(f"CUDA可用: 是")
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                print(f"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        else:
            print("CUDA可用: 否")
    except ImportError:
        print("PyTorch未安装")
    
    # 项目信息
    print("\n项目信息:")
    print(f"项目根目录: {project_root}")
    
    # 配置信息
    try:
        config = get_config()
        print(f"应用名称: {config.app_name}")
        print(f"系统版本: {config.version}")
        print(f"调试模式: {config.debug}")
        print(f"数据目录: {config.data_dir}")
        print(f"模型目录: {config.model_dir}")
    except Exception as e:
        print(f"配置加载失败: {e}")
    
    print("=" * 60)


def get_config():
    """获取系统配置实例"""
    # 返回SystemConfig实例
    from utils.component_manager import ComponentManager
    return ComponentManager().config


def setup_directories():
    """设置必要的目录，使用绝对路径
    
    Returns:
        bool: 目录创建是否成功
    """
    # 基本目录
    directories = [
        "data",
        "logs",
        "cache",
        "models",
        "exports",
        "temp",
        "config"
    ]
    
    # 子目录
    subdirectories = [
        os.path.join("data", "uploads"),
        os.path.join("data", "exports"),
        os.path.join("data", "cache"),
        os.path.join("data", "models"),
        os.path.join("data", "backups"),
        os.path.join("data", "index")
    ]
    
    # 合并所有需要创建的目录
    all_directories = directories + subdirectories
    
    try:
        for directory in all_directories:
            dir_path = project_root / directory
            dir_path.mkdir(exist_ok=True, parents=True)
            
        print(f"已创建必要目录: {', '.join(all_directories)}")
        return True
    except Exception as e:
        print(f"创建目录失败: {e}")
        logging.error(f"创建目录失败: {e}", exc_info=True)
        return False


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 设置日志级别 - 仅在未调用setup_environment的情况下使用基本配置
        # 避免重复配置日志系统
        log_level = getattr(args, 'log_level', 'INFO')
        
        # 使用临时的基本日志配置，后续可能会被setup_logging覆盖
        # 这样可以确保在setup_environment之前也能记录日志
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            force=True  # 强制重新配置，避免重复配置问题
        )
        
        logger = logging.getLogger(__name__)
        logger.info("Fabric Search v2 启动中...")
        print("开始处理命令...")
        print(f"args: {args}")
        print(f"hasattr(args, 'command'): {hasattr(args, 'command')}")
        
        # 处理命令
        if hasattr(args, 'command') and args.command is not None:
            # 新的子命令结构
            if args.command == 'server':
                return run_api_server(
                    host=args.host,
                    port=args.port,
                    debug=args.debug
                )
            elif args.command == 'gui':
                return run_gui_mode(args)
            elif args.command == 'batch':
                return 0 if run_batch_processing(
                    source_path=args.source_path,
                    max_workers=args.max_workers,
                    batch_size=args.batch_size,
                    recursive=args.recursive,
                    skip_existing=args.skip_existing,
                    update_existing=args.update_existing
                ) else 1
            elif args.command == 'config':
                if args.config_action == 'get':
                    return 0 if run_config_management('get', key=args.key) else 1
                elif args.config_action == 'set':
                    return 0 if run_config_management('set', key=args.key, value=args.value) else 1
                elif args.config_action == 'list':
                    return 0 if run_config_management('list') else 1
                elif args.config_action == 'export':
                    return 0 if run_config_management('export', output_path=args.output) else 1
                elif args.config_action == 'import':
                    return 0 if run_config_management('import', input_path=args.input) else 1
            elif args.command == 'info':
                show_system_info()
                return 0
            elif args.command == 'setup':
                setup_environment()
                print("环境设置完成")
                return 0
        else:
            # 向后兼容的旧命令结构
            if hasattr(args, 'mode') and args.mode is not None:
                if args.mode == 'gui':
                    return run_gui_mode(args)
                else:
                    print("警告: 使用了已弃用的命令格式，请使用新的子命令结构")
                    print("运行 'python main.py --help' 查看新的命令格式")
                    return 1
            else:
                # 默认启动GUI界面
                print("Fabric Search v2 - 智能面料图像搜索系统")
                print("默认启动GUI界面...")
                print(f"use_pyqt: {use_pyqt}, use_tkinter: {use_tkinter}")
                # 创建一个模拟的args对象用于GUI模式
                class DefaultArgs:
                    def __init__(self):
                        self.theme = 'auto'
                        self.command = 'gui'
                
                default_args = DefaultArgs()
                print("准备调用run_gui_mode...")
                result = run_gui_mode(default_args)
                print(f"run_gui_mode返回: {result}")
                return result
            
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return 0
    except Exception as e:
        print(f"启动失败: {e}")
        print(f"异常类型: {type(e).__name__}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        logging.exception("程序异常")
        return 1


if __name__ == '__main__':
    sys.exit(main())