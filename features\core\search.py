"""搜索处理模块

处理图像搜索相关功能。
"""

import time
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

from .data_models import SearchRequest, SearchResponse
from features.search.similarity_search import SimilarityCalculator
from features.utils import validate_search_request, validate_image_path, log_performance


class SearchHandler:
    """搜索处理器
    
    负责处理图像搜索相关功能。
    """
    
    def __init__(self, feature_extractor, feature_storage, similarity_calculator, logger):
        """初始化搜索处理器
        
        Args:
            feature_extractor: 特征提取器
            feature_storage: 特征存储
            similarity_calculator: 相似度计算器
            logger: 日志记录器
        """
        self.feature_extractor = feature_extractor
        self.feature_storage = feature_storage
        self.similarity_calculator = similarity_calculator
        self.logger = logger
        
    def cleanup(self):
        """清理资源
        
        在FeatureManager的cleanup方法中被调用
        """
        try:
            # 注意：不在这里清理similarity_calculator，避免重复清理
            # similarity_calculator由FeatureManager统一管理和清理
                
            # 记录日志
            self.logger.info("搜索处理器资源已清理")
        except Exception as e:
            self.logger.error(f"清理搜索处理器资源时出错: {e}")
        
    @log_performance
    def search_similar_images(self, request: SearchRequest, performance_monitor) -> SearchResponse:
        """搜索相似图像
        
        Args:
            request: 搜索请求
            performance_monitor: 性能监控器
            
        Returns:
            SearchResponse: 搜索响应
        """
        # 验证搜索请求
        if not self._validate_search_request(request):
            return SearchResponse(
                results=[],
                total_found=0,
                search_time=0.0,
                query_info={},
                success=False,
                error_message="无效的搜索请求"
            )
            
        try:
            with performance_monitor:
                start_time = time.time()
                
                # 获取查询特征
                query_features = self._get_query_features(request)
                if query_features is None:
                    return SearchResponse(
                        results=[],
                        total_found=0,
                        search_time=0.0,
                        query_info={},
                        success=False,
                        error_message="无法获取查询特征"
                    )
                    
                # 执行搜索
                search_results = self._execute_search(request, query_features)
                
                search_time = time.time() - start_time
                
                return SearchResponse(
                    results=search_results,
                    total_found=len(search_results),
                    search_time=search_time,
                    query_info={
                        'feature_dim': len(query_features) if hasattr(query_features, '__len__') else 0,
                        'top_k': request.top_k,
                        'similarity_metric': request.similarity_metric
                    },
                    success=True
                )
                
        except Exception as e:
            self.logger.error(f"搜索过程中发生错误: {e}")
            return SearchResponse(
                results=[],
                total_found=0,
                search_time=0.0,
                query_info={},
                success=False,
                error_message=str(e)
            )
            
    def _get_query_features(self, request: SearchRequest):
        """获取查询特征
        
        Args:
            request: 搜索请求
            
        Returns:
            查询特征或None
        """
        if request.query_features is not None:
            return request.query_features
            
        if request.query_image_path:
            if not validate_image_path(request.query_image_path):
                return None
                
            result = self.feature_extractor.extract_features(request.query_image_path)
            if result and result.success:
                return result.features
                
        if request.query_id:
            feature_data = self.feature_storage.get_features(request.query_id)
            if feature_data:
                return feature_data['features']
                
        return None
        
    def _execute_search(self, request: SearchRequest, query_features):
        """执行搜索
        
        Args:
            request: 搜索请求
            query_features: 查询特征
            
        Returns:
            搜索结果列表
        """
        if not self.similarity_calculator or not self.similarity_calculator.has_index():
            raise RuntimeError("搜索索引未构建")
            
        # 执行相似度搜索
        search_results = self.similarity_calculator.search_by_features(
            query_features=query_features,
            request=request
        )
        
        # 获取详细结果
        detailed_results = []
        for result in search_results:
            feature_id = result['id']
            similarity = result['similarity']
            
            # 获取特征相似度分数（如果存在）
            feature_similarities = result.get('feature_similarities', {
                'deep': similarity,
                'color': 0.0,
                'texture': 0.0,
                'shape': 0.0
            })
            
            # 从存储中获取特征数据（验证特征是否存在）
            feature_data = self.feature_storage.get_features(feature_id)
            if feature_data is not None:
                detailed_results.append({
                    'id': feature_id,
                    'similarity': similarity,
                    'feature_similarities': feature_similarities,
                    'metadata': {}  # 暂时使用空元数据
                })
                
        return detailed_results
    
    def _validate_search_request(self, request: SearchRequest) -> bool:
        """验证搜索请求
        
        Args:
            request: 搜索请求对象
            
        Returns:
            bool: 请求是否有效
        """
        if not isinstance(request, SearchRequest):
            return False
            
        # 检查是否至少提供了一种查询方式
        has_query_image_path = request.query_image_path is not None and request.query_image_path.strip()
        has_query_features = request.query_features is not None
        has_query_id = request.query_id is not None
        
        if not (has_query_image_path or has_query_features or has_query_id):
            return False
            
        # 验证top_k参数
        if not isinstance(request.top_k, int) or request.top_k <= 0:
            return False
            
        # 验证相似度阈值（如果存在）
        if hasattr(request, 'similarity_threshold') and request.similarity_threshold is not None:
            if not isinstance(request.similarity_threshold, (int, float)) or not (0.0 <= request.similarity_threshold <= 1.0):
                return False
                
        return True
        
    @log_performance
    def build_search_index(self, performance_monitor, force_rebuild: bool = False) -> bool:
        """构建搜索索引
        
        Args:
            performance_monitor: 性能监控器
            force_rebuild: 是否强制重建
            
        Returns:
            bool: 是否成功构建
        """
        if not self.similarity_calculator:
            raise RuntimeError("相似度计算器未初始化")
            
        try:
            with performance_monitor:
                # 检查是否需要重建
                if not force_rebuild and self.similarity_calculator.has_index():
                    self.logger.info("索引已存在，跳过构建")
                    return True
                    
                # 获取所有特征
                all_features = self.feature_storage.get_all_features()
                if not all_features:
                    self.logger.warning("没有可用的特征数据")
                    return False
                    
                # 构建索引
                feature_matrix = []
                feature_ids = []
                
                from features.utils.validators import validate_features
                # all_features是一个元组列表: [(feature_id, features), ...]
                for feature_id, features in all_features:
                    if validate_features(features):
                        feature_matrix.append(features)
                        feature_ids.append(feature_id)
                        
                if not feature_matrix:
                    self.logger.warning("没有有效的特征数据")
                    return False
                    
                # 将特征矩阵转换为numpy数组
                import numpy as np
                feature_matrix_array = np.array(feature_matrix, dtype=np.float32)
                    
                # 使用相似度计算器构建索引
                success = self.similarity_calculator.build_index(
                    features_matrix=feature_matrix_array,
                    item_ids=feature_ids
                )
                
                if success:
                    self.logger.info(f"索引构建完成，包含 {len(feature_matrix)} 个特征")
                    return True
                else:
                    self.logger.error("索引构建失败")
                    return False
                    
        except Exception as e:
            self.logger.error(f"构建索引时发生错误: {e}")
            return False
            
    def save_index(self, index_path: str) -> bool:
        """保存索引
        
        Args:
            index_path: 索引保存路径
            
        Returns:
            bool: 是否成功保存
        """
        try:
            if not self.similarity_calculator or not self.similarity_calculator.has_index():
                self.logger.warning("没有可保存的索引")
                return False
                
            return self.similarity_calculator.save_index(index_path)
            
        except Exception as e:
            self.logger.error(f"保存索引失败: {e}")
            return False
            
    def load_index(self, index_path: str) -> bool:
        """加载索引
        
        Args:
            index_path: 索引文件路径
            
        Returns:
            bool: 是否成功加载
        """
        try:
            if not self.similarity_calculator:
                self.logger.error("相似度计算器未初始化")
                return False
                
            return self.similarity_calculator.load_index(index_path)
            
        except Exception as e:
            self.logger.error(f"加载索引失败: {e}")
            return False