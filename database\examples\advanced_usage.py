#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模块高级使用示例

该示例展示了如何使用数据库模块进行高级操作，包括：
1. 自定义查询
2. 布料搜索
3. 统计分析
"""

import logging
import sys
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# 导入数据库模块
from database import (
    get_database_manager, 
    close_database_manager,
    get_fabric_repository,
    close_fabric_repository
)


def example_custom_query():
    """自定义查询示例"""
    logger.info("=== 自定义查询示例 ===")
    db_manager = get_database_manager()
    
    # 执行自定义查询
    sql = """
        SELECT 
            id, name, material, color, pattern, 
            width, weight, thickness, 
            created_at, updated_at
        FROM fabric
        WHERE material LIKE ? AND color LIKE ?
        ORDER BY created_at DESC
        LIMIT ?
    """
    
    params = ('%棉%', '%蓝%', 5)  # 查询含棉且颜色含蓝的布料
    
    try:
        results = db_manager.execute_query(sql, params)
        logger.info(f"查询到 {len(results)} 条记录")
        
        for row in results:
            logger.info(f"ID: {row['id']}, 名称: {row.get('name', '未命名')}, "
                       f"材质: {row.get('material', '未知')}, 颜色: {row.get('color', '未知')}")
    except Exception as e:
        logger.error(f"自定义查询失败: {e}")


def example_fabric_search():
    """布料搜索示例"""
    logger.info("\n=== 布料搜索示例 ===")
    fabric_repo = get_fabric_repository()
    
    # 使用搜索仓库进行搜索
    try:
        # 文本搜索
        logger.info("执行文本搜索...")
        text_search_results = fabric_repo.search.search_by_text(
            query="棉",  # 搜索含有"棉"的布料
            fields=["name", "material", "description"],
            limit=5
        )
        logger.info(f"文本搜索结果数量: {len(text_search_results)}")
        
        # 属性搜索
        logger.info("\n执行属性搜索...")
        attribute_search_results = fabric_repo.search.search_by_attributes(
            attributes={
                "material": "棉",
                "color": "蓝"
            },
            limit=5
        )
        logger.info(f"属性搜索结果数量: {len(attribute_search_results)}")
        
        # 显示搜索结果
        if attribute_search_results:
            logger.info("\n搜索结果示例:")
            for fabric in attribute_search_results[:2]:  # 只显示前两条
                logger.info(f"ID: {fabric['id']}, 名称: {fabric.get('name', '未命名')}, "
                           f"材质: {fabric.get('material', '未知')}, 颜色: {fabric.get('color', '未知')}")
    except Exception as e:
        logger.error(f"布料搜索失败: {e}")


def example_statistics():
    """统计分析示例"""
    logger.info("\n=== 统计分析示例 ===")
    fabric_repo = get_fabric_repository()
    
    try:
        # 获取材质分布
        logger.info("获取材质分布...")
        material_stats = fabric_repo.statistics.get_attribute_distribution("material")
        if material_stats:
            logger.info("材质分布:")
            for material, count in material_stats.items():
                logger.info(f"  {material}: {count}件")
        
        # 获取颜色分布
        logger.info("\n获取颜色分布...")
        color_stats = fabric_repo.statistics.get_attribute_distribution("color")
        if color_stats:
            logger.info("颜色分布:")
            for color, count in color_stats.items():
                logger.info(f"  {color}: {count}件")
                
    except Exception as e:
        logger.error(f"统计分析失败: {e}")


def main():
    """主函数"""
    logger.info("开始数据库模块高级使用示例...")
    
    try:
        # 自定义查询示例
        example_custom_query()
        
        # 布料搜索示例
        example_fabric_search()
        
        # 统计分析示例
        example_statistics()
        
    except Exception as e:
        logger.error(f"示例执行异常: {e}")
    finally:
        # 关闭数据库连接
        close_fabric_repository()
        close_database_manager()
        logger.info("\n示例执行完毕，数据库连接已关闭")


if __name__ == "__main__":
    main()