"""进度监控模块

提供批处理任务的进度监控和通知功能。
"""

import threading
import time
from typing import Dict, Any, Callable, Optional, List
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass, field
from enum import Enum

from .batch_task import BatchTask, BatchTaskStatus


class NotificationLevel(Enum):
    """通知级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


@dataclass
class ProgressNotification:
    """进度通知"""
    task_id: str
    level: NotificationLevel
    message: str
    timestamp: datetime = field(default_factory=datetime.now)
    data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'level': self.level.value,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'data': self.data
        }


class ProgressMonitor:
    """进度监控器
    
    提供批处理任务的进度监控功能，包括：
    - 实时进度跟踪
    - 性能统计
    - 通知回调
    - 历史记录
    """
    
    def __init__(self, update_interval: float = 0.2):
        """初始化进度监控器
        
        Args:
            update_interval: 更新间隔（秒）
        """
        self.update_interval = update_interval
        self.logger = logging.getLogger(__name__)
        
        # 监控的任务
        self._tasks: Dict[str, BatchTask] = {}
        self._task_lock = threading.RLock()
        
        # 回调函数
        self._progress_callbacks: List[Callable[[str, Dict[str, Any]], None]] = []
        self._notification_callbacks: List[Callable[[ProgressNotification], None]] = []
        
        # 监控线程
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._running = False
        
        # 历史记录
        self._notifications_history: List[ProgressNotification] = []
        self._max_history_size = 1000
        
        # 性能统计
        self._performance_stats: Dict[str, Any] = {
            'total_tasks_monitored': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'average_processing_rate': 0.0,
            'peak_processing_rate': 0.0
        }
        
    def add_task(self, task: BatchTask) -> None:
        """添加要监控的任务
        
        Args:
            task: 批处理任务
        """
        with self._task_lock:
            self._tasks[task.task_id] = task
            self._performance_stats['total_tasks_monitored'] += 1
            
        self._notify(ProgressNotification(
            task_id=task.task_id,
            level=NotificationLevel.INFO,
            message=f"开始监控任务: {task.name}",
            data={'total_items': task.total_items}
        ))
        
        # 如果监控器未运行，启动它
        if not self._running:
            self.start()
            
    def remove_task(self, task_id: str) -> None:
        """移除监控的任务
        
        Args:
            task_id: 任务ID
        """
        with self._task_lock:
            if task_id in self._tasks:
                task = self._tasks.pop(task_id)
                self._notify(ProgressNotification(
                    task_id=task_id,
                    level=NotificationLevel.INFO,
                    message=f"停止监控任务: {task.name}"
                ))
                
    def get_task_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务进度
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 进度信息
        """
        with self._task_lock:
            if task_id in self._tasks:
                task = self._tasks[task_id]
                task.update_progress()
                return task.get_summary()
            return None
            
    def get_all_tasks_progress(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务的进度
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有任务的进度信息
        """
        progress_data = {}
        with self._task_lock:
            for task_id, task in self._tasks.items():
                task.update_progress()
                progress_data[task_id] = task.get_summary()
        return progress_data
        
    def add_progress_callback(self, callback: Callable[[str, Dict[str, Any]], None]) -> None:
        """添加进度回调函数
        
        Args:
            callback: 回调函数，参数为(task_id, progress_data)
        """
        self._progress_callbacks.append(callback)
        
    def add_notification_callback(self, callback: Callable[[ProgressNotification], None]) -> None:
        """添加通知回调函数
        
        Args:
            callback: 回调函数，参数为ProgressNotification
        """
        self._notification_callbacks.append(callback)
        
    def start(self) -> None:
        """启动监控器"""
        if self._running:
            return
            
        self._running = True
        self._stop_event.clear()
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        self.logger.info("进度监控器已启动")
        
    def stop(self) -> None:
        """停止监控器"""
        if not self._running:
            return
            
        self._running = False
        self._stop_event.set()
        
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5.0)
            
        self.logger.info("进度监控器已停止")
        
    def _monitor_loop(self) -> None:
        """监控循环"""
        last_update_time = time.time()
        
        while self._running and not self._stop_event.is_set():
            try:
                current_time = time.time()
                
                # 检查是否需要更新
                if current_time - last_update_time >= self.update_interval:
                    self._update_tasks()
                    last_update_time = current_time
                    
                # 短暂休眠
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(1.0)
                
    def _update_tasks(self) -> None:
        """更新任务状态"""
        with self._task_lock:
            completed_tasks = []
            
            for task_id, task in self._tasks.items():
                try:
                    # 更新任务进度
                    old_progress = task.progress_percentage
                    task.update_progress()
                    new_progress = task.progress_percentage
                    
                    # 检查进度变化
                    if abs(new_progress - old_progress) >= 0.5:  # 进度变化超过0.5%
                        progress_data = task.get_summary()
                        self._call_progress_callbacks(task_id, progress_data)
                        
                    # 检查任务状态变化
                    if task.status in [BatchTaskStatus.COMPLETED, BatchTaskStatus.FAILED, BatchTaskStatus.CANCELLED]:
                        if task_id not in completed_tasks:
                            completed_tasks.append(task_id)
                            self._handle_task_completion(task)
                            
                    # 更新性能统计
                    self._update_performance_stats(task)
                    
                except Exception as e:
                    self.logger.error(f"更新任务 {task_id} 状态失败: {e}")
                    
            # 移除已完成的任务（可选）
            # for task_id in completed_tasks:
            #     self._tasks.pop(task_id, None)
                
    def _handle_task_completion(self, task: BatchTask) -> None:
        """处理任务完成
        
        Args:
            task: 完成的任务
        """
        if task.status == BatchTaskStatus.COMPLETED:
            self._performance_stats['completed_tasks'] += 1
            level = NotificationLevel.SUCCESS
            message = f"任务完成: {task.name} (成功率: {task.get_success_rate():.1f}%)"
        elif task.status == BatchTaskStatus.FAILED:
            self._performance_stats['failed_tasks'] += 1
            level = NotificationLevel.ERROR
            message = f"任务失败: {task.name} - {task.error_message}"
        else:  # CANCELLED
            level = NotificationLevel.WARNING
            message = f"任务取消: {task.name}"
            
        self._notify(ProgressNotification(
            task_id=task.task_id,
            level=level,
            message=message,
            data=task.get_summary()
        ))
        
    def _update_performance_stats(self, task: BatchTask) -> None:
        """更新性能统计
        
        Args:
            task: 任务对象
        """
        if task.processing_rate > self._performance_stats['peak_processing_rate']:
            self._performance_stats['peak_processing_rate'] = task.processing_rate
            
        # 计算平均处理速率
        total_tasks = self._performance_stats['total_tasks_monitored']
        if total_tasks > 0:
            current_avg = self._performance_stats['average_processing_rate']
            new_avg = (current_avg * (total_tasks - 1) + task.processing_rate) / total_tasks
            self._performance_stats['average_processing_rate'] = new_avg
            
    def _call_progress_callbacks(self, task_id: str, progress_data: Dict[str, Any]) -> None:
        """调用进度回调函数
        
        Args:
            task_id: 任务ID
            progress_data: 进度数据
        """
        for callback in self._progress_callbacks:
            try:
                callback(task_id, progress_data)
            except Exception as e:
                self.logger.error(f"调用进度回调函数失败: {e}")
                
    def _notify(self, notification: ProgressNotification) -> None:
        """发送通知
        
        Args:
            notification: 通知对象
        """
        # 添加到历史记录
        self._notifications_history.append(notification)
        
        # 限制历史记录大小
        if len(self._notifications_history) > self._max_history_size:
            self._notifications_history = self._notifications_history[-self._max_history_size:]
            
        # 调用通知回调函数
        for callback in self._notification_callbacks:
            try:
                callback(notification)
            except Exception as e:
                self.logger.error(f"调用通知回调函数失败: {e}")
                
    def get_notifications_history(self, task_id: Optional[str] = None, 
                                 level: Optional[NotificationLevel] = None,
                                 limit: int = 100) -> List[ProgressNotification]:
        """获取通知历史
        
        Args:
            task_id: 任务ID过滤
            level: 通知级别过滤
            limit: 返回数量限制
            
        Returns:
            List[ProgressNotification]: 通知列表
        """
        notifications = self._notifications_history
        
        # 应用过滤条件
        if task_id:
            notifications = [n for n in notifications if n.task_id == task_id]
        if level:
            notifications = [n for n in notifications if n.level == level]
            
        # 按时间倒序排列并限制数量
        notifications.sort(key=lambda x: x.timestamp, reverse=True)
        return notifications[:limit]
        
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息
        
        Returns:
            Dict[str, Any]: 性能统计
        """
        return self._performance_stats.copy()
        
    def clear_history(self) -> None:
        """清空历史记录"""
        self._notifications_history.clear()
        
    def get_active_tasks_count(self) -> int:
        """获取活跃任务数量
        
        Returns:
            int: 活跃任务数量
        """
        with self._task_lock:
            return len([task for task in self._tasks.values() 
                       if task.status == BatchTaskStatus.RUNNING])
            
    def cleanup(self) -> None:
        """清理资源"""
        self.stop()
        with self._task_lock:
            self._tasks.clear()
        self._progress_callbacks.clear()
        self._notification_callbacks.clear()
        self.clear_history()