#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像工具模块

该模块提供图像处理、转换和操作功能。
"""

import os
import logging
import numpy as np
from pathlib import Path
from typing import List, Optional, Tuple, Union, Dict, Any
from dataclasses import dataclass
from PIL import Image, ImageOps, ImageFilter, ImageEnhance
from PIL.ExifTags import TAGS
import cv2


@dataclass
class ImageInfo:
    """图像信息数据类"""
    path: str
    width: int
    height: int
    channels: int
    format: str
    mode: str
    size_bytes: int
    has_transparency: bool = False
    exif_data: Optional[Dict[str, Any]] = None


class ImageProcessor:
    """图像处理器"""
    
    # 支持的图像格式
    SUPPORTED_FORMATS = {
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', 
        '.tiff', '.tif', '.webp', '.ico'
    }
    
    def __init__(self):
        """初始化图像处理器"""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def is_supported_format(self, path: Union[str, Path]) -> bool:
        """检查是否为支持的图像格式
        
        Args:
            path: 图像路径
            
        Returns:
            bool: 是否支持
        """
        return Path(path).suffix.lower() in self.SUPPORTED_FORMATS
    
    def load_image(self, path: Union[str, Path]) -> Optional[Image.Image]:
        """加载图像
        
        Args:
            path: 图像路径
            
        Returns:
            Optional[Image.Image]: PIL图像对象，如果加载失败则返回None
        """
        try:
            path = Path(path)
            
            if not path.exists():
                self.logger.error(f"图像文件不存在: {path}")
                return None
            
            if not self.is_supported_format(path):
                self.logger.error(f"不支持的图像格式: {path.suffix}")
                return None
            
            image = Image.open(path)
            
            # 处理EXIF方向信息
            image = ImageOps.exif_transpose(image)
            
            self.logger.debug(f"图像加载成功: {path} ({image.size})")
            return image
            
        except Exception as e:
            self.logger.error(f"加载图像失败 {path}: {e}")
            return None
    
    def save_image(self, image: Image.Image, 
                   path: Union[str, Path], 
                   quality: int = 95,
                   optimize: bool = True) -> bool:
        """保存图像
        
        Args:
            image: PIL图像对象
            path: 保存路径
            quality: JPEG质量（1-100）
            optimize: 是否优化
            
        Returns:
            bool: 保存是否成功
        """
        try:
            path = Path(path)
            
            # 确保目录存在
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # 根据文件扩展名确定保存参数
            save_kwargs = {}
            format_name = path.suffix.lower()
            
            if format_name in ['.jpg', '.jpeg']:
                save_kwargs.update({
                    'format': 'JPEG',
                    'quality': quality,
                    'optimize': optimize
                })
                # 确保RGB模式
                if image.mode in ('RGBA', 'LA', 'P'):
                    # 创建白色背景
                    background = Image.new('RGB', image.size, (255, 255, 255))
                    if image.mode == 'P':
                        image = image.convert('RGBA')
                    background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                    image = background
            
            elif format_name == '.png':
                save_kwargs.update({
                    'format': 'PNG',
                    'optimize': optimize
                })
            
            elif format_name == '.webp':
                save_kwargs.update({
                    'format': 'WEBP',
                    'quality': quality,
                    'method': 6
                })
            
            image.save(path, **save_kwargs)
            self.logger.debug(f"图像保存成功: {path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存图像失败 {path}: {e}")
            return False
    
    def get_image_info(self, path: Union[str, Path]) -> Optional[ImageInfo]:
        """获取图像信息
        
        Args:
            path: 图像路径
            
        Returns:
            Optional[ImageInfo]: 图像信息，如果获取失败则返回None
        """
        try:
            path = Path(path)
            image = self.load_image(path)
            
            if image is None:
                return None
            
            # 获取基本信息
            width, height = image.size
            channels = len(image.getbands())
            has_transparency = image.mode in ('RGBA', 'LA') or 'transparency' in image.info
            
            # 获取文件大小
            size_bytes = path.stat().st_size
            
            # 获取EXIF数据
            exif_data = None
            if hasattr(image, '_getexif') and image._getexif():
                exif_dict = image._getexif()
                exif_data = {}
                for tag_id, value in exif_dict.items():
                    tag = TAGS.get(tag_id, tag_id)
                    exif_data[tag] = value
            
            image_info = ImageInfo(
                path=str(path),
                width=width,
                height=height,
                channels=channels,
                format=image.format or path.suffix.upper().lstrip('.'),
                mode=image.mode,
                size_bytes=size_bytes,
                has_transparency=has_transparency,
                exif_data=exif_data
            )
            
            return image_info
            
        except Exception as e:
            self.logger.error(f"获取图像信息失败 {path}: {e}")
            return None
    
    def resize_image(self, image: Image.Image, 
                    size: Tuple[int, int],
                    method: str = 'lanczos',
                    maintain_aspect: bool = True) -> Image.Image:
        """调整图像大小
        
        Args:
            image: PIL图像对象
            size: 目标大小 (width, height)
            method: 重采样方法
            maintain_aspect: 是否保持宽高比
            
        Returns:
            Image.Image: 调整后的图像
        """
        try:
            # 重采样方法映射
            resample_methods = {
                'nearest': Image.NEAREST,
                'bilinear': Image.BILINEAR,
                'bicubic': Image.BICUBIC,
                'lanczos': Image.LANCZOS
            }
            
            resample = resample_methods.get(method.lower(), Image.LANCZOS)
            
            if maintain_aspect:
                # 保持宽高比
                image.thumbnail(size, resample)
                return image
            else:
                # 直接调整到指定大小
                return image.resize(size, resample)
                
        except Exception as e:
            self.logger.error(f"调整图像大小失败: {e}")
            return image
    
    def crop_image(self, image: Image.Image, 
                   box: Tuple[int, int, int, int]) -> Image.Image:
        """裁剪图像
        
        Args:
            image: PIL图像对象
            box: 裁剪区域 (left, top, right, bottom)
            
        Returns:
            Image.Image: 裁剪后的图像
        """
        try:
            return image.crop(box)
        except Exception as e:
            self.logger.error(f"裁剪图像失败: {e}")
            return image
    
    def rotate_image(self, image: Image.Image, 
                    angle: float,
                    expand: bool = True,
                    fillcolor: Tuple[int, int, int] = (255, 255, 255)) -> Image.Image:
        """旋转图像
        
        Args:
            image: PIL图像对象
            angle: 旋转角度（度）
            expand: 是否扩展画布以适应旋转后的图像
            fillcolor: 填充颜色
            
        Returns:
            Image.Image: 旋转后的图像
        """
        try:
            return image.rotate(angle, expand=expand, fillcolor=fillcolor)
        except Exception as e:
            self.logger.error(f"旋转图像失败: {e}")
            return image
    
    def flip_image(self, image: Image.Image, 
                   direction: str = 'horizontal') -> Image.Image:
        """翻转图像
        
        Args:
            image: PIL图像对象
            direction: 翻转方向（'horizontal'或'vertical'）
            
        Returns:
            Image.Image: 翻转后的图像
        """
        try:
            if direction.lower() == 'horizontal':
                return image.transpose(Image.FLIP_LEFT_RIGHT)
            elif direction.lower() == 'vertical':
                return image.transpose(Image.FLIP_TOP_BOTTOM)
            else:
                self.logger.warning(f"未知的翻转方向: {direction}")
                return image
        except Exception as e:
            self.logger.error(f"翻转图像失败: {e}")
            return image
    
    def enhance_image(self, image: Image.Image,
                     brightness: float = 1.0,
                     contrast: float = 1.0,
                     saturation: float = 1.0,
                     sharpness: float = 1.0) -> Image.Image:
        """增强图像
        
        Args:
            image: PIL图像对象
            brightness: 亮度因子（1.0为原始）
            contrast: 对比度因子（1.0为原始）
            saturation: 饱和度因子（1.0为原始）
            sharpness: 锐度因子（1.0为原始）
            
        Returns:
            Image.Image: 增强后的图像
        """
        try:
            enhanced = image
            
            if brightness != 1.0:
                enhancer = ImageEnhance.Brightness(enhanced)
                enhanced = enhancer.enhance(brightness)
            
            if contrast != 1.0:
                enhancer = ImageEnhance.Contrast(enhanced)
                enhanced = enhancer.enhance(contrast)
            
            if saturation != 1.0:
                enhancer = ImageEnhance.Color(enhanced)
                enhanced = enhancer.enhance(saturation)
            
            if sharpness != 1.0:
                enhancer = ImageEnhance.Sharpness(enhanced)
                enhanced = enhancer.enhance(sharpness)
            
            return enhanced
            
        except Exception as e:
            self.logger.error(f"增强图像失败: {e}")
            return image
    
    def apply_filter(self, image: Image.Image, 
                    filter_type: str) -> Image.Image:
        """应用滤镜
        
        Args:
            image: PIL图像对象
            filter_type: 滤镜类型
            
        Returns:
            Image.Image: 应用滤镜后的图像
        """
        try:
            filters = {
                'blur': ImageFilter.BLUR,
                'detail': ImageFilter.DETAIL,
                'edge_enhance': ImageFilter.EDGE_ENHANCE,
                'edge_enhance_more': ImageFilter.EDGE_ENHANCE_MORE,
                'emboss': ImageFilter.EMBOSS,
                'find_edges': ImageFilter.FIND_EDGES,
                'smooth': ImageFilter.SMOOTH,
                'smooth_more': ImageFilter.SMOOTH_MORE,
                'sharpen': ImageFilter.SHARPEN
            }
            
            filter_obj = filters.get(filter_type.lower())
            if filter_obj:
                return image.filter(filter_obj)
            else:
                self.logger.warning(f"未知的滤镜类型: {filter_type}")
                return image
                
        except Exception as e:
            self.logger.error(f"应用滤镜失败: {e}")
            return image
    
    def convert_format(self, image: Image.Image, 
                      target_mode: str = 'RGB') -> Image.Image:
        """转换图像格式
        
        Args:
            image: PIL图像对象
            target_mode: 目标模式（RGB, RGBA, L等）
            
        Returns:
            Image.Image: 转换后的图像
        """
        try:
            if image.mode != target_mode:
                if target_mode == 'RGB' and image.mode in ('RGBA', 'LA', 'P'):
                    # 创建白色背景
                    background = Image.new('RGB', image.size, (255, 255, 255))
                    if image.mode == 'P':
                        image = image.convert('RGBA')
                    if image.mode == 'RGBA':
                        background.paste(image, mask=image.split()[-1])
                    return background
                else:
                    return image.convert(target_mode)
            return image
            
        except Exception as e:
            self.logger.error(f"转换图像格式失败: {e}")
            return image
    
    def create_thumbnail(self, image: Image.Image, 
                        size: Tuple[int, int] = (128, 128)) -> Image.Image:
        """创建缩略图
        
        Args:
            image: PIL图像对象
            size: 缩略图大小
            
        Returns:
            Image.Image: 缩略图
        """
        try:
            thumbnail = image.copy()
            thumbnail.thumbnail(size, Image.LANCZOS)
            return thumbnail
        except Exception as e:
            self.logger.error(f"创建缩略图失败: {e}")
            return image
    
    def to_numpy(self, image: Image.Image) -> np.ndarray:
        """将PIL图像转换为NumPy数组
        
        Args:
            image: PIL图像对象
            
        Returns:
            np.ndarray: NumPy数组
        """
        try:
            return np.array(image)
        except Exception as e:
            self.logger.error(f"转换为NumPy数组失败: {e}")
            return np.array([])
    
    def from_numpy(self, array: np.ndarray, mode: str = 'RGB') -> Image.Image:
        """从NumPy数组创建PIL图像
        
        Args:
            array: NumPy数组
            mode: 图像模式
            
        Returns:
            Image.Image: PIL图像对象
        """
        try:
            return Image.fromarray(array, mode)
        except Exception as e:
            self.logger.error(f"从NumPy数组创建图像失败: {e}")
            return Image.new(mode, (1, 1))
    
    def batch_process(self, input_dir: Union[str, Path],
                     output_dir: Union[str, Path],
                     operations: List[Dict[str, Any]],
                     overwrite: bool = False) -> List[str]:
        """批量处理图像
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            operations: 操作列表
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            List[str]: 处理成功的文件列表
        """
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        processed_files = []
        
        # 获取所有图像文件
        image_files = []
        for ext in self.SUPPORTED_FORMATS:
            image_files.extend(input_dir.glob(f'*{ext}'))
            image_files.extend(input_dir.glob(f'*{ext.upper()}'))
        
        for image_path in image_files:
            try:
                output_path = output_dir / image_path.name
                
                if output_path.exists() and not overwrite:
                    self.logger.info(f"跳过已存在的文件: {output_path}")
                    continue
                
                # 加载图像
                image = self.load_image(image_path)
                if image is None:
                    continue
                
                # 应用操作
                for operation in operations:
                    op_type = operation.get('type')
                    params = operation.get('params', {})
                    
                    if op_type == 'resize':
                        image = self.resize_image(image, **params)
                    elif op_type == 'crop':
                        image = self.crop_image(image, **params)
                    elif op_type == 'rotate':
                        image = self.rotate_image(image, **params)
                    elif op_type == 'flip':
                        image = self.flip_image(image, **params)
                    elif op_type == 'enhance':
                        image = self.enhance_image(image, **params)
                    elif op_type == 'filter':
                        image = self.apply_filter(image, **params)
                    elif op_type == 'convert':
                        image = self.convert_format(image, **params)
                
                # 保存图像
                if self.save_image(image, output_path):
                    processed_files.append(str(image_path))
                    self.logger.info(f"处理完成: {image_path} -> {output_path}")
                
            except Exception as e:
                self.logger.error(f"处理图像失败 {image_path}: {e}")
        
        self.logger.info(f"批量处理完成，共处理 {len(processed_files)} 个文件")
        return processed_files


def calculate_image_similarity(image1: Image.Image, 
                             image2: Image.Image,
                             method: str = 'histogram') -> float:
    """计算图像相似度
    
    Args:
        image1: 第一张图像
        image2: 第二张图像
        method: 计算方法（'histogram', 'mse', 'ssim'）
        
    Returns:
        float: 相似度分数（0-1）
    """
    try:
        if method == 'histogram':
            # 直方图相似度
            hist1 = image1.histogram()
            hist2 = image2.histogram()
            
            # 计算相关系数
            sum1 = sum(hist1)
            sum2 = sum(hist2)
            
            if sum1 == 0 or sum2 == 0:
                return 0.0
            
            # 归一化
            hist1 = [h / sum1 for h in hist1]
            hist2 = [h / sum2 for h in hist2]
            
            # 计算相关系数
            correlation = sum(h1 * h2 for h1, h2 in zip(hist1, hist2))
            return correlation
        
        elif method == 'mse':
            # 均方误差
            arr1 = np.array(image1.convert('RGB'))
            arr2 = np.array(image2.convert('RGB'))
            
            if arr1.shape != arr2.shape:
                # 调整大小
                min_shape = tuple(min(s1, s2) for s1, s2 in zip(arr1.shape[:2], arr2.shape[:2]))
                image1_resized = image1.resize(min_shape[::-1])
                image2_resized = image2.resize(min_shape[::-1])
                arr1 = np.array(image1_resized)
                arr2 = np.array(image2_resized)
            
            mse = np.mean((arr1 - arr2) ** 2)
            # 转换为相似度（0-1）
            similarity = 1.0 / (1.0 + mse / 255.0)
            return similarity
        
        else:
            logging.warning(f"未知的相似度计算方法: {method}")
            return 0.0
            
    except Exception as e:
        logging.error(f"计算图像相似度失败: {e}")
        return 0.0


def get_dominant_colors(image: Image.Image, 
                       num_colors: int = 5) -> List[Tuple[int, int, int]]:
    """获取图像主要颜色
    
    Args:
        image: PIL图像对象
        num_colors: 颜色数量
        
    Returns:
        List[Tuple[int, int, int]]: 主要颜色列表
    """
    try:
        # 转换为RGB模式
        image = image.convert('RGB')
        
        # 缩小图像以提高性能
        image.thumbnail((150, 150))
        
        # 获取颜色
        colors = image.getcolors(maxcolors=256*256*256)
        if colors is None:
            return []
        
        # 按出现频率排序
        colors.sort(key=lambda x: x[0], reverse=True)
        
        # 返回前N个颜色
        return [color[1] for color in colors[:num_colors]]
        
    except Exception as e:
        logging.error(f"获取主要颜色失败: {e}")
        return []


def extract_shape_features(image: Image.Image) -> Dict[str, float]:
    """提取图像形状特征
    
    Args:
        image: PIL图像对象
        
    Returns:
        Dict[str, float]: 形状特征字典
    """
    try:
        # 转换为灰度图像
        gray_image = image.convert('L')
        gray_array = np.array(gray_image)
        
        # 二值化
        _, binary = cv2.threshold(gray_array, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return {
                'area': 0.0,
                'perimeter': 0.0,
                'circularity': 0.0,
                'aspect_ratio': 1.0,
                'extent': 0.0,
                'solidity': 0.0,
                'compactness': 0.0,
                'eccentricity': 0.0
            }
        
        # 选择最大轮廓
        largest_contour = max(contours, key=cv2.contourArea)
        
        # 计算基本几何特征
        area = cv2.contourArea(largest_contour)
        perimeter = cv2.arcLength(largest_contour, True)
        
        # 计算边界矩形
        x, y, w, h = cv2.boundingRect(largest_contour)
        rect_area = w * h
        aspect_ratio = float(w) / h if h > 0 else 1.0
        
        # 计算凸包
        hull = cv2.convexHull(largest_contour)
        hull_area = cv2.contourArea(hull)
        
        # 计算各种形状特征
        features = {}
        
        # 面积
        features['area'] = area
        
        # 周长
        features['perimeter'] = perimeter
        
        # 圆形度 (4π*面积/周长²)
        features['circularity'] = (4 * np.pi * area) / (perimeter * perimeter) if perimeter > 0 else 0.0
        
        # 长宽比
        features['aspect_ratio'] = aspect_ratio
        
        # 范围 (轮廓面积/边界矩形面积)
        features['extent'] = area / rect_area if rect_area > 0 else 0.0
        
        # 实心度 (轮廓面积/凸包面积)
        features['solidity'] = area / hull_area if hull_area > 0 else 0.0
        
        # 紧密度 (周长²/面积)
        features['compactness'] = (perimeter * perimeter) / area if area > 0 else 0.0
        
        # 偏心率
        if len(largest_contour) >= 5:
            ellipse = cv2.fitEllipse(largest_contour)
            (center, axes, orientation) = ellipse
            major_axis = max(axes)
            minor_axis = min(axes)
            features['eccentricity'] = np.sqrt(1 - (minor_axis / major_axis) ** 2) if major_axis > 0 else 0.0
        else:
            features['eccentricity'] = 0.0
        
        return features
        
    except Exception as e:
        logging.error(f"提取形状特征失败: {e}")
        return {
            'area': 0.0,
            'perimeter': 0.0,
            'circularity': 0.0,
            'aspect_ratio': 1.0,
            'extent': 0.0,
            'solidity': 0.0,
            'compactness': 0.0,
            'eccentricity': 0.0
        }


def extract_contour_features(image: Image.Image) -> Dict[str, Any]:
    """提取轮廓特征
    
    Args:
        image: PIL图像对象
        
    Returns:
        Dict[str, Any]: 轮廓特征字典
    """
    try:
        # 转换为灰度图像
        gray_image = image.convert('L')
        gray_array = np.array(gray_image)
        
        # 边缘检测
        edges = cv2.Canny(gray_array, 50, 150)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return {
                'contour_count': 0,
                'total_contour_length': 0.0,
                'avg_contour_length': 0.0,
                'contour_complexity': 0.0,
                'largest_contour_area': 0.0
            }
        
        # 计算轮廓特征
        contour_lengths = [cv2.arcLength(contour, True) for contour in contours]
        contour_areas = [cv2.contourArea(contour) for contour in contours]
        
        features = {
            'contour_count': len(contours),
            'total_contour_length': sum(contour_lengths),
            'avg_contour_length': np.mean(contour_lengths) if contour_lengths else 0.0,
            'contour_complexity': np.std(contour_lengths) if len(contour_lengths) > 1 else 0.0,
            'largest_contour_area': max(contour_areas) if contour_areas else 0.0
        }
        
        return features
        
    except Exception as e:
        logging.error(f"提取轮廓特征失败: {e}")
        return {
            'contour_count': 0,
            'total_contour_length': 0.0,
            'avg_contour_length': 0.0,
            'contour_complexity': 0.0,
            'largest_contour_area': 0.0
        }


def extract_texture_features(image: Image.Image) -> Dict[str, float]:
    """提取纹理特征
    
    Args:
        image: PIL图像对象
        
    Returns:
        Dict[str, float]: 纹理特征字典
    """
    try:
        # 转换为灰度图像
        gray_image = image.convert('L')
        gray_array = np.array(gray_image)
        
        # 计算灰度共生矩阵 (GLCM)
        from skimage.feature import graycomatrix, graycoprops
        
        # 计算不同方向和距离的GLCM
        distances = [1, 2, 3]
        angles = [0, 45, 90, 135]
        
        # 归一化灰度值到0-255
        gray_normalized = ((gray_array - gray_array.min()) / 
                          (gray_array.max() - gray_array.min()) * 255).astype(np.uint8)
        
        # 计算GLCM
        glcm = graycomatrix(gray_normalized, distances, np.radians(angles), 
                           levels=256, symmetric=True, normed=True)
        
        # 计算纹理特征
        features = {}
        
        # 对比度
        contrast = graycoprops(glcm, 'contrast')
        features['contrast'] = np.mean(contrast)
        
        # 相关性
        correlation = graycoprops(glcm, 'correlation')
        features['correlation'] = np.mean(correlation)
        
        # 能量
        energy = graycoprops(glcm, 'energy')
        features['energy'] = np.mean(energy)
        
        # 同质性
        homogeneity = graycoprops(glcm, 'homogeneity')
        features['homogeneity'] = np.mean(homogeneity)
        
        # 局部二值模式 (LBP)
        from skimage.feature import local_binary_pattern
        
        radius = 3
        n_points = 8 * radius
        lbp = local_binary_pattern(gray_array, n_points, radius, method='uniform')
        
        # LBP直方图
        lbp_hist, _ = np.histogram(lbp.ravel(), bins=n_points + 2, 
                                  range=(0, n_points + 2), density=True)
        
        # LBP特征统计
        features['lbp_uniformity'] = np.sum(lbp_hist ** 2)
        features['lbp_entropy'] = -np.sum(lbp_hist * np.log2(lbp_hist + 1e-10))
        
        # Gabor滤波器特征
        from skimage.filters import gabor
        
        gabor_responses = []
        for theta in range(0, 180, 30):
            for frequency in [0.1, 0.3, 0.5]:
                real, _ = gabor(gray_array, frequency=frequency, 
                               theta=np.radians(theta))
                gabor_responses.append(real)
        
        # Gabor特征统计
        gabor_mean = np.mean([np.mean(response) for response in gabor_responses])
        gabor_std = np.mean([np.std(response) for response in gabor_responses])
        
        features['gabor_mean'] = gabor_mean
        features['gabor_std'] = gabor_std
        
        return features
        
    except Exception as e:
        logging.error(f"提取纹理特征失败: {e}")
        return {
            'contrast': 0.0,
            'correlation': 0.0,
            'energy': 0.0,
            'homogeneity': 0.0,
            'lbp_uniformity': 0.0,
            'lbp_entropy': 0.0,
            'gabor_mean': 0.0,
            'gabor_std': 0.0
        }


def extract_color_features(image: Image.Image) -> Dict[str, Any]:
    """提取颜色特征
    
    Args:
        image: PIL图像对象
        
    Returns:
        Dict[str, Any]: 颜色特征字典
    """
    try:
        # 转换为RGB模式
        rgb_image = image.convert('RGB')
        rgb_array = np.array(rgb_image)
        
        # 转换为不同颜色空间
        hsv_array = cv2.cvtColor(rgb_array, cv2.COLOR_RGB2HSV)
        lab_array = cv2.cvtColor(rgb_array, cv2.COLOR_RGB2LAB)
        
        features = {}
        
        # RGB直方图
        rgb_hist = []
        for i in range(3):
            hist, _ = np.histogram(rgb_array[:, :, i], bins=256, range=(0, 256), density=True)
            rgb_hist.extend(hist)
        features['rgb_histogram'] = rgb_hist
        
        # HSV直方图
        hsv_hist = []
        for i in range(3):
            if i == 0:  # H通道
                hist, _ = np.histogram(hsv_array[:, :, i], bins=180, range=(0, 180), density=True)
            else:  # S, V通道
                hist, _ = np.histogram(hsv_array[:, :, i], bins=256, range=(0, 256), density=True)
            hsv_hist.extend(hist)
        features['hsv_histogram'] = hsv_hist
        
        # 颜色矩
        for i, channel in enumerate(['r', 'g', 'b']):
            channel_data = rgb_array[:, :, i].flatten()
            features[f'{channel}_mean'] = np.mean(channel_data)
            features[f'{channel}_std'] = np.std(channel_data)
            # 计算偏度和峰度
        mean_val = np.mean(channel_data)
        std_val = np.std(channel_data)
        normalized_data = (channel_data - mean_val) / std_val
        
        features[f'{channel}_skewness'] = float(np.mean(normalized_data ** 3))
        features[f'{channel}_kurtosis'] = float(np.mean(normalized_data ** 4))
        
        # 主要颜色
        dominant_colors = get_dominant_colors(image, num_colors=5)
        features['dominant_colors'] = dominant_colors
        
        # 颜色丰富度
        unique_colors = len(np.unique(rgb_array.reshape(-1, 3), axis=0))
        total_pixels = rgb_array.shape[0] * rgb_array.shape[1]
        features['color_richness'] = unique_colors / total_pixels
        
        return features
        
    except Exception as e:
        logging.error(f"提取颜色特征失败: {e}")
        return {}


# 全局图像处理器实例
image_processor = ImageProcessor()