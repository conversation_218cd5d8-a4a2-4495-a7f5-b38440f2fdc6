#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像处理工具模块

该模块提供图像加载、预处理和验证等功能。
"""

import os
import logging
from typing import Optional, Tuple, Dict, Any
from PIL import Image, ImageOps
import numpy as np
import torch
from torchvision import transforms

logger = logging.getLogger(__name__)


def validate_image_path(image_path: str) -> bool:
    """验证图像路径
    
    Args:
        image_path: 图像路径
        
    Returns:
        bool: 路径是否有效
    """
    if not image_path:
        return False
    
    if not os.path.exists(image_path):
        return False
    
    if not os.path.isfile(image_path):
        return False
    
    # 检查文件扩展名
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
    _, ext = os.path.splitext(image_path.lower())
    return ext in valid_extensions


def load_image(image_path: str, convert_rgb: bool = True) -> Optional[Image.Image]:
    """加载图像
    
    Args:
        image_path: 图像路径
        convert_rgb: 是否转换为RGB模式
        
    Returns:
        Optional[Image.Image]: 加载的图像，失败时返回None
    """
    try:
        if not validate_image_path(image_path):
            logger.error(f"Invalid image path: {image_path}")
            return None
        
        image = Image.open(image_path)
        
        if convert_rgb and image.mode != 'RGB':
            image = image.convert('RGB')
        
        return image
        
    except Exception as e:
        logger.error(f"Error loading image {image_path}: {str(e)}")
        return None


def get_image_info(image_path: str) -> Dict[str, Any]:
    """获取图像信息
    
    Args:
        image_path: 图像路径
        
    Returns:
        Dict[str, Any]: 图像信息
    """
    info = {
        'path': image_path,
        'exists': False,
        'valid': False,
        'size': None,
        'mode': None,
        'format': None,
        'file_size': None
    }
    
    try:
        if not os.path.exists(image_path):
            return info
        
        info['exists'] = True
        info['file_size'] = os.path.getsize(image_path)
        
        if not validate_image_path(image_path):
            return info
        
        with Image.open(image_path) as image:
            info['valid'] = True
            info['size'] = image.size
            info['mode'] = image.mode
            info['format'] = image.format
            
    except Exception as e:
        logger.error(f"Error getting image info for {image_path}: {str(e)}")
    
    return info


def resize_image(image: Image.Image, size: Tuple[int, int], 
                method: str = 'lanczos') -> Image.Image:
    """调整图像大小
    
    Args:
        image: 输入图像
        size: 目标尺寸 (width, height)
        method: 重采样方法
        
    Returns:
        Image.Image: 调整后的图像
    """
    resample_methods = {
        'lanczos': Image.Resampling.LANCZOS,
        'bilinear': Image.Resampling.BILINEAR,
        'bicubic': Image.Resampling.BICUBIC,
        'nearest': Image.Resampling.NEAREST
    }
    
    resample = resample_methods.get(method.lower(), Image.Resampling.LANCZOS)
    return image.resize(size, resample)


def normalize_image(image: Image.Image, mean: Tuple[float, float, float] = None,
                   std: Tuple[float, float, float] = None) -> torch.Tensor:
    """归一化图像
    
    Args:
        image: 输入图像
        mean: 均值
        std: 标准差
        
    Returns:
        torch.Tensor: 归一化后的张量
    """
    if mean is None:
        mean = (0.485, 0.456, 0.406)  # ImageNet默认值
    if std is None:
        std = (0.229, 0.224, 0.225)   # ImageNet默认值
    
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=mean, std=std)
    ])
    
    return transform(image)


def preprocess_image(image: Image.Image, size: int = 224, 
                    mean: Tuple[float, float, float] = None,
                    std: Tuple[float, float, float] = None,
                    center_crop: bool = True) -> torch.Tensor:
    """预处理图像
    
    Args:
        image: 输入图像
        size: 目标尺寸
        mean: 归一化均值
        std: 归一化标准差
        center_crop: 是否中心裁剪
        
    Returns:
        torch.Tensor: 预处理后的张量
    """
    if mean is None:
        mean = (0.485, 0.456, 0.406)
    if std is None:
        std = (0.229, 0.224, 0.225)
    
    transform_list = []
    
    # 调整大小
    if center_crop:
        transform_list.extend([
            transforms.Resize(int(size * 1.14)),  # 稍微大一点用于裁剪
            transforms.CenterCrop(size)
        ])
    else:
        transform_list.append(transforms.Resize((size, size)))
    
    # 转换为张量并归一化
    transform_list.extend([
        transforms.ToTensor(),
        transforms.Normalize(mean=mean, std=std)
    ])
    
    transform = transforms.Compose(transform_list)
    return transform(image)


def create_default_transforms(input_size: int = 224) -> transforms.Compose:
    """创建默认的图像变换
    
    Args:
        input_size: 输入尺寸
        
    Returns:
        transforms.Compose: 变换组合
    """
    return transforms.Compose([
        transforms.Resize(int(input_size * 1.14)),
        transforms.CenterCrop(input_size),
        transforms.ToTensor(),
        transforms.Normalize(
            mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225]
        )
    ])


def apply_transforms(image: Image.Image, transform: transforms.Compose) -> torch.Tensor:
    """应用图像变换
    
    Args:
        image: 输入图像
        transform: 变换组合
        
    Returns:
        torch.Tensor: 变换后的张量
    """
    try:
        return transform(image)
    except Exception as e:
        logger.error(f"Error applying transforms: {str(e)}")
        # 回退到默认变换
        default_transform = create_default_transforms()
        return default_transform(image)


def batch_load_images(image_paths: list, convert_rgb: bool = True) -> list:
    """批量加载图像
    
    Args:
        image_paths: 图像路径列表
        convert_rgb: 是否转换为RGB模式
        
    Returns:
        list: 加载的图像列表
    """
    images = []
    for path in image_paths:
        image = load_image(path, convert_rgb)
        if image is not None:
            images.append(image)
        else:
            logger.warning(f"Failed to load image: {path}")
    
    return images