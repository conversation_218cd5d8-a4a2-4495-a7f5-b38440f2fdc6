#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件处理器

该模块负责处理各种UI事件和用户交互。
"""

from typing import Optional, Dict, Any, List
from PyQt6.QtCore import QObject, pyqtSignal, QModelIndex
from PyQt6.QtWidgets import QMainWindow, QMessageBox, QListWidget, QListWidgetItem
from PyQt6.QtGui import QCloseEvent, QResizeEvent, QMoveEvent

from utils.logger_mixin import LoggerMixin
from utils.task_manager import TaskManager
from gui.dialogs import SettingsDialog, SettingsData
from gui.model_selector_dialog import ModelSelectorDialog
from gui.dialogs import AboutDialog
from gui.dialogs.task_manager_dialog import TaskManagerDialog
from gui.image_compare_panel import ImageComparePanel


class EventHandler(QObject, LoggerMixin):
    """事件处理器"""
    
    # 信号
    itemSelected = pyqtSignal(str)  # 选中的项目路径
    itemDoubleClicked = pyqtSignal(str)  # 双击的项目路径
    imageComparisonRequested = pyqtSignal(str, str)  # 图像对比请求
    settingsApplied = pyqtSignal(dict)  # 设置已应用
    modelChanged = pyqtSignal(str)  # 模型已更改
    
    def __init__(self, main_window: QMainWindow):
        super().__init__()
        self.main_window = main_window
        
        # 对话框实例
        self.settings_dialog: Optional[SettingsDialog] = None
        self.model_selector_dialog: Optional[ModelSelectorDialog] = None
        self.about_dialog: Optional[AboutDialog] = None
        self.task_manager_dialog: Optional[TaskManagerDialog] = None
        self.image_comparison_panel: Optional[ImageComparePanel] = None
        
        # 当前选中的项目
        self.current_selected_item: Optional[str] = None
        
    def show_settings_dialog(self, current_settings: Dict[str, Any] = None):
        """
        显示设置对话框
        
        Args:
            current_settings: 当前设置字典
        """
        try:
            # 如果对话框已经存在，则关闭它
            if self.settings_dialog:
                self.settings_dialog.close()
                self.settings_dialog = None
            
            # 创建新的设置对话框
            self.settings_dialog = SettingsDialog(self.main_window)
            
            # 如果提供了当前设置，则设置到对话框
            if current_settings:
                # 将嵌套结构的配置字典转换为扁平结构
                flattened_settings = self._flatten_settings(current_settings)
                # 创建SettingsData对象并设置
                settings_data = SettingsData.from_dict(flattened_settings)
                self.settings_dialog.set_settings_data(settings_data)
            
            # 连接信号
            self.settings_dialog.settingsChanged.connect(self._on_settings_applied)
            
            # 显示对话框
            self.settings_dialog.exec()
            
            self.logger.info("设置对话框已显示")
            
        except Exception as e:
            self.logger.error(f"显示设置对话框失败: {e}")
    
    def _flatten_settings(self, config_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        将嵌套结构的配置字典转换为扁平结构
        
        Args:
            config_dict: 嵌套结构的配置字典
            
        Returns:
            Dict[str, Any]: 扁平结构的设置字典
        """
        settings = {}
        
        # 提取UI设置
        if 'ui' in config_dict:
            ui_config = config_dict['ui']
            if 'theme' in ui_config:
                settings['theme'] = ui_config['theme']
            if 'font_family' in ui_config:
                settings['font_family'] = ui_config['font_family']
            if 'font_size' in ui_config:
                settings['font_size'] = ui_config['font_size']
            if 'language' in ui_config:
                settings['language'] = ui_config['language']
            if 'animation_enabled' in ui_config:
                settings['animation_enabled'] = ui_config['animation_enabled']
            if 'show_tooltips' in ui_config:
                settings['show_tooltips'] = ui_config['show_tooltips']
        
        # 提取搜索设置
        if 'search' in config_dict:
            search_config = config_dict['search']
            if 'similarity_threshold' in search_config:
                settings['default_similarity_threshold'] = search_config['similarity_threshold']
            if 'max_results' in search_config:
                settings['max_search_results'] = search_config['max_results']
            if 'search_timeout' in search_config:
                settings['search_timeout'] = search_config['search_timeout']
            if 'enable_history' in search_config:
                settings['enable_search_history'] = search_config['enable_history']
            if 'max_history_items' in search_config:
                settings['max_history_items'] = search_config['max_history_items']
            if 'cache_size' in search_config:
                settings['cache_size'] = search_config['cache_size']
        
        # 提取数据库设置
        if 'database' in config_dict:
            db_config = config_dict['database']
            if 'auto_backup' in db_config:
                settings['backup_enabled'] = db_config['auto_backup']
            if 'backup_interval' in db_config:
                settings['backup_interval'] = db_config['backup_interval']
        
        # 提取窗口设置
        if 'window' in config_dict:
            window_config = config_dict['window']
            if 'toolbar_visible' in window_config:
                settings['toolbar_visible'] = window_config['toolbar_visible']
            if 'statusbar_visible' in window_config:
                settings['statusbar_visible'] = window_config['statusbar_visible']
        
        # 添加其他设置
        for key in config_dict:
            if key not in ['ui', 'search', 'database', 'window']:
                settings[key] = config_dict[key]
        
        return settings
    
    def _on_settings_applied(self, settings: Dict[str, Any]):
        """
        当设置被应用时的处理函数
        
        Args:
            settings: 应用的设置字典
        """
        try:
            self.settingsApplied.emit(settings)
            self.logger.info("设置已应用")
            
        except Exception as e:
            self.logger.error(f"应用设置失败: {e}")
    
    def show_model_selector_dialog(self, current_model: str = None):
        """
        显示模型选择器对话框
        
        Args:
            current_model: 当前选中的模型名称
        """
        try:
            # 如果对话框已经存在，则关闭它
            if self.model_selector_dialog:
                self.model_selector_dialog.close()
                self.model_selector_dialog = None
            
            # 创建新的模型选择器对话框
            self.model_selector_dialog = ModelSelectorDialog(self.main_window)
            
            # 如果提供了当前模型，则设置到对话框
            if current_model:
                self.model_selector_dialog.set_current_model(current_model)
            
            # 连接信号
            self.model_selector_dialog.modelSelected.connect(self._on_model_selected)
            
            # 显示对话框
            self.model_selector_dialog.exec()
            
            self.logger.info("模型选择器对话框已显示")
            
        except Exception as e:
            self.logger.error(f"显示模型选择器对话框失败: {e}")
    
    def _on_model_selected(self, model_name: str):
        """
        当模型被选中时的处理函数
        
        Args:
            model_name: 选中的模型名称
        """
        try:
            self.modelChanged.emit(model_name)
            self.logger.info(f"模型已更改为: {model_name}")
            
        except Exception as e:
            self.logger.error(f"更改模型失败: {e}")
    
    def show_about_dialog(self):
        """显示关于对话框"""
        try:
            # 如果对话框已经存在，则关闭它
            if self.about_dialog:
                self.about_dialog.close()
                self.about_dialog = None
            
            # 创建新的关于对话框
            self.about_dialog = AboutDialog(self.main_window)
            
            # 显示对话框
            self.about_dialog.exec()
            
            self.logger.info("关于对话框已显示")
            
        except Exception as e:
            self.logger.error(f"显示关于对话框失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            # 关闭所有对话框
            if self.settings_dialog:
                self.settings_dialog.close()
                self.settings_dialog = None
            
            if self.model_selector_dialog:
                self.model_selector_dialog.close()
                self.model_selector_dialog = None
            
            if self.about_dialog:
                self.about_dialog.close()
                self.about_dialog = None
            
            if hasattr(self, 'task_manager_dialog') and self.task_manager_dialog:
                self.task_manager_dialog.close()
                self.task_manager_dialog = None
            
            if self.image_comparison_panel:
                self.image_comparison_panel.close()
                self.image_comparison_panel = None
            
            # 注意：event_handler只负责清理自己的资源，不调用其他组件的cleanup方法
            # 其他组件由ComponentManager统一管理和清理
            
            self.logger.info("事件处理器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理事件处理器资源失败: {e}")
    
    def show_task_manager_dialog(self):
        """显示任务管理器对话框"""
        try:
            # 如果对话框已经存在，则关闭它
            if self.task_manager_dialog:
                self.task_manager_dialog.close()
                self.task_manager_dialog = None
            
            # 获取任务管理器实例
            task_manager = self.main_window.task_manager
            if not task_manager:
                self.logger.error("任务管理器实例不存在")
                return
            
            # 创建新的任务管理器对话框
            self.task_manager_dialog = TaskManagerDialog(task_manager, self.main_window)
            
            # 显示对话框
            self.task_manager_dialog.exec()
            
            self.logger.info("任务管理器对话框已显示")
            
        except Exception as e:
            self.logger.error(f"显示任务管理器对话框失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
    
    def handle_item_selection(self, item: QListWidgetItem):
        """
        处理项目选择
        
        Args:
            item: 选中的列表项
        """
        try:
            if item and hasattr(item, 'data'):
                item_data = item.data(0)  # 获取项目数据
                if isinstance(item_data, dict) and 'path' in item_data:
                    path = item_data['path']
                    self.current_selected_item = path
                    self.itemSelected.emit(path)
                    self.logger.debug(f"选中项目: {path}")
            
        except Exception as e:
            self.logger.error(f"处理项目选择失败: {e}")
    
    def handle_item_double_click(self, item: QListWidgetItem):
        """
        处理项目双击
        
        Args:
            item: 双击的列表项
        """
        try:
            if item and hasattr(item, 'data'):
                item_data = item.data(0)
                if isinstance(item_data, dict) and 'path' in item_data:
                    path = item_data['path']
                    self.itemDoubleClicked.emit(path)
                    self.logger.debug(f"双击项目: {path}")
            
        except Exception as e:
            self.logger.error(f"处理项目双击失败: {e}")
    
    def compare_images(self, image1_path: str, image2_path: Optional[str] = None):
        """
        打开图像对比面板
        
        Args:
            image1_path: 第一个图像路径
            image2_path: 第二个图像路径（可选）
        """
        try:
            if not self.image_comparison_panel:
                self.image_comparison_panel = ImageComparePanel()
            
            # 设置图像
            self.image_comparison_panel.set_images(image1_path, image2_path)
            
            # 显示面板
            self.image_comparison_panel.show()
            self.image_comparison_panel.raise_()
            self.image_comparison_panel.activateWindow()
            
            self.logger.info(f"打开图像对比面板: {image1_path} vs {image2_path}")
            
        except Exception as e:
            self.logger.error(f"打开图像对比面板失败: {e}")
    
    def handle_close_event(self, event: QCloseEvent) -> bool:
        """
        处理窗口关闭事件
        
        Args:
            event: 关闭事件
            
        Returns:
            bool: 是否允许关闭
        """
        try:
            # 显示确认对话框
            reply = QMessageBox.question(
                self.main_window,
                "确认退出",
                "确定要退出 Fabric Search 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.logger.info("用户确认退出应用程序")
                event.accept()
                return True
            else:
                self.logger.info("用户取消退出")
                event.ignore()
                return False
                
        except Exception as e:
            self.logger.error(f"处理关闭事件失败: {e}")
            event.accept()
            return True
    
    def handle_resize_event(self, event: QResizeEvent):
        """
        处理窗口大小改变事件
        
        Args:
            event: 大小改变事件
        """
        try:
            new_size = event.size()
            self.logger.debug(f"窗口大小改变: {new_size.width()}x{new_size.height()}")
            
            # 这里可以添加响应窗口大小改变的逻辑
            # 例如调整布局、更新UI元素等
            
        except Exception as e:
            self.logger.error(f"处理窗口大小改变事件失败: {e}")
    
    def handle_move_event(self, event: QMoveEvent):
        """
        处理窗口移动事件
        
        Args:
            event: 移动事件
        """
        try:
            new_pos = event.pos()
            self.logger.debug(f"窗口位置改变: ({new_pos.x()}, {new_pos.y()})")
            
            # 这里可以添加响应窗口位置改变的逻辑
            
        except Exception as e:
            self.logger.error(f"处理窗口移动事件失败: {e}")
    
    def show_error_message(self, title: str, message: str):
        """
        显示错误消息
        
        Args:
            title: 错误标题
            message: 错误消息
        """
        try:
            QMessageBox.critical(self.main_window, title, message)
            self.logger.error(f"显示错误消息: {title} - {message}")
            
        except Exception as e:
            self.logger.error(f"显示错误消息失败: {e}")
    
    def show_warning_message(self, title: str, message: str):
        """
        显示警告消息
        
        Args:
            title: 警告标题
            message: 警告消息
        """
        try:
            QMessageBox.warning(self.main_window, title, message)
            self.logger.warning(f"显示警告消息: {title} - {message}")
            
        except Exception as e:
            self.logger.error(f"显示警告消息失败: {e}")
    
    def show_info_message(self, title: str, message: str):
        """
        显示信息消息
        
        Args:
            title: 信息标题
            message: 信息消息
        """
        try:
            QMessageBox.information(self.main_window, title, message)
            self.logger.info(f"显示信息消息: {title} - {message}")
            
        except Exception as e:
            self.logger.error(f"显示信息消息失败: {e}")
    
    def confirm_action(self, title: str, message: str) -> bool:
        """
        确认操作
        
        Args:
            title: 确认标题
            message: 确认消息
            
        Returns:
            bool: 用户是否确认
        """
        try:
            reply = QMessageBox.question(
                self.main_window,
                title,
                message,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            confirmed = reply == QMessageBox.StandardButton.Yes
            self.logger.info(f"用户确认操作 '{title}': {confirmed}")
            return confirmed
            
        except Exception as e:
            self.logger.error(f"确认操作失败: {e}")
            return False
    
    def update_image_loading_status(self, loading: bool, message: str = ""):
        """
        更新图像加载状态
        
        Args:
            loading: 是否正在加载
            message: 状态消息
        """
        try:
            # 这里可以更新状态栏或显示加载指示器
            if loading:
                self.logger.debug(f"开始加载图像: {message}")
            else:
                self.logger.debug(f"图像加载完成: {message}")
            
        except Exception as e:
            self.logger.error(f"更新图像加载状态失败: {e}")
