#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库内容的脚本
"""

import sqlite3
import os
import json
import pickle
import numpy as np

def check_database():
    """检查数据库内容"""
    db_path = 'data/fabric_search.db'
    
    if not os.path.exists(db_path):
        print('数据库文件不存在')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print('数据库表:')
        for table in tables:
            print(f'  - {table[0]}')
        
        # 查看fabric_images表
        if any('fabric_images' in table for table in tables):
            print('\n=== fabric_images表 ===')
            cursor.execute('PRAGMA table_info(fabric_images);')
            columns = cursor.fetchall()
            print('表结构:')
            for col in columns:
                print(f'  {col[1]} ({col[2]})')
            
            cursor.execute('SELECT COUNT(*) FROM fabric_images;')
            count = cursor.fetchone()[0]
            print(f'\n总记录数: {count}')
            
            if count > 0:
                print('\n数据示例:')
                cursor.execute('SELECT id, file_path, LENGTH(features), created_at FROM fabric_images LIMIT 5;')
                rows = cursor.fetchall()
                for row in rows:
                    print(f'  ID: {row[0]}, Path: {row[1]}, Features length: {row[2]}, Created: {row[3]}')
        
        # 查看image_features表
        if any('image_features' in table for table in tables):
            print('\n=== image_features表 ===')
            cursor.execute('PRAGMA table_info(image_features);')
            columns = cursor.fetchall()
            print('表结构:')
            for col in columns:
                print(f'  {col[1]} ({col[2]})')

            cursor.execute('SELECT COUNT(*) FROM image_features;')
            count = cursor.fetchone()[0]
            print(f'\n总记录数: {count}')

            if count > 0:
                print('\n特征类型统计:')
                cursor.execute('SELECT feature_type, COUNT(*) FROM image_features GROUP BY feature_type;')
                rows = cursor.fetchall()
                for row in rows:
                    print(f'  {row[0]}: {row[1]} 条记录')

                print('\n数据示例:')
                cursor.execute('SELECT image_id, feature_type, LENGTH(feature_data) FROM image_features LIMIT 10;')
                rows = cursor.fetchall()
                for row in rows:
                    print(f'  Image ID: {row[0]}, Type: {row[1]}, Data length: {row[2]} bytes')
        
        # 查看search_history表
        if any('search_history' in table for table in tables):
            print('\n=== search_history表 ===')
            cursor.execute('PRAGMA table_info(search_history);')
            columns = cursor.fetchall()
            print('表结构:')
            for col in columns:
                print(f'  {col[1]} ({col[2]})')

            cursor.execute('SELECT COUNT(*) FROM search_history;')
            count = cursor.fetchone()[0]
            print(f'\n总记录数: {count}')

            if count > 0:
                print('\n最近搜索记录:')
                cursor.execute('SELECT * FROM search_history ORDER BY created_at DESC LIMIT 3;')
                rows = cursor.fetchall()
                for i, row in enumerate(rows):
                    print(f'  记录 {i+1}: {row}')
        
        conn.close()
        
    except Exception as e:
        print(f'检查数据库失败: {e}')

if __name__ == '__main__':
    check_database()
