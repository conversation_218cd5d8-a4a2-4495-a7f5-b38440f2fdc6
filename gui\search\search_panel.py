#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索面板主模块

该模块整合所有搜索组件，提供统一的搜索界面。
"""

from typing import Dict, Any, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QStackedWidget,
    QButtonGroup, QRadioButton
)
from PyQt6.QtCore import pyqtSignal

from utils.log_utils import LoggerMixin
from gui.helpers.message_helper import MessageHelper
from gui.widget_factory import WidgetFactory

from .models import SearchMode, SearchConfig
from .similarity_widget import SimilaritySearchWidget
from .text_widget import TextSearchWidget
from .category_widget import CategorySearchWidget
from .advanced_widget import AdvancedSearchWidget


class SearchPanel(QWidget, LoggerMixin):
    """搜索面板主类"""
    
    # 信号
    searchRequested = pyqtSignal(str, dict)  # 通用搜索请求
    searchStopped = pyqtSignal()  # 停止搜索请求
    
    similaritySearchRequested = pyqtSignal(str, dict)  # 相似度搜索请求
    textSearchRequested = pyqtSignal(str, dict)  # 文本搜索请求
    categorySearchRequested = pyqtSignal(str, dict)  # 类别搜索请求
    advancedSearchRequested = pyqtSignal(str, dict)  # 高级搜索请求
    
    searchModeChanged = pyqtSignal(str)  # 搜索模式变化
    imageLoaded = pyqtSignal(str)  # 图像加载
    categoryRefreshRequested = pyqtSignal()  # 类别刷新请求
    folderPathSet = pyqtSignal(str)  # 文件夹路径设置
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.widget_factory = WidgetFactory()
        self.current_mode = SearchMode.SIMILARITY
        
        self.setup_ui()
        self.connect_signals()
        
        # 默认选择相似度搜索
        self.set_search_mode(SearchMode.SIMILARITY)
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 搜索模式选择
        self._setup_mode_selection(layout)
        
        # 搜索组件堆叠
        self._setup_search_stack(layout)
    
    def _setup_mode_selection(self, layout):
        """设置搜索模式选择"""
        mode_layout = QHBoxLayout()
        mode_layout.setContentsMargins(10, 10, 10, 5)
        
        # 创建按钮组
        self.mode_button_group = QButtonGroup(self)
        
        # 相似度搜索
        self.similarity_rb = QRadioButton("相似度搜索")
        self.similarity_rb.setChecked(True)
        self.mode_button_group.addButton(self.similarity_rb, 0)
        mode_layout.addWidget(self.similarity_rb)
        
        # 文本搜索
        self.text_rb = QRadioButton("文本搜索")
        self.mode_button_group.addButton(self.text_rb, 1)
        mode_layout.addWidget(self.text_rb)
        
        # 类别搜索
        self.category_rb = QRadioButton("类别搜索")
        self.mode_button_group.addButton(self.category_rb, 2)
        mode_layout.addWidget(self.category_rb)
        
        # 高级搜索
        self.advanced_rb = QRadioButton("高级搜索")
        self.mode_button_group.addButton(self.advanced_rb, 3)
        mode_layout.addWidget(self.advanced_rb)
        
        mode_layout.addStretch()
        layout.addLayout(mode_layout)
    
    def _setup_search_stack(self, layout):
        """设置搜索组件堆叠"""
        self.search_stack = QStackedWidget()
        
        # 相似度搜索组件
        self.similarity_widget = SimilaritySearchWidget()
        self.search_stack.addWidget(self.similarity_widget)
        
        # 文本搜索组件
        self.text_widget = TextSearchWidget()
        self.search_stack.addWidget(self.text_widget)
        
        # 类别搜索组件
        self.category_widget = CategorySearchWidget()
        self.search_stack.addWidget(self.category_widget)
        
        # 高级搜索组件
        self.advanced_widget = AdvancedSearchWidget()
        self.search_stack.addWidget(self.advanced_widget)
        
        layout.addWidget(self.search_stack)
    
    def connect_signals(self):
        """连接信号"""
        # 搜索模式切换
        self.mode_button_group.idClicked.connect(self._on_mode_changed)
        
        # 各组件搜索请求
        self.similarity_widget.searchRequested.connect(self._on_similarity_search)
        self.text_widget.searchRequested.connect(self._on_text_search)
        self.category_widget.searchRequested.connect(self._on_category_search)
        self.advanced_widget.searchRequested.connect(self._on_advanced_search)
        
        # 其他信号
        self.similarity_widget.imageLoaded.connect(self.imageLoaded.emit)
        self.category_widget.categoryRefreshRequested.connect(self.categoryRefreshRequested.emit)
    
    def _on_mode_changed(self, mode_id: int):
        """搜索模式变化处理"""
        try:
            # ID到SearchMode的映射
            mode_mapping = {
                0: SearchMode.SIMILARITY,
                1: SearchMode.TEXT,
                2: SearchMode.CATEGORY,
                3: SearchMode.ADVANCED
            }
            
            mode = mode_mapping.get(mode_id, SearchMode.SIMILARITY)
            self.set_search_mode(mode)
            
        except Exception as e:
            self.logger.error(f"切换搜索模式失败: {e}")
    
    def _on_similarity_search(self, image_path: str, params: Dict[str, Any]):
        """相似度搜索请求处理"""
        try:
            self.logger.info(f"发起相似度搜索: {image_path}")
            # 发送特定搜索信号
            self.similaritySearchRequested.emit(image_path, params)
            # 同时发送通用搜索信号
            self.searchRequested.emit(image_path, params)
            
        except Exception as e:
            self.logger.error(f"相似度搜索请求处理失败: {e}")
            MessageHelper.show_error(self, "错误", f"搜索请求失败: {e}")
    
    def _on_text_search(self, query: str, params: Dict[str, Any]):
        """文本搜索请求处理"""
        try:
            self.logger.info(f"发起文本搜索: {query}")
            # 发送特定搜索信号
            self.textSearchRequested.emit(query, params)
            # 同时发送通用搜索信号
            self.searchRequested.emit(query, params)
            
        except Exception as e:
            self.logger.error(f"文本搜索请求处理失败: {e}")
            MessageHelper.show_error(self, "错误", f"搜索请求失败: {e}")
    
    def _on_category_search(self, query: str, params: Dict[str, Any]):
        """类别搜索请求处理"""
        try:
            self.logger.info(f"发起类别搜索: {query}")
            # 发送特定搜索信号
            self.categorySearchRequested.emit(query, params)
            # 同时发送通用搜索信号
            self.searchRequested.emit(query, params)
            
        except Exception as e:
            self.logger.error(f"类别搜索请求处理失败: {e}")
            MessageHelper.show_error(self, "错误", f"搜索请求失败: {e}")
    
    def _on_advanced_search(self, query: str, params: Dict[str, Any]):
        """高级搜索请求处理"""
        try:
            self.logger.info(f"发起高级搜索: {query}")
            # 发送特定搜索信号
            self.advancedSearchRequested.emit(query, params)
            # 同时发送通用搜索信号
            self.searchRequested.emit(query, params)
            
        except Exception as e:
            self.logger.error(f"高级搜索请求处理失败: {e}")
            MessageHelper.show_error(self, "错误", f"搜索请求失败: {e}")
    
    def set_search_mode(self, mode: SearchMode):
        """设置搜索模式
        
        Args:
            mode: 搜索模式
        """
        try:
            self.current_mode = mode
            
            # 更新界面
            if mode == SearchMode.SIMILARITY:
                self.search_stack.setCurrentWidget(self.similarity_widget)
                self.similarity_rb.setChecked(True)
            elif mode == SearchMode.TEXT:
                self.search_stack.setCurrentWidget(self.text_widget)
                self.text_rb.setChecked(True)
            elif mode == SearchMode.CATEGORY:
                self.search_stack.setCurrentWidget(self.category_widget)
                self.category_rb.setChecked(True)
            elif mode == SearchMode.ADVANCED:
                self.search_stack.setCurrentWidget(self.advanced_widget)
                self.advanced_rb.setChecked(True)
            elif mode == SearchMode.TAG:
                # TAG模式使用高级搜索界面
                self.search_stack.setCurrentWidget(self.advanced_widget)
                self.advanced_rb.setChecked(True)
            
            # 发送模式变化信号
            self.searchModeChanged.emit(mode.name.lower())
            
            self.logger.info(f"搜索模式已切换到: {mode.name}")
            
        except Exception as e:
            self.logger.error(f"设置搜索模式失败: {e}")
            MessageHelper.show_error(self, "错误", f"设置搜索模式失败: {e}")
    
    def get_current_mode(self) -> SearchMode:
        """获取当前搜索模式"""
        return self.current_mode
    
    def get_search_config(self) -> SearchConfig:
        """获取搜索配置

        Returns:
            当前的搜索配置
        """
        try:
            config = SearchConfig(mode=self.current_mode)
            self.logger.info(f"创建搜索配置，模式: {self.current_mode}")

            if self.current_mode == SearchMode.SIMILARITY:
                params = self.similarity_widget.get_search_params()
                self.logger.info(f"相似度搜索参数: {params}")

                # 设置相似度搜索特有参数
                if 'similarity_threshold' in params:
                    config.similarity_threshold = params['similarity_threshold']
                    self.logger.info(f"设置相似度阈值: {config.similarity_threshold}")

                if 'feature_weights' in params:
                    config.feature_weights = params['feature_weights']
                    self.logger.info(f"设置特征权重: {config.feature_weights}")

                if 'feature_extraction_params' in params:
                    config.feature_extraction_params = params['feature_extraction_params']

                if 'max_results' in params:
                    config.max_results = params['max_results']

            return config

        except Exception as e:
            self.logger.error(f"获取搜索配置失败: {e}")
            return SearchConfig(mode=SearchMode.SIMILARITY)
    
    def set_search_config(self, config: SearchConfig):
        """设置搜索配置
        
        Args:
            config: 搜索配置
        """
        try:
            # 设置搜索模式
            self.set_search_mode(config.mode)
            
            self.logger.info(f"搜索配置已设置: {config.mode.name}")
            
        except Exception as e:
            self.logger.error(f"设置搜索配置失败: {e}")
            MessageHelper.show_error(self, "错误", f"设置搜索配置失败: {e}")
    
    def update_categories(self, categories):
        """更新类别列表
        
        Args:
            categories: 类别列表
        """
        try:
            self.category_widget.update_categories(categories)
            
        except Exception as e:
            self.logger.error(f"更新类别列表失败: {e}")
    
    def load_image(self, image_path: str):
        """加载图像到相似度搜索
        
        Args:
            image_path: 图像路径
        """
        try:
            # 切换到相似度搜索模式
            self.set_search_mode(SearchMode.SIMILARITY)
            
            # 加载图像
            self.similarity_widget.load_image(image_path)
            
        except Exception as e:
            self.logger.error(f"加载图像失败: {e}")
            MessageHelper.show_error(self, "错误", f"加载图像失败: {e}")
    
    def clear_search(self):
        """清除搜索"""
        try:
            # 清除相似度搜索
            self.similarity_widget.clear_image()
            
            # 清除文本搜索
            self.text_widget.clear_search()
            
            # 清除类别搜索
            self.category_widget.clear_selection()
            
            # 重置高级搜索
            self.advanced_widget.reset_filters()
            
            self.logger.info("搜索已清除")
            
        except Exception as e:
            self.logger.error(f"清除搜索失败: {e}")
    
    def get_similarity_widget(self) -> SimilaritySearchWidget:
        """获取相似度搜索组件"""
        return self.similarity_widget
    
    def get_text_widget(self) -> TextSearchWidget:
        """获取文本搜索组件"""
        return self.text_widget
    
    def get_category_widget(self) -> CategorySearchWidget:
        """获取类别搜索组件"""
        return self.category_widget
    
    def get_advanced_widget(self) -> AdvancedSearchWidget:
        """获取高级搜索组件"""
        return self.advanced_widget
    
    def set_folder_path(self, folder_path: str):
        """设置文件夹路径并触发特征提取
        
        Args:
            folder_path: 文件夹路径
        """
        try:
            self.logger.info(f"设置文件夹路径: {folder_path}")
            
            # 发出文件夹路径设置信号，让主窗口处理特征提取
            self.folderPathSet.emit(folder_path)
            
        except Exception as e:
            self.logger.error(f"设置文件夹路径失败: {e}")
            MessageHelper.show_error(self, "错误", f"设置文件夹路径失败: {e}")
    
    def set_image_path(self, image_path: str):
        """设置图像路径
        
        Args:
            image_path: 图像路径
        """
        try:
            self.logger.info(f"设置图像路径: {image_path}")
            # 加载图像到相似度搜索
            self.load_image(image_path)
            
        except Exception as e:
            self.logger.error(f"设置图像路径失败: {e}")
            MessageHelper.show_error(self, "错误", f"设置图像路径失败: {e}")
    
    def get_image_path(self) -> Optional[str]:
        """获取当前图像路径
        
        Returns:
            Optional[str]: 当前图像路径
        """
        try:
            if self.current_mode == SearchMode.SIMILARITY:
                return self.similarity_widget.get_current_image_path()
            return None
            
        except Exception as e:
            self.logger.error(f"获取图像路径失败: {e}")
            return None
        
    def stop_search(self):
        """停止搜索"""
        try:
            self.logger.info("停止搜索")
            # 触发停止搜索信号
            self.searchStopped.emit()
            # 更新搜索状态
            self.set_searching(False)
            
        except Exception as e:
            self.logger.error(f"停止搜索失败: {e}")
            MessageHelper.show_error(self, "错误", f"停止搜索失败: {e}")
            
    def set_searching(self, is_searching: bool):
        """设置搜索状态
        
        Args:
            is_searching: 是否正在搜索
        """
        try:
            # 根据当前模式更新对应组件的搜索状态
            if self.current_mode == SearchMode.SIMILARITY:
                self.similarity_widget.set_searching(is_searching)
            elif self.current_mode == SearchMode.TEXT:
                self.text_widget.set_searching(is_searching)
            elif self.current_mode == SearchMode.CATEGORY:
                self.category_widget.set_searching(is_searching)
            elif self.current_mode == SearchMode.ADVANCED:
                self.advanced_widget.set_searching(is_searching)
                
            self.logger.debug(f"搜索状态已更新: {'搜索中' if is_searching else '空闲'}")
            
        except Exception as e:
            self.logger.error(f"设置搜索状态失败: {e}")