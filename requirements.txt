# Fabric Search v2 - 依赖包列表

# Web框架
Flask>=2.3.0
Flask-CORS>=4.0.0
Werkzeug>=2.3.0

# GUI框架
PyQt6>=6.4.0

# 深度学习框架
torch>=1.13.0
torchvision>=0.14.0
timm>=0.9.0  # 预训练模型
transformers>=4.30.0  # Hugging Face模型
sentence-transformers>=2.2.0  # 句子嵌入

# 图像处理
Pillow>=9.0.0
opencv-python>=4.6.0
scikit-image>=0.19.0

# 数据处理
numpy>=1.21.0
pandas>=1.4.0
scipy>=1.8.0
scikit-learn>=1.3.0
h5py>=3.8.0  # HDF5文件支持

# 数据库
SQLAlchemy>=2.0.0
psycopg2-binary>=2.9.0  # PostgreSQL支持

# 向量搜索
faiss-cpu>=1.7.0  # CPU版本
# faiss-gpu>=1.7.0  # GPU版本（可选）

# 配置文件
PyYAML>=6.0
toml>=0.10.0

# 日志和监控
loguru>=0.6.0
tqdm>=4.64.0
psutil>=5.9.0  # 系统信息

# 网络请求
requests>=2.28.0

# 文件处理
send2trash>=1.8.0  # 安全删除文件
watchdog>=2.1.0  # 文件监控

# 数据验证
pydantic>=1.10.0

# 缓存
redis>=4.5.0  # Redis缓存

# 任务队列
celery>=5.3.0  # 异步任务

# 文件格式支持
openpyxl>=3.1.0  # Excel文件
xlsxwriter>=3.1.0  # Excel写入

# API文档
flask-restx>=1.2.0  # API文档生成

# 安全
cryptography>=41.0.0

# 开发工具
black>=23.0.0  # 代码格式化
flake8>=6.0.0  # 代码检查

# 测试
pytest>=7.0.0
pytest-qt>=4.0.0
pytest-cov>=4.0.0
pytest-flask>=1.2.0

# 开发工具
black>=22.0.0
flake8>=5.0.0
mypy>=0.991

# 性能分析
memory-profiler>=0.60.0
psutil>=5.9.0

# 其他工具
colorama>=0.4.0  # 彩色终端输出
rich>=12.0.0  # 富文本显示