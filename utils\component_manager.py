#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
组件管理器

使用单例模式管理核心组件，避免重复初始化和资源浪费。
"""

import logging
import threading
from typing import Optional, Dict, Any
from pathlib import Path

from config.config_manager import get_config_manager
from config.app_config import AppConfig
from database.database_manager import DatabaseManager, DatabaseConfig
from database.fabric_repository import FabricRepository
from database.repositories.search_history_repository import SearchHistoryRepository
from search.search_history import SearchHistoryManager
from search.statistics_manager import StatisticsManager
from utils.gpu_utils import GPUManager
from config.model_config import ModelConfigManager

logger = logging.getLogger(__name__)


class ComponentManager:
    """组件管理器 - 单例模式
    
    负责管理所有核心组件的生命周期，避免重复初始化。
    """
    
    _instance = None
    _lock = threading.Lock()
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ComponentManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        with self._lock:
            if self._initialized:
                return
                
            logger.info("初始化组件管理器")
            
            # 核心组件
            self._config_manager = None
            self._config = None
            self._db_manager = None
            self._fabric_repository = None
            self._search_repository = None
            self._search_history_manager = None
            self._statistics_manager = None
            self._gpu_manager = None
            self._model_config_manager = None
            self._feature_manager = None
            
            # 初始化状态
            self._components_initialized = False
            
            ComponentManager._initialized = True
            logger.info("组件管理器初始化完成")
    
    def initialize_components(self) -> bool:
        """初始化所有组件
        
        Returns:
            bool: 是否成功初始化
        """
        if self._components_initialized:
            logger.debug("组件已初始化，跳过重复初始化")
            return True
            
        try:
            logger.info("开始初始化核心组件")
            
            # 1. 初始化配置管理器
            self._config_manager = get_config_manager()
            self._config = self._config_manager.get_config()
            logger.debug("配置管理器初始化完成")
            
            # 2. 初始化数据库管理器
            db_config = DatabaseConfig(
                db_path=Path(self._config.data_dir) / 'fabric_search.db'
            )
            self._db_manager = DatabaseManager(config=db_config)
            
            # 确保数据库表已创建
            try:
                # 数据库表已在DatabaseManager的初始化过程中通过SchemaManager创建
                # 不需要显式调用create_tables方法
                logger.debug("数据库表创建/验证完成")
            except Exception as e:
                logger.warning(f"数据库表创建/验证警告: {e}")
            
            # 3. 初始化仓库
            self._fabric_repository = FabricRepository(db_manager=self._db_manager)
            self._search_repository = SearchHistoryRepository(db_manager=self._db_manager)
            logger.debug("数据仓库初始化完成")
            
            # 4. 初始化搜索历史管理器
            self._search_history_manager = SearchHistoryManager(
                search_repository=self._search_repository,
                max_history_days=90,
                max_sessions_per_user=100
            )
            logger.debug("搜索历史管理器初始化完成")
            
            # 5. 初始化统计管理器
            self._statistics_manager = StatisticsManager(
                fabric_repository=self._fabric_repository,
                history_manager=self._search_history_manager,
                max_suggestions=10
            )
            logger.debug("统计管理器初始化完成")
            
            # 6. 初始化GPU管理器
            self._gpu_manager = GPUManager()
            logger.debug("GPU管理器初始化完成")
            
            # 7. 初始化模型配置管理器
            self._model_config_manager = ModelConfigManager()
            logger.debug("模型配置管理器初始化完成")
            
            self._components_initialized = True
            logger.info("所有核心组件初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"组件初始化失败: {e}", exc_info=True)
            return False
    
    # get_feature_manager方法已被移除，请使用feature_manager属性访问器代替
    
    @property
    def config_manager(self):
        """获取配置管理器"""
        if not self._components_initialized:
            self.initialize_components()
        return self._config_manager
    
    @property
    def config(self):
        """获取配置"""
        if not self._components_initialized:
            self.initialize_components()
        return self._config
    
    @property
    def db_manager(self):
        """获取数据库管理器"""
        if not self._components_initialized:
            self.initialize_components()
        return self._db_manager
    
    @property
    def fabric_repository(self):
        """获取面料仓库"""
        if not self._components_initialized:
            self.initialize_components()
        return self._fabric_repository
    
    @property
    def search_repository(self):
        """获取搜索仓库"""
        if not self._components_initialized:
            self.initialize_components()
        return self._search_repository
    
    @property
    def search_history_manager(self):
        """获取搜索历史管理器"""
        if not self._components_initialized:
            self.initialize_components()
        return self._search_history_manager
    
    @property
    def statistics_manager(self):
        """获取统计管理器"""
        if not self._components_initialized:
            self.initialize_components()
        return self._statistics_manager
    
    @property
    def gpu_manager(self):
        """获取GPU管理器"""
        if not self._components_initialized:
            self.initialize_components()
        return self._gpu_manager
    
    @property
    def model_config_manager(self):
        """获取模型配置管理器"""
        if not self._components_initialized:
            self.initialize_components()
        return self._model_config_manager
    
    @property
    def feature_manager(self):
        """获取特征管理器"""
        if self._feature_manager is None:
            # 确保核心组件已初始化
            if not self._components_initialized:
                self.initialize_components()
                
            try:
                from features.core.manager import FeatureManager
                
                # 创建AppConfig实例
                app_config = AppConfig()
                app_config.data_dir = self._config.data_dir
                
                logger.info("创建新的特征管理器实例")
                self._feature_manager = FeatureManager(
                    app_config=app_config,
                    data_repository=self._fabric_repository,
                    gpu_manager=self._gpu_manager,
                    model_config_manager=self._model_config_manager,
                    config_dir="./config"
                )
                
                # 记录实例ID，便于跟踪
                logger.info(f"新创建的feature_manager ID: {id(self._feature_manager)}")
                
                # FeatureManager的构造函数中已经调用了initialize_components()，无需重复调用
                logger.info("特征管理器创建成功")
                
            except Exception as e:
                logger.error(f"创建特征管理器失败: {e}", exc_info=True)
                self._feature_manager = None
                raise
        else:
            # 记录已存在实例的ID，便于跟踪
            logger.debug(f"get_feature_manager: 返回已存在的实例，ID: {id(self._feature_manager)}")
                
        return self._feature_manager
    
    def cleanup(self):
        """清理所有资源"""
        # 添加静态类变量来跟踪清理状态
        if not hasattr(ComponentManager, '_cleanup_called'):
            ComponentManager._cleanup_called = False
            
        # 如果已经清理过，则跳过
        if ComponentManager._cleanup_called:
            logger.debug("组件管理器资源已清理，跳过重复清理")
            return
            
        logger.info("开始清理组件管理器资源")
        
        try:
            # 清理特征管理器
            if self._feature_manager is not None:
                try:
                    self._feature_manager.cleanup()
                    logger.debug("特征管理器资源已清理")
                except Exception as e:
                    logger.warning(f"清理特征管理器失败: {e}")
                finally:
                    self._feature_manager = None
            
            # 清理其他组件
            components_to_cleanup = [
                ('search_history_manager', self._search_history_manager),
                ('statistics_manager', self._statistics_manager),
                ('gpu_manager', self._gpu_manager),
            ]
            
            for name, component in components_to_cleanup:
                if component is not None:
                    try:
                        if hasattr(component, 'cleanup'):
                            component.cleanup()
                        logger.debug(f"{name}资源已清理")
                    except Exception as e:
                        logger.warning(f"清理{name}失败: {e}")
            
            # 重置状态
            self._components_initialized = False
            
            # 标记为已清理
            ComponentManager._cleanup_called = True
            logger.info("组件管理器资源清理完成")
            
        except Exception as e:
            logger.error(f"清理组件管理器资源失败: {e}", exc_info=True)
    
    @classmethod
    def reset_instance(cls):
        """重置单例实例（主要用于测试）"""
        with cls._lock:
            if cls._instance is not None:
                try:
                    cls._instance.cleanup()
                except Exception as e:
                    logger.warning(f"重置实例时清理失败: {e}")
            # 重置清理标志
            if hasattr(cls, '_cleanup_called'):
                cls._cleanup_called = False
            cls._instance = None
            cls._initialized = False


# 便捷函数
def get_component_manager() -> ComponentManager:
    """获取组件管理器实例"""
    return ComponentManager()