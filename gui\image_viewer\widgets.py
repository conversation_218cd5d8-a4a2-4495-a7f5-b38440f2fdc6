#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像查看器基础组件

该模块提供图像查看器使用的基础UI组件。
"""

from typing import Optional
from PyQt6.QtWidgets import QLabel
from PyQt6.QtCore import Qt, pyqtSignal, QPoint
from PyQt6.QtGui import QMouseEvent, QDragEnterEvent, QDropEvent

from utils.log_utils import LoggerMixin
from .config import ImageViewerConfig


class ImageLabel(QLabel, LoggerMixin):
    """自定义图像标签
    
    支持拖拽、鼠标事件和图像显示的标签组件。
    """
    
    # 信号
    imageClicked = pyqtSignal(QPoint)  # 图像点击
    imageDoubleClicked = pyqtSignal(QPoint)  # 图像双击
    imageRightClicked = pyqtSignal(QPoint)  # 图像右键点击
    
    def __init__(self, parent=None):
        """初始化图像标签
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        self._setup_ui()
        self._setup_events()
    
    def _setup_ui(self):
        """设置界面"""
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setMinimumSize(*ImageViewerConfig.IMAGE_LABEL_MIN_SIZE)
        self.setStyleSheet(ImageViewerConfig.IMAGE_LABEL_STYLE)
    
    def _setup_events(self):
        """设置事件处理"""
        # 拖拽支持
        self.setAcceptDrops(True)
        
        # 鼠标跟踪
        self.setMouseTracking(True)
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件
        
        Args:
            event: 鼠标事件
        """
        try:
            if event.button() == Qt.MouseButton.LeftButton:
                self.imageClicked.emit(event.pos())
            elif event.button() == Qt.MouseButton.RightButton:
                self.imageRightClicked.emit(event.pos())
            super().mousePressEvent(event)
        except Exception as e:
            self.logger.error(f"处理鼠标按下事件失败: {e}")
    
    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """鼠标双击事件
        
        Args:
            event: 鼠标事件
        """
        try:
            if event.button() == Qt.MouseButton.LeftButton:
                self.imageDoubleClicked.emit(event.pos())
            super().mouseDoubleClickEvent(event)
        except Exception as e:
            self.logger.error(f"处理鼠标双击事件失败: {e}")
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件
        
        Args:
            event: 拖拽事件
        """
        try:
            if event.mimeData().hasUrls():
                event.acceptProposedAction()
        except Exception as e:
            self.logger.error(f"处理拖拽进入事件失败: {e}")
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件
        
        Args:
            event: 拖拽事件
        """
        try:
            urls = event.mimeData().urls()
            if urls:
                file_path = urls[0].toLocalFile()
                if self.parent() and hasattr(self.parent(), 'load_image'):
                    self.parent().load_image(file_path)
        except Exception as e:
            self.logger.error(f"处理拖拽放下事件失败: {e}")
    
    def set_placeholder_text(self, text: str):
        """设置占位符文本
        
        Args:
            text: 占位符文本
        """
        self.clear()
        self.setText(text)