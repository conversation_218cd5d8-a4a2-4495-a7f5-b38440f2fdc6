#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试权重修复效果

该脚本用于测试修复后的权重传递机制是否正常工作。
"""

import sys
import os
import logging
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_search_config_creation():
    """测试搜索配置创建"""
    logger.info("=" * 50)
    logger.info("测试搜索配置创建")
    logger.info("=" * 50)
    
    try:
        from gui.search.models import SearchConfig, SearchMode
        
        # 创建搜索配置
        config = SearchConfig(mode=SearchMode.SIMILARITY)
        
        # 设置特征权重
        config.feature_weights = {
            'color': 0.8,
            'texture': 0.2,
            'shape': 0.0,
            'deep_learning': 0.0
        }
        
        config.similarity_threshold = 0.7
        config.max_results = 10
        
        logger.info(f"创建的配置: mode={config.mode}")
        logger.info(f"特征权重: {config.feature_weights}")
        logger.info(f"相似度阈值: {config.similarity_threshold}")
        logger.info(f"最大结果数: {config.max_results}")
        
        return config
        
    except Exception as e:
        logger.error(f"测试搜索配置创建失败: {e}")
        return None


def test_config_to_query_conversion():
    """测试配置到查询的转换"""
    logger.info("=" * 50)
    logger.info("测试配置到查询的转换")
    logger.info("=" * 50)
    
    try:
        # 创建测试配置
        config = test_search_config_creation()
        if not config:
            return False
        
        # 模拟search_handler中的转换过程
        from search.models import SearchQuery, SearchType
        
        query = SearchQuery(query_type=SearchType.IMAGE_SIMILARITY)
        query.query_image_path = "/test/image.jpg"
        
        # 设置特征权重
        if hasattr(config, 'feature_weights') and config.feature_weights:
            query.feature_weights = config.feature_weights
            logger.info(f"转换后查询特征权重: {query.feature_weights}")
        
        # 设置相似度阈值
        if hasattr(config, 'similarity_threshold'):
            query.similarity_threshold = config.similarity_threshold
            logger.info(f"转换后相似度阈值: {query.similarity_threshold}")
        
        # 验证转换结果
        if query.feature_weights == config.feature_weights:
            logger.info("✅ 特征权重转换正确")
            return True
        else:
            logger.error("❌ 特征权重转换错误")
            return False
            
    except Exception as e:
        logger.error(f"测试配置到查询转换失败: {e}")
        return False


def test_cache_key_with_weights():
    """测试带权重的缓存键生成"""
    logger.info("=" * 50)
    logger.info("测试带权重的缓存键生成")
    logger.info("=" * 50)
    
    try:
        from search.models import SearchQuery, SearchType
        from search.cache_manager import CacheManager
        
        cache_manager = CacheManager()
        
        # 创建两个具有不同权重的查询
        query1 = SearchQuery(
            query_type=SearchType.IMAGE_SIMILARITY,
            query_image_path="/test/image.jpg",
            feature_weights={
                'color': 0.8,
                'texture': 0.2,
                'shape': 0.0,
                'deep_learning': 0.0
            }
        )
        
        query2 = SearchQuery(
            query_type=SearchType.IMAGE_SIMILARITY,
            query_image_path="/test/image.jpg",
            feature_weights={
                'color': 0.2,
                'texture': 0.8,
                'shape': 0.0,
                'deep_learning': 0.0
            }
        )
        
        # 生成缓存键
        key1 = cache_manager.generate_cache_key(query1)
        key2 = cache_manager.generate_cache_key(query2)
        
        logger.info(f"查询1 (颜色主导) 缓存键: {key1}")
        logger.info(f"查询2 (纹理主导) 缓存键: {key2}")
        
        if key1 != key2:
            logger.info("✅ 缓存键生成正确 - 不同权重产生不同缓存键")
            return True
        else:
            logger.error("❌ 缓存键生成错误 - 相同权重产生相同缓存键")
            return False
            
    except Exception as e:
        logger.error(f"测试缓存键生成失败: {e}")
        return False


def test_weight_normalization_edge_cases():
    """测试权重归一化边界情况"""
    logger.info("=" * 50)
    logger.info("测试权重归一化边界情况")
    logger.info("=" * 50)
    
    test_cases = [
        # (输入权重, 期望输出, 描述)
        ({'color': 100, 'texture': 0, 'shape': 0, 'deep_learning': 0}, 
         {'color': 1.0, 'texture': 0.0, 'shape': 0.0, 'deep_learning': 0.0}, 
         "单一特征100%"),
        
        ({'color': 50, 'texture': 50, 'shape': 0, 'deep_learning': 0}, 
         {'color': 0.5, 'texture': 0.5, 'shape': 0.0, 'deep_learning': 0.0}, 
         "两个特征各50%"),
        
        ({'color': 25, 'texture': 25, 'shape': 25, 'deep_learning': 25}, 
         {'color': 0.25, 'texture': 0.25, 'shape': 0.25, 'deep_learning': 0.25}, 
         "四个特征均等"),
        
        ({'color': 0, 'texture': 0, 'shape': 0, 'deep_learning': 0}, 
         {'color': 0.25, 'texture': 0.25, 'shape': 0.25, 'deep_learning': 0.25}, 
         "全零权重（应使用默认值）"),
    ]
    
    all_passed = True
    
    for input_weights, expected_output, description in test_cases:
        logger.info(f"测试用例: {description}")
        logger.info(f"输入权重: {input_weights}")
        
        # 执行归一化
        total_weight = sum(input_weights.values())
        normalized_weights = {}
        
        if total_weight > 0:
            for feature, weight in input_weights.items():
                normalized_weights[feature] = weight / total_weight
        else:
            # 使用默认权重
            normalized_weights = {
                'color': 0.25,
                'texture': 0.25,
                'shape': 0.25,
                'deep_learning': 0.25
            }
        
        logger.info(f"归一化结果: {normalized_weights}")
        logger.info(f"期望结果: {expected_output}")
        
        # 验证结果
        if normalized_weights == expected_output:
            logger.info("✅ 测试通过")
        else:
            logger.error("❌ 测试失败")
            all_passed = False
        
        logger.info("-" * 30)
    
    return all_passed


def main():
    """主函数"""
    logger.info("开始测试权重修复效果")
    logger.info("=" * 80)
    
    results = []
    
    # 测试1: 搜索配置创建
    config = test_search_config_creation()
    results.append(config is not None)
    
    # 测试2: 配置到查询转换
    results.append(test_config_to_query_conversion())
    
    # 测试3: 缓存键生成
    results.append(test_cache_key_with_weights())
    
    # 测试4: 权重归一化边界情况
    results.append(test_weight_normalization_edge_cases())
    
    # 总结
    logger.info("=" * 80)
    logger.info("测试总结")
    logger.info("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    logger.info(f"通过测试: {passed}/{total}")
    
    if passed == total:
        logger.info("✅ 所有测试通过 - 权重修复有效")
    else:
        logger.error("❌ 部分测试失败 - 需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
