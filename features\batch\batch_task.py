"""批处理任务模块

定义批处理任务的数据结构和状态管理。
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from pathlib import Path
import uuid


class BatchTaskStatus(Enum):
    """批处理任务状态枚举"""
    PENDING = "pending"          # 等待中
    RUNNING = "running"          # 运行中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"            # 失败
    CANCELLED = "cancelled"      # 已取消
    PAUSED = "paused"            # 已暂停


@dataclass
class BatchTaskItem:
    """批处理任务项"""
    item_id: Union[int, str]
    file_path: Union[str, Path]
    status: BatchTaskStatus = BatchTaskStatus.PENDING
    error_message: Optional[str] = None
    retry_count: int = 0
    processing_time: Optional[float] = None
    features_extracted: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'item_id': self.item_id,
            'file_path': str(self.file_path),
            'status': self.status.value,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'processing_time': self.processing_time,
            'features_extracted': self.features_extracted,
            'metadata': self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BatchTaskItem':
        """从字典创建实例"""
        return cls(
            item_id=data['item_id'],
            file_path=Path(data['file_path']),
            status=BatchTaskStatus(data['status']),
            error_message=data.get('error_message'),
            retry_count=data.get('retry_count', 0),
            processing_time=data.get('processing_time'),
            features_extracted=data.get('features_extracted', False),
            metadata=data.get('metadata', {})
        )


@dataclass
class BatchTask:
    """批处理任务"""
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    items: List[BatchTaskItem] = field(default_factory=list)
    status: BatchTaskStatus = BatchTaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_items: int = 0
    processed_items: int = 0
    successful_items: int = 0
    failed_items: int = 0
    error_message: Optional[str] = None
    progress_percentage: float = 0.0
    estimated_time_remaining: Optional[float] = None
    processing_rate: float = 0.0  # 每秒处理项目数
    
    # 配置参数
    max_retries: int = 3
    batch_size: int = 32
    max_workers: int = 4
    timeout_seconds: int = 300
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        self.total_items = len(self.items)
        
    def add_item(self, item: BatchTaskItem) -> None:
        """添加任务项"""
        self.items.append(item)
        self.total_items = len(self.items)
        
    def add_items(self, items: List[BatchTaskItem]) -> None:
        """批量添加任务项"""
        self.items.extend(items)
        self.total_items = len(self.items)
        
    def get_pending_items(self) -> List[BatchTaskItem]:
        """获取待处理的任务项"""
        return [item for item in self.items if item.status == BatchTaskStatus.PENDING]
        
    def get_failed_items(self) -> List[BatchTaskItem]:
        """获取失败的任务项"""
        return [item for item in self.items if item.status == BatchTaskStatus.FAILED]
        
    def get_completed_items(self) -> List[BatchTaskItem]:
        """获取已完成的任务项"""
        return [item for item in self.items if item.status == BatchTaskStatus.COMPLETED]
        
    def update_progress(self) -> None:
        """更新进度信息"""
        self.processed_items = sum(1 for item in self.items 
                                 if item.status in [BatchTaskStatus.COMPLETED, BatchTaskStatus.FAILED])
        self.successful_items = sum(1 for item in self.items 
                                  if item.status == BatchTaskStatus.COMPLETED)
        self.failed_items = sum(1 for item in self.items 
                              if item.status == BatchTaskStatus.FAILED)
        
        if self.total_items > 0:
            self.progress_percentage = (self.processed_items / self.total_items) * 100
        else:
            self.progress_percentage = 0.0
            
        # 计算处理速率和预估剩余时间
        if self.started_at and self.processed_items > 0:
            elapsed_time = (datetime.now() - self.started_at).total_seconds()
            self.processing_rate = self.processed_items / elapsed_time
            
            remaining_items = self.total_items - self.processed_items
            if self.processing_rate > 0:
                self.estimated_time_remaining = remaining_items / self.processing_rate
            else:
                self.estimated_time_remaining = None
                
    def start(self) -> None:
        """开始任务"""
        self.status = BatchTaskStatus.RUNNING
        self.started_at = datetime.now()
        
    def complete(self) -> None:
        """完成任务"""
        self.status = BatchTaskStatus.COMPLETED
        self.completed_at = datetime.now()
        self.update_progress()
        
    def fail(self, error_message: str) -> None:
        """任务失败"""
        self.status = BatchTaskStatus.FAILED
        self.error_message = error_message
        self.completed_at = datetime.now()
        self.update_progress()
        
    def cancel(self) -> None:
        """取消任务"""
        self.status = BatchTaskStatus.CANCELLED
        self.completed_at = datetime.now()
        
    def pause(self) -> None:
        """暂停任务"""
        self.status = BatchTaskStatus.PAUSED
        
    def resume(self) -> None:
        """恢复任务"""
        if self.status == BatchTaskStatus.PAUSED:
            self.status = BatchTaskStatus.RUNNING
            
    def get_duration(self) -> Optional[float]:
        """获取任务持续时间（秒）"""
        if self.started_at:
            end_time = self.completed_at or datetime.now()
            return (end_time - self.started_at).total_seconds()
        return None
        
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.processed_items > 0:
            return (self.successful_items / self.processed_items) * 100
        return 0.0
        
    def get_summary(self) -> Dict[str, Any]:
        """获取任务摘要"""
        self.update_progress()
        
        return {
            'task_id': self.task_id,
            'name': self.name,
            'status': self.status.value,
            'total_items': self.total_items,
            'processed_items': self.processed_items,
            'successful_items': self.successful_items,
            'failed_items': self.failed_items,
            'progress_percentage': round(self.progress_percentage, 2),
            'success_rate': round(self.get_success_rate(), 2),
            'processing_rate': round(self.processing_rate, 2),
            'estimated_time_remaining': self.estimated_time_remaining,
            'duration': self.get_duration(),
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'error_message': self.error_message
        }
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'name': self.name,
            'description': self.description,
            'items': [item.to_dict() for item in self.items],
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'total_items': self.total_items,
            'processed_items': self.processed_items,
            'successful_items': self.successful_items,
            'failed_items': self.failed_items,
            'error_message': self.error_message,
            'progress_percentage': self.progress_percentage,
            'estimated_time_remaining': self.estimated_time_remaining,
            'processing_rate': self.processing_rate,
            'max_retries': self.max_retries,
            'batch_size': self.batch_size,
            'max_workers': self.max_workers,
            'timeout_seconds': self.timeout_seconds,
            'metadata': self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BatchTask':
        """从字典创建实例"""
        task = cls(
            task_id=data['task_id'],
            name=data.get('name', ''),
            description=data.get('description', ''),
            items=[BatchTaskItem.from_dict(item_data) for item_data in data.get('items', [])],
            status=BatchTaskStatus(data['status']),
            created_at=datetime.fromisoformat(data['created_at']),
            started_at=datetime.fromisoformat(data['started_at']) if data.get('started_at') else None,
            completed_at=datetime.fromisoformat(data['completed_at']) if data.get('completed_at') else None,
            total_items=data.get('total_items', 0),
            processed_items=data.get('processed_items', 0),
            successful_items=data.get('successful_items', 0),
            failed_items=data.get('failed_items', 0),
            error_message=data.get('error_message'),
            progress_percentage=data.get('progress_percentage', 0.0),
            estimated_time_remaining=data.get('estimated_time_remaining'),
            processing_rate=data.get('processing_rate', 0.0),
            max_retries=data.get('max_retries', 3),
            batch_size=data.get('batch_size', 32),
            max_workers=data.get('max_workers', 4),
            timeout_seconds=data.get('timeout_seconds', 300),
            metadata=data.get('metadata', {})
        )
        return task