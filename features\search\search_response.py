#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索响应模块

定义搜索响应相关的数据结构。
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field


@dataclass
class SearchResult:
    """单个搜索结果"""
    fabric_id: str
    image_path: str
    similarity_score: float
    feature_scores: Dict[str, float] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    rank: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            'fabric_id': self.fabric_id,
            'image_path': self.image_path,
            'similarity_score': self.similarity_score,
            'feature_scores': self.feature_scores,
            'metadata': self.metadata,
            'rank': self.rank
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchResult':
        """从字典创建实例
        
        Args:
            data: 字典数据
            
        Returns:
            SearchResult: 搜索结果实例
        """
        return cls(**data)


@dataclass
class SearchResponse:
    """搜索响应"""
    query_image_path: str
    search_results: List[SearchResult] = field(default_factory=list)
    total_results: int = 0
    search_time: float = 0.0
    feature_extraction_time: float = 0.0
    similarity_computation_time: float = 0.0
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    query_info: Dict[str, Any] = field(default_factory=dict)
    total_candidates: int = 0
    
    def add_result(self, result: Dict[str, Any]):
        """添加搜索结果
        
        Args:
            result: 搜索结果字典或SearchResult对象
        """
        if isinstance(result, dict):
            search_result = SearchResult(
                fabric_id=result.get('fabric_id', ''),
                image_path=result.get('image_path', ''),
                similarity_score=result.get('similarity_score', 0.0),
                feature_scores=result.get('feature_scores', {}),
                metadata=result.get('metadata', {}),
                rank=result.get('rank', len(self.search_results) + 1)
            )
        elif isinstance(result, SearchResult):
            search_result = result
        else:
            raise ValueError("Result must be dict or SearchResult")
        
        self.search_results.append(search_result)
        self.total_results = len(self.search_results)
    
    def sort_by_similarity(self, descending: bool = True):
        """按相似度排序
        
        Args:
            descending: 是否降序排列
        """
        self.search_results.sort(
            key=lambda x: x.similarity_score, 
            reverse=descending
        )
        
        # 更新排名
        for i, result in enumerate(self.search_results):
            result.rank = i + 1
    
    def filter_by_threshold(self, threshold: float):
        """按阈值过滤结果
        
        Args:
            threshold: 相似度阈值
        """
        self.search_results = [
            result for result in self.search_results 
            if result.similarity_score >= threshold
        ]
        self.total_results = len(self.search_results)
        
        # 更新排名
        for i, result in enumerate(self.search_results):
            result.rank = i + 1
    
    def get_top_k(self, k: int) -> List[SearchResult]:
        """获取前k个结果
        
        Args:
            k: 结果数量
            
        Returns:
            List[SearchResult]: 前k个结果
        """
        return self.search_results[:k]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            'query_image_path': self.query_image_path,
            'search_results': [result.to_dict() for result in self.search_results],
            'total_results': self.total_results,
            'search_time': self.search_time,
            'feature_extraction_time': self.feature_extraction_time,
            'similarity_computation_time': self.similarity_computation_time,
            'success': self.success,
            'error_message': self.error_message,
            'metadata': self.metadata,
            'query_info': self.query_info,
            'total_candidates': self.total_candidates
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchResponse':
        """从字典创建实例
        
        Args:
            data: 字典数据
            
        Returns:
            SearchResponse: 搜索响应实例
        """
        # 处理搜索结果
        search_results = []
        if 'search_results' in data:
            for result_data in data['search_results']:
                if isinstance(result_data, dict):
                    search_results.append(SearchResult.from_dict(result_data))
                else:
                    search_results.append(result_data)
        
        # 创建实例
        response = cls(
            query_image_path=data.get('query_image_path', ''),
            search_results=search_results,
            total_results=data.get('total_results', 0),
            search_time=data.get('search_time', 0.0),
            feature_extraction_time=data.get('feature_extraction_time', 0.0),
            similarity_computation_time=data.get('similarity_computation_time', 0.0),
            success=data.get('success', True),
            error_message=data.get('error_message'),
            metadata=data.get('metadata', {}),
            query_info=data.get('query_info', {}),
            total_candidates=data.get('total_candidates', 0)
        )
        
        return response


class SearchResponseBuilder:
    """搜索响应构建器"""
    
    def __init__(self, query_image_path: str):
        """初始化构建器
        
        Args:
            query_image_path: 查询图像路径
        """
        self.response = SearchResponse(query_image_path=query_image_path)
        self.start_time = time.time()
    
    def set_feature_extraction_time(self, extraction_time: float) -> 'SearchResponseBuilder':
        """设置特征提取时间
        
        Args:
            extraction_time: 特征提取时间
            
        Returns:
            SearchResponseBuilder: 构建器实例
        """
        self.response.feature_extraction_time = extraction_time
        return self
    
    def set_similarity_computation_time(self, computation_time: float) -> 'SearchResponseBuilder':
        """设置相似度计算时间
        
        Args:
            computation_time: 相似度计算时间
            
        Returns:
            SearchResponseBuilder: 构建器实例
        """
        self.response.similarity_computation_time = computation_time
        return self
    
    def add_results(self, results: List[Dict[str, Any]]) -> 'SearchResponseBuilder':
        """批量添加结果
        
        Args:
            results: 结果列表
            
        Returns:
            SearchResponseBuilder: 构建器实例
        """
        for result in results:
            self.response.add_result(result)
        return self
    
    def set_total_candidates(self, total: int) -> 'SearchResponseBuilder':
        """设置候选总数
        
        Args:
            total: 候选总数
            
        Returns:
            SearchResponseBuilder: 构建器实例
        """
        self.response.total_candidates = total
        return self
    
    def set_error(self, error_message: str) -> 'SearchResponseBuilder':
        """设置错误信息
        
        Args:
            error_message: 错误信息
            
        Returns:
            SearchResponseBuilder: 构建器实例
        """
        self.response.success = False
        self.response.error_message = error_message
        return self
    
    def set_metadata(self, metadata: Dict[str, Any]) -> 'SearchResponseBuilder':
        """设置元数据
        
        Args:
            metadata: 元数据
            
        Returns:
            SearchResponseBuilder: 构建器实例
        """
        self.response.metadata.update(metadata)
        return self
    
    def build(self) -> SearchResponse:
        """构建最终响应
        
        Returns:
            SearchResponse: 搜索响应
        """
        self.response.search_time = time.time() - self.start_time
        return self.response