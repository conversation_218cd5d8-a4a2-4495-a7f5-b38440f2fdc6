#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索统计管理模块

该模块负责管理搜索统计信息，包括搜索历史记录、搜索建议生成等功能。
"""

import time
from typing import Dict, Any, List, Optional, Set, Tuple
from collections import Counter, defaultdict
from utils.log_utils import LoggerMixin
from database.fabric_repository import FabricRepository
from .models import SearchQuery, SearchResult
from .search_history import SearchHistoryManager, SearchType


class StatisticsManager(LoggerMixin):
    """搜索统计管理器"""
    
    def __init__(self, fabric_repository: FabricRepository, 
                 history_manager: SearchHistoryManager,
                 max_suggestions: int = 10):
        """初始化统计管理器
        
        Args:
            fabric_repository: 布料数据仓库
            history_manager: 搜索历史管理器
            max_suggestions: 最大建议数量
        """
        super().__init__()
        self.fabric_repository = fabric_repository
        self.history_manager = history_manager
        self.max_suggestions = max_suggestions
        self.search_counts: Dict[str, int] = {}
        self.last_search_time: Dict[str, float] = {}
    
    def record_search_history(self, query: SearchQuery, result: SearchResult) -> None:
        """记录搜索历史
        
        Args:
            query: 搜索查询
            result: 搜索结果
        """
        try:
            # 确定搜索类型
            search_type = self._determine_search_type(query)
            
            # 准备查询数据
            query_data = {
                'text_query': query.text_query,
                'query_image_id': query.query_image_id,
                'query_image_path': query.query_image_path,
                'categories': query.categories,
                'tags': query.tags,
                'use_filters': query.use_filters,
                'active_filters': query.active_filters
            }
            
            # 记录搜索
            self.history_manager.record_search(
                user_id=query.user_id,
                search_type=search_type,
                query_data=query_data,
                result_count=result.total_results,
                search_time=result.search_time,
                session_id=query.session_id,
                metadata=query.metadata
            )
            
            # 更新搜索计数
            search_key = self._generate_search_key(query)
            self.search_counts[search_key] = self.search_counts.get(search_key, 0) + 1
            self.last_search_time[search_key] = time.time()
            
        except Exception as e:
            self.logger.error(f"记录搜索历史失败: {e}")
    
    def _determine_search_type(self, query: SearchQuery) -> SearchType:
        """确定搜索类型
        
        Args:
            query: 搜索查询
            
        Returns:
            SearchType: 搜索类型
        """
        if query.query_type == SearchType.BATCH_SEARCH:
            return SearchType.BATCH_SEARCH
        elif query.query_image_id:
            return SearchType.IMAGE_SIMILARITY
        elif query.text_query:
            return SearchType.TEXT_SEARCH
        elif query.categories:
            return SearchType.CATEGORY_BROWSE
        elif query.tags:
            return SearchType.TAG_SEARCH
        else:
            return SearchType.ADVANCED_FILTER
    
    def _generate_search_key(self, query: SearchQuery) -> str:
        """生成搜索键
        
        Args:
            query: 搜索查询
            
        Returns:
            str: 搜索键
        """
        search_type = self._determine_search_type(query)
        
        if search_type == SearchType.IMAGE_SIMILARITY:
            return f"image:{query.query_image_id}"
        elif search_type == SearchType.TEXT_SEARCH:
            return f"text:{query.text_query.lower()}"
        elif search_type == SearchType.CATEGORY_BROWSE:
            category = query.categories[0] if isinstance(query.categories, list) and query.categories else query.categories
            return f"category:{category}"
        elif search_type == SearchType.TAG_SEARCH:
            return f"tags:{','.join(sorted(query.tags))}"
        else:
            return f"advanced:{int(time.time())}"
    
    def get_search_suggestions(self, query_prefix: str = "", 
                              max_results: int = 10) -> List[Dict[str, Any]]:
        """获取搜索建议
        
        Args:
            query_prefix: 查询前缀
            max_results: 最大结果数
            
        Returns:
            List[Dict[str, Any]]: 搜索建议列表
        """
        try:
            suggestions = []
            query_prefix = query_prefix.lower()
            
            # 从搜索历史中获取建议
            history_suggestions = self._get_suggestions_from_history(query_prefix)
            suggestions.extend(history_suggestions)
            
            # 从类别中获取建议
            if len(suggestions) < max_results:
                category_suggestions = self._get_suggestions_from_categories(query_prefix)
                suggestions.extend(category_suggestions[:max_results - len(suggestions)])
            
            # 从标签中获取建议
            if len(suggestions) < max_results:
                tag_suggestions = self._get_suggestions_from_tags(query_prefix)
                suggestions.extend(tag_suggestions[:max_results - len(suggestions)])
            
            # 限制结果数量
            return suggestions[:max_results]
            
        except Exception as e:
            self.logger.error(f"获取搜索建议失败: {e}")
            return []
    
    def _get_suggestions_from_history(self, query_prefix: str) -> List[Dict[str, Any]]:
        """从搜索历史中获取建议
        
        Args:
            query_prefix: 查询前缀
            
        Returns:
            List[Dict[str, Any]]: 搜索建议列表
        """
        suggestions = []
        
        # 获取最近的文本搜索
        text_searches = self.history_manager.get_recent_searches(
            search_type=SearchType.TEXT_SEARCH,
            limit=self.max_suggestions * 2
        )
        
        # 过滤并格式化建议
        for search in text_searches:
            if not query_prefix or search.query_text.lower().startswith(query_prefix):
                suggestions.append({
                    'type': 'history',
                    'text': search.query_text,
                    'count': self.search_counts.get(f"text:{search.query_text.lower()}", 1),
                    'timestamp': search.timestamp
                })
        
        # 按计数和时间戳排序
        suggestions.sort(key=lambda x: (x['count'], x['timestamp']), reverse=True)
        
        return suggestions[:self.max_suggestions]
    
    def _get_suggestions_from_categories(self, query_prefix: str) -> List[Dict[str, Any]]:
        """从类别中获取建议
        
        Args:
            query_prefix: 查询前缀
            
        Returns:
            List[Dict[str, Any]]: 搜索建议列表
        """
        suggestions = []
        
        try:
            # 获取所有类别
            categories = self.fabric_repository.get_all_categories()
            
            # 过滤并格式化建议
            for category in categories:
                if not query_prefix or category.lower().startswith(query_prefix):
                    count = self.search_counts.get(f"category:{category}", 0)
                    timestamp = self.last_search_time.get(f"category:{category}", 0)
                    
                    suggestions.append({
                        'type': 'category',
                        'text': category,
                        'count': count,
                        'timestamp': timestamp
                    })
            
            # 按计数和时间戳排序
            suggestions.sort(key=lambda x: (x['count'], x['timestamp']), reverse=True)
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"从类别获取建议失败: {e}")
            return []
    
    def _get_suggestions_from_tags(self, query_prefix: str) -> List[Dict[str, Any]]:
        """从标签中获取建议
        
        Args:
            query_prefix: 查询前缀
            
        Returns:
            List[Dict[str, Any]]: 搜索建议列表
        """
        suggestions = []
        
        try:
            # 获取所有标签
            tags = self.fabric_repository.get_all_tags()
            
            # 过滤并格式化建议
            for tag in tags:
                if not query_prefix or tag.lower().startswith(query_prefix):
                    count = self.search_counts.get(f"tags:{tag}", 0)
                    timestamp = self.last_search_time.get(f"tags:{tag}", 0)
                    
                    suggestions.append({
                        'type': 'tag',
                        'text': tag,
                        'count': count,
                        'timestamp': timestamp
                    })
            
            # 按计数和时间戳排序
            suggestions.sort(key=lambda x: (x['count'], x['timestamp']), reverse=True)
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"从标签获取建议失败: {e}")
            return []
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """获取搜索统计信息
        
        Returns:
            Dict[str, Any]: 搜索统计信息
        """
        try:
            # 获取搜索历史统计
            history_stats = self.history_manager.get_statistics()
            
            # 计算搜索类型分布
            search_type_distribution = defaultdict(int)
            for search in self.history_manager.get_all_searches():
                search_type_distribution[search.search_type.value] += 1
            
            # 计算热门搜索
            popular_searches = Counter()
            for key, count in self.search_counts.items():
                if key.startswith("text:"):
                    query_text = key[5:]  # 移除 "text:" 前缀
                    popular_searches[query_text] = count
            
            # 获取热门类别和标签
            popular_categories = Counter()
            popular_tags = Counter()
            
            for key, count in self.search_counts.items():
                if key.startswith("category:"):
                    category = key[9:]  # 移除 "category:" 前缀
                    popular_categories[category] = count
                elif key.startswith("tags:"):
                    tags = key[5:].split(',')  # 移除 "tags:" 前缀并分割
                    for tag in tags:
                        popular_tags[tag] = popular_tags.get(tag, 0) + count
            
            return {
                'total_searches': history_stats.get('total_searches', 0),
                'unique_queries': len(self.search_counts),
                'search_type_distribution': dict(search_type_distribution),
                'popular_searches': dict(popular_searches.most_common(10)),
                'popular_categories': dict(popular_categories.most_common(10)),
                'popular_tags': dict(popular_tags.most_common(10)),
                'average_results_per_search': history_stats.get('average_results', 0),
                'filter_usage_rate': history_stats.get('filter_usage_rate', 0)
            }
            
        except Exception as e:
            self.logger.error(f"获取搜索统计信息失败: {e}")
            return {
                'error': str(e),
                'total_searches': 0,
                'unique_queries': 0
            }