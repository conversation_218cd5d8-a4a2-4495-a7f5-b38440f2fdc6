#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索缓存管理模块

该模块负责管理搜索结果的缓存，包括缓存键生成、缓存获取和存储、缓存清理等功能。
"""

import json
import time
import hashlib
from typing import Dict, Any, Optional, Tuple, List
from utils.log_utils import LoggerMixin
from .models import SearchQuery, SearchResult


class CacheManager(LoggerMixin):
    """搜索缓存管理器"""
    
    def __init__(self, cache_ttl: int = 3600, max_cache_size: int = 100):
        """初始化缓存管理器
        
        Args:
            cache_ttl: 缓存生存时间（秒）
            max_cache_size: 最大缓存条目数
        """
        super().__init__()
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = cache_ttl
        self.max_cache_size = max_cache_size
    
    def generate_cache_key(self, query: SearchQuery) -> str:
        """生成缓存键
        
        根据查询参数生成唯一的缓存键。
        
        Args:
            query: 搜索查询对象
            
        Returns:
            str: 缓存键
        """
        try:
            # 创建查询参数字典
            query_dict = {
                'query_type': query.query_type.value if hasattr(query.query_type, 'value') else str(query.query_type),
                'text_query': query.text_query if hasattr(query, 'text_query') else None,
                'query_image_path': query.query_image_path if hasattr(query, 'query_image_path') else None,
                'query_image_id': query.query_image_id if hasattr(query, 'query_image_id') else None,
                'categories': sorted(query.categories) if hasattr(query, 'categories') and query.categories else [],
                'tags': sorted(query.tags) if hasattr(query, 'tags') and query.tags else [],
                'use_filters': query.use_filters if hasattr(query, 'use_filters') else False,
                'active_filters': [],
                # 添加特征权重和搜索策略到缓存键
                'feature_weights': None,
                'search_strategy': None,
                'similarity_threshold': None,
                'max_results': None
            }

            # 添加特征权重信息
            if hasattr(query, 'feature_weights') and query.feature_weights:
                if isinstance(query.feature_weights, dict):
                    # 确保权重字典的键顺序一致
                    sorted_weights = {k: v for k, v in sorted(query.feature_weights.items())}
                    query_dict['feature_weights'] = sorted_weights
                else:
                    # 如果是FeatureWeights对象，转换为字典
                    try:
                        weights_dict = query.feature_weights.to_dict() if hasattr(query.feature_weights, 'to_dict') else {}
                        sorted_weights = {k: v for k, v in sorted(weights_dict.items())}
                        query_dict['feature_weights'] = sorted_weights
                    except Exception as e:
                        self.logger.warning(f"转换特征权重失败: {e}")

            # 添加搜索策略信息
            if hasattr(query, 'search_strategy'):
                query_dict['search_strategy'] = str(query.search_strategy)

            # 添加相似度阈值
            if hasattr(query, 'similarity_threshold'):
                query_dict['similarity_threshold'] = query.similarity_threshold

            # 添加最大结果数
            if hasattr(query, 'max_results'):
                query_dict['max_results'] = query.max_results
            
            # 添加活动过滤器
            if hasattr(query, 'active_filters') and query.active_filters:
                # 在 models.py 中，active_filters 是一个字符串列表
                if isinstance(query.active_filters, list):
                    if all(isinstance(item, str) for item in query.active_filters):
                        # 如果是字符串列表，直接添加
                        query_dict['active_filters'] = sorted(query.active_filters)
                    else:
                        # 如果是对象列表，提取属性
                        for filter_obj in query.active_filters:
                            try:
                                filter_dict = {
                                    'filter_type': filter_obj.filter_type.value if hasattr(filter_obj, 'filter_type') and hasattr(filter_obj.filter_type, 'value') else str(getattr(filter_obj, 'filter_type', 'unknown')),
                                    'condition': filter_obj.condition.value if hasattr(filter_obj, 'condition') and hasattr(filter_obj.condition, 'value') else str(getattr(filter_obj, 'condition', 'unknown')),
                                    'value': getattr(filter_obj, 'value', None)
                                }
                                query_dict['active_filters'].append(filter_dict)
                            except Exception as e:
                                self.logger.warning(f"处理过滤器对象失败: {e}")
                        
                        # 确保过滤器顺序一致
                        query_dict['active_filters'].sort(key=lambda x: (str(x.get('filter_type', '')), str(x.get('value', ''))))
            
            # 转换为JSON字符串并计算哈希
            query_json = json.dumps(query_dict, sort_keys=True)
            cache_key = hashlib.md5(query_json.encode()).hexdigest()
            
            return cache_key
            
        except Exception as e:
            self.logger.error(f"生成缓存键失败: {e}")
            # 生成备用键
            fallback_key = f"query_{int(time.time())}_{hash(str(query))}"  
            return fallback_key
    
    def get_cached_result(self, query: SearchQuery) -> Optional[SearchResult]:
        """获取缓存结果
        
        Args:
            query: 搜索查询
            
        Returns:
            Optional[SearchResult]: 缓存的搜索结果，如果不存在则返回None
        """
        try:
            cache_key = self.generate_cache_key(query)
            
            if cache_key not in self.cache:
                return None
            
            cache_entry = self.cache[cache_key]
            
            # 检查TTL
            if time.time() - cache_entry['timestamp'] > self.cache_ttl:
                # 缓存过期
                del self.cache[cache_key]
                return None
            
            # 更新访问时间
            cache_entry['last_accessed'] = time.time()
            
            return cache_entry['result']
            
        except Exception as e:
            self.logger.error(f"获取缓存结果失败: {e}")
            return None
    
    def cache_result(self, query: SearchQuery, result: SearchResult) -> None:
        """缓存搜索结果
        
        Args:
            query: 搜索查询
            result: 搜索结果
        """
        try:
            cache_key = self.generate_cache_key(query)
            
            # 检查缓存大小
            if len(self.cache) >= self.max_cache_size:
                self._evict_cache_entries()
            
            # 存储结果
            self.cache[cache_key] = {
                'result': result,
                'timestamp': time.time(),
                'last_accessed': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"缓存搜索结果失败: {e}")
    
    def _evict_cache_entries(self) -> None:
        """清理缓存条目
        
        当缓存达到最大大小时，清理最旧的或最少访问的条目
        """
        try:
            # 按最后访问时间排序
            sorted_entries = sorted(
                self.cache.items(),
                key=lambda x: x[1]['last_accessed']
            )
            
            # 移除最旧的20%条目
            entries_to_remove = max(1, int(len(self.cache) * 0.2))
            
            for i in range(entries_to_remove):
                if i < len(sorted_entries):
                    del self.cache[sorted_entries[i][0]]
                    
        except Exception as e:
            self.logger.error(f"清理缓存条目失败: {e}")
            # 紧急情况下清空缓存
            self.clear_cache()
    
    def clear_cache(self) -> None:
        """清空缓存"""
        try:
            self.cache.clear()
            self.logger.info("缓存已清空")
        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        try:
            current_time = time.time()
            active_entries = 0
            expired_entries = 0
            
            for entry in self.cache.values():
                if current_time - entry['timestamp'] <= self.cache_ttl:
                    active_entries += 1
                else:
                    expired_entries += 1
            
            return {
                'total_entries': len(self.cache),
                'active_entries': active_entries,
                'expired_entries': expired_entries,
                'max_size': self.max_cache_size,
                'ttl': self.cache_ttl
            }
            
        except Exception as e:
            self.logger.error(f"获取缓存统计信息失败: {e}")
            return {
                'total_entries': 0,
                'active_entries': 0,
                'expired_entries': 0,
                'max_size': self.max_cache_size,
                'ttl': self.cache_ttl,
                'error': str(e)
            }