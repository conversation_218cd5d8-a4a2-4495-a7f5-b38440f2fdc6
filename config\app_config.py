#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序配置模块

该模块定义了应用程序的核心配置类，负责管理系统的各种配置参数。
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional


class AppConfig:
    """应用程序配置类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化配置
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认路径
        """
        self.project_root = Path(__file__).parent.parent
        self.config_file = config_file or self.project_root / 'config' / 'app_config.json'
        
        # 初始化基本路径属性
        self._data_dir = str(self.project_root / 'data')
        
        # 初始化默认配置
        self._init_default_config()
        
    def _init_default_config(self):
        """初始化默认配置"""
        self._default_config = {
            'app': {
                'name': '布料图片相似度搜索系统',
                'version': '1.0.0',
                'author': 'AI Assistant',
                'description': '专为纺织行业设计的图像检索工具'
            },
            'paths': {
                'data_dir': str(self.project_root / 'data'),
                'logs_dir': str(self.project_root / 'logs'),
                'models_dir': str(self.project_root / 'models' / 'pretrained'),
                'temp_dir': str(self.project_root / 'temp')
            },
            'database': {
                'default_type': 'standard',
                'chunk_size': 1000,
                'compression': True,
                'backup_enabled': True,
                'auto_sync': False
            },
            'features': {
                'default_types': ['deep', 'color', 'texture', 'shape'],
                'deep_feature_dim': 2048,
                'color_feature_dim': 256,
                'texture_feature_dim': 128,
                'shape_feature_dim': 64,
                'pca_enabled': False,
                'pca_components': 512
            },
            'feature_extraction': {
                'extract_color': True,
                'extract_texture': True,
                'extract_shape': True,
                'hist_bins': 32,
                'n_dominant_colors': 5,
                'lbp_radius': 3,
                'lbp_n_points': 24,
                'n_fourier_descriptors': 32
            },
            'models': {
                'default_model': 'resnet50',
                'available_models': [
                    'resnet50', 'vgg16', 'efficientnet', 'efficientnetv2',
                    'convnext', 'convnextv2', 'vit', 'swin'
                ],
                'model_cache_enabled': True,
                'model_cache_size': 2
            },
            'processing': {
                'default_batch_size': 16,
                'max_batch_size': 64,
                'num_workers': 4,
                'use_gpu': False,
                'gpu_memory_fraction': 0.8
            },
            'search': {
                'default_strategy': 'weighted',
                'default_top_n': 20,
                'max_top_n': 100,
                'similarity_threshold': 0.0,
                'enable_query_expansion': False,
                'expansion_count': 3,
                'enable_user_feedback': True
            },
            'ui': {
                'default_view': 'detailed',
                'items_per_page': 20,
                'max_items_per_page': 100,
                'enable_thumbnails': True,
                'thumbnail_size': 150,
                'enable_preview': True
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file_enabled': True,
                'console_enabled': True,
                'max_file_size': 10485760,  # 10MB
                'backup_count': 5
            }
        }
        
        # 当前配置
        self._config = self._default_config.copy()
        
        # 确保必要的目录存在
        self._ensure_directories()
    
    def update_from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置
        
        Args:
            config_dict: 配置字典
        """
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                # 如果属性不存在，则添加新属性
                setattr(self, key, value)
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for key, path in self._config['paths'].items():
            Path(path).mkdir(parents=True, exist_ok=True)
    
    def load_config(self) -> bool:
        """从文件加载配置
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                
                # 递归更新配置
                self._update_config(self._config, user_config)
                logging.info(f"配置已从 {self.config_file} 加载")
            else:
                logging.info("配置文件不存在，使用默认配置")
                self.save_config()
            
            return True
            
        except Exception as e:
            logging.error(f"加载配置失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置到文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保配置目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            
            logging.info(f"配置已保存到 {self.config_file}")
            return True
            
        except Exception as e:
            logging.error(f"保存配置失败: {e}")
            return False
    
    def _update_config(self, base_config: Dict[str, Any], 
                      user_config: Dict[str, Any]):
        """递归更新配置
        
        Args:
            base_config: 基础配置字典
            user_config: 用户配置字典
        """
        for key, value in user_config.items():
            if key in base_config:
                if isinstance(value, dict) and isinstance(base_config[key], dict):
                    self._update_config(base_config[key], value)
                else:
                    base_config[key] = value
            else:
                base_config[key] = value
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key_path: 配置键路径，使用点号分隔，如 'database.default_type'
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> bool:
        """设置配置值
        
        Args:
            key_path: 配置键路径，使用点号分隔
            value: 配置值
            
        Returns:
            bool: 设置是否成功
        """
        keys = key_path.split('.')
        config = self._config
        
        try:
            # 导航到父级配置
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # 设置值
            config[keys[-1]] = value
            return True
            
        except Exception as e:
            logging.error(f"设置配置失败: {e}")
            return False
    
    def reset_to_default(self) -> bool:
        """重置为默认配置
        
        Returns:
            bool: 重置是否成功
        """
        try:
            self._config = self._default_config.copy()
            self._ensure_directories()
            logging.info("配置已重置为默认值")
            return True
        except Exception as e:
            logging.error(f"重置配置失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """验证配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 验证路径
            for key, path in self._config['paths'].items():
                if not Path(path).exists():
                    logging.warning(f"路径不存在: {key} = {path}")
            
            # 验证模型
            default_model = self._config['models']['default_model']
            available_models = self._config['models']['available_models']
            if default_model not in available_models:
                logging.error(f"默认模型 {default_model} 不在可用模型列表中")
                return False
            
            # 验证批处理大小
            batch_size = self._config['processing']['default_batch_size']
            max_batch_size = self._config['processing']['max_batch_size']
            if batch_size > max_batch_size:
                logging.error(f"默认批处理大小 {batch_size} 超过最大值 {max_batch_size}")
                return False
            
            # 验证搜索参数
            top_n = self._config['search']['default_top_n']
            max_top_n = self._config['search']['max_top_n']
            if top_n > max_top_n:
                logging.error(f"默认搜索结果数量 {top_n} 超过最大值 {max_top_n}")
                return False
            
            logging.info("配置验证通过")
            return True
            
        except Exception as e:
            logging.error(f"配置验证失败: {e}")
            return False
    
    @property
    def app_name(self) -> str:
        """获取应用程序名称"""
        return self.get('app.name', '布料图片相似度搜索系统')
    
    @property
    def app_version(self) -> str:
        """获取应用程序版本"""
        return self.get('app.version', '1.0.0')
    
    @property
    def data_dir(self) -> Path:
        """获取数据目录"""
        return Path(self._data_dir)
        
    @data_dir.setter
    def data_dir(self, value):
        """设置数据目录"""
        self._data_dir = str(value)
    
    @property
    def logs_dir(self) -> Path:
        """获取日志目录"""
        return Path(self.get('paths.logs_dir'))
    
    @property
    def models_dir(self) -> Path:
        """获取模型目录"""
        return Path(self.get('paths.models_dir'))
    
    @property
    def temp_dir(self) -> Path:
        """获取临时目录"""
        return Path(self.get('paths.temp_dir'))
    
    @temp_dir.setter
    def temp_dir(self, value: str):
        """设置临时目录路径"""
        self._default_config['paths']['temp_dir'] = str(value)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"AppConfig(file={self.config_file})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"AppConfig(file={self.config_file}, config={self._config})"
    
    @classmethod
    def load_from_file(cls, config_file: str) -> 'AppConfig':
        """从指定文件加载配置
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            AppConfig: 配置实例
        """
        config = cls(config_file)
        config.load_config()
        return config


# 全局配置实例
app_config = AppConfig()