#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关于对话框

该模块提供应用程序的关于信息对话框。
"""

import os
from typing import Optional, Dict, Any, List, Callable
from dataclasses import dataclass

# PyQt6导入已在main.py中处理，这里不需要再次检查
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QFrame, QScrollArea,
    QSpacerItem, QSizePolicy, QTabWidget, QWidget
)
from PyQt6.QtCore import (
    Qt, QSize, pyqtSignal, QUrl
)
from PyQt6.QtGui import (
    QPixmap, QIcon, QFont, QDesktopServices
)

from utils.log_utils import LoggerMixin
from utils.version_utils import get_version_info
from config.app_config import AppConfig
from gui.gui_utils import GUIUtils
from gui.widgets import WidgetFactory, ButtonStyle, ButtonSize


@dataclass
class AppInfo:
    """应用程序信息"""
    name: str = "Fabric Search"
    version: str = "2.0.0"
    description: str = "基于深度学习的图像搜索工具"
    copyright: str = "© 2023-2024 Fabric Search Team"
    website: str = "https://github.com/fabric-search/fabric-search"
    license_name: str = "MIT License"
    license_url: str = "https://opensource.org/licenses/MIT"


class AboutDialog(QDialog, LoggerMixin):
    """关于对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化组件
        self.widget_factory = WidgetFactory()
        self.app_info = self._get_app_info()
        
        # 设置对话框
        self.setup_dialog()
        self.setup_ui()
    
    def _get_app_info(self) -> AppInfo:
        """获取应用程序信息"""
        try:
            # 尝试从配置或版本工具获取信息
            version_info = get_version_info()
            
            return AppInfo(
                name=version_info.get("name", "Fabric Search"),
                version=version_info.get("version", "2.0.0"),
                description=version_info.get("description", "基于深度学习的图像搜索工具"),
                copyright=version_info.get("copyright", "© 2023-2024 Fabric Search Team"),
                website=version_info.get("website", "https://github.com/fabric-search/fabric-search"),
                license_name=version_info.get("license_name", "MIT License"),
                license_url=version_info.get("license_url", "https://opensource.org/licenses/MIT")
            )
        except Exception as e:
            self.logger.error(f"获取应用程序信息失败: {e}")
            return AppInfo()
    
    def setup_dialog(self):
        """设置对话框"""
        self.setWindowTitle(f"关于 {self.app_info.name}")
        self.setModal(True)
        self.resize(500, 400)
        
        # 居中显示
        GUIUtils.center_window(self)
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题部分
        self._create_header(layout)
        
        # 标签页
        tab_widget = QTabWidget()
        
        # 关于标签页
        about_widget = self._create_about_tab()
        tab_widget.addTab(about_widget, "关于")
        
        # 许可标签页
        license_widget = self._create_license_tab()
        tab_widget.addTab(license_widget, "许可")
        
        # 第三方库标签页
        third_party_widget = self._create_third_party_tab()
        tab_widget.addTab(third_party_widget, "第三方库")
        
        layout.addWidget(tab_widget)
        
        # 底部按钮
        self._create_footer(layout)
    
    def _create_header(self, layout):
        """创建标题部分"""
        header_layout = QHBoxLayout()
        
        # 应用图标
        icon_label = QLabel()
        icon_path = os.path.join(os.path.dirname(__file__), "../../resources/icons/app_icon.png")
        if os.path.exists(icon_path):
            pixmap = QPixmap(icon_path).scaled(64, 64, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            icon_label.setPixmap(pixmap)
        else:
            # 使用默认图标
            icon_label.setText("图标")
            icon_label.setStyleSheet("font-size: 24px; color: #666;")
            icon_label.setFixedSize(64, 64)
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        header_layout.addWidget(icon_label)
        
        # 应用信息
        info_layout = QVBoxLayout()
        
        # 应用名称和版本
        title_label = QLabel(f"{self.app_info.name} {self.app_info.version}")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        info_layout.addWidget(title_label)
        
        # 应用描述
        desc_label = QLabel(self.app_info.description)
        desc_label.setStyleSheet("font-size: 12px; color: #666;")
        info_layout.addWidget(desc_label)
        
        # 版权信息
        copyright_label = QLabel(self.app_info.copyright)
        copyright_label.setStyleSheet("font-size: 10px; color: #888;")
        info_layout.addWidget(copyright_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
    
    def _create_about_tab(self) -> QWidget:
        """创建关于标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 应用介绍
        intro_text = """
<p><b>Fabric Search</b> 是一款基于深度学习的图像搜索工具，专为纺织品设计师和研究人员打造。</p>

<p>主要功能：</p>
<ul>
  <li>基于内容的图像检索 (CBIR)</li>
  <li>多种深度学习模型支持</li>
  <li>高效的相似度计算</li>
  <li>直观的用户界面</li>
  <li>强大的过滤和分类功能</li>
</ul>

<p>本软件使用 Python 和 PyQt6 开发，采用 MIT 许可证发布。</p>

<p>感谢您使用 Fabric Search！</p>
"""
        
        intro_label = QLabel(intro_text)
        intro_label.setWordWrap(True)
        intro_label.setTextFormat(Qt.TextFormat.RichText)
        intro_label.setOpenExternalLinks(True)
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(intro_label)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        
        layout.addWidget(scroll_area)
        
        return widget
    
    def _create_license_tab(self) -> QWidget:
        """创建许可标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 许可证信息
        license_text = f"""
<p>本软件使用 <a href="{self.app_info.license_url}">{self.app_info.license_name}</a> 发布。</p>

<p>MIT License</p>

<p>Copyright (c) 2023-2024 Fabric Search Team</p>

<p>Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:</p>

<p>The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.</p>

<p>THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.</p>
"""
        
        license_label = QLabel(license_text)
        license_label.setWordWrap(True)
        license_label.setTextFormat(Qt.TextFormat.RichText)
        license_label.setOpenExternalLinks(True)
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(license_label)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        
        layout.addWidget(scroll_area)
        
        return widget
    
    def _create_third_party_tab(self) -> QWidget:
        """创建第三方库标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 第三方库信息
        third_party_text = """
<p>本软件使用了以下开源库：</p>

<ul>
  <li><b>PyQt6</b> - Qt for Python (<a href="https://www.riverbankcomputing.com/software/pyqt/">https://www.riverbankcomputing.com/software/pyqt/</a>)</li>
  <li><b>PyTorch</b> - 开源机器学习框架 (<a href="https://pytorch.org/">https://pytorch.org/</a>)</li>
  <li><b>NumPy</b> - 科学计算库 (<a href="https://numpy.org/">https://numpy.org/</a>)</li>
  <li><b>Pillow</b> - Python 图像处理库 (<a href="https://python-pillow.org/">https://python-pillow.org/</a>)</li>
  <li><b>FAISS</b> - 高效相似性搜索库 (<a href="https://github.com/facebookresearch/faiss">https://github.com/facebookresearch/faiss</a>)</li>
  <li><b>SQLite</b> - 轻量级数据库 (<a href="https://www.sqlite.org/">https://www.sqlite.org/</a>)</li>
  <li><b>Matplotlib</b> - 绘图库 (<a href="https://matplotlib.org/">https://matplotlib.org/</a>)</li>
</ul>

<p>每个库都有其各自的许可证，详情请参阅各自的官方网站。</p>
"""
        
        third_party_label = QLabel(third_party_text)
        third_party_label.setWordWrap(True)
        third_party_label.setTextFormat(Qt.TextFormat.RichText)
        third_party_label.setOpenExternalLinks(True)
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(third_party_label)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        
        layout.addWidget(scroll_area)
        
        return widget
    
    def _create_footer(self, layout):
        """创建底部按钮"""
        footer_layout = QHBoxLayout()
        
        # 网站按钮
        website_btn = self.widget_factory.create_button(
            "访问网站",
            icon_name="globe",
            style=ButtonStyle.SECONDARY,
            size=ButtonSize.MEDIUM
        )
        website_btn.clicked.connect(self._open_website)
        
        # 确定按钮
        ok_btn = self.widget_factory.create_button(
            "确定",
            style=ButtonStyle.PRIMARY,
            size=ButtonSize.MEDIUM
        )
        ok_btn.clicked.connect(self.accept)
        
        footer_layout.addWidget(website_btn)
        footer_layout.addStretch()
        footer_layout.addWidget(ok_btn)
        
        layout.addLayout(footer_layout)
    
    def _open_website(self):
        """打开网站"""
        try:
            QDesktopServices.openUrl(QUrl(self.app_info.website))
        except Exception as e:
            self.logger.error(f"打开网站失败: {e}")
    
    @staticmethod
    def show_about_dialog(parent=None):
        """显示关于对话框
        
        Args:
            parent: 父窗口
        """
        try:
            dialog = AboutDialog(parent)
            dialog.exec()
            
        except Exception as e:
            print(f"显示关于对话框失败: {e}")