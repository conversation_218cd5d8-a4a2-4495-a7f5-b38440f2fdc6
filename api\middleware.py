#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API中间件模块

提供请求处理中间件，包括认证、限流、日志记录等功能。
"""

import time
import functools
from typing import Dict, Any, Optional, Callable
from flask import Flask, request, jsonify, g
from werkzeug.exceptions import TooManyRequests, Unauthorized
import hashlib
import json
from collections import defaultdict, deque
from datetime import datetime, timedelta
import threading

from config.config_manager import get_config
from utils.logger_mixin import LoggerMixin


class RateLimiter(LoggerMixin):
    """速率限制器"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        super().__init__()
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = defaultdict(deque)
        self.lock = threading.Lock()
    
    def is_allowed(self, key: str) -> bool:
        """检查是否允许请求
        
        Args:
            key: 限流键（通常是IP地址或用户ID）
            
        Returns:
            bool: 是否允许请求
        """
        now = time.time()
        cutoff = now - self.window_seconds
        
        with self.lock:
            # 清理过期请求
            while self.requests[key] and self.requests[key][0] < cutoff:
                self.requests[key].popleft()
            
            # 检查请求数量
            if len(self.requests[key]) >= self.max_requests:
                return False
            
            # 记录新请求
            self.requests[key].append(now)
            return True
    
    def get_remaining(self, key: str) -> int:
        """获取剩余请求数
        
        Args:
            key: 限流键
            
        Returns:
            int: 剩余请求数
        """
        now = time.time()
        cutoff = now - self.window_seconds
        
        with self.lock:
            # 清理过期请求
            while self.requests[key] and self.requests[key][0] < cutoff:
                self.requests[key].popleft()
            
            return max(0, self.max_requests - len(self.requests[key]))
    
    def get_reset_time(self, key: str) -> float:
        """获取重置时间
        
        Args:
            key: 限流键
            
        Returns:
            float: 重置时间戳
        """
        with self.lock:
            if not self.requests[key]:
                return time.time()
            
            return self.requests[key][0] + self.window_seconds


class RequestLogger(LoggerMixin):
    """请求日志记录器"""
    
    def __init__(self):
        super().__init__()
        self.start_times = {}
    
    def log_request_start(self, request_id: str):
        """记录请求开始
        
        Args:
            request_id: 请求ID
        """
        self.start_times[request_id] = time.time()
        
        self.logger.info(
            f"请求开始 - ID: {request_id}, "
            f"方法: {request.method}, "
            f"路径: {request.path}, "
            f"IP: {request.remote_addr}, "
            f"User-Agent: {request.headers.get('User-Agent', 'Unknown')}"
        )
    
    def log_request_end(self, request_id: str, status_code: int, response_size: int = 0):
        """记录请求结束
        
        Args:
            request_id: 请求ID
            status_code: 响应状态码
            response_size: 响应大小（字节）
        """
        start_time = self.start_times.pop(request_id, time.time())
        duration = time.time() - start_time
        
        self.logger.info(
            f"请求结束 - ID: {request_id}, "
            f"状态码: {status_code}, "
            f"耗时: {duration:.3f}s, "
            f"响应大小: {response_size} bytes"
        )


class APIKeyValidator(LoggerMixin):
    """API密钥验证器"""
    
    def __init__(self, required_keys: Optional[Dict[str, str]] = None):
        super().__init__()
        self.required_keys = required_keys or {}
        self.config = get_config()
    
    def validate_api_key(self, api_key: str) -> bool:
        """验证API密钥
        
        Args:
            api_key: API密钥
            
        Returns:
            bool: 验证是否通过
        """
        if not self.config.security.api_key_required:
            return True
        
        if not api_key:
            return False
        
        # 这里可以实现更复杂的验证逻辑
        # 例如从数据库查询、JWT验证等
        return api_key in self.required_keys
    
    def get_user_info(self, api_key: str) -> Optional[Dict[str, Any]]:
        """获取用户信息
        
        Args:
            api_key: API密钥
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息
        """
        if api_key in self.required_keys:
            return {
                'user_id': self.required_keys[api_key],
                'api_key': api_key
            }
        return None


class RequestValidator(LoggerMixin):
    """请求验证器"""
    
    def __init__(self):
        super().__init__()
        self.config = get_config()
    
    def validate_content_type(self, allowed_types: list) -> bool:
        """验证内容类型
        
        Args:
            allowed_types: 允许的内容类型列表
            
        Returns:
            bool: 验证是否通过
        """
        content_type = request.content_type
        if not content_type:
            return 'application/json' in allowed_types
        
        return any(allowed_type in content_type for allowed_type in allowed_types)
    
    def validate_file_upload(self, file) -> tuple[bool, str]:
        """验证文件上传
        
        Args:
            file: 上传的文件
            
        Returns:
            tuple[bool, str]: (是否通过, 错误信息)
        """
        if not file:
            return False, "没有文件"
        
        if not file.filename:
            return False, "文件名为空"
        
        # 检查文件扩展名
        file_ext = '.' + file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
        if file_ext not in self.config.security.allowed_extensions:
            return False, f"不支持的文件类型: {file_ext}"
        
        # 检查文件大小（这里只是基本检查，实际大小在Flask配置中限制）
        if hasattr(file, 'content_length') and file.content_length:
            if file.content_length > self.config.security.max_file_size:
                return False, f"文件过大: {file.content_length} bytes"
        
        return True, ""


# 全局实例
rate_limiter = None
request_logger = None
api_key_validator = None
request_validator = None


def setup_middleware(app: Flask):
    """设置中间件
    
    Args:
        app: Flask应用实例
    """
    global rate_limiter, request_logger, api_key_validator, request_validator
    
    config = get_config()
    
    # 初始化中间件组件
    if config.security.rate_limit_enabled:
        rate_limiter = RateLimiter(
            max_requests=config.security.max_requests_per_minute,
            window_seconds=60
        )
    
    request_logger = RequestLogger()
    api_key_validator = APIKeyValidator()
    request_validator = RequestValidator()
    
    @app.before_request
    def before_request_middleware():
        """请求前中间件"""
        # 生成请求ID
        request_id = hashlib.md5(
            f"{time.time()}{request.remote_addr}{request.path}".encode()
        ).hexdigest()[:8]
        g.request_id = request_id
        
        # 记录请求开始
        if config.logging.performance_log:
            request_logger.log_request_start(request_id)
        
        # 速率限制
        if rate_limiter and config.security.rate_limit_enabled:
            client_ip = request.remote_addr
            if not rate_limiter.is_allowed(client_ip):
                remaining = rate_limiter.get_remaining(client_ip)
                reset_time = rate_limiter.get_reset_time(client_ip)
                
                response = jsonify({
                    'error': 'Rate limit exceeded',
                    'message': '请求过于频繁，请稍后再试',
                    'remaining': remaining,
                    'reset_time': reset_time
                })
                response.status_code = 429
                response.headers['X-RateLimit-Limit'] = str(config.security.max_requests_per_minute)
                response.headers['X-RateLimit-Remaining'] = str(remaining)
                response.headers['X-RateLimit-Reset'] = str(int(reset_time))
                
                raise TooManyRequests()
        
        # API密钥验证（对于需要认证的端点）
        if config.security.api_key_required and not _is_public_endpoint(request.endpoint):
            api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
            
            if not api_key_validator.validate_api_key(api_key):
                response = jsonify({
                    'error': 'Unauthorized',
                    'message': 'Invalid or missing API key'
                })
                response.status_code = 401
                raise Unauthorized()
            
            # 存储用户信息
            g.user_info = api_key_validator.get_user_info(api_key)
    
    @app.after_request
    def after_request_middleware(response):
        """请求后中间件"""
        # 添加请求ID到响应头
        if hasattr(g, 'request_id'):
            response.headers['X-Request-ID'] = g.request_id
        
        # 添加速率限制信息
        if rate_limiter and config.security.rate_limit_enabled:
            client_ip = request.remote_addr
            remaining = rate_limiter.get_remaining(client_ip)
            reset_time = rate_limiter.get_reset_time(client_ip)
            
            response.headers['X-RateLimit-Limit'] = str(config.security.max_requests_per_minute)
            response.headers['X-RateLimit-Remaining'] = str(remaining)
            response.headers['X-RateLimit-Reset'] = str(int(reset_time))
        
        # 记录请求结束
        if config.logging.performance_log and hasattr(g, 'request_id'):
            response_size = len(response.get_data())
            request_logger.log_request_end(g.request_id, response.status_code, response_size)
        
        return response


def _is_public_endpoint(endpoint: str) -> bool:
    """检查是否为公开端点
    
    Args:
        endpoint: 端点名称
        
    Returns:
        bool: 是否为公开端点
    """
    public_endpoints = {
        'health_check',
        'api_info',
        'static'
    }
    
    return endpoint in public_endpoints


def require_api_key(f: Callable) -> Callable:
    """API密钥验证装饰器
    
    Args:
        f: 被装饰的函数
        
    Returns:
        Callable: 装饰后的函数
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        config = get_config()
        
        if config.security.api_key_required:
            api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
            
            if not api_key_validator.validate_api_key(api_key):
                return jsonify({
                    'error': 'Unauthorized',
                    'message': 'Invalid or missing API key'
                }), 401
            
            g.user_info = api_key_validator.get_user_info(api_key)
        
        return f(*args, **kwargs)
    
    return decorated_function


def validate_json(required_fields: list = None) -> Callable:
    """JSON验证装饰器
    
    Args:
        required_fields: 必需字段列表
        
    Returns:
        Callable: 装饰器函数
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({
                    'error': 'Bad Request',
                    'message': 'Content-Type must be application/json'
                }), 400
            
            try:
                data = request.get_json()
            except Exception as e:
                return jsonify({
                    'error': 'Bad Request',
                    'message': f'Invalid JSON: {str(e)}'
                }), 400
            
            if required_fields:
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    return jsonify({
                        'error': 'Bad Request',
                        'message': f'Missing required fields: {", ".join(missing_fields)}'
                    }), 400
            
            g.json_data = data
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def validate_file_upload(allowed_extensions: list = None) -> Callable:
    """文件上传验证装饰器
    
    Args:
        allowed_extensions: 允许的文件扩展名列表
        
    Returns:
        Callable: 装饰器函数
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            if 'file' not in request.files:
                return jsonify({
                    'error': 'Bad Request',
                    'message': 'No file provided'
                }), 400
            
            file = request.files['file']
            is_valid, error_message = request_validator.validate_file_upload(file)
            
            if not is_valid:
                return jsonify({
                    'error': 'Bad Request',
                    'message': error_message
                }), 400
            
            # 额外的扩展名检查
            if allowed_extensions:
                file_ext = '.' + file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
                if file_ext not in allowed_extensions:
                    return jsonify({
                        'error': 'Bad Request',
                        'message': f'File type not allowed. Allowed types: {", ".join(allowed_extensions)}'
                    }), 400
            
            g.uploaded_file = file
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def log_performance(f: Callable) -> Callable:
    """性能日志装饰器
    
    Args:
        f: 被装饰的函数
        
    Returns:
        Callable: 装饰后的函数
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = f(*args, **kwargs)
            duration = time.time() - start_time
            
            request_logger.logger.info(
                f"函数执行完成 - {f.__name__}, 耗时: {duration:.3f}s"
            )
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            
            request_logger.logger.error(
                f"函数执行失败 - {f.__name__}, 耗时: {duration:.3f}s, 错误: {str(e)}"
            )
            
            raise
    
    return decorated_function