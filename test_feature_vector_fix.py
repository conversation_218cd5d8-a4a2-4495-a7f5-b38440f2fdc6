#!/usr/bin/env python3
"""测试FeatureVector对象存储修复"""

import sys
import os
from pathlib import Path
import numpy as np

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_feature_vector_storage():
    """测试FeatureVector对象的存储"""
    try:
        # 导入必要的模块
        from features.config.config_manager import ConfigManager
        from features.config.feature_config import FeatureExtractorConfig, TraditionalFeatureConfig
        from features.core.feature_extractor import FeatureExtractor
        from features.storage.feature_storage import FeatureStorage
        from features.data_models.feature_models import FeatureVector
        from database.fabric_repository import FabricRepository
        from database.database_manager import DatabaseManager
        
        print("=== 测试FeatureVector对象存储修复 ===\n")
        
        # 初始化组件
        print("1. 初始化组件...")
        config_manager = ConfigManager()
        config_manager.load_all_configs()
        
        db_manager = DatabaseManager()
        fabric_repository = FabricRepository(db_manager)
        feature_storage = FeatureStorage(fabric_repository)
        
        print("✓ 组件初始化成功")
        
        # 创建模拟的FeatureVector对象
        print("2. 创建模拟FeatureVector对象...")
        
        # 创建不同类型的特征向量
        deep_vector = FeatureVector(
            vector=np.random.rand(2048).astype(np.float32),
            dimension=2048,
            feature_type="deep",
            model_name="resnet50"
        )
        
        color_vector = FeatureVector(
            vector=np.random.rand(125).astype(np.float32),
            dimension=125,
            feature_type="color"
        )
        
        texture_vector = FeatureVector(
            vector=np.random.rand(58).astype(np.float32),
            dimension=58,
            feature_type="texture"
        )
        
        shape_vector = FeatureVector(
            vector=np.random.rand(39).astype(np.float32),
            dimension=39,
            feature_type="shape"
        )
        
        # 创建特征字典
        feature_dict = {
            'deep': deep_vector,
            'color': color_vector,
            'texture': texture_vector,
            'shape': shape_vector
        }
        
        print("✓ FeatureVector对象创建成功")
        print(f"  deep: {deep_vector.dimension} 维")
        print(f"  color: {color_vector.dimension} 维")
        print(f"  texture: {texture_vector.dimension} 维")
        print(f"  shape: {shape_vector.dimension} 维")
        
        # 创建测试图像记录
        print("3. 创建测试图像记录...")
        test_image_path = str(Path("test_images/fabric_1.jpg"))
        fabric_image = fabric_repository.create_fabric_image(test_image_path)
        
        if not fabric_image:
            print("❌ 创建图像记录失败")
            return False
        
        print(f"✓ 创建图像记录成功，ID: {fabric_image.id}")
        
        # 测试存储FeatureVector对象
        print("4. 测试存储FeatureVector对象...")
        success = feature_storage.store_multiple_features(
            fabric_image.id, feature_dict
        )
        
        if success:
            print("✓ FeatureVector对象存储成功")
        else:
            print("❌ FeatureVector对象存储失败")
            return False
        
        # 验证数据库中的数据
        print("5. 验证数据库存储...")

        # 直接查询数据库验证存储
        import sqlite3
        conn = sqlite3.connect('data/fabric_search.db')
        cursor = conn.cursor()

        # 检查每种特征类型是否都存储成功
        for feature_type in ['deep_features', 'color_features', 'texture_features', 'shape_features']:
            cursor.execute(
                'SELECT feature_data FROM image_features WHERE image_id = ? AND feature_type = ?',
                (fabric_image.id, feature_type)
            )
            result = cursor.fetchone()
            if result:
                feature_data = result[0]
                print(f"  ✓ {feature_type}: {len(feature_data)} 字节")
            else:
                print(f"  ❌ {feature_type}: 未找到")

        conn.close()
        
        print("\n🎉 FeatureVector对象存储修复测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_feature_vector_storage()
