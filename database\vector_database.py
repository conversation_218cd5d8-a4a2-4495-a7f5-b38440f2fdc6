#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量数据库模块

该模块实现向量数据库的索引构建和搜索功能。
"""

import numpy as np
import pickle
import json
from typing import Dict, List, Any, Optional, Union, Tuple
from pathlib import Path
from abc import ABC, abstractmethod
import faiss
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import normalize

from utils.log_utils import LoggerMixin
from utils.file_utils import FileManager


class VectorDatabase(ABC, LoggerMixin):
    """向量数据库抽象基类"""
    
    def __init__(self, db_path: Union[str, Path]):
        super().__init__()
        self.db_path = Path(db_path)
        self.index = None
        self.feature_vectors = {}
        self.image_paths = []
        
        # 确保目录存在
        file_manager = FileManager()
        file_manager.ensure_dir(self.db_path.parent)
    
    @abstractmethod
    def build_index(self, features: Dict[str, np.ndarray], 
                   index_type: str = 'flat') -> bool:
        """构建向量索引
        
        Args:
            features: 特征字典，键为图像路径，值为特征向量
            index_type: 索引类型
            
        Returns:
            bool: 构建是否成功
        """
        pass
    
    @abstractmethod
    def search(self, query_vector: np.ndarray, k: int = 10, 
              threshold: float = 0.0) -> List[Tuple[str, float]]:
        """搜索相似向量
        
        Args:
            query_vector: 查询向量
            k: 返回结果数量
            threshold: 相似度阈值
            
        Returns:
            List[Tuple[str, float]]: 搜索结果，包含图像路径和相似度
        """
        pass
    
    def save_index(self) -> bool:
        """保存索引"""
        try:
            if self.index is None:
                self.logger.warning("索引为空，无法保存")
                return False
            
            # 保存索引文件
            index_path = self.db_path.with_suffix('.index')
            self._save_index_file(index_path)
            
            # 保存元数据
            meta_path = self.db_path.with_suffix('.meta')
            meta_data = {
                'image_paths': self.image_paths,
                'feature_vectors': {path: vec.tolist() for path, vec in self.feature_vectors.items()}
            }
            
            with open(meta_path, 'w', encoding='utf-8') as f:
                json.dump(meta_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"向量索引保存成功: {self.db_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存向量索引失败: {e}")
            return False
    
    def load_index(self) -> bool:
        """加载索引"""
        try:
            index_path = self.db_path.with_suffix('.index')
            meta_path = self.db_path.with_suffix('.meta')
            
            if not index_path.exists() or not meta_path.exists():
                self.logger.info("索引文件不存在，将创建新索引")
                return True
            
            # 加载索引文件
            self._load_index_file(index_path)
            
            # 加载元数据
            with open(meta_path, 'r', encoding='utf-8') as f:
                meta_data = json.load(f)
            
            self.image_paths = meta_data.get('image_paths', [])
            self.feature_vectors = {
                path: np.array(vec) 
                for path, vec in meta_data.get('feature_vectors', {}).items()
            }
            
            self.logger.info(f"向量索引加载成功，包含 {len(self.image_paths)} 条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"加载向量索引失败: {e}")
            return False
    
    @abstractmethod
    def _save_index_file(self, index_path: Path):
        """保存索引文件"""
        pass
    
    @abstractmethod
    def _load_index_file(self, index_path: Path):
        """加载索引文件"""
        pass


class FaissVectorDatabase(VectorDatabase):
    """基于Faiss的向量数据库"""
    
    def __init__(self, db_path: Union[str, Path], dimension: int = 512):
        super().__init__(db_path)
        self.dimension = dimension
        self.index_type = 'flat'
    
    def build_index(self, features: Dict[str, np.ndarray], 
                   index_type: str = 'flat') -> bool:
        """构建Faiss索引"""
        try:
            if not features:
                self.logger.warning("特征字典为空，无法构建索引")
                return False
            
            self.index_type = index_type
            
            # 准备特征向量
            vectors = []
            paths = []
            
            for image_path, feature_vector in features.items():
                if isinstance(feature_vector, dict):
                    # 如果是字典，取第一个特征
                    feature_vector = next(iter(feature_vector.values()))
                
                # 确保向量是一维的
                if feature_vector.ndim > 1:
                    feature_vector = feature_vector.flatten()
                
                # 标准化向量
                feature_vector = normalize(feature_vector.reshape(1, -1))[0]
                
                vectors.append(feature_vector)
                paths.append(image_path)
            
            if not vectors:
                self.logger.warning("没有有效的特征向量")
                return False
            
            # 转换为numpy数组
            vectors_array = np.array(vectors, dtype=np.float32)
            self.dimension = vectors_array.shape[1]
            
            # 创建Faiss索引
            if index_type == 'flat':
                self.index = faiss.IndexFlatIP(self.dimension)  # 内积索引
            elif index_type == 'ivf':
                quantizer = faiss.IndexFlatIP(self.dimension)
                nlist = min(100, len(vectors) // 10)  # 聚类中心数量
                self.index = faiss.IndexIVFFlat(quantizer, self.dimension, nlist)
                self.index.train(vectors_array)
            else:
                raise ValueError(f"不支持的索引类型: {index_type}")
            
            # 添加向量到索引
            self.index.add(vectors_array)
            
            # 保存数据
            self.image_paths = paths
            self.feature_vectors = dict(zip(paths, vectors))
            
            self.logger.info(f"Faiss索引构建成功，类型: {index_type}, 向量数量: {len(vectors)}")
            return True
            
        except Exception as e:
            self.logger.error(f"构建Faiss索引失败: {e}")
            return False
    
    def search(self, query_vector: np.ndarray, k: int = 10, 
              threshold: float = 0.0) -> List[Tuple[str, float]]:
        """使用Faiss搜索相似向量"""
        try:
            if self.index is None:
                self.logger.warning("索引未构建，无法搜索")
                return []
            
            # 预处理查询向量
            if isinstance(query_vector, dict):
                query_vector = next(iter(query_vector.values()))
            
            if query_vector.ndim > 1:
                query_vector = query_vector.flatten()
            
            # 标准化查询向量
            query_vector = normalize(query_vector.reshape(1, -1))[0]
            query_vector = query_vector.astype(np.float32).reshape(1, -1)
            
            # 搜索
            k = min(k, len(self.image_paths))
            scores, indices = self.index.search(query_vector, k)
            
            # 处理结果
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx >= 0 and idx < len(self.image_paths) and score >= threshold:
                    image_path = self.image_paths[idx]
                    results.append((image_path, float(score)))
            
            self.logger.debug(f"Faiss搜索完成，返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            self.logger.error(f"Faiss搜索失败: {e}")
            return []
    
    def _save_index_file(self, index_path: Path):
        """保存Faiss索引文件"""
        faiss.write_index(self.index, str(index_path))
    
    def _load_index_file(self, index_path: Path):
        """加载Faiss索引文件"""
        self.index = faiss.read_index(str(index_path))


class SimpleVectorDatabase(VectorDatabase):
    """简单向量数据库（基于余弦相似度）"""
    
    def build_index(self, features: Dict[str, np.ndarray], 
                   index_type: str = 'flat') -> bool:
        """构建简单索引"""
        try:
            if not features:
                self.logger.warning("特征字典为空，无法构建索引")
                return False
            
            # 准备特征向量
            vectors = []
            paths = []
            
            for image_path, feature_vector in features.items():
                if isinstance(feature_vector, dict):
                    # 如果是字典，取第一个特征
                    feature_vector = next(iter(feature_vector.values()))
                
                # 确保向量是一维的
                if feature_vector.ndim > 1:
                    feature_vector = feature_vector.flatten()
                
                # 标准化向量
                feature_vector = normalize(feature_vector.reshape(1, -1))[0]
                
                vectors.append(feature_vector)
                paths.append(image_path)
            
            if not vectors:
                self.logger.warning("没有有效的特征向量")
                return False
            
            # 转换为numpy数组
            self.index = np.array(vectors, dtype=np.float32)
            self.image_paths = paths
            self.feature_vectors = dict(zip(paths, vectors))
            
            self.logger.info(f"简单索引构建成功，向量数量: {len(vectors)}")
            return True
            
        except Exception as e:
            self.logger.error(f"构建简单索引失败: {e}")
            return False
    
    def search(self, query_vector: np.ndarray, k: int = 10, 
              threshold: float = 0.0) -> List[Tuple[str, float]]:
        """使用余弦相似度搜索"""
        try:
            if self.index is None:
                self.logger.warning("索引未构建，无法搜索")
                return []
            
            # 预处理查询向量
            if isinstance(query_vector, dict):
                query_vector = next(iter(query_vector.values()))
            
            if query_vector.ndim > 1:
                query_vector = query_vector.flatten()
            
            # 标准化查询向量
            query_vector = normalize(query_vector.reshape(1, -1))[0]
            
            # 计算余弦相似度
            similarities = cosine_similarity([query_vector], self.index)[0]
            
            # 获取top-k结果
            k = min(k, len(similarities))
            top_indices = np.argsort(similarities)[::-1][:k]
            
            # 处理结果
            results = []
            for idx in top_indices:
                score = similarities[idx]
                if score >= threshold:
                    image_path = self.image_paths[idx]
                    results.append((image_path, float(score)))
            
            self.logger.debug(f"余弦相似度搜索完成，返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            self.logger.error(f"余弦相似度搜索失败: {e}")
            return []
    
    def _save_index_file(self, index_path: Path):
        """保存简单索引文件"""
        with open(index_path, 'wb') as f:
            pickle.dump(self.index, f)
    
    def _load_index_file(self, index_path: Path):
        """加载简单索引文件"""
        with open(index_path, 'rb') as f:
            self.index = pickle.load(f)


def create_vector_database(db_path: Union[str, Path], 
                          db_type: str = 'simple',
                          **kwargs) -> VectorDatabase:
    """创建向量数据库实例
    
    Args:
        db_path: 数据库路径
        db_type: 数据库类型，可选值：'simple', 'faiss'
        **kwargs: 其他参数
        
    Returns:
        VectorDatabase: 向量数据库实例
    """
    if db_type == 'simple':
        return SimpleVectorDatabase(db_path)
    elif db_type == 'faiss':
        dimension = kwargs.get('dimension', 512)
        return FaissVectorDatabase(db_path, dimension)
    else:
        raise ValueError(f"不支持的向量数据库类型: {db_type}")