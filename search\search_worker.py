#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索工作线程

该模块提供在后台线程中执行搜索任务的功能。
"""

from typing import List, Dict, Any, Optional
from PyQt6.QtCore import QObject, pyqtSignal

from .models import SearchQuery
from .search_engine import SearchEngine
from utils.logger_mixin import LoggerMixin


class SearchWorker(QObject, LoggerMixin):
    """搜索工作线程"""
    
    # 信号
    finished = pyqtSignal(list)  # 搜索完成
    error = pyqtSignal(str)  # 搜索错误
    progress = pyqtSignal(int, str)  # 搜索进度
    results = pyqtSignal(list)  # 搜索结果
    
    def __init__(self, search_engine: SearchEngine, search_query: SearchQuery, 
                 task_manager=None, task_id: Optional[str] = None):
        super().__init__()
        self.search_engine = search_engine
        self.search_query = search_query
        self.task_manager = task_manager
        self.task_id = task_id
        self._stop_requested = False
        self.search_result = None  # 存储SearchResult对象
    
    def run(self):
        """执行搜索"""
        try:
            self.logger.info("开始执行搜索任务")
            self.progress.emit(0, "开始搜索...")
            
            # 更新任务状态为运行中
            if self.task_manager and self.task_id:
                self.task_manager.update_task_status(self.task_id, "running")
            
            # 模拟搜索进度更新
            progress_steps = [
                (10, "初始化搜索引擎..."),
                (30, "加载索引..."),
                (50, "执行搜索..."),
                (80, "处理结果..."),
                (95, "完成搜索...")
            ]
            
            for progress_value, message in progress_steps:
                if self._stop_requested:
                    self.logger.info("搜索被用户停止")
                    return
                
                self.progress.emit(progress_value, message)
                
                # 更新任务进度
                if self.task_manager and self.task_id:
                    self.task_manager.update_task_progress(self.task_id, progress_value / 100.0)
            
            # 执行搜索
            self.search_result = self.search_engine.search(self.search_query)
            
            if self._stop_requested:
                self.logger.info("搜索被用户停止")
                return
            
            self.progress.emit(100, "搜索完成")
            
            # 更新任务进度为100%
            if self.task_manager and self.task_id:
                self.task_manager.update_task_progress(self.task_id, 1.0)
            
            # 从SearchResult对象中提取结果列表
            images = self.search_result.get_images() if hasattr(self.search_result, 'get_images') else []
            scores = self.search_result.get_scores() if hasattr(self.search_result, 'get_scores') else []
            
            # 将FabricImage对象转换为包含必需字段的字典
            result_list = []
            for i, image in enumerate(images):
                # 确保有足够的分数
                similarity = scores[i] if i < len(scores) else 0.0
                
                # 创建符合验证要求的结果字典
                result_dict = {
                    'path': image.file_path,  # 必需字段
                    'similarity': similarity,  # 必需字段
                    'id': image.id,
                    'file_name': image.file_name,
                    'width': image.width,
                    'height': image.height,
                    'format': image.format,
                    'category': image.category
                }
                result_list.append(result_dict)
            
            # 记录搜索结果信息
            total_results = self.search_result.total_results if hasattr(self.search_result, 'total_results') else len(result_list)
            self.logger.info(f"搜索完成，总结果数: {total_results}，当前页结果数: {len(result_list)}")
            
            # 只通过finished信号发送结果列表，避免重复处理
            self.finished.emit(result_list)
            
            self.logger.info(f"搜索结果已发送，总结果数: {total_results}，当前页结果数: {len(result_list)}")
            self.logger.info(f"SearchResult对象: {self.search_result is not None}，类型: {type(self.search_result) if self.search_result else 'None'}")
            if self.search_result:
                self.logger.info(f"SearchResult.total_results: {self.search_result.total_results}, SearchResult.results长度: {len(self.search_result.results)}")
            
            
        except Exception as e:
            self.logger.error(f"搜索执行失败: {e}")
            self.error.emit(str(e))
    
    def stop(self):
        """停止搜索"""
        self._stop_requested = True
        self.logger.info("收到停止搜索请求")