#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理器模块

该模块提供异步任务管理功能，支持任务创建、监控和结果处理。
"""

import os
import time
import uuid
import threading
import queue
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import json
import logging

try:
    from celery import Celery
    CELERY_AVAILABLE = True
except ImportError:
    CELERY_AVAILABLE = False

from utils.logger_mixin import LoggerMixin


@dataclass
class TaskInfo:
    """任务信息"""
    task_id: str
    name: str
    status: str  # pending, running, completed, failed
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: float = 0.0  # 0.0 - 1.0
    result: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class TaskManager(LoggerMixin):
    """任务管理器"""
    
    def __init__(self, use_celery: bool = False, broker_url: str = 'memory://'):
        """初始化任务管理器
        
        Args:
            use_celery: 是否使用Celery
            broker_url: Celery消息代理URL
        """
        super().__init__()
        
        self.use_celery = use_celery and CELERY_AVAILABLE
        self.broker_url = broker_url
        
        # 任务存储
        self.tasks: Dict[str, TaskInfo] = {}
        self.task_lock = threading.RLock()
        
        # 任务队列
        self.task_queue = queue.PriorityQueue()
        
        # 任务回调
        self.task_callbacks: Dict[str, List[Callable]] = {}
        
        # 初始化Celery（如果可用）
        self.celery_app = None
        if self.use_celery:
            try:
                self.celery_app = Celery('fabric_search', broker=broker_url)
                self.celery_app.conf.update(
                    task_serializer='json',
                    accept_content=['json'],
                    result_serializer='json',
                    enable_utc=True,
                    task_track_started=True,
                    worker_prefetch_multiplier=1
                )
                self.logger.info(f"Celery初始化成功，使用代理: {broker_url}")
            except Exception as e:
                self.logger.error(f"Celery初始化失败: {e}")
                self.use_celery = False
        
        # 启动工作线程
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_running = True
        self.worker_thread.start()
        
        self.logger.info("任务管理器初始化完成")
    
    def create_task(self, name: str, metadata: Dict[str, Any] = None) -> str:
        """创建任务
        
        Args:
            name: 任务名称
            metadata: 任务元数据
            
        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        now = datetime.now()
        
        task_info = TaskInfo(
            task_id=task_id,
            name=name,
            status="pending",
            created_at=now,
            metadata=metadata or {}
        )
        
        with self.task_lock:
            self.tasks[task_id] = task_info
        
        self.logger.info(f"创建任务: {task_id} - {name}")
        return task_id
    
    def get_task(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[TaskInfo]: 任务信息
        """
        with self.task_lock:
            return self.tasks.get(task_id)
    
    def get_task_status(self, task_id: str) -> Optional[str]:
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[str]: 任务状态，如果任务不存在则返回None
        """
        with self.task_lock:
            task = self.tasks.get(task_id)
            return task.status if task else None
    
    def get_all_tasks(self) -> List[TaskInfo]:
        """获取所有任务
        
        Returns:
            List[TaskInfo]: 任务列表
        """
        try:
            with self.task_lock:
                return list(self.tasks.values())
        except Exception as e:
            self.logger.error(f"获取所有任务失败: {e}")
            return []
    
    def update_task_progress(self, task_id: str, progress: float, status: str = None) -> bool:
        """更新任务进度
        
        Args:
            task_id: 任务ID
            progress: 进度值（0.0-1.0）
            status: 状态（可选）
            
        Returns:
            bool: 更新是否成功
        """
        with self.task_lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            task.progress = max(0.0, min(1.0, progress))  # 确保在0-1范围内
            
            if status:
                task.status = status
                if status == "running" and not task.started_at:
                    task.started_at = datetime.now()
                elif status in ["completed", "failed"] and not task.completed_at:
                    task.completed_at = datetime.now()
        
        # 触发回调
        self._trigger_callbacks(task_id)
        
        return True
    
    def update_task_status(self, task_id: str, status: str, error: str = None) -> bool:
        """更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            error: 可选的错误信息
            
        Returns:
            bool: 更新是否成功
        """
        with self.task_lock:
            if task_id not in self.tasks:
                return False
            
            task = self.tasks[task_id]
            task.status = status
            
            # 如果状态变为运行中，记录开始时间
            if status == "running" and not task.started_at:
                task.started_at = datetime.now()
            
            # 如果状态变为完成或失败，记录完成时间
            if status in ["completed", "failed"] and not task.completed_at:
                task.completed_at = datetime.now()
            
            # 如果提供了错误信息，则更新错误信息
            if error is not None:
                task.error = error
        
        # 触发回调
        self._trigger_callbacks(task_id)
        
        return True
    
    def complete_task(self, task_id: str, result: Any = None, error: str = None) -> bool:
        """完成任务
        
        Args:
            task_id: 任务ID
            result: 任务结果
            error: 错误信息（如果失败）
            
        Returns:
            bool: 操作是否成功
        """
        with self.task_lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            task.completed_at = datetime.now()
            task.result = result
            
            if error:
                task.status = "failed"
                task.error = error
            else:
                task.status = "completed"
                task.progress = 1.0
        
        # 触发回调
        self._trigger_callbacks(task_id)
        
        return True
    
    def register_callback(self, task_id: str, callback: Callable[[TaskInfo], None]) -> bool:
        """注册任务回调
        
        Args:
            task_id: 任务ID
            callback: 回调函数
            
        Returns:
            bool: 注册是否成功
        """
        with self.task_lock:
            if task_id not in self.task_callbacks:
                self.task_callbacks[task_id] = []
            
            self.task_callbacks[task_id].append(callback)
        
        return True
    
    def unregister_callback(self, task_id: str, callback: Callable = None) -> bool:
        """取消注册任务回调
        
        Args:
            task_id: 任务ID
            callback: 回调函数（如果为None，则移除所有回调）
            
        Returns:
            bool: 操作是否成功
        """
        with self.task_lock:
            if task_id not in self.task_callbacks:
                return False
            
            if callback is None:
                self.task_callbacks.pop(task_id)
            else:
                try:
                    self.task_callbacks[task_id].remove(callback)
                except ValueError:
                    return False
        
        return True
    
    def _trigger_callbacks(self, task_id: str) -> None:
        """触发任务回调
        
        Args:
            task_id: 任务ID
        """
        callbacks = []
        task = None
        
        try:
            with self.task_lock:
                if task_id in self.task_callbacks:
                    callbacks = self.task_callbacks[task_id].copy()
                
                task = self.tasks.get(task_id)
        except Exception as e:
            self.logger.error(f"获取任务回调信息失败: {e}")
            return
        
        if task and callbacks:
            for callback in callbacks:
                try:
                    callback(task)
                except Exception as e:
                    self.logger.error(f"任务回调执行失败: {e}")
                    # 继续执行其他回调，不让一个回调的失败影响其他回调
    
    def _worker_loop(self) -> None:
        """工作线程循环"""
        while self.worker_running:
            try:
                # 处理队列中的任务
                try:
                    priority, task_id, func, args, kwargs = self.task_queue.get(timeout=1)
                except queue.Empty:
                    # 队列为空，继续等待
                    continue
                except Exception as e:
                    self.logger.error(f"从任务队列获取任务失败: {e}")
                    time.sleep(0.1)
                    continue
                
                # 更新任务状态
                try:
                    self.update_task_progress(task_id, 0.0, "running")
                except Exception as e:
                    self.logger.error(f"更新任务状态失败: {e}")
                
                try:
                    # 执行任务
                    result = func(*args, **kwargs)
                    self.complete_task(task_id, result)
                except Exception as e:
                    self.logger.error(f"任务执行失败: {e}")
                    try:
                        self.complete_task(task_id, error=str(e))
                    except Exception as complete_error:
                        self.logger.error(f"完成失败任务时出错: {complete_error}")
                
                try:
                    self.task_queue.task_done()
                except Exception as e:
                    self.logger.error(f"标记任务完成失败: {e}")
                
            except Exception as e:
                self.logger.error(f"工作线程异常: {e}")
                time.sleep(1)  # 避免CPU占用过高
    
    def submit_task(self, task_id: str, func: Callable, *args, priority: int = 0, **kwargs) -> bool:
        """提交任务到队列
        
        Args:
            task_id: 任务ID
            func: 任务函数
            *args: 函数参数
            priority: 优先级（数字越小优先级越高）
            **kwargs: 函数关键字参数
            
        Returns:
            bool: 提交是否成功
        """
        try:
            self.task_queue.put((priority, task_id, func, args, kwargs))
            return True
        except Exception as e:
            self.logger.error(f"提交任务失败: {e}")
            return False
    
    def shutdown(self) -> None:
        """关闭任务管理器"""
        self.worker_running = False
        
        if self.worker_thread.is_alive():
            self.worker_thread.join(timeout=2)
        
        self.logger.info("任务管理器已关闭")