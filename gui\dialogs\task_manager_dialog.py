#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理器对话框

该模块提供任务管理器界面，用于查看和管理系统中的后台任务。
"""

import os
import time
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime, timedelta

# PyQt6导入
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QProgressBar, QListWidget, QListWidgetItem,
    QFrame, QSplitter, QWidget, QScrollArea, QTabWidget,
    QDialogButtonBox, QSizePolicy, QSpacerItem, QTableWidget,
    QTableWidgetItem, QHeaderView, QAbstractItemView
)
from PyQt6.QtCore import (
    Qt, QTimer, pyqtSignal, QSize, QThread, QObject,
    QDateTime, QElapsedTimer
)
from PyQt6.QtGui import QIcon, QPixmap, QColor, QFont

from utils.logger_mixin import LoggerMixin
from utils.task_manager import TaskManager, TaskInfo
from gui.helpers.message_helper import MessageHelper
from gui.widget_factory import WidgetFactory


class TaskManagerDialog(QDialog, LoggerMixin):
    """任务管理器对话框"""
    
    # 信号
    taskCompleted = pyqtSignal(str, object)  # 任务ID, 结果
    taskFailed = pyqtSignal(str, str)  # 任务ID, 错误信息
    allTasksCompleted = pyqtSignal()  # 所有任务完成
    
    def __init__(self, task_manager: TaskManager, parent=None):
        super().__init__(parent)
        
        self.setWindowTitle("任务管理器")
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)
        
        # 组件
        self.task_manager = task_manager
        self.widget_factory = WidgetFactory()
        
        # 任务监控
        self.monitored_tasks = set()  # 监控的任务ID集合
        self.completed_tasks = set()  # 已完成的任务ID集合
        self.failed_tasks = set()  # 失败的任务ID集合
        self.task_callbacks = {}  # 存储每个任务的回调函数引用
        
        # 更新计时器
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_progress)
        self.update_timer.start(500)  # 500毫秒更新一次
        
        # 设置界面
        self.setup_ui()
        
        # 加载所有任务
        self.load_all_tasks()
    
    def setup_ui(self):
        """设置界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # 标题标签
        title_label = self.widget_factory.create_label(
            "任务管理器", bold=True
        )
        # 手动设置字体大小
        font = title_label.font()
        font.setPointSize(14)
        title_label.setFont(font)
        main_layout.addWidget(title_label)
        
        # 总体进度
        progress_frame = QFrame()
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setContentsMargins(0, 10, 0, 10)
        
        # 总进度标签
        self.overall_progress_label = self.widget_factory.create_label(
            "总体进度: 0%"
        )
        progress_layout.addWidget(self.overall_progress_label)
        
        # 总进度条
        self.overall_progress_bar = self.widget_factory.create_progress_bar(
            minimum=0, maximum=100, value=0
        )
        progress_layout.addWidget(self.overall_progress_bar)
        
        # 状态标签
        self.status_label = self.widget_factory.create_label(
            "就绪"
        )
        progress_layout.addWidget(self.status_label)
        
        main_layout.addWidget(progress_frame)
        
        # 任务表格
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(5)
        self.task_table.setHorizontalHeaderLabels(["任务名称", "状态", "进度", "创建时间", "耗时"])
        self.task_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.task_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.task_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.task_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.task_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        self.task_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        self.task_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        self.task_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        self.task_table.setAlternatingRowColors(True)
        self.task_table.itemSelectionChanged.connect(self.on_task_selected)
        main_layout.addWidget(self.task_table)
        
        # 详情区域
        details_frame = QFrame()
        details_layout = QVBoxLayout(details_frame)
        details_layout.setContentsMargins(0, 0, 0, 0)
        
        details_label = self.widget_factory.create_label("任务详情", bold=True)
        details_layout.addWidget(details_label)
        
        self.details_text = self.widget_factory.create_text_edit()
        self.details_text.setReadOnly(True)
        self.details_text.setMinimumHeight(100)
        details_layout.addWidget(self.details_text)
        
        main_layout.addWidget(details_frame)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        
        # 刷新按钮
        self.refresh_button = self.widget_factory.create_button(
            "刷新", icon_name="refresh"
        )
        self.refresh_button.clicked.connect(self.refresh_tasks)
        button_layout.addWidget(self.refresh_button)
        
        # 取消任务按钮
        self.cancel_button = self.widget_factory.create_button(
            "取消任务", icon_name="cancel"
        )
        self.cancel_button.clicked.connect(self.cancel_selected_task)
        self.cancel_button.setEnabled(False)  # 初始禁用
        button_layout.addWidget(self.cancel_button)
        
        # 清理已完成按钮
        self.clear_button = self.widget_factory.create_button(
            "清理已完成", icon_name="clear"
        )
        self.clear_button.clicked.connect(self.clear_completed_tasks)
        button_layout.addWidget(self.clear_button)
        
        # 弹簧
        button_layout.addSpacerItem(
            QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        )
        
        # 关闭按钮
        self.close_button = self.widget_factory.create_button(
            "关闭", icon_name="close"
        )
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)
        
        main_layout.addLayout(button_layout)
    
    def load_all_tasks(self):
        """加载所有任务"""
        try:
            self.logger.info("开始加载所有任务...")
            
            # 获取所有任务
            tasks = self.task_manager.get_all_tasks()
            self.logger.info(f"从任务管理器获取到 {len(tasks)} 个任务")
            
            # 按创建时间排序（最新的在前）
            tasks.sort(key=lambda x: x.created_at, reverse=True)
            
            # 清空表格
            self.task_table.setRowCount(0)
            self.logger.info("已清空任务表格")
            
            # 清空监控集合
            self.monitored_tasks.clear()
            self.completed_tasks.clear()
            self.failed_tasks.clear()
            
            # 添加任务到表格
            for i, task_info in enumerate(tasks):
                self.logger.info(f"添加任务 {i+1}/{len(tasks)}: {task_info.name} (状态: {task_info.status})")
                self._add_task_to_table(task_info)
                self.monitored_tasks.add(task_info.task_id)
                
                # 根据状态添加到相应集合
                if task_info.status == "completed":
                    self.completed_tasks.add(task_info.task_id)
                elif task_info.status == "failed":
                    self.failed_tasks.add(task_info.task_id)
            
            # 更新总体进度
            self.update_overall_progress()
            
            # 更新状态标签
            status_text = f"已加载 {len(tasks)} 个任务"
            self.status_label.setText(status_text)
            self.logger.info(f"任务加载完成: {status_text}")
            
        except Exception as e:
            error_msg = f"加载任务失败: {e}"
            self.logger.error(error_msg)
            self.status_label.setText(error_msg)
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def refresh_tasks(self):
        """刷新任务列表"""
        self.load_all_tasks()
    
    def cancel_selected_task(self):
        """取消选中的任务"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            return
        
        # 获取选中行的第一个单元格
        item = self.task_table.item(selected_items[0].row(), 0)
        if not item:
            return
        
        # 获取任务ID
        task_id = item.data(Qt.ItemDataRole.UserRole)
        if not task_id:
            return
        
        # 获取任务信息
        task_info = self.task_manager.get_task(task_id)
        if not task_info:
            return
        
        # 只能取消等待中或运行中的任务
        if task_info.status not in ["pending", "running"]:
            MessageHelper.show_warning(
                self, "无法取消", "只能取消等待中或运行中的任务"
            )
            return
        
        # 确认取消
        if not MessageHelper.show_confirmation(
            self, "确认取消", f"确定要取消任务 '{task_info.name}' 吗？"
        ):
            return
        
        # 取消任务
        try:
            # 这里需要实现任务取消逻辑
            # 由于原始代码中没有取消任务的方法，这里只是示例
            # self.task_manager.cancel_task(task_id)
            
            # 更新任务状态
            self.task_manager.update_task_status(task_id, "failed", error="用户取消")
            
            # 刷新任务列表
            self.refresh_tasks()
            
            MessageHelper.show_info(self, "任务已取消", f"任务 '{task_info.name}' 已取消")
            
        except Exception as e:
            self.logger.error(f"取消任务失败: {e}")
            MessageHelper.show_error(self, "取消失败", f"取消任务失败: {e}")
    
    def clear_completed_tasks(self):
        """清理已完成的任务"""
        # 确认清理
        if not MessageHelper.show_confirmation(
            self, "确认清理", "确定要清理所有已完成的任务吗？"
        ):
            return
        
        try:
            # 获取所有已完成的任务
            completed_tasks = self.completed_tasks.union(self.failed_tasks)
            
            # 从表格中移除
            for row in range(self.task_table.rowCount() - 1, -1, -1):
                item = self.task_table.item(row, 0)
                if item and item.data(Qt.ItemDataRole.UserRole) in completed_tasks:
                    self.task_table.removeRow(row)
            
            # 从任务管理器中移除
            # 注意：这里假设任务管理器有remove_task方法，实际可能需要调整
            # for task_id in completed_tasks:
            #     self.task_manager.remove_task(task_id)
            
            # 清空集合
            self.completed_tasks.clear()
            self.failed_tasks.clear()
            
            # 更新状态
            self.status_label.setText("已清理完成的任务")
            
        except Exception as e:
            self.logger.error(f"清理任务失败: {e}")
            MessageHelper.show_error(self, "清理失败", f"清理任务失败: {e}")
    
    def _add_task_to_table(self, task_info: TaskInfo) -> None:
        """添加任务到表格
        
        Args:
            task_info: 任务信息
        """
        row = self.task_table.rowCount()
        self.task_table.insertRow(row)
        
        # 设置任务ID为行的数据
        self.task_table.setItem(row, 0, QTableWidgetItem(task_info.name))
        self.task_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, task_info.task_id)
        
        # 状态
        status_item = QTableWidgetItem(self._get_status_text(task_info.status))
        self._set_status_style(status_item, task_info.status)
        self.task_table.setItem(row, 1, status_item)
        
        # 进度条
        progress_bar = QProgressBar()
        progress_bar.setMinimum(0)
        progress_bar.setMaximum(100)
        progress_bar.setValue(int(task_info.progress * 100))
        progress_bar.setTextVisible(True)
        progress_bar.setFormat("%p%")
        self.task_table.setCellWidget(row, 2, progress_bar)
        
        # 创建时间
        created_time = task_info.created_at.strftime("%Y-%m-%d %H:%M:%S")
        self.task_table.setItem(row, 3, QTableWidgetItem(created_time))
        
        # 耗时
        if task_info.started_at:
            if task_info.completed_at:
                delta = task_info.completed_at - task_info.started_at
            else:
                delta = datetime.now() - task_info.started_at
            duration = str(timedelta(seconds=int(delta.total_seconds())))
        else:
            duration = "--"
        self.task_table.setItem(row, 4, QTableWidgetItem(duration))
    
    def _get_status_text(self, status: str) -> str:
        """获取状态文本
        
        Args:
            status: 状态代码
            
        Returns:
            str: 状态文本
        """
        status_map = {
            "pending": "等待中",
            "running": "运行中",
            "completed": "已完成",
            "failed": "失败"
        }
        return status_map.get(status, status)
    
    def _set_status_style(self, item: QTableWidgetItem, status: str) -> None:
        """设置状态样式
        
        Args:
            item: 表格项
            status: 状态代码
        """
        if status == "pending":
            item.setForeground(QColor(128, 128, 128))  # 灰色
        elif status == "running":
            item.setForeground(QColor(0, 0, 255))  # 蓝色
        elif status == "completed":
            item.setForeground(QColor(0, 128, 0))  # 绿色
        elif status == "failed":
            item.setForeground(QColor(255, 0, 0))  # 红色
    
    def on_task_updated(self, task_info: TaskInfo) -> None:
        """任务更新回调
        
        Args:
            task_info: 任务信息
        """
        # 查找任务在表格中的行
        for row in range(self.task_table.rowCount()):
            item = self.task_table.item(row, 0)
            if item and item.data(Qt.ItemDataRole.UserRole) == task_info.task_id:
                # 更新状态
                status_item = QTableWidgetItem(self._get_status_text(task_info.status))
                self._set_status_style(status_item, task_info.status)
                self.task_table.setItem(row, 1, status_item)
                
                # 更新进度
                progress = int(task_info.progress * 100)
                progress_bar = self.task_table.cellWidget(row, 2)
                if progress_bar and isinstance(progress_bar, QProgressBar):
                    progress_bar.setValue(progress)
                
                # 更新耗时
                if task_info.started_at:
                    if task_info.completed_at:
                        delta = task_info.completed_at - task_info.started_at
                    else:
                        delta = datetime.now() - task_info.started_at
                    duration = str(timedelta(seconds=int(delta.total_seconds())))
                    self.task_table.setItem(row, 4, QTableWidgetItem(duration))
                
                # 如果任务完成或失败，更新集合
                if task_info.status == "completed" and task_info.task_id not in self.completed_tasks:
                    self.completed_tasks.add(task_info.task_id)
                    self.taskCompleted.emit(task_info.task_id, task_info.result)
                elif task_info.status == "failed" and task_info.task_id not in self.failed_tasks:
                    self.failed_tasks.add(task_info.task_id)
                    self.taskFailed.emit(task_info.task_id, task_info.error or "未知错误")
                
                break
        
        # 更新总体进度
        self.update_overall_progress()
        
        # 检查是否所有任务都已完成
        self.check_all_completed()
    
    def update_overall_progress(self) -> None:
        """更新总体进度"""
        if not self.monitored_tasks:
            return
        
        total_progress = 0.0
        running_count = 0
        completed_count = len(self.completed_tasks) + len(self.failed_tasks)
        
        for task_id in self.monitored_tasks:
            task_info = self.task_manager.get_task(task_id)
            if task_info:
                total_progress += task_info.progress
                if task_info.status == "running":
                    running_count += 1
        
        # 计算平均进度
        if self.monitored_tasks:
            avg_progress = total_progress / len(self.monitored_tasks) * 100
            self.overall_progress_bar.setValue(int(avg_progress))
            self.overall_progress_label.setText(f"总体进度: {int(avg_progress)}%")
        
        # 更新状态标签
        total_count = len(self.monitored_tasks)
        self.status_label.setText(
            f"总计: {total_count} | 运行中: {running_count} | 已完成: {len(self.completed_tasks)} | 失败: {len(self.failed_tasks)}"
        )
    
    def update_progress(self) -> None:
        """定时更新进度"""
        # 更新所有任务的耗时
        for row in range(self.task_table.rowCount()):
            item = self.task_table.item(row, 0)
            if item:
                task_id = item.data(Qt.ItemDataRole.UserRole)
                task_info = self.task_manager.get_task(task_id)
                
                if task_info and task_info.started_at and task_info.status == "running":
                    delta = datetime.now() - task_info.started_at
                    duration = str(timedelta(seconds=int(delta.total_seconds())))
                    self.task_table.setItem(row, 4, QTableWidgetItem(duration))
    
    def check_all_completed(self) -> None:
        """检查是否所有任务都已完成"""
        if not self.monitored_tasks:
            return
        
        all_completed = True
        for task_id in self.monitored_tasks:
            task_info = self.task_manager.get_task(task_id)
            if task_info and task_info.status not in ["completed", "failed"]:
                all_completed = False
                break
        
        if all_completed:
            self.allTasksCompleted.emit()
    
    def _delayed_close(self) -> None:
        """延迟关闭对话框"""
        try:
            self.close()
        except Exception as e:
            self.logger.error(f"延迟关闭对话框失败: {e}")
    
    def on_task_selected(self) -> None:
        """任务选择变更"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            self.details_text.setText("选择一个任务以查看详情")
            self.cancel_button.setEnabled(False)
            return
        
        # 获取选中行的第一个单元格
        item = self.task_table.item(selected_items[0].row(), 0)
        if not item:
            return
        
        # 获取任务ID
        task_id = item.data(Qt.ItemDataRole.UserRole)
        if not task_id:
            return
        
        # 获取任务信息
        task_info = self.task_manager.get_task(task_id)
        if not task_info:
            self.details_text.setText(f"任务 {task_id} 不存在")
            self.cancel_button.setEnabled(False)
            return
        
        # 更新详情文本
        details = f"任务ID: {task_info.task_id}\n"
        details += f"名称: {task_info.name}\n"
        details += f"状态: {self._get_status_text(task_info.status)}\n"
        details += f"进度: {int(task_info.progress * 100)}%\n"
        details += f"创建时间: {task_info.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        if task_info.started_at:
            details += f"开始时间: {task_info.started_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        if task_info.completed_at:
            details += f"完成时间: {task_info.completed_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        if task_info.error:
            details += f"错误: {task_info.error}\n"
        
        if task_info.metadata:
            details += "\n元数据:\n"
            for key, value in task_info.metadata.items():
                details += f"  {key}: {value}\n"
        
        self.details_text.setText(details)
        
        # 只有等待中或运行中的任务可以取消
        self.cancel_button.setEnabled(task_info.status in ["pending", "running"])
    
    def closeEvent(self, event):
        """关闭事件"""
        try:
            # 停止定时器
            self.update_timer.stop()
            
            # 清理资源
            self.monitored_tasks.clear()
            self.completed_tasks.clear()
            self.failed_tasks.clear()
            self.task_callbacks.clear()
            
            # 接受关闭事件
            event.accept()
            
        except Exception as e:
            self.logger.error(f"关闭对话框失败: {e}")
            event.accept()  # 强制接受关闭事件