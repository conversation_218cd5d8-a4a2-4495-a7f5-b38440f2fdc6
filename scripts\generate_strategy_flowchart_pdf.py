#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成搜索策略详细流程图PDF

该脚本专门生成搜索策略工作机制的详细流程图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, FancyArrowPatch
import numpy as np
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_strategy_flowchart_pdf():
    """创建搜索策略详细流程图PDF"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 20))
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 20)
    ax.axis('off')
    
    # 定义颜色方案
    colors = {
        'input': '#e3f2fd',         # 浅蓝色 - 输入
        'factory': '#f3e5f5',       # 浅紫色 - 工厂
        'weighted': '#e8f5e8',      # 浅绿色 - 加权策略
        'adaptive': '#fff3e0',      # 浅橙色 - 自适应策略
        'expansion': '#fce4ec',     # 浅粉色 - 查询扩展
        'hybrid': '#f1f8e9',        # 浅黄绿色 - 混合策略
        'single': '#e1f5fe',        # 浅青色 - 单特征
        'compute': '#ffebee',       # 浅红色 - 计算
        'result': '#f9fbe7'         # 浅lime - 结果
    }
    
    # 定义节点
    nodes = [
        # 输入层
        {'pos': (8, 19), 'text': '搜索策略输入\nSearchQuery + Features', 'color': colors['input'], 'size': (4, 0.8)},
        
        # 策略工厂
        {'pos': (8, 17.5), 'text': 'SearchStrategyFactory\n策略工厂模式', 'color': colors['factory'], 'size': (4, 0.8)},
        
        # 策略选择
        {'pos': (8, 16), 'text': '策略类型选择\nStrategy Type Selection', 'color': colors['factory'], 'size': (4, 0.8)},
        
        # 五种策略
        {'pos': (2, 14), 'text': 'WeightedSearchStrategy\n加权搜索策略', 'color': colors['weighted'], 'size': (3, 0.8)},
        {'pos': (5.5, 14), 'text': 'AdaptiveSearchStrategy\n自适应搜索策略', 'color': colors['adaptive'], 'size': (3, 0.8)},
        {'pos': (9, 14), 'text': 'QueryExpansionStrategy\n查询扩展策略', 'color': colors['expansion'], 'size': (3, 0.8)},
        {'pos': (12.5, 14), 'text': 'HybridSearchStrategy\n混合搜索策略', 'color': colors['hybrid'], 'size': (3, 0.8)},
        {'pos': (8, 12), 'text': 'SingleFeatureStrategy\n单特征搜索策略', 'color': colors['single'], 'size': (3, 0.8)},
        
        # 加权策略详细流程
        {'pos': (2, 12.5), 'text': '权重归一化\nweight normalization', 'color': colors['weighted'], 'size': (2.5, 0.6)},
        {'pos': (2, 11.5), 'text': '特征相似度计算\nfeature similarities', 'color': colors['weighted'], 'size': (2.5, 0.6)},
        {'pos': (2, 10.5), 'text': '加权融合\nΣ(wi × si)', 'color': colors['weighted'], 'size': (2.5, 0.6)},
        
        # 自适应策略详细流程
        {'pos': (5.5, 12.5), 'text': '特征方差分析\nfeature variance analysis', 'color': colors['adaptive'], 'size': (2.5, 0.6)},
        {'pos': (5.5, 11.5), 'text': '自动权重调整\nauto weight adjustment', 'color': colors['adaptive'], 'size': (2.5, 0.6)},
        {'pos': (5.5, 10.5), 'text': '执行加权搜索\nexecute weighted search', 'color': colors['adaptive'], 'size': (2.5, 0.6)},
        
        # 查询扩展策略详细流程
        {'pos': (9, 12.5), 'text': '第一轮搜索\ninitial search', 'color': colors['expansion'], 'size': (2.5, 0.6)},
        {'pos': (9, 11.5), 'text': '查询特征扩展\nquery expansion', 'color': colors['expansion'], 'size': (2.5, 0.6)},
        {'pos': (9, 10.5), 'text': '第二轮搜索\nrefined search', 'color': colors['expansion'], 'size': (2.5, 0.6)},
        
        # 混合策略详细流程
        {'pos': (12.5, 12.5), 'text': '多策略并行执行\nparallel execution', 'color': colors['hybrid'], 'size': (2.5, 0.6)},
        {'pos': (12.5, 11.5), 'text': '结果投票融合\nvoting fusion', 'color': colors['hybrid'], 'size': (2.5, 0.6)},
        {'pos': (12.5, 10.5), 'text': '综合排序\nfinal ranking', 'color': colors['hybrid'], 'size': (2.5, 0.6)},
        
        # 单特征策略详细流程
        {'pos': (8, 10.8), 'text': '单一特征提取\nsingle feature extraction', 'color': colors['single'], 'size': (2.5, 0.6)},
        {'pos': (8, 9.8), 'text': '特征相似度计算\nfeature similarity', 'color': colors['single'], 'size': (2.5, 0.6)},
        
        # 相似度计算层
        {'pos': (8, 8.5), 'text': '相似度计算引擎\nSimilarity Calculator', 'color': colors['compute'], 'size': (4, 0.8)},
        
        # 计算方法
        {'pos': (3, 7.5), 'text': '余弦相似度\nCosine Similarity', 'color': colors['compute'], 'size': (2.5, 0.6)},
        {'pos': (6, 7.5), 'text': '欧几里得距离\nEuclidean Distance', 'color': colors['compute'], 'size': (2.5, 0.6)},
        {'pos': (9, 7.5), 'text': '曼哈顿距离\nManhattan Distance', 'color': colors['compute'], 'size': (2.5, 0.6)},
        {'pos': (12, 7.5), 'text': '卡方距离\nChi-square Distance', 'color': colors['compute'], 'size': (2.5, 0.6)},
        
        # 索引搜索
        {'pos': (8, 6.2), 'text': 'FAISS向量索引搜索\nVector Index Search', 'color': colors['compute'], 'size': (4, 0.8)},
        
        # 索引类型
        {'pos': (4, 5.2), 'text': 'FLAT索引\n精确搜索\n<1K数据', 'color': colors['compute'], 'size': (2.2, 0.8)},
        {'pos': (7, 5.2), 'text': 'IVF_FLAT索引\n倒排索引\n1K-10K数据', 'color': colors['compute'], 'size': (2.2, 0.8)},
        {'pos': (10, 5.2), 'text': 'IVF_PQ索引\n压缩索引\n>10K数据', 'color': colors['compute'], 'size': (2.2, 0.8)},
        {'pos': (13, 5.2), 'text': 'HNSW索引\n图索引\n高维数据', 'color': colors['compute'], 'size': (2.2, 0.8)},
        
        # 结果处理
        {'pos': (8, 3.8), 'text': '搜索结果处理\nResult Processing', 'color': colors['result'], 'size': (4, 0.8)},
        
        # 处理步骤
        {'pos': (4, 2.8), 'text': '相似度排序\nSimilarity Ranking', 'color': colors['result'], 'size': (2.5, 0.6)},
        {'pos': (8, 2.8), 'text': '过滤器应用\nFilter Application', 'color': colors['result'], 'size': (2.5, 0.6)},
        {'pos': (12, 2.8), 'text': '分页处理\nPagination', 'color': colors['result'], 'size': (2.5, 0.6)},
        
        # 最终输出
        {'pos': (8, 1.5), 'text': 'SearchResult对象列表\nList[SearchResult]', 'color': colors['result'], 'size': (4, 0.8)},
        
        # 结果结构
        {'pos': (8, 0.5), 'text': 'fabric_image + similarity_score +\nfeature_scores + strategy_used', 'color': colors['result'], 'size': (4.5, 0.6)},
    ]
    
    # 绘制节点
    for node in nodes:
        x, y = node['pos']
        width, height = node['size']
        
        # 创建圆角矩形
        box = FancyBboxPatch(
            (x - width/2, y - height/2), width, height,
            boxstyle="round,pad=0.1",
            facecolor=node['color'],
            edgecolor='#333333',
            linewidth=1.2
        )
        ax.add_patch(box)
        
        # 添加文本
        ax.text(x, y, node['text'], 
               ha='center', va='center', 
               fontsize=8, fontweight='bold',
               wrap=True)
    
    # 定义主要连接线
    main_connections = [
        # 输入到工厂
        ((8, 18.6), (8, 17.9)),
        # 工厂到选择
        ((8, 17.1), (8, 16.4)),
        # 选择到策略
        ((6.5, 15.6), (2, 14.4)),
        ((7, 15.6), (5.5, 14.4)),
        ((8, 15.6), (9, 14.4)),
        ((9, 15.6), (12.5, 14.4)),
        ((8, 15.6), (8, 12.4)),
        
        # 策略内部流程
        ((2, 13.6), (2, 12.8)),
        ((2, 12.2), (2, 11.8)),
        ((2, 11.2), (2, 10.8)),
        
        ((5.5, 13.6), (5.5, 12.8)),
        ((5.5, 12.2), (5.5, 11.8)),
        ((5.5, 11.2), (5.5, 10.8)),
        
        ((9, 13.6), (9, 12.8)),
        ((9, 12.2), (9, 11.8)),
        ((9, 11.2), (9, 10.8)),
        
        ((12.5, 13.6), (12.5, 12.8)),
        ((12.5, 12.2), (12.5, 11.8)),
        ((12.5, 11.2), (12.5, 10.8)),
        
        ((8, 11.6), (8, 11.1)),
        ((8, 10.5), (8, 10.1)),
        
        # 策略到相似度计算
        ((2, 10.2), (7, 8.9)),
        ((5.5, 10.2), (7.5, 8.9)),
        ((9, 10.2), (8.5, 8.9)),
        ((12.5, 10.2), (9, 8.9)),
        ((8, 9.5), (8, 8.9)),
        
        # 相似度计算到方法
        ((6.5, 8.1), (3, 7.8)),
        ((7.2, 8.1), (6, 7.8)),
        ((8.8, 8.1), (9, 7.8)),
        ((9.5, 8.1), (12, 7.8)),
        
        # 相似度计算到索引
        ((8, 8.1), (8, 6.6)),
        
        # 索引到索引类型
        ((6.5, 5.8), (4, 5.6)),
        ((7.2, 5.8), (7, 5.6)),
        ((8.8, 5.8), (10, 5.6)),
        ((9.5, 5.8), (13, 5.6)),
        
        # 索引到结果处理
        ((8, 4.8), (8, 4.2)),
        
        # 结果处理到处理步骤
        ((6.5, 3.4), (4, 3.1)),
        ((8, 3.4), (8, 3.1)),
        ((9.5, 3.4), (12, 3.1)),
        
        # 处理步骤到最终输出
        ((4, 2.5), (7, 1.9)),
        ((8, 2.5), (8, 1.9)),
        ((12, 2.5), (9, 1.9)),
        
        # 最终输出到结果结构
        ((8, 1.1), (8, 0.8)),
    ]
    
    # 绘制连接线
    for start, end in main_connections:
        arrow = FancyArrowPatch(start, end,
                              arrowstyle='->', 
                              mutation_scale=15,
                              color='#333333',
                              linewidth=1.5,
                              alpha=0.8)
        ax.add_patch(arrow)
    
    # 添加标题
    ax.text(8, 19.7, 'Fabric Search - 搜索策略详细工作机制流程图', 
           ha='center', va='center', fontsize=16, fontweight='bold')
    
    # 添加图例
    legend_elements = [
        mpatches.Patch(color=colors['input'], label='输入层'),
        mpatches.Patch(color=colors['factory'], label='策略工厂'),
        mpatches.Patch(color=colors['weighted'], label='加权策略'),
        mpatches.Patch(color=colors['adaptive'], label='自适应策略'),
        mpatches.Patch(color=colors['expansion'], label='查询扩展'),
        mpatches.Patch(color=colors['hybrid'], label='混合策略'),
        mpatches.Patch(color=colors['single'], label='单特征策略'),
        mpatches.Patch(color=colors['compute'], label='计算层'),
        mpatches.Patch(color=colors['result'], label='结果层')
    ]
    
    ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0.02, 0.95), ncol=3)
    
    # 保存文件
    output_dir = Path('docs/diagrams')
    output_dir.mkdir(exist_ok=True)
    
    # 保存PDF
    pdf_path = output_dir / '搜索策略详细工作机制流程图.pdf'
    plt.savefig(pdf_path, format='pdf', bbox_inches='tight', dpi=300, 
                facecolor='white', edgecolor='none')
    print(f"搜索策略PDF流程图已生成: {pdf_path}")
    
    # 保存PNG
    png_path = output_dir / '搜索策略详细工作机制流程图.png'
    plt.savefig(png_path, format='png', bbox_inches='tight', dpi=300,
                facecolor='white', edgecolor='none')
    print(f"搜索策略PNG流程图已生成: {png_path}")
    
    plt.close()

if __name__ == "__main__":
    create_strategy_flowchart_pdf()
