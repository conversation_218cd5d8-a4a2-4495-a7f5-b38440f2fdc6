#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多特征搜索功能
"""

import sys
import os
sys.path.append('.')

import logging
import numpy as np
from search.models import SearchQuery, SearchType
from database.fabric_repository import FabricRepository

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_weight_logic():
    """测试权重判断逻辑"""
    
    def should_use_multi_feature_search(feature_weights):
        """判断是否应该使用多特征搜索"""
        if not feature_weights:
            return False
        
        # 检查是否有非深度学习特征的权重大于0
        non_deep_weights = {k: v for k, v in feature_weights.items() 
                           if k != 'deep_learning' and v > 0}
        
        # 如果有非深度学习特征权重，或者深度学习权重为0，使用多特征搜索
        has_non_deep_features = len(non_deep_weights) > 0
        deep_weight_zero = feature_weights.get('deep_learning', 0) == 0
        
        logger.info(f"特征权重分析: {feature_weights}")
        logger.info(f"非深度学习特征: {non_deep_weights}")
        logger.info(f"是否有非深度学习特征: {has_non_deep_features}")
        logger.info(f"深度学习权重是否为0: {deep_weight_zero}")
        
        return has_non_deep_features or deep_weight_zero
    
    # 测试不同权重组合
    test_weights = [
        {'deep_learning': 1.0, 'color': 0.0, 'texture': 0.0, 'shape': 0.0},  # 只用深度学习
        {'deep_learning': 0.0, 'color': 1.0, 'texture': 0.0, 'shape': 0.0},  # 只用颜色
        {'deep_learning': 0.5, 'color': 0.5, 'texture': 0.0, 'shape': 0.0},  # 混合
        {'deep_learning': 0.0, 'color': 0.0, 'texture': 0.0, 'shape': 0.0},  # 全零
        {'deep_learning': 0.0, 'color': 0.3, 'texture': 0.4, 'shape': 0.3},  # 无深度学习
    ]
    
    for i, weights in enumerate(test_weights):
        print(f'\n=== 测试权重组合 {i+1} ===')
        should_use_multi = should_use_multi_feature_search(weights)
        print(f'结果: {"使用多特征搜索" if should_use_multi else "使用FAISS深度学习搜索"}')

def test_search_strategy_selection():
    """测试搜索策略选择"""
    try:
        from search.search_strategies import FeatureWeights, WeightedSearchStrategy
        from search.similarity_calculator import SimpleSimilarityCalculator
        
        print(f'\n=== 测试搜索策略创建 ===')
        
        # 测试权重对象创建
        weights = FeatureWeights(
            color_weight=1.0,
            texture_weight=0.0,
            shape_weight=0.0,
            deep_learning_weight=0.0
        )
        
        print(f"创建的权重对象: {weights}")
        print(f"权重字典: {weights.to_dict()}")
        
        # 测试搜索策略创建
        similarity_calculator = SimpleSimilarityCalculator()
        strategy = WeightedSearchStrategy(similarity_calculator, weights)
        
        print(f"搜索策略创建成功: {strategy}")
        print(f"策略权重: {strategy.feature_weights.to_dict()}")
        
    except Exception as e:
        logger.error(f"搜索策略测试失败: {e}")

def test_feature_extraction():
    """测试特征提取"""
    try:
        from features.core.feature_extractor import FeatureExtractor
        
        print(f'\n=== 测试特征提取器 ===')
        
        # 创建特征提取器
        feature_extractor = FeatureExtractor()
        print(f"特征提取器创建成功: {feature_extractor}")
        
        # 测试是否可以提取传统特征
        print("特征提取器支持的功能:")
        print(f"- 深度学习特征提取: {hasattr(feature_extractor, 'extract_features_from_path')}")
        print(f"- 传统特征提取: {hasattr(feature_extractor, 'extract_features_from_path')}")
        
    except Exception as e:
        logger.error(f"特征提取器测试失败: {e}")

if __name__ == "__main__":
    print("开始测试多特征搜索功能...")
    
    # 测试权重逻辑
    test_weight_logic()
    
    # 测试搜索策略
    test_search_strategy_selection()
    
    # 测试特征提取
    test_feature_extraction()
    
    print("\n测试完成!")
