#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask应用主文件

创建和配置Flask应用，注册路由和中间件。
"""

import os
from flask import Flask, jsonify, request
from flask_cors import CORS
from werkzeug.exceptions import HTTPException
import logging
from typing import Optional

from config.config_manager import get_config_manager, get_config
from utils.logger_mixin import LoggerMixin
from .middleware import setup_middleware
from .routes import register_routes
from .error_handlers import register_error_handlers


class FabricSearchApp(LoggerMixin):
    """Fabric Search应用类"""
    
    def __init__(self, config_file: Optional[str] = None):
        super().__init__()
        self.config_manager = get_config_manager(config_file)
        self.config = get_config()
        self.app = None
    
    def create_app(self) -> Flask:
        """创建Flask应用
        
        Returns:
            Flask: Flask应用实例
        """
        # 创建Flask应用
        self.app = Flask(__name__)
        
        # 配置应用
        self._configure_app()
        
        # 设置CORS
        self._setup_cors()
        
        # 设置中间件
        setup_middleware(self.app)
        
        # 注册路由
        register_routes(self.app)
        
        # 注册错误处理器
        register_error_handlers(self.app)
        
        # 设置日志
        self._setup_logging()
        
        # 注册应用钩子
        self._register_hooks()
        
        self.logger.info(f"Flask应用创建成功: {self.config.app_name} v{self.config.version}")
        
        return self.app
    
    def _configure_app(self):
        """配置Flask应用"""
        # 基本配置
        self.app.config['SECRET_KEY'] = self.config.security.secret_key or os.urandom(32)
        self.app.config['DEBUG'] = self.config.debug
        
        # JSON配置
        self.app.config['JSON_AS_ASCII'] = False
        self.app.config['JSON_SORT_KEYS'] = False
        
        # 上传配置
        self.app.config['MAX_CONTENT_LENGTH'] = self.config.security.max_file_size
        
        # 性能配置
        self.app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 31536000  # 1年
        
        # 安全配置
        if not self.config.debug:
            self.app.config['SESSION_COOKIE_SECURE'] = True
            self.app.config['SESSION_COOKIE_HTTPONLY'] = True
            self.app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
    
    def _setup_cors(self):
        """设置CORS"""
        CORS(self.app, 
             origins=['*'] if self.config.debug else [],
             methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
             allow_headers=['Content-Type', 'Authorization', 'X-API-Key'],
             expose_headers=['X-Total-Count', 'X-Page-Count'],
             supports_credentials=True)
    
    def _setup_logging(self):
        """设置日志"""
        if not self.config.debug:
            # 生产环境下禁用Flask默认日志
            log = logging.getLogger('werkzeug')
            log.setLevel(logging.ERROR)
    
    def _register_hooks(self):
        """注册应用钩子"""
        
        @self.app.before_request
        def before_request():
            """请求前处理"""
            # 记录请求信息
            if self.config.logging.performance_log:
                self.logger.debug(f"请求: {request.method} {request.url}")
        
        @self.app.after_request
        def after_request(response):
            """请求后处理"""
            # 添加安全头
            if not self.config.debug:
                response.headers['X-Content-Type-Options'] = 'nosniff'
                response.headers['X-Frame-Options'] = 'DENY'
                response.headers['X-XSS-Protection'] = '1; mode=block'
                response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            
            # 记录响应信息
            if self.config.logging.performance_log:
                self.logger.debug(f"响应: {response.status_code} {request.url}")
            
            return response
        
        @self.app.teardown_appcontext
        def teardown_db(error):
            """清理数据库连接"""
            # 这里可以添加数据库连接清理逻辑
            pass


def create_app(config_file: Optional[str] = None) -> Flask:
    """创建Flask应用
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        Flask: Flask应用实例
    """
    app_instance = FabricSearchApp(config_file)
    return app_instance.create_app()


if __name__ == '__main__':
    # 开发服务器
    app = create_app()
    config = get_config()
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=config.debug,
        threaded=True
    )