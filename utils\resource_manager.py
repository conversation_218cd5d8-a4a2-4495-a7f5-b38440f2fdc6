#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源管理工具

该模块提供统一的资源管理功能，确保资源正确释放。
"""

import gc
import threading
import weakref
from typing import Any, Dict, List, Optional, Set
from contextlib import contextmanager
import logging

logger = logging.getLogger(__name__)


class ResourceManager:
    """资源管理器"""
    
    def __init__(self):
        self._resources: Dict[str, Any] = {}
        self._cleanup_callbacks: Dict[str, callable] = {}
        self._lock = threading.RLock()
        self._weak_refs: Set[weakref.ref] = set()
    
    def register_resource(self, name: str, resource: Any, cleanup_callback: Optional[callable] = None):
        """注册资源
        
        Args:
            name: 资源名称
            resource: 资源对象
            cleanup_callback: 清理回调函数
        """
        with self._lock:
            self._resources[name] = resource
            if cleanup_callback:
                self._cleanup_callbacks[name] = cleanup_callback
            
            # 创建弱引用以便自动清理
            def cleanup_ref(ref):
                self._weak_refs.discard(ref)
            
            weak_ref = weakref.ref(resource, cleanup_ref)
            self._weak_refs.add(weak_ref)
            
            logger.debug(f"注册资源: {name}")
    
    def unregister_resource(self, name: str):
        """注销资源
        
        Args:
            name: 资源名称
        """
        with self._lock:
            if name in self._resources:
                resource = self._resources.pop(name)
                cleanup_callback = self._cleanup_callbacks.pop(name, None)
                
                if cleanup_callback:
                    try:
                        cleanup_callback(resource)
                    except Exception as e:
                        logger.error(f"清理资源 {name} 失败: {e}")
                
                logger.debug(f"注销资源: {name}")
    
    def get_resource(self, name: str) -> Optional[Any]:
        """获取资源
        
        Args:
            name: 资源名称
            
        Returns:
            Optional[Any]: 资源对象
        """
        with self._lock:
            return self._resources.get(name)
    
    def cleanup_all(self):
        """清理所有资源"""
        with self._lock:
            resource_names = list(self._resources.keys())
            for name in resource_names:
                self.unregister_resource(name)
            
            # 强制垃圾回收
            gc.collect()
            
            logger.info(f"清理了 {len(resource_names)} 个资源")
    
    def get_resource_count(self) -> int:
        """获取资源数量
        
        Returns:
            int: 资源数量
        """
        with self._lock:
            return len(self._resources)
    
    def list_resources(self) -> List[str]:
        """列出所有资源名称
        
        Returns:
            List[str]: 资源名称列表
        """
        with self._lock:
            return list(self._resources.keys())


class GPUResourceManager:
    """GPU资源管理器"""
    
    def __init__(self):
        self._gpu_resources: Dict[str, Any] = {}
        self._lock = threading.RLock()
    
    def register_gpu_resource(self, name: str, resource: Any):
        """注册GPU资源
        
        Args:
            name: 资源名称
            resource: GPU资源对象
        """
        with self._lock:
            self._gpu_resources[name] = resource
            logger.debug(f"注册GPU资源: {name}")
    
    def unregister_gpu_resource(self, name: str):
        """注销GPU资源
        
        Args:
            name: 资源名称
        """
        with self._lock:
            if name in self._gpu_resources:
                resource = self._gpu_resources.pop(name)
                
                # 尝试将资源移到CPU
                try:
                    if hasattr(resource, 'cpu'):
                        resource.cpu()
                    elif hasattr(resource, 'to'):
                        resource.to('cpu')
                except Exception as e:
                    logger.warning(f"将GPU资源 {name} 移到CPU失败: {e}")
                
                logger.debug(f"注销GPU资源: {name}")
    
    def cleanup_gpu_cache(self):
        """清理GPU缓存"""
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                logger.info("GPU缓存已清理")
        except ImportError:
            logger.warning("PyTorch未安装，无法清理GPU缓存")
        except Exception as e:
            logger.error(f"清理GPU缓存失败: {e}")
    
    def cleanup_all_gpu_resources(self):
        """清理所有GPU资源"""
        with self._lock:
            resource_names = list(self._gpu_resources.keys())
            for name in resource_names:
                self.unregister_gpu_resource(name)
            
            self.cleanup_gpu_cache()
            logger.info(f"清理了 {len(resource_names)} 个GPU资源")


class ThreadResourceManager:
    """线程资源管理器"""
    
    def __init__(self):
        self._threads: Dict[str, threading.Thread] = {}
        self._lock = threading.RLock()
    
    def register_thread(self, name: str, thread: threading.Thread):
        """注册线程
        
        Args:
            name: 线程名称
            thread: 线程对象
        """
        with self._lock:
            self._threads[name] = thread
            logger.debug(f"注册线程: {name}")
    
    def stop_thread(self, name: str, timeout: float = 5.0):
        """停止线程
        
        Args:
            name: 线程名称
            timeout: 超时时间（秒）
        """
        with self._lock:
            if name in self._threads:
                thread = self._threads.pop(name)
                
                if thread.is_alive():
                    # 尝试优雅停止
                    if hasattr(thread, 'stop'):
                        thread.stop()
                    
                    # 等待线程结束
                    thread.join(timeout)
                    
                    if thread.is_alive():
                        logger.warning(f"线程 {name} 未能在 {timeout} 秒内停止")
                    else:
                        logger.debug(f"线程 {name} 已停止")
                else:
                    logger.debug(f"线程 {name} 已经停止")
    
    def stop_all_threads(self, timeout: float = 5.0):
        """停止所有线程
        
        Args:
            timeout: 超时时间（秒）
        """
        with self._lock:
            thread_names = list(self._threads.keys())
            for name in thread_names:
                self.stop_thread(name, timeout)
            
            logger.info(f"停止了 {len(thread_names)} 个线程")


# 全局资源管理器实例
_resource_manager = ResourceManager()
_gpu_resource_manager = GPUResourceManager()
_thread_resource_manager = ThreadResourceManager()


@contextmanager
def managed_resource(name: str, resource: Any, cleanup_callback: Optional[callable] = None):
    """资源管理上下文管理器
    
    Args:
        name: 资源名称
        resource: 资源对象
        cleanup_callback: 清理回调函数
    """
    _resource_manager.register_resource(name, resource, cleanup_callback)
    try:
        yield resource
    finally:
        _resource_manager.unregister_resource(name)


@contextmanager
def managed_gpu_resource(name: str, resource: Any):
    """GPU资源管理上下文管理器
    
    Args:
        name: 资源名称
        resource: GPU资源对象
    """
    _gpu_resource_manager.register_gpu_resource(name, resource)
    try:
        yield resource
    finally:
        _gpu_resource_manager.unregister_gpu_resource(name)


def register_resource(name: str, resource: Any, cleanup_callback: Optional[callable] = None):
    """注册资源"""
    _resource_manager.register_resource(name, resource, cleanup_callback)


def unregister_resource(name: str):
    """注销资源"""
    _resource_manager.unregister_resource(name)


def register_gpu_resource(name: str, resource: Any):
    """注册GPU资源"""
    _gpu_resource_manager.register_gpu_resource(name, resource)


def unregister_gpu_resource(name: str):
    """注销GPU资源"""
    _gpu_resource_manager.unregister_gpu_resource(name)


def register_thread(name: str, thread: threading.Thread):
    """注册线程"""
    _thread_resource_manager.register_thread(name, thread)


def stop_thread(name: str, timeout: float = 5.0):
    """停止线程"""
    _thread_resource_manager.stop_thread(name, timeout)


def cleanup_all_resources():
    """清理所有资源"""
    _thread_resource_manager.stop_all_threads()
    _gpu_resource_manager.cleanup_all_gpu_resources()
    _resource_manager.cleanup_all()


def get_resource_stats() -> Dict[str, int]:
    """获取资源统计信息
    
    Returns:
        Dict[str, int]: 资源统计
    """
    return {
        'total_resources': _resource_manager.get_resource_count(),
        'gpu_resources': len(_gpu_resource_manager._gpu_resources),
        'threads': len(_thread_resource_manager._threads)
    }