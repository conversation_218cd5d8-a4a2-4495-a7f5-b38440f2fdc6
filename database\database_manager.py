#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器

该模块提供数据库的统一管理接口，整合连接管理、事务管理、架构管理和查询执行功能。
"""

import sqlite3
import logging
import threading
import shutil
from pathlib import Path
from typing import Optional, Tuple, List, Any, Dict
from contextlib import contextmanager

from .config.database_config import DatabaseConfig, get_default_config
from .core.connection_manager import ConnectionManager
from .core.transaction_manager import TransactionManager
from .core.schema_manager import SchemaManager
from .core.query_executor import QueryExecutor
from .utils.database_utils import serialize_json, deserialize_json
from .exceptions.database_exceptions import DatabaseError, ConnectionError


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: Optional[DatabaseConfig] = None):
        """初始化数据库管理器
        
        Args:
            config: 数据库配置，如果为None则使用默认配置
        """
        self.config = config or get_default_config()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化各个管理器
        self.connection_manager = ConnectionManager(self.config)
        self.transaction_manager = TransactionManager(self.connection_manager)
        self.schema_manager = SchemaManager(self.connection_manager)
        self.query_executor = QueryExecutor(self.connection_manager)
        
        # 初始化数据库
        self._initialize()
    
    def _initialize(self):
        """初始化数据库"""
        try:
            self.schema_manager.initialize_database()
            self.logger.info("数据库管理器初始化完成")
        except Exception as e:
            self.logger.error(f"数据库管理器初始化失败: {e}")
            raise DatabaseError(f"数据库管理器初始化失败: {e}")
    
    # 连接管理相关方法
    @contextmanager
    def get_connection(self):
        """获取数据库连接上下文管理器
        
        Yields:
            sqlite3.Connection: 数据库连接
        """
        with self.connection_manager.connection_context() as conn:
            yield conn
    
    # 事务管理相关方法
    @contextmanager
    def transaction(self):
        """事务上下文管理器
        
        Yields:
            sqlite3.Connection: 数据库连接
        """
        with self.transaction_manager.transaction() as conn:
            yield conn
    
    def execute_in_transaction(self, operations: List[Dict[str, Any]]) -> List[Any]:
        """在事务中执行多个操作
        
        Args:
            operations: 操作列表
            
        Returns:
            List[Any]: 执行结果列表
        """
        return self.transaction_manager.execute_in_transaction(operations)
    
    def execute_batch_in_transaction(self, sql: str, params_list: List[tuple]) -> int:
        """在事务中执行批量操作
        
        Args:
            sql: SQL语句
            params_list: 参数列表
            
        Returns:
            int: 影响的行数
        """
        return self.transaction_manager.execute_batch_in_transaction(sql, params_list)
    
    # 查询执行相关方法
    def execute_query(self, sql: str, params: Optional[Tuple] = None) -> List[sqlite3.Row]:
        """执行查询
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            List[sqlite3.Row]: 查询结果
        """
        return self.query_executor.execute_query(sql, params)
    
    def execute_update(self, sql: str, params: Optional[Tuple] = None) -> int:
        """执行更新操作
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            int: 影响的行数
        """
        return self.query_executor.execute_update(sql, params)
    
    def execute_insert(self, sql: str, params: Optional[Tuple] = None) -> int:
        """执行插入操作
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            int: 插入记录的ID
        """
        return self.query_executor.execute_insert(sql, params)
    
    def execute_batch(self, sql: str, params_list: List[Tuple]) -> int:
        """执行批量操作
        
        Args:
            sql: SQL语句
            params_list: 参数列表
            
        Returns:
            int: 影响的行数
        """
        return self.query_executor.execute_batch(sql, params_list)
    
    def get_last_insert_id(self) -> int:
        """获取最后插入的ID
        
        Returns:
            int: 最后插入的ID
        """
        result = self.execute_query("SELECT last_insert_rowid()")
        return result[0][0] if result else 0
    
    # 数据库信息相关方法
    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 表是否存在
        """
        return self.query_executor.table_exists(table_name)
    
    def get_table_info(self, table_name: str) -> List[sqlite3.Row]:
        """获取表信息
        
        Args:
            table_name: 表名
            
        Returns:
            List[sqlite3.Row]: 表信息
        """
        return self.query_executor.get_table_info(table_name)
    
    def get_database_size(self) -> int:
        """获取数据库大小（字节）
        
        Returns:
            int: 数据库大小
        """
        return self.query_executor.get_database_size()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.query_executor.get_statistics()
    
    # 数据库维护相关方法
    def vacuum(self):
        """清理数据库"""
        self.query_executor.vacuum_database()
        self.logger.info("数据库清理完成")
    
    def analyze(self):
        """分析数据库"""
        self.query_executor.analyze_database()
        self.logger.info("数据库分析完成")
    
    def backup(self, backup_path: str):
        """备份数据库
        
        Args:
            backup_path: 备份文件路径
        """
        try:
            backup_path_obj = Path(backup_path)
            backup_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(self.config.db_path, backup_path)
            self.logger.info(f"数据库备份完成: {backup_path}")
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            raise DatabaseError(f"数据库备份失败: {e}")
    
    def close(self):
        """关闭数据库连接"""
        try:
            self.connection_manager.close()
            self.logger.info("数据库连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭数据库连接失败: {e}")
    
    def check_and_update_compatibility(self):
        """检查并更新数据库兼容性"""
        try:
            # 这里可以添加版本检查和升级逻辑
            self.logger.info("数据库兼容性检查完成")
        except Exception as e:
            self.logger.error(f"数据库兼容性检查失败: {e}")
            raise DatabaseError(f"数据库兼容性检查失败: {e}")
    
    def perform_incremental_sync(self):
        """执行增量同步"""
        try:
            # 这里可以添加增量同步逻辑
            self.logger.info("增量同步完成")
        except Exception as e:
            self.logger.error(f"增量同步失败: {e}")
            raise DatabaseError(f"增量同步失败: {e}")


# 全局数据库管理器实例
_global_db_manager: Optional[DatabaseManager] = None
_db_manager_lock = threading.Lock()


def get_database_manager(config: Optional[DatabaseConfig] = None) -> DatabaseManager:
    """获取全局数据库管理器实例
    
    Args:
        config: 数据库配置
        
    Returns:
        DatabaseManager: 数据库管理器实例
    """
    global _global_db_manager
    
    with _db_manager_lock:
        if _global_db_manager is None:
            _global_db_manager = DatabaseManager(config)
        return _global_db_manager


def close_database_manager():
    """关闭全局数据库管理器"""
    global _global_db_manager
    
    with _db_manager_lock:
        if _global_db_manager is not None:
            _global_db_manager.close()
            _global_db_manager = None