#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布料图片统计模块

该模块提供布料图片的统计功能，包括总数统计、分类统计、格式统计等。
"""

import logging
from typing import Dict, Any, List, Optional

from .base_repository import BaseRepository
from ..database_manager import DatabaseManager
from ..exceptions.database_exceptions import RepositoryError


class FabricStatisticsRepository(BaseRepository):
    """布料图片统计仓库类"""
    
    @property
    def table_name(self) -> str:
        """表名"""
        return "fabric_images"
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化布料图片统计仓库
        
        Args:
            db_manager: 数据库管理器
        """
        super().__init__(db_manager)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {}
            
            # 总数统计
            stats['total_count'] = self.get_total_count()
            
            # 按类别统计
            stats['by_category'] = self.get_statistics_by_category()
            
            # 按格式统计
            stats['by_format'] = self.get_statistics_by_format()
            
            # 文件大小统计
            stats['file_size'] = self.get_file_size_statistics()
            
            # 最近添加统计
            stats['recent_count'] = self.get_recent_count()
            
            # 图像尺寸统计
            stats['image_dimensions'] = self.get_image_dimensions_statistics()
            
            # 按月份统计
            stats['by_month'] = self.get_statistics_by_month()
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            raise RepositoryError(f"获取统计信息失败: {e}")
    
    def get_total_count(self, include_inactive: bool = False) -> int:
        """获取总数统计
        
        Args:
            include_inactive: 是否包含非活跃记录
            
        Returns:
            int: 总数
        """
        try:
            sql = "SELECT COUNT(*) as total FROM fabric_images"
            if not include_inactive:
                sql += " WHERE is_active = 1"
            
            result = self.db_manager.execute_query(sql)
            return result[0]['total'] if result else 0
            
        except Exception as e:
            self.logger.error(f"获取总数统计失败: {e}")
            raise RepositoryError(f"获取总数统计失败: {e}")
    
    def get_statistics_by_category(self, include_inactive: bool = False) -> Dict[str, int]:
        """按类别统计
        
        Args:
            include_inactive: 是否包含非活跃记录
            
        Returns:
            Dict[str, int]: 类别统计字典
        """
        try:
            sql = """
                SELECT category, COUNT(*) as count 
                FROM fabric_images 
                WHERE category IS NOT NULL AND category != ''
            """
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " GROUP BY category ORDER BY count DESC"
            
            result = self.db_manager.execute_query(sql)
            return {row['category']: row['count'] for row in result}
            
        except Exception as e:
            self.logger.error(f"按类别统计失败: {e}")
            raise RepositoryError(f"按类别统计失败: {e}")
    
    def get_statistics_by_format(self, include_inactive: bool = False) -> Dict[str, int]:
        """按格式统计
        
        Args:
            include_inactive: 是否包含非活跃记录
            
        Returns:
            Dict[str, int]: 格式统计字典
        """
        try:
            sql = """
                SELECT format, COUNT(*) as count 
                FROM fabric_images 
                WHERE format IS NOT NULL
            """
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " GROUP BY format ORDER BY count DESC"
            
            result = self.db_manager.execute_query(sql)
            return {row['format']: row['count'] for row in result}
            
        except Exception as e:
            self.logger.error(f"按格式统计失败: {e}")
            raise RepositoryError(f"按格式统计失败: {e}")
    
    def get_file_size_statistics(self, include_inactive: bool = False) -> Dict[str, float]:
        """获取文件大小统计
        
        Args:
            include_inactive: 是否包含非活跃记录
            
        Returns:
            Dict[str, float]: 文件大小统计字典
        """
        try:
            sql = """
                SELECT 
                    AVG(file_size) as avg_size,
                    MIN(file_size) as min_size,
                    MAX(file_size) as max_size,
                    SUM(file_size) as total_size
                FROM fabric_images 
                WHERE file_size IS NOT NULL
            """
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            result = self.db_manager.execute_query(sql)
            if result:
                row = result[0]
                return {
                    'average': row['avg_size'] or 0,
                    'minimum': row['min_size'] or 0,
                    'maximum': row['max_size'] or 0,
                    'total': row['total_size'] or 0
                }
            else:
                return {
                    'average': 0,
                    'minimum': 0,
                    'maximum': 0,
                    'total': 0
                }
            
        except Exception as e:
            self.logger.error(f"获取文件大小统计失败: {e}")
            raise RepositoryError(f"获取文件大小统计失败: {e}")
    
    def get_recent_count(self, days: int = 7, include_inactive: bool = False) -> int:
        """获取最近添加统计
        
        Args:
            days: 天数
            include_inactive: 是否包含非活跃记录
            
        Returns:
            int: 最近添加的数量
        """
        try:
            sql = f"""
                SELECT COUNT(*) as count 
                FROM fabric_images 
                WHERE created_at >= datetime('now', '-{days} days')
            """
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            result = self.db_manager.execute_query(sql)
            return result[0]['count'] if result else 0
            
        except Exception as e:
            self.logger.error(f"获取最近添加统计失败: {e}")
            raise RepositoryError(f"获取最近添加统计失败: {e}")
    
    def get_image_dimensions_statistics(self, include_inactive: bool = False) -> Dict[str, Any]:
        """获取图像尺寸统计
        
        Args:
            include_inactive: 是否包含非活跃记录
            
        Returns:
            Dict[str, Any]: 图像尺寸统计字典
        """
        try:
            sql = """
                SELECT 
                    AVG(width) as avg_width,
                    MIN(width) as min_width,
                    MAX(width) as max_width,
                    AVG(height) as avg_height,
                    MIN(height) as min_height,
                    MAX(height) as max_height
                FROM fabric_images 
                WHERE width IS NOT NULL AND height IS NOT NULL
            """
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            result = self.db_manager.execute_query(sql)
            if result:
                row = result[0]
                return {
                    'width': {
                        'average': row['avg_width'] or 0,
                        'minimum': row['min_width'] or 0,
                        'maximum': row['max_width'] or 0
                    },
                    'height': {
                        'average': row['avg_height'] or 0,
                        'minimum': row['min_height'] or 0,
                        'maximum': row['max_height'] or 0
                    }
                }
            else:
                return {
                    'width': {'average': 0, 'minimum': 0, 'maximum': 0},
                    'height': {'average': 0, 'minimum': 0, 'maximum': 0}
                }
            
        except Exception as e:
            self.logger.error(f"获取图像尺寸统计失败: {e}")
            raise RepositoryError(f"获取图像尺寸统计失败: {e}")
    
    def get_statistics_by_month(self, months: int = 12, include_inactive: bool = False) -> Dict[str, int]:
        """按月份统计
        
        Args:
            months: 统计的月份数
            include_inactive: 是否包含非活跃记录
            
        Returns:
            Dict[str, int]: 按月份统计字典
        """
        try:
            sql = f"""
                SELECT 
                    strftime('%Y-%m', created_at) as month,
                    COUNT(*) as count
                FROM fabric_images 
                WHERE created_at >= datetime('now', '-{months} months')
            """
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " GROUP BY strftime('%Y-%m', created_at) ORDER BY month DESC"
            
            result = self.db_manager.execute_query(sql)
            return {row['month']: row['count'] for row in result}
            
        except Exception as e:
            self.logger.error(f"按月份统计失败: {e}")
            raise RepositoryError(f"按月份统计失败: {e}")
    
    def get_statistics_by_size_range(self, include_inactive: bool = False) -> Dict[str, int]:
        """按文件大小范围统计
        
        Args:
            include_inactive: 是否包含非活跃记录
            
        Returns:
            Dict[str, int]: 按文件大小范围统计字典
        """
        try:
            sql = """
                SELECT 
                    CASE 
                        WHEN file_size < 1024 * 1024 THEN 'Small (< 1MB)'
                        WHEN file_size < 5 * 1024 * 1024 THEN 'Medium (1-5MB)'
                        WHEN file_size < 10 * 1024 * 1024 THEN 'Large (5-10MB)'
                        ELSE 'Very Large (> 10MB)'
                    END as size_range,
                    COUNT(*) as count
                FROM fabric_images 
                WHERE file_size IS NOT NULL
            """
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " GROUP BY size_range ORDER BY count DESC"
            
            result = self.db_manager.execute_query(sql)
            return {row['size_range']: row['count'] for row in result}
            
        except Exception as e:
            self.logger.error(f"按文件大小范围统计失败: {e}")
            raise RepositoryError(f"按文件大小范围统计失败: {e}")
    
    def get_statistics_by_resolution_range(self, include_inactive: bool = False) -> Dict[str, int]:
        """按分辨率范围统计
        
        Args:
            include_inactive: 是否包含非活跃记录
            
        Returns:
            Dict[str, int]: 按分辨率范围统计字典
        """
        try:
            sql = """
                SELECT 
                    CASE 
                        WHEN width * height < 1024 * 768 THEN 'Low (< 1024x768)'
                        WHEN width * height < 1920 * 1080 THEN 'Medium (< 1920x1080)'
                        WHEN width * height < 3840 * 2160 THEN 'High (< 4K)'
                        ELSE 'Very High (>= 4K)'
                    END as resolution_range,
                    COUNT(*) as count
                FROM fabric_images 
                WHERE width IS NOT NULL AND height IS NOT NULL
            """
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " GROUP BY resolution_range ORDER BY count DESC"
            
            result = self.db_manager.execute_query(sql)
            return {row['resolution_range']: row['count'] for row in result}
            
        except Exception as e:
            self.logger.error(f"按分辨率范围统计失败: {e}")
            raise RepositoryError(f"按分辨率范围统计失败: {e}")
    
    def get_top_categories(self, limit: int = 10, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """获取热门类别
        
        Args:
            limit: 限制数量
            include_inactive: 是否包含非活跃记录
            
        Returns:
            List[Dict[str, Any]]: 热门类别列表
        """
        try:
            sql = """
                SELECT 
                    category,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM fabric_images WHERE is_active = 1), 2) as percentage
                FROM fabric_images 
                WHERE category IS NOT NULL AND category != ''
            """
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += f" GROUP BY category ORDER BY count DESC LIMIT {limit}"
            
            result = self.db_manager.execute_query(sql)
            return [
                {
                    'category': row['category'],
                    'count': row['count'],
                    'percentage': row['percentage']
                }
                for row in result
            ]
            
        except Exception as e:
            self.logger.error(f"获取热门类别失败: {e}")
            raise RepositoryError(f"获取热门类别失败: {e}")
    
    def get_storage_usage(self, include_inactive: bool = False) -> Dict[str, Any]:
        """获取存储使用情况
        
        Args:
            include_inactive: 是否包含非活跃记录
            
        Returns:
            Dict[str, Any]: 存储使用情况字典
        """
        try:
            # 总存储使用量
            sql = "SELECT SUM(file_size) as total_size FROM fabric_images WHERE file_size IS NOT NULL"
            if not include_inactive:
                sql += " AND is_active = 1"
            
            result = self.db_manager.execute_query(sql)
            total_size = result[0]['total_size'] if result and result[0]['total_size'] else 0
            
            # 按格式统计存储使用量
            sql = """
                SELECT 
                    format,
                    SUM(file_size) as size,
                    COUNT(*) as count
                FROM fabric_images 
                WHERE file_size IS NOT NULL AND format IS NOT NULL
            """
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " GROUP BY format ORDER BY size DESC"
            
            format_usage = self.db_manager.execute_query(sql)
            
            return {
                'total_size': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'total_size_gb': round(total_size / (1024 * 1024 * 1024), 2),
                'by_format': [
                    {
                        'format': row['format'],
                        'size': row['size'],
                        'size_mb': round(row['size'] / (1024 * 1024), 2),
                        'count': row['count'],
                        'percentage': round(row['size'] * 100.0 / total_size, 2) if total_size > 0 else 0
                    }
                    for row in format_usage
                ]
            }
            
        except Exception as e:
            self.logger.error(f"获取存储使用情况失败: {e}")
            raise RepositoryError(f"获取存储使用情况失败: {e}")
    
    def get_activity_statistics(self, days: int = 30, include_inactive: bool = False) -> Dict[str, Any]:
        """获取活动统计
        
        Args:
            days: 统计天数
            include_inactive: 是否包含非活跃记录
            
        Returns:
            Dict[str, Any]: 活动统计字典
        """
        try:
            # 按天统计
            sql = f"""
                SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as count
                FROM fabric_images 
                WHERE created_at >= datetime('now', '-{days} days')
            """
            
            if not include_inactive:
                sql += " AND is_active = 1"
            
            sql += " GROUP BY DATE(created_at) ORDER BY date DESC"
            
            daily_stats = self.db_manager.execute_query(sql)
            
            # 总体统计
            total_in_period = sum(row['count'] for row in daily_stats)
            avg_per_day = total_in_period / days if days > 0 else 0
            
            # 最活跃的一天
            most_active_day = max(daily_stats, key=lambda x: x['count']) if daily_stats else None
            
            return {
                'period_days': days,
                'total_in_period': total_in_period,
                'average_per_day': round(avg_per_day, 2),
                'most_active_day': most_active_day,
                'daily_breakdown': [
                    {
                        'date': row['date'],
                        'count': row['count']
                    }
                    for row in daily_stats
                ]
            }
            
        except Exception as e:
            self.logger.error(f"获取活动统计失败: {e}")
            raise RepositoryError(f"获取活动统计失败: {e}")
    
    def get_comprehensive_statistics(self, include_inactive: bool = False) -> Dict[str, Any]:
        """获取综合统计信息
        
        Args:
            include_inactive: 是否包含非活跃记录
            
        Returns:
            Dict[str, Any]: 综合统计信息字典
        """
        try:
            return {
                'basic': {
                    'total_count': self.get_total_count(include_inactive),
                    'recent_count': self.get_recent_count(7, include_inactive)
                },
                'categories': self.get_statistics_by_category(include_inactive),
                'formats': self.get_statistics_by_format(include_inactive),
                'file_size': self.get_file_size_statistics(include_inactive),
                'dimensions': self.get_image_dimensions_statistics(include_inactive),
                'monthly': self.get_statistics_by_month(12, include_inactive),
                'size_ranges': self.get_statistics_by_size_range(include_inactive),
                'resolution_ranges': self.get_statistics_by_resolution_range(include_inactive),
                'top_categories': self.get_top_categories(10, include_inactive),
                'storage': self.get_storage_usage(include_inactive),
                'activity': self.get_activity_statistics(30, include_inactive)
            }
            
        except Exception as e:
            self.logger.error(f"获取综合统计信息失败: {e}")
            raise RepositoryError(f"获取综合统计信息失败: {e}")