#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
索引管理器模块

提供特征索引的统一管理功能，包括：
- 索引创建和维护
- 索引查询和更新
- 索引存储管理
- 索引性能优化
"""

import os
import logging
import threading
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from .feature_index import FeatureIndex
from .index_storage import IndexStorage
from features.config import ConfigManager
from features.utils import setup_logger, create_performance_monitor
from features.utils.file_utils import ensure_directory

logger = logging.getLogger(__name__)


class IndexManager:
    """
    索引管理器
    
    负责管理多个特征索引，提供统一的索引操作接口。
    """
    
    def __init__(self, storage_path: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化索引管理器
        
        Args:
            storage_path: 索引存储路径
            config: 配置参数
        """
        self.storage_path = Path(storage_path)
        self.config = config or {}
        self._indexes: Dict[str, FeatureIndex] = {}
        self._storage = IndexStorage(str(self.storage_path))
        self._lock = threading.RLock()
        
        # 确保存储目录存在
        ensure_directory(str(self.storage_path))
        
        # 初始化日志
        self.logger = setup_logger(
            name=f"{__name__}.{self.__class__.__name__}",
            level=self.config.get('log_level', 'INFO')
        )
        
        # 加载已存在的索引
        self._load_existing_indexes()
    
    def _load_existing_indexes(self) -> None:
        """
        加载已存在的索引
        """
        try:
            index_files = list(self.storage_path.glob("*.index"))
            for index_file in index_files:
                index_name = index_file.stem
                try:
                    index = self._storage.load_index(index_name)
                    if index:
                        self._indexes[index_name] = index
                        self.logger.info(f"已加载索引: {index_name}")
                except Exception as e:
                    self.logger.error(f"加载索引 {index_name} 失败: {str(e)}")
        except Exception as e:
            self.logger.error(f"加载已存在索引失败: {str(e)}")
    
    def create_index(self, name: str, dimension: int, 
                    index_type: str = 'flat') -> bool:
        """
        创建新索引
        
        Args:
            name: 索引名称
            dimension: 特征维度
            index_type: 索引类型
            
        Returns:
            bool: 是否创建成功
        """
        with self._lock:
            try:
                if name in self._indexes:
                    self.logger.warning(f"索引 {name} 已存在")
                    return False
                
                # 创建新索引
                index = FeatureIndex(
                    dimension=dimension,
                    index_type=index_type,
                    config=self.config.get('index_config', {})
                )
                
                self._indexes[name] = index
                
                # 保存索引
                if self._storage.save_index(name, index):
                    self.logger.info(f"成功创建索引: {name}")
                    return True
                else:
                    # 如果保存失败，从内存中移除
                    del self._indexes[name]
                    return False
                    
            except Exception as e:
                self.logger.error(f"创建索引 {name} 失败: {str(e)}")
                return False
    
    def get_index(self, name: str) -> Optional[FeatureIndex]:
        """
        获取索引
        
        Args:
            name: 索引名称
            
        Returns:
            Optional[FeatureIndex]: 索引对象，不存在时返回None
        """
        with self._lock:
            return self._indexes.get(name)
    
    def add_features(self, index_name: str, features: List[Any], 
                    ids: Optional[List[str]] = None) -> bool:
        """
        向索引添加特征
        
        Args:
            index_name: 索引名称
            features: 特征列表
            ids: 特征ID列表
            
        Returns:
            bool: 是否添加成功
        """
        with self._lock:
            try:
                index = self._indexes.get(index_name)
                if not index:
                    self.logger.error(f"索引 {index_name} 不存在")
                    return False
                
                # 添加特征
                if index.add_features(features, ids):
                    # 保存更新后的索引
                    return self._storage.save_index(index_name, index)
                else:
                    return False
                    
            except Exception as e:
                self.logger.error(f"向索引 {index_name} 添加特征失败: {str(e)}")
                return False
    
    def search(self, index_name: str, query_features: Any, 
              top_k: int = 10) -> List[Tuple[str, float]]:
        """
        在索引中搜索
        
        Args:
            index_name: 索引名称
            query_features: 查询特征
            top_k: 返回结果数量
            
        Returns:
            List[Tuple[str, float]]: 搜索结果列表 (id, score)
        """
        with self._lock:
            try:
                index = self._indexes.get(index_name)
                if not index:
                    self.logger.error(f"索引 {index_name} 不存在")
                    return []
                
                return index.search(query_features, top_k)
                
            except Exception as e:
                self.logger.error(f"在索引 {index_name} 中搜索失败: {str(e)}")
                return []
    
    def remove_features(self, index_name: str, ids: List[str]) -> bool:
        """
        从索引中移除特征
        
        Args:
            index_name: 索引名称
            ids: 要移除的特征ID列表
            
        Returns:
            bool: 是否移除成功
        """
        with self._lock:
            try:
                index = self._indexes.get(index_name)
                if not index:
                    self.logger.error(f"索引 {index_name} 不存在")
                    return False
                
                # 移除特征
                if index.remove_features(ids):
                    # 保存更新后的索引
                    return self._storage.save_index(index_name, index)
                else:
                    return False
                    
            except Exception as e:
                self.logger.error(f"从索引 {index_name} 移除特征失败: {str(e)}")
                return False
    
    def delete_index(self, name: str) -> bool:
        """
        删除索引
        
        Args:
            name: 索引名称
            
        Returns:
            bool: 是否删除成功
        """
        with self._lock:
            try:
                # 从内存中移除
                if name in self._indexes:
                    del self._indexes[name]
                
                # 从存储中删除
                if self._storage.delete_index(name):
                    self.logger.info(f"成功删除索引: {name}")
                    return True
                else:
                    return False
                    
            except Exception as e:
                self.logger.error(f"删除索引 {name} 失败: {str(e)}")
                return False
    
    def list_indexes(self) -> List[str]:
        """
        列出所有索引名称
        
        Returns:
            List[str]: 索引名称列表
        """
        with self._lock:
            return list(self._indexes.keys())
    
    def get_index_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取索引信息
        
        Args:
            name: 索引名称
            
        Returns:
            Optional[Dict[str, Any]]: 索引信息，不存在时返回None
        """
        with self._lock:
            index = self._indexes.get(name)
            if not index:
                return None
            
            return {
                'name': name,
                'dimension': index.dimension,
                'size': index.size(),
                'index_type': getattr(index, 'index_type', 'unknown'),
                'created_at': getattr(index, 'created_at', None),
                'updated_at': getattr(index, 'updated_at', None)
            }
    
    def optimize_index(self, name: str) -> bool:
        """
        优化索引
        
        Args:
            name: 索引名称
            
        Returns:
            bool: 是否优化成功
        """
        with self._lock:
            try:
                index = self._indexes.get(name)
                if not index:
                    self.logger.error(f"索引 {name} 不存在")
                    return False
                
                # 执行索引优化
                if hasattr(index, 'optimize'):
                    if index.optimize():
                        # 保存优化后的索引
                        return self._storage.save_index(name, index)
                    else:
                        return False
                else:
                    self.logger.warning(f"索引 {name} 不支持优化操作")
                    return True
                    
            except Exception as e:
                self.logger.error(f"优化索引 {name} 失败: {str(e)}")
                return False
    
    def backup_index(self, name: str, backup_path: str) -> bool:
        """
        备份索引
        
        Args:
            name: 索引名称
            backup_path: 备份路径
            
        Returns:
            bool: 是否备份成功
        """
        with self._lock:
            try:
                index = self._indexes.get(name)
                if not index:
                    self.logger.error(f"索引 {name} 不存在")
                    return False
                
                # 确保备份目录存在
                ensure_directory(os.path.dirname(backup_path))
                
                # 执行备份
                backup_storage = IndexStorage(os.path.dirname(backup_path))
                backup_name = os.path.basename(backup_path)
                
                if backup_storage.save_index(backup_name, index):
                    self.logger.info(f"成功备份索引 {name} 到 {backup_path}")
                    return True
                else:
                    return False
                    
            except Exception as e:
                self.logger.error(f"备份索引 {name} 失败: {str(e)}")
                return False
    
    def restore_index(self, name: str, backup_path: str) -> bool:
        """
        恢复索引
        
        Args:
            name: 索引名称
            backup_path: 备份路径
            
        Returns:
            bool: 是否恢复成功
        """
        with self._lock:
            try:
                # 从备份加载索引
                backup_storage = IndexStorage(os.path.dirname(backup_path))
                backup_name = os.path.basename(backup_path)
                
                index = backup_storage.load_index(backup_name)
                if not index:
                    self.logger.error(f"无法从 {backup_path} 加载索引")
                    return False
                
                # 保存到当前存储
                self._indexes[name] = index
                
                if self._storage.save_index(name, index):
                    self.logger.info(f"成功恢复索引 {name} 从 {backup_path}")
                    return True
                else:
                    # 如果保存失败，从内存中移除
                    del self._indexes[name]
                    return False
                    
            except Exception as e:
                self.logger.error(f"恢复索引 {name} 失败: {str(e)}")
                return False
    
    def close(self) -> None:
        """
        关闭索引管理器
        """
        with self._lock:
            try:
                # 保存所有索引
                for name, index in self._indexes.items():
                    try:
                        self._storage.save_index(name, index)
                    except Exception as e:
                        self.logger.error(f"保存索引 {name} 失败: {str(e)}")
                
                # 清理资源
                self._indexes.clear()
                
                self.logger.info("索引管理器已关闭")
                
            except Exception as e:
                self.logger.error(f"关闭索引管理器失败: {str(e)}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()