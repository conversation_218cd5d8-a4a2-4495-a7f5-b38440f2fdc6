#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像查看器数据模型

该模块定义图像查看器使用的数据模型和枚举。
"""

from enum import Enum
from dataclasses import dataclass


class ViewMode(Enum):
    """查看模式"""
    FIT_TO_WINDOW = "fit_to_window"
    FIT_TO_WIDTH = "fit_to_width"
    FIT_TO_HEIGHT = "fit_to_height"
    ACTUAL_SIZE = "actual_size"
    CUSTOM = "custom"


class ZoomMode(Enum):
    """缩放模式"""
    ZOOM_IN = "zoom_in"
    ZOOM_OUT = "zoom_out"
    ZOOM_FIT = "zoom_fit"
    ZOOM_RESET = "zoom_reset"


class RotationAngle(Enum):
    """旋转角度"""
    ROTATE_90 = 90
    ROTATE_180 = 180
    ROTATE_270 = 270


@dataclass
class ViewerState:
    """查看器状态"""
    zoom_factor: float = 1.0
    rotation_angle: int = 0
    horizontal_flip: bool = False
    vertical_flip: bool = False
    view_mode: ViewMode = ViewMode.FIT_TO_WINDOW
    
    def __init__(self):
        """初始化状态"""
        self.zoom_factor = 1.0
        self.rotation_angle = 0
        self.horizontal_flip = False
        self.vertical_flip = False
        self.view_mode = ViewMode.FIT_TO_WINDOW
        
        # 为了兼容测试用例，添加属性映射
        self.__class__.flipped_horizontal = property(
            lambda self: self.horizontal_flip,
            lambda self, value: setattr(self, 'horizontal_flip', value)
        )
        self.__class__.flipped_vertical = property(
            lambda self: self.vertical_flip,
            lambda self, value: setattr(self, 'vertical_flip', value)
        )
    
    def reset(self):
        """重置状态"""
        self.zoom_factor = 1.0
        self.rotation_angle = 0
        self.horizontal_flip = False
        self.vertical_flip = False
        self.view_mode = ViewMode.FIT_TO_WINDOW