#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控模块

提供详细的性能监控和分析功能，帮助识别和解决性能瓶颈。
"""

import time
import threading
import psutil
import numpy as np
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import logging
from contextlib import contextmanager
import json
from pathlib import Path


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    unit: str
    timestamp: float
    category: str = 'general'
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class TimingResult:
    """计时结果"""
    operation: str
    duration: float
    start_time: float
    end_time: float
    success: bool = True
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self, operation: str, monitor: 'PerformanceMonitor' = None):
        self.operation = operation
        self.monitor = monitor
        self.start_time = None
        self.end_time = None
        
    def __enter__(self):
        self.start_time = time.time()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration = self.end_time - self.start_time
        
        result = TimingResult(
            operation=self.operation,
            duration=duration,
            start_time=self.start_time,
            end_time=self.end_time,
            success=exc_type is None,
            error=str(exc_val) if exc_val else None
        )
        
        if self.monitor:
            self.monitor.record_timing(result)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, 
                 max_history: int = 10000,
                 enable_system_monitoring: bool = True,
                 monitoring_interval: float = 1.0):
        """初始化性能监控器
        
        Args:
            max_history: 最大历史记录数
            enable_system_monitoring: 是否启用系统监控
            monitoring_interval: 监控间隔(秒)
        """
        self.max_history = max_history
        self.enable_system_monitoring = enable_system_monitoring
        self.monitoring_interval = monitoring_interval
        
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        
        # 性能数据存储
        self._metrics: deque = deque(maxlen=max_history)
        self._timings: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._counters: Dict[str, int] = defaultdict(int)
        self._gauges: Dict[str, float] = defaultdict(float)
        
        # 系统监控
        self._system_metrics: deque = deque(maxlen=max_history)
        self._monitoring_thread = None
        self._stop_monitoring = threading.Event()
        
        # 性能阈值
        self._thresholds: Dict[str, float] = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_io_wait': 5.0,
            'operation_duration': 10.0
        }
        
        # 警告回调
        self._warning_callbacks: List[Callable[[str, Dict[str, Any]], None]] = []
        
        if enable_system_monitoring:
            self.start_system_monitoring()
    
    def timer(self, operation: str) -> PerformanceTimer:
        """创建性能计时器
        
        Args:
            operation: 操作名称
            
        Returns:
            PerformanceTimer: 计时器对象
        """
        return PerformanceTimer(operation, self)
    
    @contextmanager
    def measure(self, operation: str):
        """测量操作性能的上下文管理器
        
        Args:
            operation: 操作名称
        """
        timer = self.timer(operation)
        with timer:
            yield timer
    
    def record_timing(self, result: TimingResult) -> None:
        """记录计时结果
        
        Args:
            result: 计时结果
        """
        with self._lock:
            self._timings[result.operation].append(result)
            
            # 检查性能阈值
            if result.duration > self._thresholds.get('operation_duration', 10.0):
                self._trigger_warning(
                    'slow_operation',
                    {
                        'operation': result.operation,
                        'duration': result.duration,
                        'threshold': self._thresholds['operation_duration']
                    }
                )
    
    def record_metric(self, name: str, value: float, unit: str = '', 
                     category: str = 'general', tags: Dict[str, str] = None) -> None:
        """记录性能指标
        
        Args:
            name: 指标名称
            value: 指标值
            unit: 单位
            category: 类别
            tags: 标签
        """
        metric = PerformanceMetric(
            name=name,
            value=value,
            unit=unit,
            timestamp=time.time(),
            category=category,
            tags=tags or {}
        )
        
        with self._lock:
            self._metrics.append(metric)
    
    def increment_counter(self, name: str, value: int = 1) -> None:
        """增加计数器
        
        Args:
            name: 计数器名称
            value: 增加值
        """
        with self._lock:
            self._counters[name] += value
    
    def set_gauge(self, name: str, value: float) -> None:
        """设置仪表值
        
        Args:
            name: 仪表名称
            value: 值
        """
        with self._lock:
            self._gauges[name] = value
    
    def get_timing_stats(self, operation: str) -> Dict[str, Any]:
        """获取操作的计时统计
        
        Args:
            operation: 操作名称
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            timings = self._timings.get(operation, deque())
            if not timings:
                return {}
            
            durations = [t.duration for t in timings]
            success_count = sum(1 for t in timings if t.success)
            
            return {
                'count': len(durations),
                'success_count': success_count,
                'success_rate': success_count / len(durations),
                'min_duration': min(durations),
                'max_duration': max(durations),
                'avg_duration': np.mean(durations),
                'median_duration': np.median(durations),
                'p95_duration': np.percentile(durations, 95),
                'p99_duration': np.percentile(durations, 99),
                'total_duration': sum(durations)
            }
    
    def get_all_timing_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有操作的计时统计
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有操作的统计信息
        """
        with self._lock:
            return {
                operation: self.get_timing_stats(operation)
                for operation in self._timings.keys()
            }
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息
        
        Returns:
            Dict[str, Any]: 系统统计信息
        """
        try:
            # CPU信息
            cpu_percent = psutil.cpu_percent(interval=0.1)
            cpu_count = psutil.cpu_count()
            
            # 内存信息
            memory = psutil.virtual_memory()
            
            # 磁盘信息
            disk_usage = psutil.disk_usage('/')
            disk_io = psutil.disk_io_counters()
            
            # 进程信息
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return {
                'cpu_percent': cpu_percent,
                'cpu_count': cpu_count,
                'memory_total': memory.total,
                'memory_available': memory.available,
                'memory_percent': memory.percent,
                'disk_total': disk_usage.total,
                'disk_used': disk_usage.used,
                'disk_free': disk_usage.free,
                'disk_percent': disk_usage.used / disk_usage.total * 100,
                'disk_read_bytes': disk_io.read_bytes if disk_io else 0,
                'disk_write_bytes': disk_io.write_bytes if disk_io else 0,
                'process_memory_rss': process_memory.rss,
                'process_memory_vms': process_memory.vms,
                'timestamp': time.time()
            }
        except Exception as e:
            self.logger.error(f"获取系统统计失败: {e}")
            return {}
    
    def start_system_monitoring(self) -> None:
        """启动系统监控"""
        if self._monitoring_thread is None or not self._monitoring_thread.is_alive():
            self._stop_monitoring.clear()
            self._monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self._monitoring_thread.start()
            self.logger.info("系统监控已启动")
    
    def stop_system_monitoring(self) -> None:
        """停止系统监控"""
        self._stop_monitoring.set()
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            self._monitoring_thread.join(timeout=5)
        self.logger.info("系统监控已停止")
    
    def _monitoring_loop(self) -> None:
        """监控循环"""
        while not self._stop_monitoring.wait(self.monitoring_interval):
            try:
                stats = self.get_system_stats()
                if stats:
                    with self._lock:
                        self._system_metrics.append(stats)
                    
                    # 检查系统性能阈值
                    self._check_system_thresholds(stats)
                    
            except Exception as e:
                self.logger.error(f"系统监控循环错误: {e}")
    
    def _check_system_thresholds(self, stats: Dict[str, Any]) -> None:
        """检查系统性能阈值"""
        # CPU使用率检查
        if stats.get('cpu_percent', 0) > self._thresholds.get('cpu_percent', 80):
            self._trigger_warning('high_cpu_usage', {
                'cpu_percent': stats['cpu_percent'],
                'threshold': self._thresholds['cpu_percent']
            })
        
        # 内存使用率检查
        if stats.get('memory_percent', 0) > self._thresholds.get('memory_percent', 85):
            self._trigger_warning('high_memory_usage', {
                'memory_percent': stats['memory_percent'],
                'threshold': self._thresholds['memory_percent']
            })
    
    def _trigger_warning(self, warning_type: str, data: Dict[str, Any]) -> None:
        """触发警告"""
        self.logger.warning(f"性能警告 [{warning_type}]: {data}")
        
        for callback in self._warning_callbacks:
            try:
                callback(warning_type, data)
            except Exception as e:
                self.logger.error(f"执行警告回调失败: {e}")
    
    def add_warning_callback(self, callback: Callable[[str, Dict[str, Any]], None]) -> None:
        """添加警告回调
        
        Args:
            callback: 警告回调函数
        """
        self._warning_callbacks.append(callback)
    
    def set_threshold(self, name: str, value: float) -> None:
        """设置性能阈值
        
        Args:
            name: 阈值名称
            value: 阈值
        """
        self._thresholds[name] = value
    
    def export_report(self, file_path: str) -> bool:
        """导出性能报告
        
        Args:
            file_path: 报告文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            report = {
                'timestamp': time.time(),
                'timing_stats': self.get_all_timing_stats(),
                'counters': dict(self._counters),
                'gauges': dict(self._gauges),
                'system_stats': self.get_system_stats(),
                'thresholds': self._thresholds
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"性能报告已导出: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出性能报告失败: {e}")
            return False
    
    def clear_history(self) -> None:
        """清空历史数据"""
        with self._lock:
            self._metrics.clear()
            self._timings.clear()
            self._system_metrics.clear()
            self._counters.clear()
            self._gauges.clear()
    
    def __del__(self):
        """析构函数"""
        self.stop_system_monitoring()


# 全局性能监控器实例
_performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器实例
    
    Returns:
        PerformanceMonitor: 性能监控器实例
    """
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor


def measure_performance(operation: str):
    """性能测量装饰器
    
    Args:
        operation: 操作名称
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            monitor = get_performance_monitor()
            with monitor.measure(operation):
                return func(*args, **kwargs)
        return wrapper
    return decorator
