#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI样式工具模块

该模块提供GUI样式相关的工具函数。
"""

from typing import Optional
from PyQt6.QtWidgets import QWidget, QScrollArea, QTabWidget, QFrame
from PyQt6.QtCore import Qt, QSize

from utils.log_utils import get_logger

logger = get_logger(__name__)


class StyleUtils:
    """样式工具类"""
    
    @staticmethod
    def apply_stylesheet(widget: QWidget, stylesheet: str) -> None:
        """
        应用样式表
        
        Args:
            widget: 目标控件
            stylesheet: 样式表内容
        """
        try:
            widget.setStyleSheet(stylesheet)
            logger.debug(f"应用样式表到 {widget.__class__.__name__}")
        except Exception as e:
            logger.error(f"应用样式表失败: {e}")
    
    @staticmethod
    def create_separator(orientation: Qt.Orientation = Qt.Orientation.Horizontal) -> QFrame:
        """
        创建分隔线
        
        Args:
            orientation: 方向
            
        Returns:
            分隔线控件
        """
        try:
            separator = QFrame()
            if orientation == Qt.Orientation.Horizontal:
                separator.setFrameShape(QFrame.Shape.HLine)
            else:
                separator.setFrameShape(QFrame.Shape.VLine)
            separator.setFrameShadow(QFrame.Shadow.Sunken)
            return separator
        except Exception as e:
            logger.error(f"创建分隔线失败: {e}")
            return QFrame()
    
    @staticmethod
    def create_scroll_area(widget: QWidget, 
                          horizontal_policy: Qt.ScrollBarPolicy = Qt.ScrollBarPolicy.ScrollBarAsNeeded,
                          vertical_policy: Qt.ScrollBarPolicy = Qt.ScrollBarPolicy.ScrollBarAsNeeded) -> QScrollArea:
        """
        创建滚动区域
        
        Args:
            widget: 要包装的控件
            horizontal_policy: 水平滚动条策略
            vertical_policy: 垂直滚动条策略
            
        Returns:
            滚动区域控件
        """
        try:
            scroll_area = QScrollArea()
            scroll_area.setWidget(widget)
            scroll_area.setWidgetResizable(True)
            scroll_area.setHorizontalScrollBarPolicy(horizontal_policy)
            scroll_area.setVerticalScrollBarPolicy(vertical_policy)
            return scroll_area
        except Exception as e:
            logger.error(f"创建滚动区域失败: {e}")
            return QScrollArea()
    
    @staticmethod
    def create_tab_widget() -> QTabWidget:
        """
        创建标签页控件
        
        Returns:
            标签页控件
        """
        try:
            tab_widget = QTabWidget()
            tab_widget.setTabPosition(QTabWidget.TabPosition.North)
            tab_widget.setTabsClosable(False)
            tab_widget.setMovable(True)
            return tab_widget
        except Exception as e:
            logger.error(f"创建标签页控件失败: {e}")
            return QTabWidget()
    
    @staticmethod
    def set_widget_minimum_size_hint(widget: QWidget, size: Optional[QSize] = None) -> None:
        """
        设置控件最小尺寸提示
        
        Args:
            widget: 目标控件
            size: 最小尺寸，如果为None则使用控件的sizeHint
        """
        try:
            if size is None:
                size = widget.sizeHint()
            widget.setMinimumSize(size)
            logger.debug(f"设置 {widget.__class__.__name__} 最小尺寸: {size}")
        except Exception as e:
            logger.error(f"设置控件最小尺寸失败: {e}")