#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像查看器控制组件

该模块提供图像查看器的控制组件，包括缩放控制和工具栏。
"""

from typing import Optional
from PyQt6.QtWidgets import QWidget, QHBoxLayout
from PyQt6.QtCore import Qt, pyqtSignal

from utils.log_utils import LoggerMixin
from ..widget_factory import WidgetFactory
from .models import ViewMode
from .config import ImageViewerConfig


class ZoomWidget(QWidget, LoggerMixin):
    """缩放控制组件
    
    提供图像缩放和查看模式控制功能。
    """
    
    # 信号
    zoomChanged = pyqtSignal(float)  # 缩放变更
    viewModeChanged = pyqtSignal(ViewMode)  # 查看模式变更
    
    def __init__(self, parent=None):
        """初始化缩放控制组件
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        self.widget_factory = WidgetFactory()
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(
            ImageViewerConfig.WIDGET_MARGIN,
            ImageViewerConfig.WIDGET_MARGIN,
            ImageViewerConfig.WIDGET_MARGIN,
            ImageViewerConfig.WIDGET_MARGIN
        )
        layout.setSpacing(ImageViewerConfig.WIDGET_SPACING)
        
        # 缩放按钮
        self.zoom_out_btn = self.widget_factory.create_tool_button(
            "缩小", icon_name="zoom_out", tooltip="缩小", 
            click_handler=self._zoom_out
        )
        layout.addWidget(self.zoom_out_btn)
        
        # 缩放滑块
        self.zoom_slider = self.widget_factory.create_slider(
            minimum=ImageViewerConfig.ZOOM_MIN,
            maximum=ImageViewerConfig.ZOOM_MAX,
            value=ImageViewerConfig.ZOOM_DEFAULT,
            orientation=Qt.Orientation.Horizontal,
            value_changed_handler=self._on_zoom_slider_changed
        )
        self.zoom_slider.setFixedWidth(ImageViewerConfig.ZOOM_SLIDER_WIDTH)
        layout.addWidget(self.zoom_slider)
        
        # 缩放输入框
        self.zoom_spin = self.widget_factory.create_spin_box(
            minimum=ImageViewerConfig.ZOOM_MIN,
            maximum=ImageViewerConfig.ZOOM_MAX,
            value=ImageViewerConfig.ZOOM_DEFAULT,
            suffix="%",
            value_changed_handler=self._on_zoom_spin_changed
        )
        self.zoom_spin.setFixedWidth(ImageViewerConfig.ZOOM_SPIN_WIDTH)
        layout.addWidget(self.zoom_spin)
        
        # 放大按钮
        self.zoom_in_btn = self.widget_factory.create_tool_button(
            "放大", icon_name="zoom_in", tooltip="放大",
            click_handler=self._zoom_in
        )
        layout.addWidget(self.zoom_in_btn)
        
        # 分隔线
        layout.addWidget(
            self.widget_factory.create_separator(Qt.Orientation.Vertical)
        )
        
        # 查看模式
        self.view_mode_combo = self.widget_factory.create_combo_box(
            items=["适应窗口", "适应宽度", "适应高度", "实际大小", "自定义"],
            selection_changed_handler=self._on_view_mode_changed
        )
        self.view_mode_combo.setFixedWidth(
            ImageViewerConfig.VIEW_MODE_COMBO_WIDTH
        )
        layout.addWidget(self.view_mode_combo)
        
        # 重置按钮
        self.reset_btn = self.widget_factory.create_tool_button(
            "重置", icon_name="reset", tooltip="重置",
            click_handler=self._reset_zoom
        )
        layout.addWidget(self.reset_btn)
        
        layout.addStretch()
    
    def _connect_signals(self):
        """连接信号"""
        try:
            # 连接缩放控件信号
            self.zoom_in_btn.clicked.connect(self._zoom_in)
            self.zoom_out_btn.clicked.connect(self._zoom_out)
            self.zoom_slider.valueChanged.connect(self._on_zoom_slider_changed)
            
            # 连接其他控件信号
            if hasattr(self, 'reset_btn'):
                self.reset_btn.clicked.connect(self._reset_zoom)
                
        except Exception as e:
            self.logger.error(f"连接信号失败: {e}")
    
    def _zoom_in(self):
        """放大"""
        try:
            current_value = self.zoom_slider.value()
            new_value = min(
                ImageViewerConfig.ZOOM_MAX,
                current_value + ImageViewerConfig.ZOOM_STEP
            )
            self.zoom_slider.setValue(new_value)
        except Exception as e:
            self.logger.error(f"放大操作失败: {e}")
    
    def _zoom_out(self):
        """缩小"""
        try:
            current_value = self.zoom_slider.value()
            new_value = max(
                ImageViewerConfig.ZOOM_MIN,
                current_value - ImageViewerConfig.ZOOM_STEP
            )
            self.zoom_slider.setValue(new_value)
        except Exception as e:
            self.logger.error(f"缩小操作失败: {e}")
    
    def _reset_zoom(self):
        """重置缩放"""
        try:
            self.zoom_slider.setValue(ImageViewerConfig.ZOOM_DEFAULT)
            self.view_mode_combo.setCurrentIndex(0)
        except Exception as e:
            self.logger.error(f"重置缩放失败: {e}")
    
    def _on_zoom_slider_changed(self, value: int):
        """缩放滑块变更
        
        Args:
            value: 滑块值
        """
        try:
            self.zoom_spin.setValue(value)
            zoom_factor = value / 100.0
            self.zoomChanged.emit(zoom_factor)
        except Exception as e:
            self.logger.error(f"处理缩放滑块变更失败: {e}")
    
    def _on_zoom_spin_changed(self, value: int):
        """缩放输入框变更
        
        Args:
            value: 输入框值
        """
        try:
            self.zoom_slider.setValue(value)
        except Exception as e:
            self.logger.error(f"处理缩放输入框变更失败: {e}")
    
    def _on_view_mode_changed(self, text: str):
        """查看模式变更
        
        Args:
            text: 模式文本
        """
        try:
            mode_name = ImageViewerConfig.VIEW_MODE_MAP.get(text, "FIT_TO_WINDOW")
            mode = getattr(ViewMode, mode_name)
            self.viewModeChanged.emit(mode)
        except Exception as e:
            self.logger.error(f"处理查看模式变更失败: {e}")
    
    def set_zoom_factor(self, factor: float):
        """设置缩放因子
        
        Args:
            factor: 缩放因子
        """
        try:
            value = int(factor * 100)
            self.zoom_slider.setValue(value)
            self.zoom_spin.setValue(value)
        except Exception as e:
            self.logger.error(f"设置缩放因子失败: {e}")
    
    def set_view_mode(self, mode: ViewMode):
        """设置查看模式
        
        Args:
            mode: 查看模式
        """
        try:
            index = ImageViewerConfig.VIEW_MODE_INDEX_MAP.get(mode.value, 0)
            self.view_mode_combo.setCurrentIndex(index)
        except Exception as e:
            self.logger.error(f"设置查看模式失败: {e}")


class ToolsWidget(QWidget, LoggerMixin):
    """工具栏组件
    
    提供图像变换和编辑工具。
    """
    
    # 信号
    rotateRequested = pyqtSignal(int)  # 旋转请求
    flipRequested = pyqtSignal(str)  # 翻转请求
    cropRequested = pyqtSignal()  # 裁剪请求
    saveRequested = pyqtSignal()  # 保存请求
    
    def __init__(self, parent=None):
        """初始化工具栏组件
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        self.widget_factory = WidgetFactory()
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(
            ImageViewerConfig.WIDGET_MARGIN,
            ImageViewerConfig.WIDGET_MARGIN,
            ImageViewerConfig.WIDGET_MARGIN,
            ImageViewerConfig.WIDGET_MARGIN
        )
        layout.setSpacing(ImageViewerConfig.WIDGET_SPACING)
        
        # 旋转按钮组
        rotate_group = self.widget_factory.create_group_box("旋转")
        rotate_layout = QHBoxLayout(rotate_group)
        
        self.rotate_left_btn = self.widget_factory.create_tool_button(
            "逆时针旋转90°", icon_name="rotate_left", tooltip="逆时针旋转90°",
            click_handler=lambda: self.rotateRequested.emit(-90)
        )
        rotate_layout.addWidget(self.rotate_left_btn)
        
        self.rotate_right_btn = self.widget_factory.create_tool_button(
            "顺时针旋转90°", icon_name="rotate_right", tooltip="顺时针旋转90°",
            click_handler=lambda: self.rotateRequested.emit(90)
        )
        rotate_layout.addWidget(self.rotate_right_btn)
        
        self.rotate_180_btn = self.widget_factory.create_tool_button(
            "旋转180°", icon_name="rotate_180", tooltip="旋转180°",
            click_handler=lambda: self.rotateRequested.emit(180)
        )
        rotate_layout.addWidget(self.rotate_180_btn)
        
        layout.addWidget(rotate_group)
        
        # 翻转按钮组
        flip_group = self.widget_factory.create_group_box("翻转")
        flip_layout = QHBoxLayout(flip_group)
        
        self.flip_h_btn = self.widget_factory.create_tool_button(
            "水平翻转", icon_name="flip_horizontal", tooltip="水平翻转",
            click_handler=lambda: self.flipRequested.emit("horizontal")
        )
        flip_layout.addWidget(self.flip_h_btn)
        
        self.flip_v_btn = self.widget_factory.create_tool_button(
            "垂直翻转", icon_name="flip_vertical", tooltip="垂直翻转",
            click_handler=lambda: self.flipRequested.emit("vertical")
        )
        flip_layout.addWidget(self.flip_v_btn)
        
        layout.addWidget(flip_group)
        
        # 编辑按钮组
        edit_group = self.widget_factory.create_group_box("编辑")
        edit_layout = QHBoxLayout(edit_group)
        
        self.crop_btn = self.widget_factory.create_tool_button(
            "裁剪", icon_name="crop", tooltip="裁剪",
            click_handler=self.cropRequested.emit
        )
        edit_layout.addWidget(self.crop_btn)
        
        layout.addWidget(edit_group)
        
        # 文件按钮组
        file_group = self.widget_factory.create_group_box("文件")
        file_layout = QHBoxLayout(file_group)
        
        self.save_btn = self.widget_factory.create_tool_button(
            "保存", icon_name="save", tooltip="保存",
            click_handler=self.saveRequested.emit
        )
        file_layout.addWidget(self.save_btn)
        
        layout.addWidget(file_group)
        
        layout.addStretch()
    
    def _connect_signals(self):
        """连接信号"""
        # 信号连接在setup_ui中已经处理
        pass