#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像特征管理模块

该模块提供图像特征的管理功能，包括特征的增删改查操作。
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any

from .base_repository import BaseRepository
from ..database_manager import DatabaseManager
from ..exceptions.database_exceptions import RepositoryError
from ..models import ImageFeature


class ImageFeatureRepository(BaseRepository):
    """图像特征仓库类"""
    
    @property
    def table_name(self) -> str:
        """表名"""
        return "image_features"
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化图像特征仓库
        
        Args:
            db_manager: 数据库管理器
        """
        super().__init__(db_manager)
    
    def _safe_get(self, row, key, default=None):
        """安全地从数据库行获取值
        
        Args:
            row: 数据库行
            key: 键名
            default: 默认值
            
        Returns:
            值或默认值
        """
        try:
            return row[key]
        except (KeyError, IndexError, TypeError):
            return default
    
    def _row_to_image_feature(self, row) -> ImageFeature:
        """将数据库行转换为ImageFeature对象
        
        Args:
            row: 数据库行
            
        Returns:
            ImageFeature: 图像特征对象
        """
        try:
            # 处理时间字段
            created_at = None
            created_at_value = self._safe_get(row, 'created_at')
            if created_at_value:
                try:
                    created_at = datetime.fromisoformat(created_at_value.replace('Z', '+00:00'))
                except ValueError:
                    created_at = datetime.strptime(created_at_value, '%Y-%m-%d %H:%M:%S')
            
            updated_at = None
            updated_at_value = self._safe_get(row, 'updated_at')
            if updated_at_value:
                try:
                    updated_at = datetime.fromisoformat(updated_at_value.replace('Z', '+00:00'))
                except ValueError:
                    updated_at = datetime.strptime(updated_at_value, '%Y-%m-%d %H:%M:%S')
            
            return ImageFeature(
                id=self._safe_get(row, 'id'),
                image_id=self._safe_get(row, 'image_id'),
                feature_type=self._safe_get(row, 'feature_type'),
                feature_data=self._safe_get(row, 'feature_data'),
                model_name=self._safe_get(row, 'model_name'),
                feature_dim=self._safe_get(row, 'feature_dim', 0),
                created_at=created_at,
                updated_at=updated_at
            )
            
        except Exception as e:
            self.logger.error(f"转换数据库行为ImageFeature对象失败: {e}")
            raise RepositoryError(f"转换数据库行为ImageFeature对象失败: {e}")
    
    def add_feature(self, image_id_or_feature, feature_type=None, 
                   feature_data=None, model_name=None) -> Optional[int]:
        """添加特征到图像
        
        Args:
            image_id_or_feature: 图像ID或ImageFeature对象
            feature_type: 特征类型（当第一个参数为图像ID时使用）
            feature_data: 特征数据（当第一个参数为图像ID时使用）
            model_name: 模型名称（当第一个参数为图像ID时使用）
            
        Returns:
            Optional[int]: 特征ID，如果失败则返回None
        """
        try:
            sql = """
                INSERT INTO image_features (
                    image_id, feature_type, feature_data, model_name, created_at
                ) VALUES (?, ?, ?, ?, ?)
            """
            
            # 根据第一个参数类型确定处理方式
            if isinstance(image_id_or_feature, ImageFeature):
                # 如果是ImageFeature对象
                feature = image_id_or_feature
                params = (
                    feature.image_id,
                    feature.feature_type,
                    feature.feature_data,
                    feature.model_name,
                    datetime.now().isoformat()
                )
            else:
                # 如果是图像ID
                image_id = image_id_or_feature
                params = (
                    image_id,
                    feature_type,
                    feature_data,
                    model_name,
                    datetime.now().isoformat()
                )
            
            feature_id = self.db_manager.execute_insert(sql, params)
            self.logger.debug(f"特征添加成功: {feature_id}")
            return feature_id
            
        except Exception as e:
            self.logger.error(f"添加特征失败: {e}")
            raise RepositoryError(f"添加特征失败: {e}")
    
    def get_feature_by_id(self, feature_id: int) -> Optional[ImageFeature]:
        """根据ID获取特征
        
        Args:
            feature_id: 特征ID
            
        Returns:
            Optional[ImageFeature]: 特征对象，如果不存在则返回None
        """
        try:
            sql = "SELECT * FROM image_features WHERE id = ?"
            rows = self.db_manager.execute_query(sql, (feature_id,))
            
            if rows:
                return self._row_to_image_feature(rows[0])
            return None
            
        except Exception as e:
            self.logger.error(f"根据ID获取特征失败: {e}")
            raise RepositoryError(f"根据ID获取特征失败: {e}")
    
    def get_feature_by_image_id(self, image_id: int, 
                               feature_type: str = None) -> Optional[ImageFeature]:
        """根据图像ID获取特征
        
        Args:
            image_id: 图像ID
            feature_type: 特征类型，如果为None则获取任意类型的第一个特征
            
        Returns:
            Optional[ImageFeature]: 特征对象，如果不存在则返回None
        """
        try:
            sql = "SELECT * FROM image_features WHERE image_id = ?"
            params = [image_id]
            
            if feature_type:
                sql += " AND feature_type = ?"
                params.append(feature_type)
                
            sql += " ORDER BY created_at DESC LIMIT 1"
            
            rows = self.db_manager.execute_query(sql, tuple(params))
            
            if rows:
                return self._row_to_image_feature(rows[0])
            return None
            
        except Exception as e:
            self.logger.error(f"根据图像ID获取特征失败: {e}")
            raise RepositoryError(f"根据图像ID获取特征失败: {e}")
    
    def get_features_by_image_id(self, image_id: int) -> List[ImageFeature]:
        """根据图像ID获取所有特征
        
        Args:
            image_id: 图像ID
            
        Returns:
            List[ImageFeature]: 特征列表
        """
        try:
            sql = "SELECT * FROM image_features WHERE image_id = ? ORDER BY created_at DESC"
            rows = self.db_manager.execute_query(sql, (image_id,))
            
            return [self._row_to_image_feature(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"根据图像ID获取特征列表失败: {e}")
            raise RepositoryError(f"根据图像ID获取特征列表失败: {e}")
    
    def get_features_by_type(self, feature_type: str) -> List[ImageFeature]:
        """根据特征类型获取所有特征
        
        Args:
            feature_type: 特征类型
            
        Returns:
            List[ImageFeature]: 特征列表
        """
        try:
            sql = "SELECT * FROM image_features WHERE feature_type = ? ORDER BY created_at DESC"
            rows = self.db_manager.execute_query(sql, (feature_type,))
            
            return [self._row_to_image_feature(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"根据特征类型获取特征列表失败: {e}")
            raise RepositoryError(f"根据特征类型获取特征列表失败: {e}")
    
    def get_features_by_model(self, model_name: str) -> List[ImageFeature]:
        """根据模型名称获取所有特征
        
        Args:
            model_name: 模型名称
            
        Returns:
            List[ImageFeature]: 特征列表
        """
        try:
            sql = "SELECT * FROM image_features WHERE model_name = ? ORDER BY created_at DESC"
            rows = self.db_manager.execute_query(sql, (model_name,))
            
            return [self._row_to_image_feature(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"根据模型名称获取特征列表失败: {e}")
            raise RepositoryError(f"根据模型名称获取特征列表失败: {e}")
    
    def update_feature(self, feature: ImageFeature) -> bool:
        """更新特征
        
        Args:
            feature: 特征对象
            
        Returns:
            bool: 更新是否成功
        """
        try:
            sql = """
                UPDATE image_features SET
                    image_id = ?, feature_type = ?, feature_data = ?, 
                    model_name = ?, feature_dim = ?, updated_at = ?
                WHERE id = ?
            """
            
            params = (
                feature.image_id,
                feature.feature_type,
                feature.feature_data,
                feature.model_name,
                feature.feature_dim,
                datetime.now().isoformat(),
                feature.id
            )
            
            affected_rows = self.db_manager.execute_update(sql, params)
            success = affected_rows > 0
            
            if success:
                self.logger.debug(f"特征更新成功: {feature.id}")
            else:
                self.logger.warning(f"特征更新失败，未找到记录: {feature.id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"更新特征失败: {e}")
            raise RepositoryError(f"更新特征失败: {e}")
    
    def delete_feature(self, feature_id: int) -> bool:
        """删除特征
        
        Args:
            feature_id: 特征ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            sql = "DELETE FROM image_features WHERE id = ?"
            affected_rows = self.db_manager.execute_update(sql, (feature_id,))
            success = affected_rows > 0
            
            if success:
                self.logger.debug(f"特征删除成功: {feature_id}")
            else:
                self.logger.warning(f"特征删除失败，未找到记录: {feature_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"删除特征失败: {e}")
            raise RepositoryError(f"删除特征失败: {e}")
    
    def delete_feature_by_image_id(self, image_id: int, 
                                  feature_type: str = None) -> bool:
        """根据图像ID删除特征
        
        Args:
            image_id: 图像ID
            feature_type: 特征类型，如果为None则删除所有类型的特征
            
        Returns:
            bool: 删除是否成功
        """
        try:
            sql = "DELETE FROM image_features WHERE image_id = ?"
            params = [image_id]
            
            if feature_type:
                sql += " AND feature_type = ?"
                params.append(feature_type)
                
            affected_rows = self.db_manager.execute_update(sql, tuple(params))
            success = affected_rows > 0
            
            if success:
                self.logger.debug(f"根据图像ID删除特征成功: image_id={image_id}, feature_type={feature_type}")
            else:
                self.logger.warning(f"根据图像ID删除特征失败，未找到记录: image_id={image_id}, feature_type={feature_type}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"根据图像ID删除特征失败: {e}")
            raise RepositoryError(f"根据图像ID删除特征失败: {e}")
    
    def update_or_create_feature(self, image_id: int, feature_type: str, 
                                feature_data: bytes, model_name: str = None) -> Optional[int]:
        """更新或创建特征
        
        Args:
            image_id: 图像ID
            feature_type: 特征类型
            feature_data: 特征数据
            model_name: 模型名称
            
        Returns:
            Optional[int]: 特征ID
        """
        try:
            # 先尝试查找现有特征
            existing_feature = self.get_feature_by_image_id(image_id, feature_type)
            
            if existing_feature:
                # 更新现有特征
                existing_feature.feature_data = feature_data
                existing_feature.model_name = model_name
                existing_feature.updated_at = datetime.now()
                
                success = self.update_feature(existing_feature)
                if success:
                    self.logger.debug(f"特征更新成功: image_id={image_id}, feature_type={feature_type}")
                    return existing_feature.id
                else:
                    self.logger.error(f"特征更新失败: image_id={image_id}, feature_type={feature_type}")
                    return None
            else:
                # 创建新特征
                return self.add_feature(image_id, feature_type, feature_data, model_name)
                
        except Exception as e:
            self.logger.error(f"更新或创建特征失败: {e}")
            raise RepositoryError(f"更新或创建特征失败: {e}")
    
    def batch_add_features(self, features: List[ImageFeature]) -> List[int]:
        """批量添加特征
        
        Args:
            features: 特征列表
            
        Returns:
            List[int]: 创建的特征ID列表
        """
        try:
            sql = """
                INSERT INTO image_features (
                    image_id, feature_type, feature_data, model_name, feature_dim, created_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            """
            
            params_list = []
            for feature in features:
                params = (
                    feature.image_id,
                    feature.feature_type,
                    feature.feature_data,
                    feature.model_name,
                    feature.feature_dim,
                    datetime.now().isoformat()
                )
                params_list.append(params)
            
            affected_rows = self.db_manager.execute_batch(sql, params_list)
            
            # 获取插入的ID
            last_id = self.db_manager.get_last_insert_id()
            new_ids = list(range(last_id - len(features) + 1, last_id + 1))
            
            self.logger.info(f"批量添加特征成功: {len(features)} 条记录")
            return new_ids
            
        except Exception as e:
            self.logger.error(f"批量添加特征失败: {e}")
            raise RepositoryError(f"批量添加特征失败: {e}")
    
    def batch_delete_features(self, feature_ids: List[int]) -> int:
        """批量删除特征
        
        Args:
            feature_ids: 特征ID列表
            
        Returns:
            int: 删除的记录数量
        """
        try:
            if not feature_ids:
                return 0
            
            placeholders = ','.join(['?'] * len(feature_ids))
            sql = f"DELETE FROM image_features WHERE id IN ({placeholders})"
            
            affected_rows = self.db_manager.execute_update(sql, tuple(feature_ids))
            
            self.logger.info(f"批量删除特征成功: {affected_rows} 条记录")
            return affected_rows
            
        except Exception as e:
            self.logger.error(f"批量删除特征失败: {e}")
            raise RepositoryError(f"批量删除特征失败: {e}")
    
    def get_feature_types(self) -> List[str]:
        """获取所有特征类型
        
        Returns:
            List[str]: 特征类型列表
        """
        try:
            sql = """
                SELECT DISTINCT feature_type 
                FROM image_features 
                WHERE feature_type IS NOT NULL 
                ORDER BY feature_type
            """
            result = self.db_manager.execute_query(sql)
            return [row['feature_type'] for row in result]
            
        except Exception as e:
            self.logger.error(f"获取特征类型列表失败: {e}")
            raise RepositoryError(f"获取特征类型列表失败: {e}")
    
    def get_model_names(self) -> List[str]:
        """获取所有模型名称
        
        Returns:
            List[str]: 模型名称列表
        """
        try:
            sql = """
                SELECT DISTINCT model_name 
                FROM image_features 
                WHERE model_name IS NOT NULL 
                ORDER BY model_name
            """
            result = self.db_manager.execute_query(sql)
            return [row['model_name'] for row in result]
            
        except Exception as e:
            self.logger.error(f"获取模型名称列表失败: {e}")
            raise RepositoryError(f"获取模型名称列表失败: {e}")
    
    def get_feature_statistics(self) -> Dict[str, Any]:
        """获取特征统计信息
        
        Returns:
            Dict[str, Any]: 特征统计信息
        """
        try:
            stats = {}
            
            # 总数统计
            sql = "SELECT COUNT(*) as total FROM image_features"
            result = self.db_manager.execute_query(sql)
            stats['total_count'] = result[0]['total'] if result else 0
            
            # 按类型统计
            sql = """
                SELECT feature_type, COUNT(*) as count 
                FROM image_features 
                WHERE feature_type IS NOT NULL
                GROUP BY feature_type
                ORDER BY count DESC
            """
            result = self.db_manager.execute_query(sql)
            stats['by_type'] = {row['feature_type']: row['count'] for row in result}
            
            # 按模型统计
            sql = """
                SELECT model_name, COUNT(*) as count 
                FROM image_features 
                WHERE model_name IS NOT NULL
                GROUP BY model_name
                ORDER BY count DESC
            """
            result = self.db_manager.execute_query(sql)
            stats['by_model'] = {row['model_name']: row['count'] for row in result}
            
            # 最近添加统计
            sql = """
                SELECT COUNT(*) as count 
                FROM image_features 
                WHERE created_at >= datetime('now', '-7 days')
            """
            result = self.db_manager.execute_query(sql)
            stats['recent_count'] = result[0]['count'] if result else 0
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取特征统计信息失败: {e}")
            raise RepositoryError(f"获取特征统计信息失败: {e}")
    
    def cleanup_orphaned_features(self) -> int:
        """清理孤立的特征（对应的图像不存在）
        
        Returns:
            int: 清理的记录数量
        """
        try:
            sql = """
                DELETE FROM image_features 
                WHERE image_id NOT IN (SELECT id FROM fabric_images)
            """
            
            affected_rows = self.db_manager.execute_update(sql)
            
            self.logger.info(f"清理孤立特征成功: {affected_rows} 条记录")
            return affected_rows
            
        except Exception as e:
            self.logger.error(f"清理孤立特征失败: {e}")
            raise RepositoryError(f"清理孤立特征失败: {e}")