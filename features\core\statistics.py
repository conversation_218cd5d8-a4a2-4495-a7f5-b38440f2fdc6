"""统计信息收集模块

处理特征管理器的统计信息收集和汇总。
"""

from typing import Dict, Any
from ..search.similarity_search import SimilarityCalculator


class StatisticsCollector:
    """统计信息收集器
    
    负责收集和汇总各种统计信息。
    """
    
    def __init__(self, feature_storage, similarity_calculator, 
                 batch_processor, performance_monitor, logger):
        """初始化统计信息收集器
        
        Args:
            feature_storage: 特征存储
            similarity_calculator: 相似度计算器
            batch_processor: 批处理器
            performance_monitor: 性能监控器
            logger: 日志记录器
        """
        self.feature_storage = feature_storage
        self.similarity_calculator = similarity_calculator
        self.batch_processor = batch_processor
        self.performance_monitor = performance_monitor
        self.logger = logger
        
    def cleanup(self):
        """清理资源
        
        在FeatureManager的cleanup方法中被调用
        """
        try:
            # 注意：如果performance_monitor在其他地方已被清理，这里可能会导致重复清理
            # 但由于没有看到其他地方清理performance_monitor，所以保留这个清理代码
            if hasattr(self.performance_monitor, 'cleanup'):
                self.performance_monitor.cleanup()
                
            # 记录日志
            self.logger.info("统计信息收集器资源已清理")
        except Exception as e:
            self.logger.error(f"清理统计信息收集器资源时出错: {e}")
        
    def get_feature_statistics(self) -> Dict[str, Any]:
        """获取特征统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        try:
            storage_stats = self.feature_storage.get_statistics()
            
            stats = {
                'storage': storage_stats,
                'index': self._get_index_statistics(),
                'performance': self.performance_monitor.get_statistics()
            }
            
            if self.batch_processor:
                stats['batch_processing'] = self.batch_processor.get_statistics()
                
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
            
    def _get_index_statistics(self) -> Dict[str, Any]:
        """获取索引统计信息
        
        Returns:
            Dict[str, Any]: 索引统计信息
        """
        if self.similarity_calculator and self.similarity_calculator.has_index():
            return self.similarity_calculator.get_index_info()
        else:
            return {'has_index': False}
            
    def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息
        
        Returns:
            Dict[str, Any]: 存储统计信息
        """
        try:
            return self.feature_storage.get_statistics()
        except Exception as e:
            self.logger.error(f"获取存储统计信息失败: {e}")
            return {}
            
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        try:
            return self.feature_storage.get_cache_statistics()
        except Exception as e:
            self.logger.error(f"获取缓存统计信息失败: {e}")
            return {}
            
    def get_batch_statistics(self) -> Dict[str, Any]:
        """获取批处理统计信息
        
        Returns:
            Dict[str, Any]: 批处理统计信息
        """
        try:
            if self.batch_processor:
                return self.batch_processor.get_statistics()
            else:
                return {'batch_processor_available': False}
        except Exception as e:
            self.logger.error(f"获取批处理统计信息失败: {e}")
            return {}
            
    def get_performance_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息
        
        Returns:
            Dict[str, Any]: 性能统计信息
        """
        try:
            return self.performance_monitor.get_statistics()
        except Exception as e:
            self.logger.error(f"获取性能统计信息失败: {e}")
            return {}
            
    def get_comprehensive_report(self) -> Dict[str, Any]:
        """获取综合报告
        
        Returns:
            Dict[str, Any]: 综合统计报告
        """
        try:
            report = {
                'summary': self._get_summary_statistics(),
                'detailed': {
                    'storage': self.get_storage_statistics(),
                    'cache': self.get_cache_statistics(),
                    'index': self._get_index_statistics(),
                    'batch_processing': self.get_batch_statistics(),
                    'performance': self.get_performance_statistics()
                },
                'health': self._get_health_status()
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成综合报告失败: {e}")
            return {}
            
    def _get_summary_statistics(self) -> Dict[str, Any]:
        """获取摘要统计信息
        
        Returns:
            Dict[str, Any]: 摘要统计信息
        """
        try:
            storage_stats = self.get_storage_statistics()
            index_stats = self._get_index_statistics()
            
            return {
                'total_features': storage_stats.get('total_features', 0),
                'cache_hit_rate': storage_stats.get('cache_hit_rate', 0.0),
                'index_available': index_stats.get('has_index', False),
                'index_size': index_stats.get('index_size_mb', 0.0)
            }
            
        except Exception as e:
            self.logger.error(f"获取摘要统计信息失败: {e}")
            return {}
            
    def _get_health_status(self) -> Dict[str, Any]:
        """获取健康状态
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        try:
            health = {
                'storage_healthy': True,
                'index_healthy': True,
                'cache_healthy': True,
                'overall_status': 'healthy'
            }
            
            # 检查存储健康状态
            storage_stats = self.get_storage_statistics()
            if not storage_stats or storage_stats.get('total_features', 0) == 0:
                health['storage_healthy'] = False
                
            # 检查索引健康状态
            index_stats = self._get_index_statistics()
            if not index_stats.get('has_index', False):
                health['index_healthy'] = False
                
            # 检查缓存健康状态
            cache_stats = self.get_cache_statistics()
            cache_hit_rate = cache_stats.get('hit_rate', 0.0)
            if cache_hit_rate < 0.1:  # 命中率过低
                health['cache_healthy'] = False
                
            # 确定整体状态
            if not health['storage_healthy']:
                health['overall_status'] = 'critical'
            elif not health['index_healthy'] or not health['cache_healthy']:
                health['overall_status'] = 'warning'
                
            return health
            
        except Exception as e:
            self.logger.error(f"获取健康状态失败: {e}")
            return {
                'storage_healthy': False,
                'index_healthy': False,
                'cache_healthy': False,
                'overall_status': 'error'
            }