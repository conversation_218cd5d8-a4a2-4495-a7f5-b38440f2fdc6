#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块

该模块提供统一的日志管理功能，支持文件和控制台输出。
"""

import os
import logging
import logging.handlers
from pathlib import Path
from typing import Optional
from datetime import datetime


def setup_logging(level: str = 'INFO', 
                 log_dir: Optional[str] = None,
                 log_file: Optional[str] = None,
                 console_output: bool = True,
                 file_output: bool = True,
                 max_file_size: int = 10485760,  # 10MB
                 backup_count: int = 5,
                 force: bool = False) -> logging.Logger:
    """设置日志系统
    
    Args:
        level: 日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        log_dir: 日志目录，如果为None则使用默认目录
        log_file: 日志文件名，如果为None则使用默认名称
        console_output: 是否输出到控制台
        file_output: 是否输出到文件
        max_file_size: 单个日志文件最大大小（字节）
        backup_count: 保留的备份文件数量
        force: 是否强制重新配置日志系统，如果为False且已有处理器则不会重新配置
        
    Returns:
        logging.Logger: 配置好的日志器
    """
    # 获取根日志器
    logger = logging.getLogger()
    
    # 检查是否已经配置过日志处理器，如果已配置且不强制重新配置则直接返回
    if not force and logger.handlers:
        return logger
    
    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 设置日志级别
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(numeric_level)
    
    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if file_output:
        # 确定日志目录
        if log_dir is None:
            project_root = Path(__file__).parent.parent
            log_dir = project_root / 'logs'
        else:
            log_dir = Path(log_dir)
        
        # 创建日志目录
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 确定日志文件名
        if log_file is None:
            today = datetime.now().strftime('%Y-%m-%d')
            log_file = f'app_{today}.log'
        
        log_path = log_dir / log_file
        
        # 创建旋转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            log_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # 记录初始化信息
    logger.info(f"日志系统初始化完成 - 级别: {level}")
    if file_output:
        logger.info(f"日志文件: {log_path}")
    
    return logger


# 从logger_mixin模块导入LoggerMixin，避免重复定义
from utils.logger_mixin import LoggerMixin


class PerformanceLogger:
    """性能日志器
    
    用于记录性能相关的日志信息。
    """
    
    def __init__(self, name: str = 'performance'):
        """初始化性能日志器
        
        Args:
            name: 日志器名称
        """
        self.logger = logging.getLogger(name)
        self._start_times = {}
    
    def start_timer(self, operation: str):
        """开始计时
        
        Args:
            operation: 操作名称
        """
        self._start_times[operation] = datetime.now()
        self.logger.debug(f"开始操作: {operation}")
    
    def end_timer(self, operation: str) -> float:
        """结束计时并记录耗时
        
        Args:
            operation: 操作名称
            
        Returns:
            float: 操作耗时（秒）
        """
        if operation not in self._start_times:
            self.logger.warning(f"操作 {operation} 未开始计时")
            return 0.0
        
        start_time = self._start_times.pop(operation)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        self.logger.info(f"操作完成: {operation}, 耗时: {duration:.3f}秒")
        return duration
    
    def log_memory_usage(self, operation: str = ''):
        """记录内存使用情况
        
        Args:
            operation: 操作描述
        """
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            message = f"内存使用: {memory_mb:.1f}MB"
            if operation:
                message = f"{operation} - {message}"
            
            self.logger.info(message)
            
        except ImportError:
            self.logger.warning("psutil未安装，无法记录内存使用情况")
        except Exception as e:
            self.logger.error(f"记录内存使用失败: {e}")
    
    def log_gpu_memory(self, device_id: int = 0):
        """记录GPU内存使用情况
        
        Args:
            device_id: GPU设备ID
        """
        try:
            import torch
            if torch.cuda.is_available():
                allocated = torch.cuda.memory_allocated(device_id) / 1024**3
                cached = torch.cuda.memory_reserved(device_id) / 1024**3
                
                self.logger.info(
                    f"GPU内存使用 (设备{device_id}): "
                    f"已分配={allocated:.2f}GB, 已缓存={cached:.2f}GB"
                )
            else:
                self.logger.warning("CUDA不可用，无法记录GPU内存")
                
        except ImportError:
            self.logger.warning("PyTorch未安装，无法记录GPU内存")
        except Exception as e:
            self.logger.error(f"记录GPU内存失败: {e}")


class ProgressLogger:
    """进度日志器
    
    用于记录长时间运行操作的进度。
    """
    
    def __init__(self, name: str = 'progress', total: int = 100):
        """初始化进度日志器
        
        Args:
            name: 日志器名称
            total: 总进度数
        """
        self.logger = logging.getLogger(name)
        self.total = total
        self.current = 0
        self.last_logged_percent = -1
        self.start_time = datetime.now()
    
    def update(self, increment: int = 1, message: str = ''):
        """更新进度
        
        Args:
            increment: 进度增量
            message: 附加消息
        """
        self.current += increment
        percent = int((self.current / self.total) * 100)
        
        # 每10%记录一次，或者在完成时记录
        if (percent >= self.last_logged_percent + 10 or 
            self.current >= self.total):
            
            elapsed = (datetime.now() - self.start_time).total_seconds()
            
            if self.current < self.total and elapsed > 0:
                # 估算剩余时间
                rate = self.current / elapsed
                remaining = (self.total - self.current) / rate
                eta_msg = f", 预计剩余: {remaining:.0f}秒"
            else:
                eta_msg = ""
            
            progress_msg = f"进度: {percent}% ({self.current}/{self.total}){eta_msg}"
            if message:
                progress_msg += f" - {message}"
            
            self.logger.info(progress_msg)
            self.last_logged_percent = percent
    
    def finish(self, message: str = ''):
        """完成进度记录
        
        Args:
            message: 完成消息
        """
        elapsed = (datetime.now() - self.start_time).total_seconds()
        finish_msg = f"操作完成，总耗时: {elapsed:.1f}秒"
        
        if message:
            finish_msg += f" - {message}"
        
        self.logger.info(finish_msg)


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(name)


def log_function_call(func):
    """函数调用日志装饰器
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"调用函数: {func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    
    return wrapper


def log_execution_time(func):
    """执行时间日志装饰器
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        start_time = datetime.now()
        
        try:
            result = func(*args, **kwargs)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"函数 {func.__name__} 执行时间: {duration:.3f}秒")
            return result
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.error(f"函数 {func.__name__} 执行失败，耗时: {duration:.3f}秒, 错误: {e}")
            raise
    
    return wrapper


# 创建全局性能日志器和进度日志器实例
performance_logger = PerformanceLogger()