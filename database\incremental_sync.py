#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量同步模块

该模块提供数据库增量更新和同步功能。
"""

import os
import json
import hashlib
from typing import Dict, List, Set, Optional, Tuple, Any
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime
import time

from utils.log_utils import LoggerMixin
from utils.file_utils import FileManager
from database.feature_database import FeatureDatabase
from features.feature_extractor import FeatureExtractor


@dataclass
class FileChangeRecord:
    """文件变更记录"""
    file_path: str
    change_type: str  # 'added', 'modified', 'deleted'
    old_hash: Optional[str]
    new_hash: Optional[str]
    timestamp: float
    file_size: Optional[int]
    metadata: Dict[str, Any]


@dataclass
class SyncResult:
    """同步结果"""
    total_files: int
    added_files: int
    modified_files: int
    deleted_files: int
    skipped_files: int
    failed_files: int
    sync_time: float
    errors: List[str]
    warnings: List[str]


class IncrementalSyncManager(LoggerMixin):
    """增量同步管理器"""
    
    def __init__(self, database: FeatureDatabase, 
                 feature_extractor: FeatureExtractor):
        super().__init__()
        self.database = database
        self.feature_extractor = feature_extractor
        self.sync_state_file = None
        self.file_index = {}  # 文件索引：路径 -> 文件信息
        
    def set_sync_state_file(self, state_file_path: Path):
        """设置同步状态文件路径"""
        self.sync_state_file = state_file_path
        self._load_sync_state()
    
    def sync_directory(self, directory_path: Path, 
                      file_extensions: Optional[List[str]] = None,
                      recursive: bool = True,
                      force_rescan: bool = False) -> SyncResult:
        """同步目录
        
        Args:
            directory_path: 目录路径
            file_extensions: 支持的文件扩展名
            recursive: 是否递归扫描
            force_rescan: 是否强制重新扫描
            
        Returns:
            SyncResult: 同步结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"开始同步目录: {directory_path}")
            
            # 扫描目录获取当前文件列表
            current_files = self._scan_directory(
                directory_path, file_extensions, recursive
            )
            
            # 检测文件变更
            changes = self._detect_changes(current_files, force_rescan)
            
            # 执行同步
            result = self._execute_sync(changes)
            
            # 更新同步状态
            self._update_sync_state(current_files)
            
            # 保存同步状态
            self._save_sync_state()
            
            result.sync_time = time.time() - start_time
            
            self.logger.info(
                f"目录同步完成: 总计 {result.total_files} 个文件, "
                f"新增 {result.added_files}, 修改 {result.modified_files}, "
                f"删除 {result.deleted_files}, 耗时 {result.sync_time:.2f}s"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"目录同步失败: {e}")
            return SyncResult(
                total_files=0, added_files=0, modified_files=0,
                deleted_files=0, skipped_files=0, failed_files=0,
                sync_time=time.time() - start_time,
                errors=[f"同步失败: {e}"],
                warnings=[]
            )
    
    def sync_single_file(self, file_path: Path) -> bool:
        """同步单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 同步是否成功
        """
        try:
            if not file_path.exists():
                # 文件已删除
                return self._remove_file_from_database(str(file_path))
            
            # 获取文件信息
            file_manager = FileManager()
            file_info = file_manager.get_file_info(file_path)
            file_hash = file_manager.calculate_file_hash(file_path)
            
            # 检查是否需要更新
            old_info = self.file_index.get(str(file_path))
            
            if old_info and old_info.get('hash') == file_hash:
                self.logger.debug(f"文件未变更，跳过: {file_path}")
                return True
            
            # 提取特征
            features = self.feature_extractor.extract_features(file_path)
            
            if features is None:
                self.logger.warning(f"特征提取失败: {file_path}")
                return False
            
            # 更新数据库
            metadata = {
                'file_size': file_info['size'],
                'modified_time': file_info['modified_time'],
                'hash': file_hash,
                'sync_time': time.time()
            }
            
            success = self.database.add_features(
                str(file_path), features, metadata
            )
            
            if success:
                # 更新文件索引
                self.file_index[str(file_path)] = {
                    'hash': file_hash,
                    'size': file_info['size'],
                    'modified_time': file_info['modified_time'],
                    'sync_time': time.time()
                }
                
                self.logger.debug(f"文件同步成功: {file_path}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"同步文件失败 {file_path}: {e}")
            return False
    
    def remove_deleted_files(self, existing_files: Set[str]) -> int:
        """移除已删除的文件
        
        Args:
            existing_files: 当前存在的文件集合
            
        Returns:
            int: 移除的文件数量
        """
        removed_count = 0
        
        try:
            # 获取数据库中的所有文件
            db_files = set(self.database.get_image_paths())
            
            # 找出已删除的文件
            deleted_files = db_files - existing_files
            
            for file_path in deleted_files:
                if self._remove_file_from_database(file_path):
                    removed_count += 1
            
            self.logger.info(f"移除已删除文件: {removed_count} 个")
            return removed_count
            
        except Exception as e:
            self.logger.error(f"移除已删除文件失败: {e}")
            return 0
    
    def get_sync_statistics(self) -> Dict[str, Any]:
        """获取同步统计信息"""
        try:
            db_files = self.database.get_image_paths()
            
            stats = {
                'total_files': len(db_files),
                'indexed_files': len([f for f in db_files if self.database.get_features(f)]),
                'last_sync_time': max(
                    [info.get('sync_time', 0) for info in self.file_index.values()],
                    default=0
                ),
                'file_index_size': len(self.file_index)
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取同步统计信息失败: {e}")
            return {}
    
    def _scan_directory(self, directory_path: Path, 
                       file_extensions: Optional[List[str]],
                       recursive: bool) -> Dict[str, Dict[str, Any]]:
        """扫描目录获取文件列表"""
        files = {}
        
        try:
            if file_extensions:
                file_extensions = [ext.lower() for ext in file_extensions]
            
            pattern = '**/*' if recursive else '*'
            
            for file_path in directory_path.glob(pattern):
                if not file_path.is_file():
                    continue
                
                # 检查文件扩展名
                if file_extensions and file_path.suffix.lower() not in file_extensions:
                    continue
                
                try:
                    file_manager = FileManager()
                    file_info_obj = file_manager.get_file_info(file_path)
                    file_hash = file_manager.calculate_file_hash(file_path)
                    
                    if file_info_obj:
                        files[str(file_path)] = {
                            'hash': file_hash,
                            'size': file_info_obj.size,
                            'modified_time': file_info_obj.modified_time
                        }
                    
                except Exception as e:
                    self.logger.warning(f"获取文件信息失败 {file_path}: {e}")
            
            self.logger.debug(f"扫描目录完成，发现 {len(files)} 个文件")
            return files
            
        except Exception as e:
            self.logger.error(f"扫描目录失败: {e}")
            return {}
    
    def _detect_changes(self, current_files: Dict[str, Dict[str, Any]], 
                       force_rescan: bool) -> List[FileChangeRecord]:
        """检测文件变更"""
        changes = []
        
        try:
            current_paths = set(current_files.keys())
            indexed_paths = set(self.file_index.keys())
            
            # 新增文件
            added_files = current_paths - indexed_paths
            for file_path in added_files:
                file_info = current_files[file_path]
                changes.append(FileChangeRecord(
                    file_path=file_path,
                    change_type='added',
                    old_hash=None,
                    new_hash=file_info['hash'],
                    timestamp=time.time(),
                    file_size=file_info['size'],
                    metadata=file_info
                ))
            
            # 删除文件
            deleted_files = indexed_paths - current_paths
            for file_path in deleted_files:
                old_info = self.file_index[file_path]
                changes.append(FileChangeRecord(
                    file_path=file_path,
                    change_type='deleted',
                    old_hash=old_info.get('hash'),
                    new_hash=None,
                    timestamp=time.time(),
                    file_size=None,
                    metadata={}
                ))
            
            # 修改文件
            common_files = current_paths & indexed_paths
            for file_path in common_files:
                current_info = current_files[file_path]
                old_info = self.file_index[file_path]
                
                if (force_rescan or 
                    current_info['hash'] != old_info.get('hash') or
                    current_info['modified_time'] != old_info.get('modified_time')):
                    
                    changes.append(FileChangeRecord(
                        file_path=file_path,
                        change_type='modified',
                        old_hash=old_info.get('hash'),
                        new_hash=current_info['hash'],
                        timestamp=time.time(),
                        file_size=current_info['size'],
                        metadata=current_info
                    ))
            
            self.logger.debug(
                f"检测到变更: 新增 {len(added_files)}, "
                f"修改 {len([c for c in changes if c.change_type == 'modified'])}, "
                f"删除 {len(deleted_files)}"
            )
            
            return changes
            
        except Exception as e:
            self.logger.error(f"检测文件变更失败: {e}")
            return []
    
    def _execute_sync(self, changes: List[FileChangeRecord]) -> SyncResult:
        """执行同步操作"""
        result = SyncResult(
            total_files=len(changes),
            added_files=0, modified_files=0, deleted_files=0,
            skipped_files=0, failed_files=0, sync_time=0,
            errors=[], warnings=[]
        )
        
        for change in changes:
            try:
                if change.change_type == 'deleted':
                    if self._remove_file_from_database(change.file_path):
                        result.deleted_files += 1
                    else:
                        result.failed_files += 1
                        result.errors.append(f"删除文件失败: {change.file_path}")
                
                elif change.change_type in ['added', 'modified']:
                    if self.sync_single_file(Path(change.file_path)):
                        if change.change_type == 'added':
                            result.added_files += 1
                        else:
                            result.modified_files += 1
                    else:
                        result.failed_files += 1
                        result.errors.append(f"同步文件失败: {change.file_path}")
                
            except Exception as e:
                result.failed_files += 1
                result.errors.append(f"处理文件失败 {change.file_path}: {e}")
        
        return result
    
    def _remove_file_from_database(self, file_path: str) -> bool:
        """从数据库中移除文件"""
        try:
            success = self.database.delete_features(file_path)
            
            if success and file_path in self.file_index:
                del self.file_index[file_path]
            
            return success
            
        except Exception as e:
            self.logger.error(f"从数据库移除文件失败 {file_path}: {e}")
            return False
    
    def _update_sync_state(self, current_files: Dict[str, Dict[str, Any]]):
        """更新同步状态"""
        try:
            # 更新文件索引
            for file_path, file_info in current_files.items():
                if file_path in self.file_index:
                    self.file_index[file_path].update(file_info)
                    self.file_index[file_path]['sync_time'] = time.time()
                else:
                    file_info['sync_time'] = time.time()
                    self.file_index[file_path] = file_info
            
            # 移除已删除文件的索引
            current_paths = set(current_files.keys())
            indexed_paths = set(self.file_index.keys())
            deleted_paths = indexed_paths - current_paths
            
            for file_path in deleted_paths:
                if file_path in self.file_index:
                    del self.file_index[file_path]
            
        except Exception as e:
            self.logger.error(f"更新同步状态失败: {e}")
    
    def _load_sync_state(self):
        """加载同步状态"""
        try:
            if self.sync_state_file and self.sync_state_file.exists():
                with open(self.sync_state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.file_index = data.get('file_index', {})
                
                self.logger.debug(f"同步状态加载成功，包含 {len(self.file_index)} 个文件")
            
        except Exception as e:
            self.logger.error(f"加载同步状态失败: {e}")
            self.file_index = {}
    
    def _save_sync_state(self):
        """保存同步状态"""
        try:
            if self.sync_state_file:
                # 确保目录存在
                self.sync_state_file.parent.mkdir(parents=True, exist_ok=True)
                
                data = {
                    'file_index': self.file_index,
                    'last_update': time.time()
                }
                
                with open(self.sync_state_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                self.logger.debug(f"同步状态保存成功: {self.sync_state_file}")
            
        except Exception as e:
            self.logger.error(f"保存同步状态失败: {e}")