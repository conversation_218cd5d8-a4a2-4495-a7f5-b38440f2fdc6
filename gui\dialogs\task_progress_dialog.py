"""任务进度对话框

该模块定义了重构后的任务进度对话框主类。
"""

from typing import Set, Dict, List, Callable, Optional
from datetime import datetime

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar,
    QCheckBox, QSplitter, QPushButton
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal

from utils.task_manager import TaskManager, TaskInfo
from utils.logger_mixin import LoggerMixin
from gui.widgets import WidgetFactory
from .task_config import TaskDisplayConfig
from .task_table import TaskTable
from .task_details import TaskDetailsWidget


class TaskProgressDialog(QDialog, LoggerMixin):
    """任务进度对话框"""
    
    # 信号
    taskCompleted = pyqtSignal(str, object)  # 任务完成信号 (task_id, result)
    taskFailed = pyqtSignal(str, str)  # 任务失败信号 (task_id, error)
    allTasksCompleted = pyqtSignal()  # 所有任务完成信号
    
    def __init__(self, task_manager: TaskManager, parent=None):
        super().__init__(parent)
        self.task_manager = task_manager
        self.config = TaskDisplayConfig()
        
        # 状态跟踪
        self.monitored_tasks: Set[str] = set()
        self.completed_tasks: Set[str] = set()
        self.failed_tasks: Set[str] = set()
        self.task_callbacks: Dict[str, Callable] = {}
        
        # UI组件
        self.task_table: Optional[TaskTable] = None
        self.details_widget: Optional[TaskDetailsWidget] = None
        self.overall_progress_bar: Optional[QProgressBar] = None
        self.overall_progress_label: Optional[QLabel] = None
        self.status_label: Optional[QLabel] = None
        self.hide_on_complete_cb: Optional[QCheckBox] = None
        
        # 定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_progress)
        
        self._setup_ui()
        self._connect_signals()
        self._start_timer()
    
    def _setup_ui(self) -> None:
        """设置UI"""
        self.setWindowTitle("任务进度")
        self.setModal(False)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 总体进度区域
        self._create_overall_progress_section(layout)
        
        # 主内容区域
        self._create_main_content_section(layout)
        
        # 控制按钮区域
        self._create_control_section(layout)
    
    def _create_overall_progress_section(self, layout: QVBoxLayout) -> None:
        """创建总体进度区域"""
        # 总体进度标签
        self.overall_progress_label = QLabel("总体进度: 0% (0/0)")
        layout.addWidget(self.overall_progress_label)
        
        # 总体进度条
        self.overall_progress_bar = QProgressBar()
        self.overall_progress_bar.setMinimum(0)
        self.overall_progress_bar.setMaximum(100)
        self.overall_progress_bar.setValue(0)
        layout.addWidget(self.overall_progress_bar)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        layout.addWidget(self.status_label)
    
    def _create_main_content_section(self, layout: QVBoxLayout) -> None:
        """创建主内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # 任务表格
        self.task_table = TaskTable(self.config)
        splitter.addWidget(self.task_table)
        
        # 任务详情（如果启用）
        if self.config.show_details_panel:
            self.details_widget = TaskDetailsWidget()
            splitter.addWidget(self.details_widget)
            
            # 设置分割器比例
            splitter.setSizes([400, 200])
    
    def _create_control_section(self, layout: QVBoxLayout) -> None:
        """创建控制区域"""
        control_layout = QHBoxLayout()
        
        # 完成后自动隐藏选项
        self.hide_on_complete_cb = QCheckBox("完成后自动关闭")
        self.hide_on_complete_cb.setChecked(self.config.auto_hide_on_complete)
        control_layout.addWidget(self.hide_on_complete_cb)
        
        control_layout.addStretch()
        
        # 关闭按钮
        close_btn = WidgetFactory.create_button("关闭", self.close)
        control_layout.addWidget(close_btn)
        
        layout.addLayout(control_layout)
    
    def _connect_signals(self) -> None:
        """连接信号"""
        if self.task_table:
            self.task_table.taskSelected.connect(self._on_task_selected)
    
    def _start_timer(self) -> None:
        """启动定时器"""
        self.update_timer.start(self.config.update_interval)
    
    def add_task(self, task_id: str) -> None:
        """添加要监控的任务
        
        Args:
            task_id: 任务ID
        """
        if task_id in self.monitored_tasks:
            return
        
        self.monitored_tasks.add(task_id)
        
        # 获取任务信息
        task_info = self.task_manager.get_task(task_id)
        if not task_info:
            self.logger.warning(f"任务 {task_id} 不存在")
            return
        
        # 添加到表格
        if self.task_table:
            self.task_table.add_task(task_info)
        
        # 注册回调
        callback = lambda info: self._on_task_updated(info)
        self.task_callbacks[task_id] = callback
        self.task_manager.register_callback(task_id, callback)
        
        # 更新总体进度
        self._update_overall_progress()
    
    def add_tasks(self, task_ids: List[str]) -> None:
        """批量添加任务
        
        Args:
            task_ids: 任务ID列表
        """
        for task_id in task_ids:
            self.add_task(task_id)
    
    def remove_task(self, task_id: str) -> None:
        """移除监控的任务
        
        Args:
            task_id: 任务ID
        """
        if task_id not in self.monitored_tasks:
            return
        
        self.monitored_tasks.discard(task_id)
        self.completed_tasks.discard(task_id)
        self.failed_tasks.discard(task_id)
        
        # 移除表格中的任务
        if self.task_table:
            self.task_table.remove_task(task_id)
        
        # 取消回调注册
        if task_id in self.task_callbacks:
            callback = self.task_callbacks[task_id]
            self.task_manager.unregister_callback(task_id, callback)
            del self.task_callbacks[task_id]
        
        # 更新总体进度
        self._update_overall_progress()
    
    def clear_tasks(self) -> None:
        """清空所有任务"""
        # 取消所有回调
        for task_id, callback in self.task_callbacks.items():
            self.task_manager.unregister_callback(task_id, callback)
        
        # 清空状态
        self.monitored_tasks.clear()
        self.completed_tasks.clear()
        self.failed_tasks.clear()
        self.task_callbacks.clear()
        
        # 清空UI
        if self.task_table:
            self.task_table.clear_tasks()
        if self.details_widget:
            self.details_widget.clear_details()
        
        # 重置进度
        if self.overall_progress_bar:
            self.overall_progress_bar.setValue(0)
        if self.overall_progress_label:
            self.overall_progress_label.setText("总体进度: 0% (0/0)")
        if self.status_label:
            self.status_label.setText("准备就绪")
    
    def _on_task_updated(self, task_info: TaskInfo) -> None:
        """任务更新回调
        
        Args:
            task_info: 任务信息
        """
        # 更新表格
        if self.task_table:
            self.task_table.update_task(task_info)
        
        # 更新详情（如果当前选中的是这个任务）
        if self.details_widget and self.task_table:
            selected_task_id = self.task_table.get_selected_task_id()
            if selected_task_id == task_info.task_id:
                self.details_widget.update_details(task_info)
        
        # 处理任务完成或失败
        if task_info.status == "completed":
            self.completed_tasks.add(task_info.task_id)
            self.taskCompleted.emit(task_info.task_id, task_info.result)
        elif task_info.status == "failed":
            self.failed_tasks.add(task_info.task_id)
            self.taskFailed.emit(task_info.task_id, task_info.error or "未知错误")
        
        # 更新总进度
        self._update_overall_progress()
        
        # 检查是否所有任务都已完成
        self._check_all_completed()
    
    def _on_task_selected(self, task_id: str) -> None:
        """任务选择处理
        
        Args:
            task_id: 任务ID
        """
        if not self.details_widget:
            return
        
        task_info = self.task_manager.get_task(task_id)
        if task_info:
            self.details_widget.update_details(task_info)
        else:
            self.details_widget.clear_details()
    
    def _update_overall_progress(self) -> None:
        """更新总体进度"""
        if not self.monitored_tasks:
            return
        
        total_progress = 0.0
        running_count = 0
        completed_count = len(self.completed_tasks) + len(self.failed_tasks)
        
        for task_id in self.monitored_tasks:
            task_info = self.task_manager.get_task(task_id)
            if task_info:
                total_progress += task_info.progress
                if task_info.status == "running":
                    running_count += 1
        
        # 计算平均进度
        avg_progress = total_progress / len(self.monitored_tasks) * 100
        
        # 更新进度条
        if self.overall_progress_bar:
            self.overall_progress_bar.setValue(int(avg_progress))
        
        # 更新标签
        if self.overall_progress_label:
            self.overall_progress_label.setText(
                f"总体进度: {int(avg_progress)}% ({completed_count}/{len(self.monitored_tasks)})"
            )
        
        # 更新状态文本
        if self.status_label:
            if completed_count == len(self.monitored_tasks):
                if len(self.failed_tasks) > 0:
                    self.status_label.setText(
                        f"所有任务已完成，但有 {len(self.failed_tasks)} 个任务失败"
                    )
                else:
                    self.status_label.setText("所有任务已成功完成")
            else:
                self.status_label.setText(
                    f"正在处理 {running_count} 个任务，已完成 {completed_count} 个任务"
                )
    
    def _update_progress(self) -> None:
        """定时更新进度"""
        # 更新所有任务的耗时显示
        if self.task_table:
            for task_id in self.monitored_tasks:
                task_info = self.task_manager.get_task(task_id)
                if task_info and task_info.status == "running":
                    self.task_table.update_task(task_info)
    
    def _check_all_completed(self) -> None:
        """检查是否所有任务都已完成"""
        if not self.monitored_tasks:
            return
        
        all_completed = True
        for task_id in self.monitored_tasks:
            task_info = self.task_manager.get_task(task_id)
            if task_info and task_info.status not in ["completed", "failed"]:
                all_completed = False
                break
        
        if all_completed:
            self.allTasksCompleted.emit()
            
            # 如果设置了完成后自动关闭，则延迟关闭对话框
            if self.hide_on_complete_cb and self.hide_on_complete_cb.isChecked():
                QTimer.singleShot(500, self._delayed_close)
    
    def _delayed_close(self) -> None:
        """延迟关闭对话框"""
        try:
            self.close()
        except Exception as e:
            self.logger.error(f"延迟关闭对话框失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        try:
            # 停止定时器
            self.update_timer.stop()
            
            # 取消所有回调注册
            for task_id, callback in self.task_callbacks.items():
                self.task_manager.unregister_callback(task_id, callback)
            
            # 清空回调引用
            self.task_callbacks.clear()
            
            event.accept()
        except Exception as e:
            self.logger.error(f"关闭任务进度对话框时发生错误: {e}")
            event.accept()