#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像查看器配置

该模块定义图像查看器的配置参数。
"""

from typing import Dict, Any


class ImageViewerConfig:
    """图像查看器配置"""
    
    # 缩放配置
    ZOOM_MIN = 10
    ZOOM_MAX = 500
    ZOOM_DEFAULT = 100
    ZOOM_STEP = 25
    
    # 界面配置
    WIDGET_MARGIN = 5
    WIDGET_SPACING = 5
    STATUS_MARGIN = 2
    
    # 控件尺寸
    ZOOM_SLIDER_WIDTH = 150
    ZOOM_SPIN_WIDTH = 80
    VIEW_MODE_COMBO_WIDTH = 100
    IMAGE_LABEL_MIN_SIZE = (200, 200)
    
    # 样式配置
    IMAGE_LABEL_STYLE = "border: 1px solid #ccc; background-color: #f5f5f5;"
    
    # 查看模式映射
    VIEW_MODE_MAP = {
        "适应窗口": "FIT_TO_WINDOW",
        "适应宽度": "FIT_TO_WIDTH", 
        "适应高度": "FIT_TO_HEIGHT",
        "实际大小": "ACTUAL_SIZE",
        "自定义": "CUSTOM"
    }
    
    # 查看模式索引映射
    VIEW_MODE_INDEX_MAP = {
        "FIT_TO_WINDOW": 0,
        "FIT_TO_WIDTH": 1,
        "FIT_TO_HEIGHT": 2,
        "ACTUAL_SIZE": 3,
        "CUSTOM": 4
    }
    
    # 支持的图像格式
    SUPPORTED_FORMATS = [
        "*.png", "*.jpg", "*.jpeg", "*.bmp", "*.tiff", "*.gif"
    ]
    
    # 文件对话框过滤器
    FILE_FILTER = "图像文件 (*.png *.jpg *.jpeg *.bmp *.tiff);;所有文件 (*)"
    
    @classmethod
    def get_config(cls) -> Dict[str, Any]:
        """获取配置字典
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        return {
            'zoom': {
                'min': cls.ZOOM_MIN,
                'max': cls.ZOOM_MAX,
                'default': cls.ZOOM_DEFAULT,
                'step': cls.ZOOM_STEP
            },
            'ui': {
                'margin': cls.WIDGET_MARGIN,
                'spacing': cls.WIDGET_SPACING,
                'status_margin': cls.STATUS_MARGIN
            },
            'sizes': {
                'zoom_slider_width': cls.ZOOM_SLIDER_WIDTH,
                'zoom_spin_width': cls.ZOOM_SPIN_WIDTH,
                'view_mode_combo_width': cls.VIEW_MODE_COMBO_WIDTH,
                'image_label_min_size': cls.IMAGE_LABEL_MIN_SIZE
            },
            'styles': {
                'image_label': cls.IMAGE_LABEL_STYLE
            },
            'formats': {
                'supported': cls.SUPPORTED_FORMATS,
                'file_filter': cls.FILE_FILTER
            }
        }