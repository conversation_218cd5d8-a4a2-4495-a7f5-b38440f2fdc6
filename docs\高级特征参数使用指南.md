# 高级特征参数使用指南

本指南介绍如何配置和使用传统特征提取的高级参数，以优化面料搜索的效果。

## 概述

传统特征提取包括三种主要类型的特征：
- **颜色特征**: 描述图像的颜色分布和主要颜色
- **纹理特征**: 描述图像的纹理模式和表面特性
- **形状特征**: 描述图像中对象的几何形状

## 配置方法

### 1. 通过配置文件

在 `config/app_config.py` 中修改 `feature_extraction` 配置：

```python
'feature_extraction': {
    'extract_color': True,
    'extract_texture': True,
    'extract_shape': True,
    'hist_bins': 32,
    'n_dominant_colors': 5,
    'lbp_radius': 3,
    'lbp_n_points': 24,
    'n_fourier_descriptors': 32
}
```

### 2. 通过代码动态配置

```python
from config.app_config import AppConfig

app_config = AppConfig()

# 设置颜色特征参数
app_config.set('feature_extraction.hist_bins', 64)
app_config.set('feature_extraction.n_dominant_colors', 8)

# 设置纹理特征参数
app_config.set('feature_extraction.lbp_radius', 5)
app_config.set('feature_extraction.lbp_n_points', 32)

# 设置形状特征参数
app_config.set('feature_extraction.n_fourier_descriptors', 64)
```

## 参数详解

### 基本开关

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `extract_color` | bool | True | 是否提取颜色特征 |
| `extract_texture` | bool | True | 是否提取纹理特征 |
| `extract_shape` | bool | True | 是否提取形状特征 |

### 颜色特征参数

| 参数 | 类型 | 默认值 | 范围 | 说明 |
|------|------|--------|------|------|
| `hist_bins` | int | 32 | 16-128 | 颜色直方图分箱数，越大越精细 |
| `n_dominant_colors` | int | 5 | 3-10 | 主要颜色数量，越大捕获更多颜色信息 |

### 纹理特征参数

| 参数 | 类型 | 默认值 | 范围 | 说明 |
|------|------|--------|------|------|
| `lbp_radius` | int | 3 | 1-8 | LBP半径，越大捕获更粗粒度的纹理 |
| `lbp_n_points` | int | 24 | 8-48 | LBP采样点数，越多纹理描述越详细 |

### 形状特征参数

| 参数 | 类型 | 默认值 | 范围 | 说明 |
|------|------|--------|------|------|
| `n_fourier_descriptors` | int | 32 | 16-128 | 傅里叶描述子数量，越多形状描述越详细 |

## 预设配置

### 高精度配置
适用于对准确性要求高的场景：
```python
{
    'extract_color': True,
    'extract_texture': True,
    'extract_shape': True,
    'hist_bins': 128,
    'n_dominant_colors': 10,
    'lbp_radius': 8,
    'lbp_n_points': 48,
    'n_fourier_descriptors': 128
}
```

### 快速配置
适用于对速度要求高的场景：
```python
{
    'extract_color': True,
    'extract_texture': True,
    'extract_shape': False,  # 禁用形状特征
    'hist_bins': 16,
    'n_dominant_colors': 3,
    'lbp_radius': 1,
    'lbp_n_points': 8,
    'n_fourier_descriptors': 16
}
```

### 颜色专用配置
只关注颜色特征：
```python
{
    'extract_color': True,
    'extract_texture': False,
    'extract_shape': False,
    'hist_bins': 64,
    'n_dominant_colors': 8
}
```

## 性能与质量权衡

### 提高质量
- 增加 `hist_bins`、`n_dominant_colors`、`lbp_n_points`、`n_fourier_descriptors`
- 增加 `lbp_radius` (适度)
- 启用所有特征类型

### 提高速度
- 减少上述参数值
- 禁用不必要的特征类型
- 优先禁用形状特征（计算最耗时）

## 使用建议

1. **面料搜索**: 建议启用所有特征类型，使用默认或稍高的参数值
2. **颜色敏感搜索**: 增加 `hist_bins` 和 `n_dominant_colors`
3. **纹理敏感搜索**: 增加 `lbp_radius` 和 `lbp_n_points`
4. **形状敏感搜索**: 增加 `n_fourier_descriptors`
5. **实时搜索**: 使用快速配置或禁用形状特征
6. **批量处理**: 可以使用高精度配置

## 示例代码

运行以下示例来测试不同配置：

```bash
# 测试基本功能
python test_advanced_features.py

# 演示不同配置
python example_advanced_features.py
```

## 故障排除

### 常见问题

1. **传统特征提取器初始化失败**
   - 检查参数值是否在合理范围内
   - 确保所有依赖库已正确安装

2. **特征提取速度慢**
   - 减少参数值
   - 禁用不必要的特征类型
   - 考虑使用快速配置

3. **搜索结果不准确**
   - 增加相关特征的参数值
   - 确保启用了合适的特征类型
   - 考虑使用高精度配置

### 调试技巧

1. 使用测试脚本验证配置是否正确加载
2. 检查日志输出确认参数传递
3. 逐步调整参数观察效果变化

## 参考资料

- `config/feature_extraction_example.yaml`: 配置文件示例
- `example_advanced_features.py`: 使用示例
- `test_advanced_features.py`: 功能测试