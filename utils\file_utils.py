#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件工具模块

该模块提供文件操作、路径处理和文件管理功能。
"""

import os
import shutil
import hashlib
import mimetypes
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any, Union, Iterator, Tuple
from datetime import datetime
import json
import pickle
from dataclasses import dataclass, asdict


@dataclass
class FileInfo:
    """文件信息数据类"""
    path: str
    name: str
    size: int
    modified_time: datetime
    created_time: datetime
    is_directory: bool
    extension: str
    mime_type: Optional[str] = None
    hash_md5: Optional[str] = None


def validate_image_file(file_path: Union[str, Path]) -> bool:
    """验证图像文件的有效性
    
    Args:
        file_path: 图像文件路径
        
    Returns:
        bool: 文件是否是有效的图像文件
    """
    try:
        path = Path(file_path)
        
        # 检查文件是否存在
        if not path.exists() or not path.is_file():
            return False
        
        # 检查文件扩展名
        valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
        if path.suffix.lower() not in valid_extensions:
            return False
        
        # 检查文件大小
        if path.stat().st_size == 0:
            return False
        
        # 可以添加更多的验证，如尝试打开图像文件
        # 但这里为了简单起见，只做基本检查
        
        return True
    except Exception:
        return False


class FileManager:
    """文件管理器"""
    
    def __init__(self):
        """初始化文件管理器"""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def ensure_dir(self, path: Union[str, Path]) -> bool:
        """确保目录存在
        
        Args:
            path: 目录路径
            
        Returns:
            bool: 操作是否成功
        """
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"创建目录失败 {path}: {e}")
            return False
    
    def get_file_info(self, path: Union[str, Path]) -> Optional[FileInfo]:
        """获取文件信息
        
        Args:
            path: 文件路径
            
        Returns:
            Optional[FileInfo]: 文件信息，如果获取失败则返回None
        """
        try:
            path = Path(path)
            if not path.exists():
                return None
            
            stat = path.stat()
            
            file_info = FileInfo(
                path=str(path.absolute()),
                name=path.name,
                size=stat.st_size,
                modified_time=datetime.fromtimestamp(stat.st_mtime),
                created_time=datetime.fromtimestamp(stat.st_ctime),
                is_directory=path.is_dir(),
                extension=path.suffix.lower(),
                mime_type=mimetypes.guess_type(str(path))[0]
            )
            
            return file_info
            
        except Exception as e:
            self.logger.error(f"获取文件信息失败 {path}: {e}")
            return None
    
    def calculate_file_hash(self, path: Union[str, Path], 
                           algorithm: str = 'md5') -> Optional[str]:
        """计算文件哈希值
        
        Args:
            path: 文件路径
            algorithm: 哈希算法（md5, sha1, sha256等）
            
        Returns:
            Optional[str]: 哈希值，如果计算失败则返回None
        """
        try:
            path = Path(path)
            if not path.is_file():
                return None
            
            hash_obj = hashlib.new(algorithm)
            
            with open(path, 'rb') as f:
                for chunk in iter(lambda: f.read(8192), b''):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            self.logger.error(f"计算文件哈希失败 {path}: {e}")
            return None
    
    def copy_file(self, src: Union[str, Path], 
                  dst: Union[str, Path], 
                  overwrite: bool = False) -> bool:
        """复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            bool: 操作是否成功
        """
        try:
            src = Path(src)
            dst = Path(dst)
            
            if not src.exists():
                self.logger.error(f"源文件不存在: {src}")
                return False
            
            if dst.exists() and not overwrite:
                self.logger.error(f"目标文件已存在: {dst}")
                return False
            
            # 确保目标目录存在
            self.ensure_dir(dst.parent)
            
            shutil.copy2(src, dst)
            self.logger.debug(f"文件复制成功: {src} -> {dst}")
            return True
            
        except Exception as e:
            self.logger.error(f"复制文件失败: {e}")
            return False
    
    def move_file(self, src: Union[str, Path], 
                  dst: Union[str, Path]) -> bool:
        """移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            bool: 操作是否成功
        """
        try:
            src = Path(src)
            dst = Path(dst)
            
            if not src.exists():
                self.logger.error(f"源文件不存在: {src}")
                return False
            
            # 确保目标目录存在
            self.ensure_dir(dst.parent)
            
            shutil.move(str(src), str(dst))
            self.logger.debug(f"文件移动成功: {src} -> {dst}")
            return True
            
        except Exception as e:
            self.logger.error(f"移动文件失败: {e}")
            return False
    
    def delete_file(self, path: Union[str, Path]) -> bool:
        """删除文件
        
        Args:
            path: 文件路径
            
        Returns:
            bool: 操作是否成功
        """
        try:
            path = Path(path)
            
            if not path.exists():
                return True  # 文件不存在，认为删除成功
            
            if path.is_file():
                path.unlink()
            elif path.is_dir():
                shutil.rmtree(path)
            
            self.logger.debug(f"文件删除成功: {path}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除文件失败 {path}: {e}")
            return False
    
    def list_files(self, directory: Union[str, Path], 
                   pattern: str = '*',
                   recursive: bool = False,
                   include_dirs: bool = False) -> List[Path]:
        """列出目录中的文件
        
        Args:
            directory: 目录路径
            pattern: 文件模式（如'*.jpg'）
            recursive: 是否递归搜索
            include_dirs: 是否包含目录
            
        Returns:
            List[Path]: 文件路径列表
        """
        try:
            directory = Path(directory)
            
            if not directory.exists() or not directory.is_dir():
                return []
            
            if recursive:
                files = directory.rglob(pattern)
            else:
                files = directory.glob(pattern)
            
            result = []
            for file_path in files:
                if file_path.is_file() or (include_dirs and file_path.is_dir()):
                    result.append(file_path)
            
            return sorted(result)
            
        except Exception as e:
            self.logger.error(f"列出文件失败 {directory}: {e}")
            return []
    
    def find_files_by_extension(self, directory: Union[str, Path], 
                               extensions: List[str],
                               recursive: bool = True) -> List[Path]:
        """根据扩展名查找文件
        
        Args:
            directory: 搜索目录
            extensions: 文件扩展名列表（如['.jpg', '.png']）
            recursive: 是否递归搜索
            
        Returns:
            List[Path]: 匹配的文件路径列表
        """
        extensions = [ext.lower() for ext in extensions]
        all_files = self.list_files(directory, recursive=recursive)
        
        return [
            file_path for file_path in all_files
            if file_path.suffix.lower() in extensions
        ]
    
    def get_directory_size(self, directory: Union[str, Path]) -> int:
        """获取目录大小
        
        Args:
            directory: 目录路径
            
        Returns:
            int: 目录大小（字节）
        """
        try:
            directory = Path(directory)
            total_size = 0
            
            for file_path in directory.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
            
            return total_size
            
        except Exception as e:
            self.logger.error(f"获取目录大小失败 {directory}: {e}")
            return 0
    
    def clean_directory(self, directory: Union[str, Path], 
                       keep_structure: bool = True) -> bool:
        """清理目录
        
        Args:
            directory: 目录路径
            keep_structure: 是否保持目录结构
            
        Returns:
            bool: 操作是否成功
        """
        try:
            directory = Path(directory)
            
            if not directory.exists():
                return True
            
            if keep_structure:
                # 只删除文件，保留目录结构
                for file_path in directory.rglob('*'):
                    if file_path.is_file():
                        file_path.unlink()
            else:
                # 删除所有内容
                shutil.rmtree(directory)
                directory.mkdir(parents=True, exist_ok=True)
            
            self.logger.info(f"目录清理完成: {directory}")
            return True
            
        except Exception as e:
            self.logger.error(f"清理目录失败 {directory}: {e}")
            return False
    
    def backup_file(self, path: Union[str, Path], 
                   backup_dir: Optional[Union[str, Path]] = None) -> Optional[Path]:
        """备份文件
        
        Args:
            path: 文件路径
            backup_dir: 备份目录，如果为None则在原目录创建备份
            
        Returns:
            Optional[Path]: 备份文件路径，如果备份失败则返回None
        """
        try:
            path = Path(path)
            
            if not path.exists():
                return None
            
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"{path.stem}_{timestamp}{path.suffix}"
            
            if backup_dir:
                backup_path = Path(backup_dir) / backup_name
                self.ensure_dir(backup_dir)
            else:
                backup_path = path.parent / backup_name
            
            if self.copy_file(path, backup_path):
                self.logger.info(f"文件备份成功: {backup_path}")
                return backup_path
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"备份文件失败 {path}: {e}")
            return None


class ConfigManager:
    """配置文件管理器"""
    
    def __init__(self, config_dir: Union[str, Path]):
        """初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.file_manager = FileManager()
        
        # 确保配置目录存在
        self.file_manager.ensure_dir(self.config_dir)
    
    def save_json(self, data: Any, filename: str) -> bool:
        """保存JSON配置
        
        Args:
            data: 要保存的数据
            filename: 文件名
            
        Returns:
            bool: 保存是否成功
        """
        try:
            file_path = self.config_dir / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.debug(f"JSON配置保存成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存JSON配置失败 {filename}: {e}")
            return False
    
    def load_json(self, filename: str, default: Any = None) -> Any:
        """加载JSON配置
        
        Args:
            filename: 文件名
            default: 默认值
            
        Returns:
            Any: 加载的数据
        """
        try:
            file_path = self.config_dir / filename
            
            if not file_path.exists():
                return default
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.debug(f"JSON配置加载成功: {file_path}")
            return data
            
        except Exception as e:
            self.logger.error(f"加载JSON配置失败 {filename}: {e}")
            return default
    
    def save_pickle(self, data: Any, filename: str) -> bool:
        """保存Pickle配置
        
        Args:
            data: 要保存的数据
            filename: 文件名
            
        Returns:
            bool: 保存是否成功
        """
        try:
            file_path = self.config_dir / filename
            
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
            
            self.logger.debug(f"Pickle配置保存成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存Pickle配置失败 {filename}: {e}")
            return False
    
    def load_pickle(self, filename: str, default: Any = None) -> Any:
        """加载Pickle配置
        
        Args:
            filename: 文件名
            default: 默认值
            
        Returns:
            Any: 加载的数据
        """
        try:
            file_path = self.config_dir / filename
            
            if not file_path.exists():
                return default
            
            with open(file_path, 'rb') as f:
                data = pickle.load(f)
            
            self.logger.debug(f"Pickle配置加载成功: {file_path}")
            return data
            
        except Exception as e:
            self.logger.error(f"加载Pickle配置失败 {filename}: {e}")
            return default
    
    def list_configs(self, extension: str = '.json') -> List[str]:
        """列出配置文件
        
        Args:
            extension: 文件扩展名
            
        Returns:
            List[str]: 配置文件名列表
        """
        files = self.file_manager.list_files(self.config_dir, f'*{extension}')
        return [f.name for f in files]
    
    def delete_config(self, filename: str) -> bool:
        """删除配置文件
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 删除是否成功
        """
        file_path = self.config_dir / filename
        return self.file_manager.delete_file(file_path)
    
    def backup_config(self, filename: str) -> Optional[Path]:
        """备份配置文件
        
        Args:
            filename: 文件名
            
        Returns:
            Optional[Path]: 备份文件路径
        """
        file_path = self.config_dir / filename
        return self.file_manager.backup_file(file_path)


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小
    
    Args:
        size_bytes: 文件大小（字节）
        
    Returns:
        str: 格式化的文件大小字符串
    """
    # 使用FileUtils类中的静态方法
    return FileUtils.format_file_size(size_bytes)


def get_safe_filename(filename: str, replacement: str = '_') -> str:
    """获取安全的文件名
    
    Args:
        filename: 原始文件名
        replacement: 替换字符
        
    Returns:
        str: 安全的文件名
    """
    # 使用FileUtils类中的静态方法
    # 注意：FileUtils.safe_filename不支持replacement参数，但实现了相同的功能
    return FileUtils.safe_filename(filename)


# 使用FileUtils.is_image_file替代此函数
def is_image_file(path: Union[str, Path]) -> bool:
    """检查是否为图片文件
    
    Args:
        path: 文件路径
        
    Returns:
        bool: 是否为图片文件
    """
    return FileUtils.is_image_file(path)


def get_relative_path(path: Union[str, Path], 
                     base_path: Union[str, Path]) -> str:
    """获取相对路径
    
    Args:
        path: 文件路径
        base_path: 基础路径
        
    Returns:
        str: 相对路径
    """
    try:
        return str(Path(path).relative_to(Path(base_path)))
    except ValueError:
        return str(Path(path).absolute())


def create_temp_file(suffix: str = '', prefix: str = 'tmp', 
                    directory: Optional[Union[str, Path]] = None) -> Path:
    """创建临时文件
    
    Args:
        suffix: 文件后缀
        prefix: 文件前缀
        directory: 临时文件目录
        
    Returns:
        Path: 临时文件路径
    """
    import tempfile
    
    if directory:
        directory = str(directory)
    
    fd, temp_path = tempfile.mkstemp(
        suffix=suffix, prefix=prefix, dir=directory
    )
    os.close(fd)  # 关闭文件描述符
    
    return Path(temp_path)


class FileUtils:
    """文件工具类
    
    提供静态方法用于常见的文件操作
    """
    
    # 支持的图像文件扩展名
    IMAGE_EXTENSIONS = {
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif',
        '.webp', '.svg', '.ico', '.psd', '.raw', '.cr2', '.nef',
        '.orf', '.sr2', '.dng', '.arw', '.rw2', '.rwl', '.srw'
    }
    
    # 支持的视频文件扩展名
    VIDEO_EXTENSIONS = {
        '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv',
        '.m4v', '.3gp', '.3g2', '.f4v', '.asf', '.rm', '.rmvb'
    }
    
    @staticmethod
    def is_image_file(file_path: Union[str, Path]) -> bool:
        """检查文件是否为图像文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否为图像文件
        """
        if isinstance(file_path, str):
            file_path = Path(file_path)
        
        return file_path.suffix.lower() in FileUtils.IMAGE_EXTENSIONS
    
    @staticmethod
    def is_video_file(file_path: Union[str, Path]) -> bool:
        """检查文件是否为视频文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否为视频文件
        """
        if isinstance(file_path, str):
            file_path = Path(file_path)
        
        return file_path.suffix.lower() in FileUtils.VIDEO_EXTENSIONS
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """格式化文件大小
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            str: 格式化后的文件大小
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB", "PB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        
        return f"{s} {size_names[i]}"
    
    @staticmethod
    def get_file_extension(file_path: Union[str, Path]) -> str:
        """获取文件扩展名
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件扩展名（小写，包含点）
        """
        if isinstance(file_path, str):
            file_path = Path(file_path)
        
        return file_path.suffix.lower()
    
    @staticmethod
    def get_file_name_without_extension(file_path: Union[str, Path]) -> str:
        """获取不带扩展名的文件名
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 不带扩展名的文件名
        """
        if isinstance(file_path, str):
            file_path = Path(file_path)
        
        return file_path.stem
    
    @staticmethod
    def safe_filename(filename: str) -> str:
        """生成安全的文件名
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 安全的文件名
        """
        # 移除或替换不安全的字符
        import re
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # 移除控制字符
        safe_name = ''.join(char for char in safe_name if ord(char) >= 32)
        
        # 限制长度
        if len(safe_name) > 255:
            name, ext = os.path.splitext(safe_name)
            safe_name = name[:255-len(ext)] + ext
        
        return safe_name
    
    @staticmethod
    def find_files(directory: Union[str, Path], 
                   pattern: str = "*", 
                   recursive: bool = True,
                   include_dirs: bool = False) -> List[Path]:
        """查找文件
        
        Args:
            directory: 搜索目录
            pattern: 文件模式
            recursive: 是否递归搜索
            include_dirs: 是否包含目录
            
        Returns:
            List[Path]: 找到的文件列表
        """
        if isinstance(directory, str):
            directory = Path(directory)
        
        if not directory.exists():
            return []
        
        if recursive:
            files = directory.rglob(pattern)
        else:
            files = directory.glob(pattern)
        
        result = []
        for file_path in files:
            if include_dirs or file_path.is_file():
                result.append(file_path)
        
        return result
    
    @staticmethod
    def find_image_files(directory: Union[str, Path], 
                        recursive: bool = True) -> List[Path]:
        """查找图像文件
        
        Args:
            directory: 搜索目录
            recursive: 是否递归搜索
            
        Returns:
            List[Path]: 找到的图像文件列表
        """
        all_files = FileUtils.find_files(directory, "*", recursive)
        return [f for f in all_files if FileUtils.is_image_file(f)]


def scan_image_folder(folder_path: Union[str, Path], 
                      recursive: bool = True) -> List[Dict[str, Any]]:
    """扫描文件夹中的图像文件
    
    Args:
        folder_path: 文件夹路径
        recursive: 是否递归扫描子文件夹
        
    Returns:
        List[Dict[str, Any]]: 图像文件信息列表
    """
    try:
        folder_path = Path(folder_path)
        if not folder_path.exists() or not folder_path.is_dir():
            return []
        
        # 使用统一的图像验证逻辑
        from utils.image_validation import validate_image_path, is_supported_extension
        
        image_files = []
        
        # 获取所有图像文件
        if recursive:
            pattern = "**/*"
        else:
            pattern = "*"
            
        for file_path in folder_path.glob(pattern):
            if file_path.is_file() and is_supported_extension(file_path):
                # 进行完整的路径验证
                if validate_image_path(file_path):
                    try:
                        stat = file_path.stat()
                        file_info = {
                            'path': str(file_path.absolute()),
                            'name': file_path.name,
                            'size': stat.st_size,
                            'modified_time': datetime.fromtimestamp(stat.st_mtime),
                            'extension': file_path.suffix.lower(),
                            'relative_path': str(file_path.relative_to(folder_path))
                        }
                        image_files.append(file_info)
                    except Exception as e:
                        logging.getLogger(__name__).warning(f"获取文件信息失败 {file_path}: {e}")
                        continue
                else:
                    logging.getLogger(__name__).debug(f"跳过无效图像文件: {file_path}")
        
        logging.getLogger(__name__).info(f"扫描完成，找到 {len(image_files)} 个有效图像文件")
        return image_files
        
    except Exception as e:
        logging.getLogger(__name__).error(f"扫描文件夹失败 {folder_path}: {e}")
        return []


# 全局文件管理器实例
file_manager = FileManager()