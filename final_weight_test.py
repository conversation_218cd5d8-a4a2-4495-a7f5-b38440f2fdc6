#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终权重传递测试

该脚本用于验证完整的权重传递流程，从GUI到搜索引擎。
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_complete_weight_flow():
    """测试完整的权重传递流程"""
    logger.info("=" * 50)
    logger.info("测试完整的权重传递流程")
    logger.info("=" * 50)
    
    try:
        from gui.search.models import SearchConfig, SearchMode
        from search.models import SearchQuery, SearchType
        
        # 步骤1: 模拟GUI中的权重设置
        gui_weights = {
            'deep_learning': 0.0,
            'color': 1.0,
            'texture': 0.0,
            'shape': 0.0
        }
        logger.info(f"步骤1 - GUI权重设置: {gui_weights}")
        
        # 步骤2: 创建搜索配置（模拟SearchPanel.get_search_config()）
        config = SearchConfig(mode=SearchMode.SIMILARITY)
        config.feature_weights = gui_weights
        config.similarity_threshold = 0.7
        logger.info(f"步骤2 - 搜索配置创建: feature_weights={config.feature_weights}")
        
        # 步骤3: 模拟主窗口的搜索请求处理
        query_path = "/test/image.jpg"
        params = {}
        
        # 模拟修复后的主窗口逻辑
        search_config = config  # 使用搜索面板的配置
        search_config.query = query_path
        
        # 合并额外参数（但不覆盖已有的重要参数）
        for key, value in params.items():
            if hasattr(search_config, key):
                existing_value = getattr(search_config, key, None)
                if existing_value is None or (isinstance(existing_value, dict) and not existing_value):
                    setattr(search_config, key, value)
            else:
                setattr(search_config, key, value)
        
        logger.info(f"步骤3 - 主窗口处理后: feature_weights={search_config.feature_weights}")
        
        # 步骤4: 模拟SearchHandler的配置转换
        query = SearchQuery(query_type=SearchType.IMAGE_SIMILARITY)
        query.query_image_path = search_config.query
        
        # 设置特征权重
        if hasattr(search_config, 'feature_weights') and search_config.feature_weights:
            query.feature_weights = search_config.feature_weights
        
        # 设置相似度阈值
        if hasattr(search_config, 'similarity_threshold'):
            query.similarity_threshold = search_config.similarity_threshold
        
        logger.info(f"步骤4 - SearchQuery转换: feature_weights={query.feature_weights}")
        
        # 步骤5: 验证权重传递的完整性
        if (gui_weights == config.feature_weights == 
            search_config.feature_weights == query.feature_weights):
            logger.info("✅ 权重传递完整性验证通过")
            return True
        else:
            logger.error("❌ 权重传递完整性验证失败")
            logger.error(f"GUI权重: {gui_weights}")
            logger.error(f"配置权重: {config.feature_weights}")
            logger.error(f"搜索配置权重: {search_config.feature_weights}")
            logger.error(f"查询权重: {query.feature_weights}")
            return False
            
    except Exception as e:
        logger.error(f"测试完整权重流程失败: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return False


def test_different_weight_scenarios():
    """测试不同权重场景"""
    logger.info("=" * 50)
    logger.info("测试不同权重场景")
    logger.info("=" * 50)
    
    scenarios = [
        {
            'name': '仅颜色特征',
            'weights': {'deep_learning': 0.0, 'color': 1.0, 'texture': 0.0, 'shape': 0.0}
        },
        {
            'name': '仅纹理特征',
            'weights': {'deep_learning': 0.0, 'color': 0.0, 'texture': 1.0, 'shape': 0.0}
        },
        {
            'name': '仅形状特征',
            'weights': {'deep_learning': 0.0, 'color': 0.0, 'texture': 0.0, 'shape': 1.0}
        },
        {
            'name': '颜色和纹理混合',
            'weights': {'deep_learning': 0.0, 'color': 0.7, 'texture': 0.3, 'shape': 0.0}
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        logger.info(f"测试场景: {scenario['name']}")
        logger.info(f"权重设置: {scenario['weights']}")
        
        try:
            from gui.search.models import SearchConfig, SearchMode
            from search.models import SearchQuery, SearchType
            from search.cache_manager import CacheManager
            
            # 创建配置
            config = SearchConfig(mode=SearchMode.SIMILARITY)
            config.feature_weights = scenario['weights']
            
            # 转换为查询
            query = SearchQuery(
                query_type=SearchType.IMAGE_SIMILARITY,
                query_image_path="/test/image.jpg",
                feature_weights=config.feature_weights
            )
            
            # 生成缓存键
            cache_manager = CacheManager()
            cache_key = cache_manager.generate_cache_key(query)
            
            logger.info(f"生成的缓存键: {cache_key[:16]}...")
            logger.info(f"权重传递成功: {config.feature_weights == query.feature_weights}")
            
            if config.feature_weights != query.feature_weights:
                all_passed = False
                logger.error(f"权重传递失败: {scenario['name']}")
            
        except Exception as e:
            logger.error(f"场景测试失败 {scenario['name']}: {e}")
            all_passed = False
        
        logger.info("-" * 30)
    
    return all_passed


def main():
    """主函数"""
    logger.info("开始最终权重传递测试")
    logger.info("=" * 80)
    
    results = []
    
    # 测试1: 完整权重流程
    results.append(test_complete_weight_flow())
    
    # 测试2: 不同权重场景
    results.append(test_different_weight_scenarios())
    
    # 总结
    logger.info("=" * 80)
    logger.info("最终测试总结")
    logger.info("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    logger.info(f"通过测试: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有测试通过 - 权重传递问题已完全解决！")
        logger.info("用户现在可以在GUI中调整特征权重，搜索结果会相应变化。")
    else:
        logger.error("❌ 部分测试失败 - 需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
