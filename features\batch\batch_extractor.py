#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量特征提取器

提供批量特征提取功能。
"""

import logging
from typing import List

from ..data_models.feature_models import FeatureExtractionResult, BatchFeatureResult
from ..validators.input_validator import InputValidator

logger = logging.getLogger(__name__)


class BatchExtractor:
    """批量特征提取器"""
    
    def __init__(self, feature_extractor):
        """初始化批量提取器
        
        Args:
            feature_extractor: 特征提取器实例
        """
        self.feature_extractor = feature_extractor
    
    def extract_features_batch(self, image_paths: List[str], 
                              extract_traditional: bool = True) -> BatchFeatureResult:
        """批量提取特征
        
        Args:
            image_paths: 图像路径列表
            extract_traditional: 是否提取传统特征
            
        Returns:
            BatchFeatureResult: 批量特征提取结果
        """
        try:
            # 验证输入路径
            valid_paths = InputValidator.validate_image_paths_batch(image_paths)
            
            if not valid_paths:
                logger.warning("没有有效的图像路径")
                return BatchFeatureResult()
            
            logger.info(f"开始批量提取特征，共 {len(valid_paths)} 个图像")
            
            batch_result = BatchFeatureResult()
            
            for i, image_path in enumerate(valid_paths):
                try:
                    logger.debug(f"处理图像 {i+1}/{len(valid_paths)}: {image_path}")
                    result = self.feature_extractor.extract_features(image_path, extract_traditional)
                    batch_result.add_result(result)
                    
                    if (i + 1) % 10 == 0:  # 每处理10个图像记录一次进度
                        logger.info(f"已处理 {i+1}/{len(valid_paths)} 个图像")
                        
                except Exception as e:
                    logger.error(f"处理图像失败: {image_path} - {str(e)}")
                    # 添加失败结果
                    failed_result = FeatureExtractionResult(
                        image_path=image_path,
                        success=False,
                        error_message=f"批量处理异常: {str(e)}"
                    )
                    batch_result.add_result(failed_result)
            
            logger.info(f"批量特征提取完成，成功: {batch_result.success_count}, 失败: {batch_result.failure_count}")
            return batch_result
            
        except Exception as e:
            logger.error(f"批量特征提取异常: {str(e)}", exc_info=True)
            return BatchFeatureResult()