#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI模块

该模块提供图形用户界面相关的功能。
"""

__version__ = "2.0.0"
__author__ = "Fabric Search Team"

# 导入主要的GUI组件
from .core import MainWindow  # 从core模块导入MainWindow
from .search_panel import SearchPanel
from .result_panel import ResultPanel
from .image_viewer import ImageViewer
from .settings_dialog import SettingsDialog
from .model_selector_dialog import ModelSelectorDialog
from .task_progress_dialog import TaskProgressDialog
from .image_compare_panel import ImageComparePanel
from .dialogs import AboutDialog  # 从dialogs模块导入AboutDialog

# 导入工具类
from .gui_utils import GUIUtils
from .themes import ThemeManager
from .widget_factory import WidgetFactory

__all__ = [
    # 主要GUI组件
    'MainWindow',
    'SearchPanel', 
    'ResultPanel',
    'ImageViewer',
    'ImageComparePanel',
    
    # 对话框
    'SettingsDialog',
    'AboutDialog',
    'ModelSelectorDialog',
    'TaskProgressDialog',
    
    # 工具类
    'GUIUtils',
    'ThemeManager',
    'WidgetFactory'
]