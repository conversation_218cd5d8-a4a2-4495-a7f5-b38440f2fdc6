#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件工具模块

该模块提供文件和目录操作相关的工具函数。
"""

import os
import shutil
import hashlib
import tempfile
import logging
from typing import Optional, List, Any
from pathlib import Path
from contextlib import contextmanager

logger = logging.getLogger(__name__)


def ensure_dir(dir_path: str) -> bool:
    """确保目录存在
    
    Args:
        dir_path: 目录路径
        
    Returns:
        bool: 是否成功创建或已存在
    """
    try:
        os.makedirs(dir_path, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Error creating directory {dir_path}: {str(e)}")
        return False


# 为了兼容性，创建别名
ensure_directory = ensure_dir


def get_file_hash(file_path: str, algorithm: str = "md5") -> Optional[str]:
    """计算文件哈希值
    
    Args:
        file_path: 文件路径
        algorithm: 哈希算法 (md5, sha1, sha256)
        
    Returns:
        Optional[str]: 哈希值，失败时返回None
    """
    try:
        if not os.path.exists(file_path):
            return None
        
        hash_algorithms = {
            'md5': hashlib.md5,
            'sha1': hashlib.sha1,
            'sha256': hashlib.sha256
        }
        
        if algorithm not in hash_algorithms:
            logger.error(f"Unsupported hash algorithm: {algorithm}")
            return None
        
        hash_func = hash_algorithms[algorithm]()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        
        return hash_func.hexdigest()
        
    except Exception as e:
        logger.error(f"Error computing hash for {file_path}: {str(e)}")
        return None


@contextmanager
def safe_file_operation(file_path: str, mode: str = 'w', backup: bool = True):
    """安全文件操作上下文管理器
    
    Args:
        file_path: 文件路径
        mode: 文件打开模式
        backup: 是否创建备份
    """
    backup_path = None
    temp_path = None
    
    try:
        # 如果文件存在且需要备份
        if backup and os.path.exists(file_path):
            backup_path = f"{file_path}.backup"
            shutil.copy2(file_path, backup_path)
        
        # 创建临时文件
        if 'w' in mode or 'a' in mode:
            temp_path = f"{file_path}.tmp"
            with open(temp_path, mode) as f:
                yield f
            
            # 原子性替换
            if os.path.exists(temp_path):
                shutil.move(temp_path, file_path)
        else:
            with open(file_path, mode) as f:
                yield f
        
        # 删除备份文件
        if backup_path and os.path.exists(backup_path):
            os.remove(backup_path)
            
    except Exception as e:
        logger.error(f"Error in safe file operation for {file_path}: {str(e)}")
        
        # 恢复备份
        if backup_path and os.path.exists(backup_path):
            if os.path.exists(file_path):
                os.remove(file_path)
            shutil.move(backup_path, file_path)
        
        # 清理临时文件
        if temp_path and os.path.exists(temp_path):
            os.remove(temp_path)
        
        raise
    
    finally:
        # 清理临时文件
        if temp_path and os.path.exists(temp_path):
            try:
                os.remove(temp_path)
            except (OSError, PermissionError) as e:
                logger.warning(f"无法删除临时文件 {temp_path}: {e}")


def cleanup_temp_files(directory: str, pattern: str = "*.tmp") -> int:
    """清理临时文件
    
    Args:
        directory: 目录路径
        pattern: 文件模式
        
    Returns:
        int: 清理的文件数量
    """
    try:
        if not os.path.exists(directory):
            return 0
        
        count = 0
        for file_path in Path(directory).glob(pattern):
            try:
                if file_path.is_file():
                    file_path.unlink()
                    count += 1
            except Exception as e:
                logger.warning(f"Failed to remove temp file {file_path}: {str(e)}")
        
        return count
        
    except Exception as e:
        logger.error(f"Error cleaning up temp files in {directory}: {str(e)}")
        return 0


def get_file_size(file_path: str) -> Optional[int]:
    """获取文件大小
    
    Args:
        file_path: 文件路径
        
    Returns:
        Optional[int]: 文件大小（字节），失败时返回None
    """
    try:
        if os.path.exists(file_path):
            return os.path.getsize(file_path)
        return None
    except Exception as e:
        logger.error(f"Error getting file size for {file_path}: {str(e)}")
        return None


def copy_file_safe(src: str, dst: str, overwrite: bool = False) -> bool:
    """安全复制文件
    
    Args:
        src: 源文件路径
        dst: 目标文件路径
        overwrite: 是否覆盖已存在的文件
        
    Returns:
        bool: 是否复制成功
    """
    try:
        if not os.path.exists(src):
            logger.error(f"Source file does not exist: {src}")
            return False
        
        if os.path.exists(dst) and not overwrite:
            logger.warning(f"Destination file already exists: {dst}")
            return False
        
        # 确保目标目录存在
        dst_dir = os.path.dirname(dst)
        if dst_dir:
            ensure_dir(dst_dir)
        
        shutil.copy2(src, dst)
        return True
        
    except Exception as e:
        logger.error(f"Error copying file from {src} to {dst}: {str(e)}")
        return False


def move_file_safe(src: str, dst: str, overwrite: bool = False) -> bool:
    """安全移动文件
    
    Args:
        src: 源文件路径
        dst: 目标文件路径
        overwrite: 是否覆盖已存在的文件
        
    Returns:
        bool: 是否移动成功
    """
    try:
        if not os.path.exists(src):
            logger.error(f"Source file does not exist: {src}")
            return False
        
        if os.path.exists(dst) and not overwrite:
            logger.warning(f"Destination file already exists: {dst}")
            return False
        
        # 确保目标目录存在
        dst_dir = os.path.dirname(dst)
        if dst_dir:
            ensure_dir(dst_dir)
        
        shutil.move(src, dst)
        return True
        
    except Exception as e:
        logger.error(f"Error moving file from {src} to {dst}: {str(e)}")
        return False


def list_files(directory: str, extensions: List[str] = None, 
              recursive: bool = False) -> List[str]:
    """列出目录中的文件
    
    Args:
        directory: 目录路径
        extensions: 文件扩展名列表
        recursive: 是否递归搜索
        
    Returns:
        List[str]: 文件路径列表
    """
    try:
        if not os.path.exists(directory):
            return []
        
        files = []
        
        if recursive:
            for root, dirs, filenames in os.walk(directory):
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    if extensions is None:
                        files.append(file_path)
                    else:
                        _, ext = os.path.splitext(filename.lower())
                        if ext in [e.lower() for e in extensions]:
                            files.append(file_path)
        else:
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                if os.path.isfile(file_path):
                    if extensions is None:
                        files.append(file_path)
                    else:
                        _, ext = os.path.splitext(filename.lower())
                        if ext in [e.lower() for e in extensions]:
                            files.append(file_path)
        
        return files
        
    except Exception as e:
        logger.error(f"Error listing files in {directory}: {str(e)}")
        return []


def create_temp_file(suffix: str = ".tmp", prefix: str = "fabric_", 
                    dir: str = None) -> str:
    """创建临时文件
    
    Args:
        suffix: 文件后缀
        prefix: 文件前缀
        dir: 临时目录
        
    Returns:
        str: 临时文件路径
    """
    try:
        fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
        os.close(fd)  # 关闭文件描述符
        return temp_path
    except Exception as e:
        logger.error(f"Error creating temp file: {str(e)}")
        raise


def get_directory_size(directory: str) -> int:
    """获取目录大小
    
    Args:
        directory: 目录路径
        
    Returns:
        int: 目录大小（字节）
    """
    try:
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                file_path = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(file_path)
                except (OSError, IOError):
                    continue
        return total_size
    except Exception as e:
        logger.error(f"Error getting directory size for {directory}: {str(e)}")
        return 0