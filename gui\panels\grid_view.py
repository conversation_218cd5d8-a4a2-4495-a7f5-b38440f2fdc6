"""
网格视图模块

包含网格视图的实现
"""

import os
from typing import List
from PyQt6.QtWidgets import (
    QScrollArea, QWidget, QGridLayout, QSizePolicy, 
    QSpacerItem, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal

from database.models import FabricImage
from utils.logger_mixin import LoggerMixin
from gui.panels.result_item import ResultItem


class GridView(QScrollArea, LoggerMixin):
    """网格视图"""
    
    # 信号
    itemClicked = pyqtSignal(FabricImage)  # 项目点击
    itemDoubleClicked = pyqtSignal(FabricImage)  # 项目双击
    selectionChanged = pyqtSignal(list)  # 选择变更
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.items = []
        self.selected_items = []
        self.thumbnail_size = 150
        self.columns = 4
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 创建容器
        self.container = QWidget()
        self.grid_layout = QGridLayout(self.container)
        self.grid_layout.setSpacing(10)
        self.grid_layout.setContentsMargins(10, 10, 10, 10)
        
        self.setWidget(self.container)
    
    def set_results(self, results: List[FabricImage]):
        """设置搜索结果"""
        try:
            self.logger.info(f"=== GridView 开始设置结果 ===")
            self.logger.info(f"接收到的结果数量: {len(results)}")
            
            # 清除现有项目
            self.clear_items()
            
            if not results:
                self.logger.info("结果为空，清空显示")
                return
            
            # 计算列数
            self.calculate_columns()
            
            # 添加新项目
            for i, fabric_image in enumerate(results):
                try:
                    self.logger.info(f"创建第 {i+1} 个结果项: {fabric_image.file_path}")
                    
                    # 检查文件是否存在
                    if not os.path.exists(fabric_image.file_path):
                        self.logger.warning(f"文件不存在，但仍继续处理: {fabric_image.file_path}")
                    
                    # 获取特征分数
                    feature_scores = {}
                    if hasattr(fabric_image, 'feature_scores') and fabric_image.feature_scores:
                        feature_scores = fabric_image.feature_scores
                        self.logger.info(f"网格模式找到特征分数: {feature_scores}")
                    elif hasattr(fabric_image, 'similarity_score') and fabric_image.similarity_score is not None:
                        # 如果只有总相似度分数，创建包含总分的feature_scores
                        feature_scores = {"总分": fabric_image.similarity_score}
                        self.logger.info(f"网格模式使用相似度分数: {fabric_image.similarity_score}")
                    else:
                        self.logger.warning(f"网格模式未找到分数数据: {fabric_image.file_path}")
                    
                    item = ResultItem(fabric_image, self.thumbnail_size, feature_scores)
                    item.itemClicked.connect(self.on_item_clicked)
                    item.itemDoubleClicked.connect(self.on_item_double_clicked)
                    
                    row = i // self.columns
                    col = i % self.columns
                    self.grid_layout.addWidget(item, row, col)
                    
                    self.items.append(item)
                    
                    self.logger.info(f"第 {i+1} 个结果项创建成功，位置: ({row}, {col})")
                    
                except Exception as item_error:
                    self.logger.error(f"创建第 {i+1} 个结果项失败: {item_error}")
                    import traceback
                    self.logger.error(f"错误堆栈: {traceback.format_exc()}")
                    continue
            
            # 添加弹性空间
            self.grid_layout.addItem(
                QSpacerItem(0, 0, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding),
                (len(results) // self.columns) + 1, 0, 1, self.columns
            )
            
            self.logger.info(f"=== GridView 结果设置完成，显示 {len(self.items)} 个结果 ===")
            
        except Exception as e:
            self.logger.error(f"GridView设置结果失败: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
    
    def clear_items(self):
        """清除所有项目"""
        try:
            for item in self.items:
                item.setParent(None)
                item.deleteLater()
            
            self.items.clear()
            self.selected_items.clear()
            
            # 清除布局
            while self.grid_layout.count():
                child = self.grid_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
                elif child.spacerItem():
                    del child
            
        except Exception as e:
            self.logger.error(f"清除项目失败: {e}")
    
    def calculate_columns(self):
        """计算列数"""
        try:
            available_width = self.viewport().width() - 20  # 减去边距
            item_width = self.thumbnail_size + 20  # 项目宽度
            self.columns = max(1, available_width // item_width)
            
        except Exception as e:
            self.logger.error(f"计算列数失败: {e}")
            self.columns = 4
    
    def set_thumbnail_size(self, size: int):
        """设置缩略图大小
        
        Args:
            size: 缩略图大小
        """
        try:
            self.thumbnail_size = size
            
            # 更新现有项目
            for item in self.items:
                item.thumbnail_size = size
                # 为加权相似度总分预留空间
                extra_height = 30  # 固定为总分标签预留空间
                item.setFixedSize(size + 20, size + 80 + extra_height)
                item.thumbnail_label.setFixedSize(size, size)
                item.load_thumbnail()
            
            # 重新计算布局
            self.calculate_columns()
            
        except Exception as e:
            self.logger.error(f"设置缩略图大小失败: {e}")
    
    def on_item_clicked(self, fabric_image: FabricImage):
        """项目点击事件"""
        try:
            # 查找对应的项目
            clicked_item = None
            for item in self.items:
                if item.fabric_image.id == fabric_image.id:
                    clicked_item = item
                    break
            
            if not clicked_item:
                return
            
            # 处理选择
            modifiers = QApplication.keyboardModifiers()
            
            if modifiers & Qt.KeyboardModifier.ControlModifier:
                # Ctrl+点击：切换选择
                if clicked_item in self.selected_items:
                    self.selected_items.remove(clicked_item)
                    clicked_item.set_selected(False)
                else:
                    self.selected_items.append(clicked_item)
                    clicked_item.set_selected(True)
            elif modifiers & Qt.KeyboardModifier.ShiftModifier:
                # Shift+点击：范围选择
                if self.selected_items:
                    # 获取最后选择的项目
                    last_selected = self.selected_items[-1]
                    last_index = self.items.index(last_selected)
                    current_index = self.items.index(clicked_item)
                    
                    # 确定范围
                    start_index = min(last_index, current_index)
                    end_index = max(last_index, current_index)
                    
                    # 清除当前选择
                    for item in self.selected_items:
                        item.set_selected(False)
                    self.selected_items.clear()
                    
                    # 选择范围内的所有项目
                    for i in range(start_index, end_index + 1):
                        item = self.items[i]
                        self.selected_items.append(item)
                        item.set_selected(True)
                else:
                    # 如果没有之前的选择，就单选
                    self.clear_selection()
                    self.selected_items.append(clicked_item)
                    clicked_item.set_selected(True)
            else:
                # 普通点击：单选
                for item in self.selected_items:
                    item.set_selected(False)
                self.selected_items.clear()
                
                self.selected_items.append(clicked_item)
                clicked_item.set_selected(True)
            
            # 发送信号
            self.itemClicked.emit(fabric_image)
            self.selectionChanged.emit([item.fabric_image for item in self.selected_items])
            
        except Exception as e:
            self.logger.error(f"项目点击事件处理失败: {e}")
    
    def on_item_double_clicked(self, fabric_image: FabricImage):
        """项目双击事件"""
        self.itemDoubleClicked.emit(fabric_image)
    
    def clear_selection(self):
        """清除选择"""
        for item in self.selected_items:
            item.set_selected(False)
        self.selected_items.clear()
    
    def resizeEvent(self, event):
        """窗口大小变更事件"""
        super().resizeEvent(event)
        self.calculate_columns()
    
    def get_selected_items(self) -> List[FabricImage]:
        """获取选中的项目
        
        Returns:
            List[FabricImage]: 选中的项目列表
        """
        return [item.fabric_image for item in self.selected_items]