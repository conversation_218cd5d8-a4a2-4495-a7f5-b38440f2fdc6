#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征提取器主入口模块

重构后的特征提取器主入口，提供向后兼容的接口。
"""

# 导入核心类
from .core.feature_extractor import FeatureExtractor
from .batch.batch_extractor import BatchExtractor
from .factory.extractor_factory import ExtractorFactory

# 导入验证器
from .validators.input_validator import InputValidator
from .validators.image_validator import ImageValidator

# 导入缓存管理器
from .storage.cache_manager import CacheManager

# 导入配置
from .config.feature_config import FeatureExtractorConfig, TraditionalFeatureConfig

# 导入数据模型
from .data_models.feature_models import FeatureExtractionResult

# 便捷函数，保持向后兼容性
def create_feature_extractor(model_name: str = "resnet50", 
                           use_gpu: bool = True,
                           extract_traditional: bool = True,
                           cache_dir: str = "cache",
                           use_cache: bool = True) -> FeatureExtractor:
    """创建特征提取器实例（向后兼容函数）
    
    Args:
        model_name: 深度学习模型名称
        use_gpu: 是否使用GPU
        extract_traditional: 是否提取传统特征
        cache_dir: 缓存目录
        use_cache: 是否使用缓存
        
    Returns:
        FeatureExtractor: 特征提取器实例
    """
    return ExtractorFactory.create_feature_extractor(
        model_name=model_name,
        use_gpu=use_gpu,
        extract_traditional=extract_traditional,
        cache_dir=cache_dir,
        use_cache=use_cache
    )


# 导出所有公共接口
__all__ = [
    'FeatureExtractor',
    'BatchExtractor', 
    'ExtractorFactory',
    'InputValidator',
    'ImageValidator',
    'CacheManager',
    'FeatureExtractorConfig',
    'TraditionalFeatureConfig',
    'FeatureExtractionResult',
    'create_feature_extractor'
]