#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PyTorch API修复

验证torch.cuda.amp.autocast()已正确更新为torch.amp.autocast('cuda')
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_pytorch_api_compatibility():
    """测试PyTorch API兼容性"""
    logger.info("测试PyTorch API兼容性...")
    
    try:
        import torch
        
        logger.info(f"PyTorch版本: {torch.__version__}")
        
        # 检查是否有CUDA支持
        if torch.cuda.is_available():
            logger.info(f"CUDA可用: {torch.cuda.is_available()}")
            logger.info(f"CUDA版本: {torch.version.cuda}")
            logger.info(f"GPU数量: {torch.cuda.device_count()}")
            
            # 测试新的API
            try:
                # 尝试使用新API
                with torch.amp.autocast('cuda'):
                    x = torch.randn(2, 3, device='cuda')
                    y = x * 2
                logger.info("✓ 新的torch.amp.autocast('cuda') API可用")
                new_api_available = True
            except (AttributeError, TypeError) as e:
                logger.warning(f"✗ 新API不可用: {e}")
                new_api_available = False
            
            # 测试旧的API
            try:
                with torch.cuda.amp.autocast():
                    x = torch.randn(2, 3, device='cuda')
                    y = x * 2
                logger.info("✓ 旧的torch.cuda.amp.autocast() API可用")
                old_api_available = True
            except (AttributeError, TypeError) as e:
                logger.warning(f"✗ 旧API不可用: {e}")
                old_api_available = False
            
            if new_api_available:
                logger.info("推荐使用新的torch.amp.autocast('cuda') API")
            elif old_api_available:
                logger.info("使用旧的torch.cuda.amp.autocast() API作为回退")
            else:
                logger.warning("混合精度API不可用")
                
        else:
            logger.info("CUDA不可用，跳过混合精度测试")
            
        return True
        
    except ImportError:
        logger.error("PyTorch未安装")
        return False
    except Exception as e:
        logger.error(f"PyTorch API测试失败: {e}")
        return False


def test_deep_extractor_import():
    """测试深度特征提取器导入"""
    logger.info("测试深度特征提取器导入...")
    
    try:
        # 尝试导入深度特征提取器
        from features.extractors.deep_extractor_core import DeepFeatureExtractor
        logger.info("✓ DeepFeatureExtractor导入成功")
        
        # 检查类是否有正确的方法
        methods_to_check = ['extract_features', 'extract_batch_features']
        for method_name in methods_to_check:
            if hasattr(DeepFeatureExtractor, method_name):
                logger.info(f"✓ {method_name}方法存在")
            else:
                logger.warning(f"✗ {method_name}方法不存在")
        
        return True
        
    except ImportError as e:
        logger.error(f"深度特征提取器导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"深度特征提取器测试失败: {e}")
        return False


def test_code_syntax():
    """测试代码语法"""
    logger.info("测试代码语法...")
    
    try:
        import ast
        
        # 检查修复的文件语法
        file_path = project_root / "features" / "extractors" / "deep_extractor_core.py"
        
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            # 解析语法
            ast.parse(code)
            logger.info("✓ deep_extractor_core.py语法正确")
            
            # 检查是否包含新的API调用
            if 'torch.amp.autocast(' in code:
                logger.info("✓ 包含新的torch.amp.autocast API")
            else:
                logger.warning("✗ 未找到新的torch.amp.autocast API")
            
            # 检查是否包含回退逻辑
            if 'torch.cuda.amp.autocast()' in code:
                logger.info("✓ 包含旧API回退逻辑")
            else:
                logger.warning("✗ 未找到旧API回退逻辑")
            
            return True
        else:
            logger.error("deep_extractor_core.py文件不存在")
            return False
            
    except SyntaxError as e:
        logger.error(f"语法错误: {e}")
        return False
    except Exception as e:
        logger.error(f"语法测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行PyTorch API修复验证测试...")
    logger.info("="*60)
    
    tests = [
        ("PyTorch API兼容性", test_pytorch_api_compatibility),
        ("深度特征提取器导入", test_deep_extractor_import),
        ("代码语法检查", test_code_syntax),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*40}")
        logger.info(f"测试: {test_name}")
        logger.info(f"{'='*40}")
        
        try:
            success = test_func()
            results[test_name] = success
            
            status = "通过" if success else "失败"
            logger.info(f"{test_name} - {status}")
            
        except Exception as e:
            logger.error(f"{test_name} - 异常: {e}")
            results[test_name] = False
    
    # 输出总结
    logger.info(f"\n{'='*60}")
    logger.info("测试总结")
    logger.info(f"{'='*60}")
    
    total_tests = len(tests)
    passed_tests = sum(1 for success in results.values() if success)
    
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {total_tests - passed_tests}")
    logger.info(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    # 详细结果
    logger.info(f"\n详细结果:")
    for test_name, success in results.items():
        status = "✓" if success else "✗"
        logger.info(f"  {status} {test_name}")
    
    if passed_tests == total_tests:
        logger.info("\n🎉 所有测试通过！PyTorch API修复成功！")
    else:
        logger.warning(f"\n⚠️  {total_tests - passed_tests}个测试失败，请检查相关问题")
    
    return results


if __name__ == "__main__":
    results = run_all_tests()
    
    # 如果所有测试都通过，返回0；否则返回1
    all_passed = all(results.values())
    sys.exit(0 if all_passed else 1)
