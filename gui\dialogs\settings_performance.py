#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能设置页面

该模块提供应用程序性能设置的用户界面。
"""

import os
import platform
import winreg
import subprocess
from typing import Dict, Any

from PyQt6.QtWidgets import <PERSON>VBoxLayout, QHBoxLayout, QFormLayout

from .settings_base import BaseSettingsWidget


class PerformanceSettingsWidget(BaseSettingsWidget):
    """性能设置组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def get_cpu_info(self) -> str:
        """获取CPU信息"""
        try:
            # 直接从Windows注册表获取CPU信息（更可靠）
            if platform.system() == "Windows":
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                       r"HARDWARE\DESCRIPTION\System\CentralProcessor\0")
                    cpu_name = winreg.QueryValueEx(key, "ProcessorNameString")[0]
                    winreg.<PERSON><PERSON><PERSON>(key)
                except Exception as e:
                    self.logger.warning(f"从注册表获取CPU信息失败: {e}")
                    cpu_name = platform.processor()
            else:
                # Linux/Mac系统尝试从/proc/cpuinfo获取
                cpu_name = ""
                if os.path.exists('/proc/cpuinfo'):
                    with open('/proc/cpuinfo', 'r') as f:
                        for line in f:
                            if line.startswith('model name'):
                                cpu_name = line.split(':')[1].strip()
                                break
                
                # 如果上面的方法失败，尝试其他方法
                if not cpu_name or cpu_name.strip() == "":
                    cpu_name = platform.processor()
            
            # 如果仍然没有获取到，使用machine信息
            if not cpu_name or cpu_name.strip() == "":
                cpu_name = platform.machine()
            
            cpu_count = os.cpu_count() or 1
            
            if cpu_name and cpu_name.strip():
                return f"{cpu_name.strip()} ({cpu_count} 核心)"
            else:
                return f"未知CPU ({cpu_count} 核心)"
                
        except Exception as e:
            self.logger.warning(f"获取CPU信息失败: {e}")
            return "CPU信息获取失败"
    
    def get_gpu_info(self) -> str:
        """获取GPU信息"""
        try:
            # 尝试检测GPU
            gpu_info = "未检测到GPU"
            
            # 检查是否有CUDA可用
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_name = torch.cuda.get_device_name(0)
                    gpu_info = f"{gpu_name} (CUDA)"
                else:
                    gpu_info = "无CUDA支持的GPU"
            except ImportError:
                # 如果没有PyTorch，尝试其他方法
                try:
                    result = subprocess.run(['nvidia-smi', '--query-gpu=name', '--format=csv,noheader,nounits'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0 and result.stdout.strip():
                        gpu_info = result.stdout.strip().split('\n')[0]
                except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
                    pass
            
            return gpu_info
            
        except Exception as e:
            self.logger.warning(f"获取GPU信息失败: {e}")
            return "GPU信息获取失败"
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # 系统信息
        system_group = self.widget_factory.create_group_box(title="系统信息", layout_type="form")
        system_layout = system_group.layout()
        
        # CPU信息
        cpu_info = self.get_cpu_info()
        cpu_label = self.widget_factory.create_label(cpu_info)
        cpu_label.setStyleSheet("color: #666; font-size: 9pt;")
        system_layout.addRow("CPU:", cpu_label)
        
        # GPU信息
        gpu_info = self.get_gpu_info()
        gpu_label = self.widget_factory.create_label(gpu_info)
        gpu_label.setStyleSheet("color: #666; font-size: 9pt;")
        system_layout.addRow("GPU:", gpu_label)
        
        layout.addWidget(system_group)
        
        # GPU设置
        gpu_group = self.widget_factory.create_group_box(title="GPU设置", layout_type="vbox")
        gpu_layout = gpu_group.layout()
        
        self.use_gpu_cb = self.widget_factory.create_checkbox(
            "启用GPU加速", checked=True
        )
        gpu_layout.addWidget(self.use_gpu_cb)
        
        layout.addWidget(gpu_group)
        
        # 线程设置
        thread_group = self.widget_factory.create_group_box(title="线程设置", layout_type="form")
        thread_layout = thread_group.layout()
        
        # 获取系统CPU核心数作为默认值
        default_threads = min(os.cpu_count() or 4, 16)
        self.max_threads_spin = self.widget_factory.create_spin_box(
            minimum=1, maximum=16, value=default_threads
        )
        thread_layout.addRow("最大线程数:", self.max_threads_spin)
        
        layout.addWidget(thread_group)
        
        # 缓存设置
        cache_group = self.widget_factory.create_group_box(title="缓存设置", layout_type="form")
        cache_layout = cache_group.layout()
        
        self.cache_size_spin = self.widget_factory.create_spin_box(
            minimum=64, maximum=4096, value=512, suffix=" MB"
        )
        cache_layout.addRow("缓存大小:", self.cache_size_spin)
        
        layout.addWidget(cache_group)
        
        # 预加载设置
        preload_group = self.widget_factory.create_group_box(title="预加载设置", layout_type="vbox")
        preload_layout = preload_group.layout()
        
        self.preload_thumbnails_cb = self.widget_factory.create_checkbox(
            "预加载缩略图", checked=True
        )
        preload_layout.addWidget(self.preload_thumbnails_cb)
        
        layout.addWidget(preload_group)
        
        layout.addStretch()
    
    def get_settings(self) -> Dict[str, Any]:
        """获取设置"""
        return {
            "use_gpu": self.use_gpu_cb.isChecked(),
            "max_threads": self.max_threads_spin.value(),
            "cache_size": self.cache_size_spin.value(),
            "preload_thumbnails": self.preload_thumbnails_cb.isChecked()
        }
    
    def set_settings(self, settings: Dict[str, Any]):
        """设置设置"""
        self.use_gpu_cb.setChecked(settings.get("use_gpu", True))
        self.max_threads_spin.setValue(settings.get("max_threads", 4))
        self.cache_size_spin.setValue(settings.get("cache_size", 512))
        self.preload_thumbnails_cb.setChecked(settings.get("preload_thumbnails", True))