#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型模块

该模块定义数据库表结构和ORM映射。
"""

import json
import pickle
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List, Union
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class ImageFeature:
    """图像特征模型"""
    id: Optional[int] = None
    image_id: int = 0
    feature_type: str = ""  # deep, color, texture, shape
    feature_data: Optional[bytes] = None
    model_name: Optional[str] = None
    feature_dim: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        data = asdict(self)
        
        # 处理datetime字段
        for field in ['created_at', 'updated_at']:
            if data[field] and isinstance(data[field], datetime):
                data[field] = data[field].isoformat()
        
        # 处理bytes字段
        if data['feature_data']:
            data['feature_data'] = None  # 不包含在字典中
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ImageFeature':
        """从字典创建实例
        
        Args:
            data: 字典数据
            
        Returns:
            ImageFeature: 实例
        """
        # 处理datetime字段
        for field in ['created_at', 'updated_at']:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.fromisoformat(data[field])
        
        return cls(**data)

@dataclass
class FabricImage:
    """布料图片模型"""
    id: Optional[int] = None
    file_path: str = ""
    file_name: str = ""
    file_size: int = 0
    width: int = 0
    height: int = 0
    channels: int = 3  # 默认为3通道（RGB）
    format: str = ""
    hash_md5: str = ""
    features: Optional[bytes] = None
    thumbnail_path: Optional[str] = None
    tags: Optional[str] = None
    category: Optional[str] = None
    color_info: Optional[str] = None
    texture_info: Optional[str] = None
    pattern_info: Optional[str] = None
    material_info: Optional[str] = None
    description: Optional[str] = None  # 添加描述字段
    metadata: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    indexed_at: Optional[datetime] = None
    is_active: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        data = asdict(self)
        
        # 处理datetime字段
        for field in ['created_at', 'updated_at', 'indexed_at']:
            if data[field] and isinstance(data[field], datetime):
                data[field] = data[field].isoformat()
        
        # 处理bytes字段
        if data['features']:
            data['features'] = None  # 不包含在字典中
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FabricImage':
        """从字典创建实例
        
        Args:
            data: 字典数据
            
        Returns:
            FabricImage: 实例
        """
        # 处理datetime字段
        for field in ['created_at', 'updated_at', 'indexed_at']:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.fromisoformat(data[field])
        
        return cls(**data)
    
    def get_tags_list(self) -> List[str]:
        """获取标签列表
        
        Returns:
            List[str]: 标签列表
        """
        if not self.tags:
            return []
        
        try:
            return json.loads(self.tags)
        except (json.JSONDecodeError, TypeError):
            return self.tags.split(',') if self.tags else []
    
    def set_tags_list(self, tags: List[str]):
        """设置标签列表
        
        Args:
            tags: 标签列表
        """
        self.tags = json.dumps(tags, ensure_ascii=False)
    
    def get_color_info_dict(self) -> Dict[str, Any]:
        """获取颜色信息字典
        
        Returns:
            Dict[str, Any]: 颜色信息
        """
        if not self.color_info:
            return {}
        
        try:
            return json.loads(self.color_info)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_color_info_dict(self, color_info: Dict[str, Any]):
        """设置颜色信息字典
        
        Args:
            color_info: 颜色信息
        """
        self.color_info = json.dumps(color_info, ensure_ascii=False)
    
    def get_texture_info_dict(self) -> Dict[str, Any]:
        """获取纹理信息字典
        
        Returns:
            Dict[str, Any]: 纹理信息
        """
        if not self.texture_info:
            return {}
        
        try:
            return json.loads(self.texture_info)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_texture_info_dict(self, texture_info: Dict[str, Any]):
        """设置纹理信息字典
        
        Args:
            texture_info: 纹理信息
        """
        self.texture_info = json.dumps(texture_info, ensure_ascii=False)
    
    def get_pattern_info_dict(self) -> Dict[str, Any]:
        """获取图案信息字典
        
        Returns:
            Dict[str, Any]: 图案信息
        """
        if not self.pattern_info:
            return {}
        
        try:
            return json.loads(self.pattern_info)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_pattern_info_dict(self, pattern_info: Dict[str, Any]):
        """设置图案信息字典
        
        Args:
            pattern_info: 图案信息
        """
        self.pattern_info = json.dumps(pattern_info, ensure_ascii=False)
    
    def get_material_info_dict(self) -> Dict[str, Any]:
        """获取材质信息字典
        
        Returns:
            Dict[str, Any]: 材质信息
        """
        if not self.material_info:
            return {}
        
        try:
            return json.loads(self.material_info)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_material_info_dict(self, material_info: Dict[str, Any]):
        """设置材质信息字典
        
        Args:
            material_info: 材质信息
        """
        self.material_info = json.dumps(material_info, ensure_ascii=False)
    
    def get_features_array(self):
        """获取特征数组
        
        Returns:
            numpy.ndarray: 特征数组
        """
        if not self.features:
            return None
        
        try:
            return pickle.loads(self.features)
        except (pickle.PickleError, TypeError):
            return None
    
    def set_features_array(self, features):
        """设置特征数组
        
        Args:
            features: 特征数组
        """
        if features is not None:
            self.features = pickle.dumps(features)
        else:
            self.features = None
    
    def get_relative_path(self, base_path: Union[str, Path]) -> str:
        """获取相对路径
        
        Args:
            base_path: 基础路径
            
        Returns:
            str: 相对路径
        """
        try:
            return str(Path(self.file_path).relative_to(Path(base_path)))
        except ValueError:
            return self.file_path
    
    def exists(self) -> bool:
        """检查文件是否存在
        
        Returns:
            bool: 文件是否存在
        """
        return Path(self.file_path).exists()


@dataclass
class SearchHistory:
    """搜索历史模型"""
    id: Optional[int] = None
    user_id: str = ""
    session_id: Optional[str] = None
    search_type: Optional[str] = None
    query_text: Optional[str] = None
    query_image_id: Optional[int] = None
    query_image_path: Optional[str] = None
    query_features: Optional[bytes] = None
    search_params: Optional[str] = None
    query_data: Optional[str] = None  # 添加query_data字段
    filters: Optional[str] = None  # 添加filters字段
    result_count: int = 0
    execution_time: float = 0.0
    search_time: float = 0.0  # 添加search_time字段
    metadata: Optional[str] = None  # 添加metadata字段
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        data = asdict(self)
        
        # 处理datetime字段
        if data['created_at'] and isinstance(data['created_at'], datetime):
            data['created_at'] = data['created_at'].isoformat()
        
        # 处理bytes字段
        if data['query_features']:
            data['query_features'] = None  # 不包含在字典中
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchHistory':
        """从字典创建实例
        
        Args:
            data: 字典数据
            
        Returns:
            SearchHistory: 实例
        """
        # 处理datetime字段
        if 'created_at' in data and data['created_at']:
            if isinstance(data['created_at'], str):
                data['created_at'] = datetime.fromisoformat(data['created_at'])
        
        return cls(**data)
    
    def get_search_params_dict(self) -> Dict[str, Any]:
        """获取搜索参数字典
        
        Returns:
            Dict[str, Any]: 搜索参数
        """
        if not self.search_params:
            return {}
        
        try:
            return json.loads(self.search_params)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_search_params_dict(self, params: Dict[str, Any]):
        """设置搜索参数字典
        
        Args:
            params: 搜索参数
        """
        self.search_params = json.dumps(params, ensure_ascii=False)
    
    def get_query_features_array(self):
        """获取查询特征数组
        
        Returns:
            numpy.ndarray: 特征数组
        """
        if not self.query_features:
            return None
        
        try:
            return pickle.loads(self.query_features)
        except (pickle.PickleError, TypeError):
            return None
    
    def set_query_features_array(self, features):
        """设置查询特征数组
        
        Args:
            features: 特征数组
        """
        if features is not None:
            self.query_features = pickle.dumps(features)
        else:
            self.query_features = None


@dataclass
class UserPreference:
    """用户偏好模型"""
    id: Optional[int] = None
    key: str = ""
    value: str = ""
    data_type: str = "string"
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        data = asdict(self)
        
        # 处理datetime字段
        for field in ['created_at', 'updated_at']:
            if data[field] and isinstance(data[field], datetime):
                data[field] = data[field].isoformat()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserPreference':
        """从字典创建实例
        
        Args:
            data: 字典数据
            
        Returns:
            UserPreference: 实例
        """
        # 处理datetime字段
        for field in ['created_at', 'updated_at']:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.fromisoformat(data[field])
        
        return cls(**data)
    
    def get_typed_value(self) -> Any:
        """获取类型化的值
        
        Returns:
            Any: 类型化的值
        """
        if not self.value:
            return None
        
        try:
            if self.data_type == 'int':
                return int(self.value)
            elif self.data_type == 'float':
                return float(self.value)
            elif self.data_type == 'bool':
                return self.value.lower() in ('true', '1', 'yes', 'on')
            elif self.data_type == 'json':
                return json.loads(self.value)
            elif self.data_type == 'list':
                return json.loads(self.value) if self.value.startswith('[') else self.value.split(',')
            else:
                return self.value
        except (ValueError, json.JSONDecodeError, TypeError):
            return self.value
    
    def set_typed_value(self, value: Any):
        """设置类型化的值
        
        Args:
            value: 要设置的值
        """
        if value is None:
            self.value = ""
            return
        
        if isinstance(value, (dict, list)):
            self.value = json.dumps(value, ensure_ascii=False)
            self.data_type = 'json' if isinstance(value, dict) else 'list'
        elif isinstance(value, bool):
            self.value = str(value).lower()
            self.data_type = 'bool'
        elif isinstance(value, int):
            self.value = str(value)
            self.data_type = 'int'
        elif isinstance(value, float):
            self.value = str(value)
            self.data_type = 'float'
        else:
            self.value = str(value)
            self.data_type = 'string'


def create_tables(db_manager):
    """创建数据库表
    
    Args:
        db_manager: 数据库管理器
    """
    logger = logging.getLogger('create_tables')
    
    try:
        # 调用数据库管理器的初始化方法，它会自动处理表创建
        db_manager._initialize_database()
        logger.info("数据库表创建完成")
        
    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        raise


def drop_tables(db_manager):
    """删除数据库表
    
    Args:
        db_manager: 数据库管理器
    """
    logger = logging.getLogger('drop_tables')
    
    try:
        tables = ['fabric_images', 'search_history', 'user_preferences']
        
        with db_manager.transaction() as conn:
            cursor = conn.cursor()
            
            for table in tables:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                logger.info(f"表 {table} 已删除")
            
            cursor.close()
        
        logger.info("所有数据库表已删除")
        
    except Exception as e:
        logger.error(f"删除数据库表失败: {e}")
        raise


def get_model_fields(model_class) -> List[str]:
    """获取模型字段列表
    
    Args:
        model_class: 模型类
        
    Returns:
        List[str]: 字段列表
    """
    if hasattr(model_class, '__dataclass_fields__'):
        return list(model_class.__dataclass_fields__.keys())
    return []


def validate_model_data(model_class, data: Dict[str, Any]) -> Dict[str, Any]:
    """验证模型数据
    
    Args:
        model_class: 模型类
        data: 数据字典
        
    Returns:
        Dict[str, Any]: 验证后的数据
    """
    valid_fields = get_model_fields(model_class)
    validated_data = {}
    
    for field in valid_fields:
        if field in data:
            validated_data[field] = data[field]
    
    return validated_data


def serialize_model(model) -> Dict[str, Any]:
    """序列化模型
    
    Args:
        model: 模型实例
        
    Returns:
        Dict[str, Any]: 序列化后的数据
    """
    if hasattr(model, 'to_dict'):
        return model.to_dict()
    elif hasattr(model, '__dict__'):
        return model.__dict__.copy()
    else:
        return {}


def deserialize_model(model_class, data: Dict[str, Any]):
    """反序列化模型
    
    Args:
        model_class: 模型类
        data: 数据字典
        
    Returns:
        模型实例
    """
    if hasattr(model_class, 'from_dict'):
        return model_class.from_dict(data)
    else:
        validated_data = validate_model_data(model_class, data)
        return model_class(**validated_data)