#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API数据模式验证模块

定义API请求和响应的数据模式，提供数据验证功能。
"""

from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
import re

from utils.logger_mixin import LoggerMixin
from .error_handlers import ValidationError


class SchemaValidator(LoggerMixin):
    """数据模式验证器"""
    
    def __init__(self):
        super().__init__()
    
    def validate(self, data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """验证数据
        
        Args:
            data: 要验证的数据
            schema: 验证模式
            
        Returns:
            Dict[str, Any]: 验证后的数据
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        try:
            return self._validate_object(data, schema)
        except ValidationError:
            raise
        except Exception as e:
            raise ValidationError(f"数据验证失败: {str(e)}")
    
    def _validate_object(self, data: Any, schema: Dict[str, Any]) -> Any:
        """验证对象"""
        if not isinstance(data, dict):
            raise ValidationError("数据必须是对象类型")
        
        result = {}
        
        # 检查必需字段
        required_fields = schema.get('required', [])
        for field_name in required_fields:
            if field_name not in data:
                raise ValidationError(f"缺少必需字段: {field_name}", field_name)
        
        # 验证字段
        properties = schema.get('properties', {})
        for field_name, field_schema in properties.items():
            if field_name in data:
                result[field_name] = self._validate_field(data[field_name], field_schema, field_name)
            elif field_name in required_fields:
                raise ValidationError(f"缺少必需字段: {field_name}", field_name)
            elif 'default' in field_schema:
                result[field_name] = field_schema['default']
        
        # 检查额外字段
        if not schema.get('additionalProperties', True):
            extra_fields = set(data.keys()) - set(properties.keys())
            if extra_fields:
                raise ValidationError(f"不允许的额外字段: {', '.join(extra_fields)}")
        
        return result
    
    def _validate_field(self, value: Any, schema: Dict[str, Any], field_name: str) -> Any:
        """验证字段"""
        # 检查类型
        field_type = schema.get('type')
        if field_type:
            if not self._check_type(value, field_type):
                raise ValidationError(f"字段 {field_name} 类型错误，期望: {field_type}", field_name)
        
        # 字符串验证
        if field_type == 'string':
            return self._validate_string(value, schema, field_name)
        
        # 数字验证
        elif field_type in ['integer', 'number']:
            return self._validate_number(value, schema, field_name)
        
        # 数组验证
        elif field_type == 'array':
            return self._validate_array(value, schema, field_name)
        
        # 对象验证
        elif field_type == 'object':
            return self._validate_object(value, schema)
        
        # 布尔值验证
        elif field_type == 'boolean':
            return bool(value)
        
        return value
    
    def _check_type(self, value: Any, expected_type: str) -> bool:
        """检查类型"""
        type_mapping = {
            'string': str,
            'integer': int,
            'number': (int, float),
            'boolean': bool,
            'array': list,
            'object': dict,
            'null': type(None)
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type is None:
            return True
        
        return isinstance(value, expected_python_type)
    
    def _validate_string(self, value: str, schema: Dict[str, Any], field_name: str) -> str:
        """验证字符串"""
        # 长度检查
        min_length = schema.get('minLength')
        if min_length is not None and len(value) < min_length:
            raise ValidationError(f"字段 {field_name} 长度不能少于 {min_length} 个字符", field_name)
        
        max_length = schema.get('maxLength')
        if max_length is not None and len(value) > max_length:
            raise ValidationError(f"字段 {field_name} 长度不能超过 {max_length} 个字符", field_name)
        
        # 模式检查
        pattern = schema.get('pattern')
        if pattern and not re.match(pattern, value):
            raise ValidationError(f"字段 {field_name} 格式不正确", field_name)
        
        # 枚举检查
        enum_values = schema.get('enum')
        if enum_values and value not in enum_values:
            raise ValidationError(f"字段 {field_name} 值必须是: {', '.join(enum_values)}", field_name)
        
        return value
    
    def _validate_number(self, value: Union[int, float], schema: Dict[str, Any], field_name: str) -> Union[int, float]:
        """验证数字"""
        # 最小值检查
        minimum = schema.get('minimum')
        if minimum is not None and value < minimum:
            raise ValidationError(f"字段 {field_name} 不能小于 {minimum}", field_name)
        
        # 最大值检查
        maximum = schema.get('maximum')
        if maximum is not None and value > maximum:
            raise ValidationError(f"字段 {field_name} 不能大于 {maximum}", field_name)
        
        # 排他性最小值检查
        exclusive_minimum = schema.get('exclusiveMinimum')
        if exclusive_minimum is not None and value <= exclusive_minimum:
            raise ValidationError(f"字段 {field_name} 必须大于 {exclusive_minimum}", field_name)
        
        # 排他性最大值检查
        exclusive_maximum = schema.get('exclusiveMaximum')
        if exclusive_maximum is not None and value >= exclusive_maximum:
            raise ValidationError(f"字段 {field_name} 必须小于 {exclusive_maximum}", field_name)
        
        # 倍数检查
        multiple_of = schema.get('multipleOf')
        if multiple_of is not None and value % multiple_of != 0:
            raise ValidationError(f"字段 {field_name} 必须是 {multiple_of} 的倍数", field_name)
        
        return value
    
    def _validate_array(self, value: List[Any], schema: Dict[str, Any], field_name: str) -> List[Any]:
        """验证数组"""
        # 长度检查
        min_items = schema.get('minItems')
        if min_items is not None and len(value) < min_items:
            raise ValidationError(f"字段 {field_name} 至少需要 {min_items} 个元素", field_name)
        
        max_items = schema.get('maxItems')
        if max_items is not None and len(value) > max_items:
            raise ValidationError(f"字段 {field_name} 最多允许 {max_items} 个元素", field_name)
        
        # 唯一性检查
        if schema.get('uniqueItems', False):
            if len(value) != len(set(str(item) for item in value)):
                raise ValidationError(f"字段 {field_name} 中的元素必须唯一", field_name)
        
        # 元素验证
        items_schema = schema.get('items')
        if items_schema:
            result = []
            for i, item in enumerate(value):
                try:
                    validated_item = self._validate_field(item, items_schema, f"{field_name}[{i}]")
                    result.append(validated_item)
                except ValidationError as e:
                    raise ValidationError(f"字段 {field_name}[{i}] {e.message}", field_name)
            return result
        
        return value


# 全局验证器实例
validator = SchemaValidator()


# 通用模式定义
PAGINATION_SCHEMA = {
    'type': 'object',
    'properties': {
        'page': {
            'type': 'integer',
            'minimum': 1,
            'default': 1
        },
        'per_page': {
            'type': 'integer',
            'minimum': 1,
            'maximum': 100,
            'default': 20
        }
    }
}

FILE_UPLOAD_SCHEMA = {
    'type': 'object',
    'properties': {
        'filename': {
            'type': 'string',
            'minLength': 1,
            'maxLength': 255
        },
        'content_type': {
            'type': 'string',
            'pattern': r'^image/'
        },
        'size': {
            'type': 'integer',
            'minimum': 1
        }
    },
    'required': ['filename']
}

# 图像搜索相关模式
IMAGE_SEARCH_REQUEST_SCHEMA = {
    'type': 'object',
    'properties': {
        'query_type': {
            'type': 'string',
            'enum': ['image', 'text', 'features'],
            'default': 'image'
        },
        'query_image': {
            'type': 'string',
            'description': 'Base64编码的图像或图像URL'
        },
        'query_text': {
            'type': 'string',
            'minLength': 1,
            'maxLength': 1000
        },
        'query_features': {
            'type': 'array',
            'items': {
                'type': 'number'
            },
            'minItems': 1
        },
        'top_k': {
            'type': 'integer',
            'minimum': 1,
            'maximum': 1000,
            'default': 50
        },
        'similarity_threshold': {
            'type': 'number',
            'minimum': 0.0,
            'maximum': 1.0,
            'default': 0.0
        },
        'filters': {
            'type': 'object',
            'properties': {
                'categories': {
                    'type': 'array',
                    'items': {
                        'type': 'string'
                    }
                },
                'tags': {
                    'type': 'array',
                    'items': {
                        'type': 'string'
                    }
                },
                'date_range': {
                    'type': 'object',
                    'properties': {
                        'start': {
                            'type': 'string',
                            'pattern': r'^\d{4}-\d{2}-\d{2}$'
                        },
                        'end': {
                            'type': 'string',
                            'pattern': r'^\d{4}-\d{2}-\d{2}$'
                        }
                    }
                },
                'size_range': {
                    'type': 'object',
                    'properties': {
                        'min_width': {
                            'type': 'integer',
                            'minimum': 1
                        },
                        'max_width': {
                            'type': 'integer',
                            'minimum': 1
                        },
                        'min_height': {
                            'type': 'integer',
                            'minimum': 1
                        },
                        'max_height': {
                            'type': 'integer',
                            'minimum': 1
                        }
                    }
                }
            }
        },
        'include_metadata': {
            'type': 'boolean',
            'default': True
        },
        'include_features': {
            'type': 'boolean',
            'default': False
        }
    },
    'anyOf': [
        {'required': ['query_image']},
        {'required': ['query_text']},
        {'required': ['query_features']}
    ]
}

IMAGE_SEARCH_RESPONSE_SCHEMA = {
    'type': 'object',
    'properties': {
        'success': {
            'type': 'boolean'
        },
        'data': {
            'type': 'object',
            'properties': {
                'results': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'id': {
                                'type': 'integer'
                            },
                            'image_path': {
                                'type': 'string'
                            },
                            'similarity_score': {
                                'type': 'number',
                                'minimum': 0.0,
                                'maximum': 1.0
                            },
                            'rank': {
                                'type': 'integer',
                                'minimum': 1
                            },
                            'metadata': {
                                'type': 'object'
                            },
                            'features': {
                                'type': 'array',
                                'items': {
                                    'type': 'number'
                                }
                            }
                        },
                        'required': ['id', 'image_path', 'similarity_score', 'rank']
                    }
                },
                'query_info': {
                    'type': 'object',
                    'properties': {
                        'query_type': {
                            'type': 'string'
                        },
                        'processing_time': {
                            'type': 'number'
                        },
                        'total_candidates': {
                            'type': 'integer'
                        }
                    }
                }
            },
            'required': ['results', 'query_info']
        }
    },
    'required': ['success', 'data']
}

# 批处理相关模式
BATCH_PROCESSING_REQUEST_SCHEMA = {
    'type': 'object',
    'properties': {
        'source_path': {
            'type': 'string',
            'minLength': 1
        },
        'recursive': {
            'type': 'boolean',
            'default': True
        },
        'batch_size': {
            'type': 'integer',
            'minimum': 1,
            'maximum': 1000,
            'default': 32
        },
        'max_workers': {
            'type': 'integer',
            'minimum': 1,
            'maximum': 16,
            'default': 4
        },
        'skip_existing': {
            'type': 'boolean',
            'default': True
        },
        'update_existing': {
            'type': 'boolean',
            'default': False
        },
        'supported_formats': {
            'type': 'array',
            'items': {
                'type': 'string',
                'pattern': r'^\.[a-zA-Z0-9]+$'
            },
            'default': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        },
        'filters': {
            'type': 'object',
            'properties': {
                'min_size': {
                    'type': 'integer',
                    'minimum': 1
                },
                'max_size': {
                    'type': 'integer',
                    'minimum': 1
                },
                'min_width': {
                    'type': 'integer',
                    'minimum': 1
                },
                'max_width': {
                    'type': 'integer',
                    'minimum': 1
                },
                'min_height': {
                    'type': 'integer',
                    'minimum': 1
                },
                'max_height': {
                    'type': 'integer',
                    'minimum': 1
                }
            }
        },
        'metadata': {
            'type': 'object',
            'additionalProperties': True
        }
    },
    'required': ['source_path']
}

BATCH_PROCESSING_RESPONSE_SCHEMA = {
    'type': 'object',
    'properties': {
        'success': {
            'type': 'boolean'
        },
        'data': {
            'type': 'object',
            'properties': {
                'job_id': {
                    'type': 'string'
                },
                'status': {
                    'type': 'string',
                    'enum': ['pending', 'running', 'completed', 'failed', 'cancelled']
                },
                'progress': {
                    'type': 'object',
                    'properties': {
                        'total': {
                            'type': 'integer'
                        },
                        'processed': {
                            'type': 'integer'
                        },
                        'successful': {
                            'type': 'integer'
                        },
                        'failed': {
                            'type': 'integer'
                        },
                        'percentage': {
                            'type': 'number',
                            'minimum': 0.0,
                            'maximum': 100.0
                        }
                    }
                },
                'timing': {
                    'type': 'object',
                    'properties': {
                        'start_time': {
                            'type': 'string'
                        },
                        'end_time': {
                            'type': 'string'
                        },
                        'duration': {
                            'type': 'number'
                        }
                    }
                },
                'errors': {
                    'type': 'array',
                    'items': {
                        'type': 'object',
                        'properties': {
                            'file_path': {
                                'type': 'string'
                            },
                            'error_message': {
                                'type': 'string'
                            },
                            'error_type': {
                                'type': 'string'
                            }
                        }
                    }
                }
            },
            'required': ['job_id', 'status']
        }
    },
    'required': ['success', 'data']
}

# 配置相关模式
CONFIG_UPDATE_REQUEST_SCHEMA = {
    'type': 'object',
    'properties': {
        'database': {
            'type': 'object',
            'properties': {
                'host': {
                    'type': 'string',
                    'minLength': 1
                },
                'port': {
                    'type': 'integer',
                    'minimum': 1,
                    'maximum': 65535
                },
                'database': {
                    'type': 'string',
                    'minLength': 1
                },
                'username': {
                    'type': 'string',
                    'minLength': 1
                },
                'password': {
                    'type': 'string'
                }
            }
        },
        'feature_extraction': {
            'type': 'object',
            'properties': {
                'model_name': {
                    'type': 'string',
                    'enum': ['resnet50', 'resnet101', 'vgg16', 'vgg19', 'efficientnet']
                },
                'device': {
                    'type': 'string',
                    'enum': ['auto', 'cpu', 'cuda']
                },
                'batch_size': {
                    'type': 'integer',
                    'minimum': 1,
                    'maximum': 256
                }
            }
        },
        'search': {
            'type': 'object',
            'properties': {
                'index_type': {
                    'type': 'string',
                    'enum': ['faiss', 'cosine', 'euclidean']
                },
                'similarity_metric': {
                    'type': 'string',
                    'enum': ['cosine', 'euclidean', 'manhattan']
                },
                'top_k': {
                    'type': 'integer',
                    'minimum': 1,
                    'maximum': 1000
                }
            }
        },
        'logging': {
            'type': 'object',
            'properties': {
                'level': {
                    'type': 'string',
                    'enum': ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
                },
                'log_to_file': {
                    'type': 'boolean'
                },
                'log_to_console': {
                    'type': 'boolean'
                }
            }
        }
    },
    'additionalProperties': False
}

# 导出结果相关模式
EXPORT_REQUEST_SCHEMA = {
    'type': 'object',
    'properties': {
        'format': {
            'type': 'string',
            'enum': ['json', 'csv', 'excel', 'html', 'xml']
        },
        'output_path': {
            'type': 'string',
            'minLength': 1
        },
        'include_images': {
            'type': 'boolean',
            'default': False
        },
        'include_features': {
            'type': 'boolean',
            'default': False
        },
        'include_metadata': {
            'type': 'boolean',
            'default': True
        },
        'max_results': {
            'type': 'integer',
            'minimum': 1,
            'maximum': 10000
        },
        'image_size': {
            'type': 'array',
            'items': {
                'type': 'integer',
                'minimum': 1
            },
            'minItems': 2,
            'maxItems': 2
        },
        'compression_quality': {
            'type': 'integer',
            'minimum': 1,
            'maximum': 100,
            'default': 85
        }
    },
    'required': ['format', 'output_path']
}


def validate_request(schema: Dict[str, Any]):
    """请求验证装饰器
    
    Args:
        schema: 验证模式
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        def wrapper(*args, **kwargs):
            from flask import request, g
            
            # 获取请求数据
            if request.is_json:
                data = request.get_json()
            elif request.form:
                data = dict(request.form)
            elif request.args:
                data = dict(request.args)
            else:
                data = {}
            
            # 验证数据
            try:
                validated_data = validator.validate(data, schema)
                g.validated_data = validated_data
            except ValidationError:
                raise
            except Exception as e:
                raise ValidationError(f"数据验证失败: {str(e)}")
            
            return f(*args, **kwargs)
        
        wrapper.__name__ = f.__name__
        wrapper.__doc__ = f.__doc__
        return wrapper
    
    return decorator


def validate_response(schema: Dict[str, Any]):
    """响应验证装饰器（开发模式下使用）
    
    Args:
        schema: 验证模式
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        def wrapper(*args, **kwargs):
            from config.config_manager import get_config
            
            result = f(*args, **kwargs)
            
            # 只在调试模式下验证响应
            config = get_config()
            if config.debug:
                try:
                    if isinstance(result, tuple):
                        response_data, status_code = result
                    else:
                        response_data = result
                    
                    validator.validate(response_data, schema)
                except Exception as e:
                    # 记录警告但不影响响应
                    import logging
                    logging.warning(f"响应数据验证失败: {str(e)}")
            
            return result
        
        wrapper.__name__ = f.__name__
        wrapper.__doc__ = f.__doc__
        return wrapper
    
    return decorator