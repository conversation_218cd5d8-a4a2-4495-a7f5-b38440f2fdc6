"""特征处理模块

处理特征提取和存储相关功能。
"""

import time
from typing import Dict, Any, List, Optional
from pathlib import Path

from .data_models import ProcessingResult
from ..utils import validate_image_path, log_performance


class FeatureProcessor:
    """特征处理器
    
    负责特征提取和存储相关功能。
    """
    
    def __init__(self, feature_extractor, feature_storage, batch_processor, 
                 config_manager, logger):
        """初始化特征处理器
        
        Args:
            feature_extractor: 特征提取器
            feature_storage: 特征存储
            batch_processor: 批处理器
            config_manager: 配置管理器
            logger: 日志记录器
        """
        self.feature_extractor = feature_extractor
        self.feature_storage = feature_storage
        self.batch_processor = batch_processor
        self.config_manager = config_manager
        self.logger = logger
        
    @log_performance
    def extract_and_store_features(self, image_paths: List[str], 
                                 performance_monitor,
                                 batch_size: Optional[int] = None) -> ProcessingResult:
        """提取并存储特征
        
        Args:
            image_paths: 图像路径列表
            performance_monitor: 性能监控器
            batch_size: 批处理大小
            
        Returns:
            ProcessingResult: 处理结果
        """
        if not self.feature_extractor:
            raise RuntimeError("特征提取器未初始化")
            
        with performance_monitor:
            # 使用统一的图像验证逻辑
            from utils.image_validation import validate_image_path
            
            # 验证输入并收集详细信息
            valid_paths = []
            invalid_paths = []
            
            for path in image_paths:
                if validate_image_path(path):
                    valid_paths.append(path)
                else:
                    invalid_paths.append(path)
                    self.logger.warning(f"无效的图像路径: {path}")
            
            # 记录验证结果
            if invalid_paths:
                self.logger.info(f"跳过 {len(invalid_paths)} 个无效文件，处理 {len(valid_paths)} 个有效文件")
                    
            if not valid_paths:
                return ProcessingResult(
                    success=False,
                    processed=0,
                    failed=len(image_paths),
                    error=f'没有有效的图像路径。无效文件数: {len(invalid_paths)}'
                )
                
            # 使用批处理器处理
            if self.batch_processor and len(valid_paths) > 10:
                return self._process_with_batch_processor(valid_paths, batch_size)
            else:
                return self._process_sequentially(valid_paths)
                
    def _process_with_batch_processor(self, image_paths: List[str], 
                                    batch_size: Optional[int]) -> ProcessingResult:
        """使用批处理器处理
        
        Args:
            image_paths: 图像路径列表
            batch_size: 批处理大小
            
        Returns:
            ProcessingResult: 处理结果
        """
        task = self.batch_processor.create_task(
            name=f"feature_extraction_{len(image_paths)}",
            image_paths=image_paths,
            description=f"提取 {len(image_paths)} 个图像的特征",
            batch_size=batch_size or self.config_manager.performance_config.io.batch_size
        )
        
        if self.batch_processor.submit_task(task):
            # 等待任务完成（简化版本，实际应该异步处理）
            import time
            while task.status.value in ['pending', 'running']:
                time.sleep(1)
                
            return ProcessingResult(
                success=task.status.value == 'completed',
                processed=task.successful_items,
                failed=task.failed_items,
                task_id=task.task_id
            )
        else:
            return ProcessingResult(
                success=False,
                processed=0,
                failed=len(image_paths),
                error='批处理任务提交失败'
            )
            
    def _process_sequentially(self, image_paths: List[str]) -> ProcessingResult:
        """顺序处理
        
        Args:
            image_paths: 图像路径列表
            
        Returns:
            ProcessingResult: 处理结果
        """
        processed = 0
        failed = 0
        skipped = 0
        
        for path in image_paths:
            try:
                # 获取或创建图像记录
                fabric_repository = self.feature_storage.fabric_repository
                
                # 首先检查路径是否已存在
                if fabric_repository.exists_by_path(path):
                    self.logger.debug(f"图像路径已存在于数据库: {path}")
                    fabric_image = fabric_repository.get_by_path(path)
                    # 使用数据库ID作为image_id
                    image_id = fabric_image.id
                    
                    # 检查是否已存在特征数据
                    existing_feature = fabric_repository.get_feature_by_image_id(image_id, 'deep_features')
                    if existing_feature:
                        self.logger.info(f"图像已存在特征数据，跳过处理: {path}")
                        skipped += 1
                        processed += 1  # 计为已处理
                        continue
                    else:
                        self.logger.debug(f"图像存在但没有特征数据，将进行处理: {path}")
                else:
                    self.logger.debug(f"图像路径不存在于数据库，创建新记录: {path}")
                    # 创建新记录
                    fabric_image = fabric_repository.create_fabric_image(path)
                    if not fabric_image:
                        self.logger.error(f"无法创建图像记录: {path}")
                        failed += 1
                        continue
                    
                    # 使用数据库ID作为image_id
                    image_id = fabric_image.id
                
                # 提取特征（包括所有类型的特征）
                result = self.feature_extractor.extract_features(path, extract_traditional=True)
                if result and result.success:
                    try:
                        # 准备元数据
                        metadata = {
                            'file_path': path,
                            'model_name': getattr(result, 'model_name', 'unknown'),
                            'feature_type': getattr(result, 'feature_type', 'deep_features')
                        }

                        # 检查是否有特征向量字典（包含多种特征类型）
                        if hasattr(result, 'feature_vectors') and result.feature_vectors:
                            # 存储多种类型的特征
                            if self.feature_storage.store_multiple_features(image_id, result.feature_vectors, metadata):
                                processed += 1
                                feature_types = list(result.feature_vectors.keys())
                                self.logger.info(f"成功提取并存储多种特征: {path}, 特征类型: {feature_types}")
                            else:
                                failed += 1
                                self.logger.error(f"多特征存储失败: {path}")
                        elif result.features is not None:
                            # 只有深度特征，使用原有方法
                            if self.feature_storage.store_features(image_id, result.features, metadata):
                                processed += 1
                                self.logger.info(f"成功提取并存储深度特征: {path}")
                            else:
                                failed += 1
                                self.logger.error(f"深度特征存储失败: {path}")
                        else:
                            failed += 1
                            self.logger.error(f"特征提取结果为空: {path}")
                    except Exception as e:
                        failed += 1
                        self.logger.error(f"处理图像记录失败: {path} - {e}")
                else:
                    failed += 1
                    error_msg = getattr(result, 'error_message', '未知错误') if result else "未知错误"
                    self.logger.error(f"特征提取失败: {path} - {error_msg}")
                    
            except Exception as e:
                failed += 1
                self.logger.error(f"处理图像失败: {path} - {e}")
        
        # 记录处理统计
        if skipped > 0:
            self.logger.info(f"处理完成 - 总计: {len(image_paths)}, 新处理: {processed - skipped}, 跳过: {skipped}, 失败: {failed}")
        
        return ProcessingResult(
            success=failed == 0,
            processed=processed,
            failed=failed
        )
        
    def change_model(self, model_name: str) -> bool:
        """更改模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 是否成功更改
        """
        try:
            # 更新模型配置
            model_config = self.config_manager.model_config
            model_config.model_name = model_name
            
            if not self.config_manager.save_model_config(model_config):
                return False
                
            # 重新初始化特征提取器需要在管理器层面处理
            self.logger.info(f"模型配置已更新为: {model_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"更改模型失败: {e}")
            return False
            
    def clear_cache(self) -> None:
        """清空缓存"""
        try:
            self.feature_storage.clear_cache()
            self.logger.info("特征存储缓存已清空")
            
        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")
            
    def cleanup(self) -> None:
        """清理资源"""
        try:
            if self.batch_processor:
                self.batch_processor.cleanup()
                
            # 注意：不在这里清理feature_storage，避免重复清理
            # feature_storage由FeatureManager统一管理和清理
                
            self.logger.info("特征处理器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"特征处理器资源清理失败: {e}")