#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索工具模块

该模块提供搜索引擎使用的各种辅助函数。
"""

import re
import jieba
import unicodedata
from typing import List, Dict, Any, Set, Optional
from utils.log_utils import LoggerMixin


class SearchUtils(LoggerMixin):
    """搜索工具类"""
    
    def __init__(self):
        """初始化搜索工具类"""
        super().__init__()
        # 初始化结巴分词
        jieba.initialize()
    
    def normalize_text(self, text: str) -> str:
        """文本标准化
        
        将文本转换为小写，移除多余空格，进行Unicode标准化。
        
        Args:
            text: 输入文本
            
        Returns:
            str: 标准化后的文本
        """
        if not text:
            return ""
        
        # 转换为小写
        text = text.lower()
        
        # Unicode标准化
        text = unicodedata.normalize('NFKC', text)
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def tokenize_text(self, text: str, use_jieba: bool = True) -> List[str]:
        """文本分词
        
        将文本分割为词语列表。
        
        Args:
            text: 输入文本
            use_jieba: 是否使用结巴分词（中文文本）
            
        Returns:
            List[str]: 分词结果
        """
        if not text:
            return []
        
        # 标准化文本
        text = self.normalize_text(text)
        
        # 检测是否包含中文字符
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', text))
        
        if has_chinese and use_jieba:
            # 使用结巴分词处理中文
            tokens = list(jieba.cut(text))
        else:
            # 简单分词（按空格和标点符号）
            tokens = re.findall(r'\w+', text)
        
        # 过滤空词
        tokens = [token for token in tokens if token.strip()]
        
        return tokens
    
    def calculate_text_overlap(self, text1: str, text2: str, 
                              use_tokenization: bool = True) -> float:
        """计算文本重叠度
        
        计算两个文本之间的重叠度（Jaccard相似度）。
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            use_tokenization: 是否使用分词
            
        Returns:
            float: 重叠度（0-1之间）
        """
        if not text1 or not text2:
            return 0.0
        
        # 标准化文本
        text1 = self.normalize_text(text1)
        text2 = self.normalize_text(text2)
        
        if use_tokenization:
            # 分词
            tokens1 = set(self.tokenize_text(text1))
            tokens2 = set(self.tokenize_text(text2))
            
            # 计算Jaccard相似度
            if not tokens1 or not tokens2:
                return 0.0
                
            intersection = len(tokens1.intersection(tokens2))
            union = len(tokens1.union(tokens2))
            
            return intersection / union if union > 0 else 0.0
        else:
            # 直接比较字符串
            if text1 == text2:
                return 1.0
            elif text1 in text2 or text2 in text1:
                # 一个是另一个的子串
                shorter = min(len(text1), len(text2))
                longer = max(len(text1), len(text2))
                return shorter / longer
            else:
                return 0.0
    
    def calculate_tag_overlap(self, tags1: List[str], tags2: List[str]) -> float:
        """计算标签重叠度
        
        计算两组标签之间的重叠度（Jaccard相似度）。
        
        Args:
            tags1: 第一组标签
            tags2: 第二组标签
            
        Returns:
            float: 重叠度（0-1之间）
        """
        if not tags1 or not tags2:
            return 0.0
        
        # 标准化标签
        tags1_set = {self.normalize_text(tag) for tag in tags1 if tag}
        tags2_set = {self.normalize_text(tag) for tag in tags2 if tag}
        
        # 计算Jaccard相似度
        if not tags1_set or not tags2_set:
            return 0.0
            
        intersection = len(tags1_set.intersection(tags2_set))
        union = len(tags1_set.union(tags2_set))
        
        return intersection / union if union > 0 else 0.0
    
    def extract_keywords(self, text: str, top_n: int = 5) -> List[str]:
        """提取关键词
        
        从文本中提取关键词。
        
        Args:
            text: 输入文本
            top_n: 返回的关键词数量
            
        Returns:
            List[str]: 关键词列表
        """
        if not text:
            return []
        
        try:
            # 标准化文本
            text = self.normalize_text(text)
            
            # 检测是否包含中文字符
            has_chinese = bool(re.search(r'[\u4e00-\u9fff]', text))
            
            if has_chinese:
                # 使用结巴提取关键词
                import jieba.analyse
                keywords = jieba.analyse.extract_tags(text, topK=top_n)
            else:
                # 简单提取（按词频）
                tokens = self.tokenize_text(text, use_jieba=False)
                word_freq = {}
                for token in tokens:
                    word_freq[token] = word_freq.get(token, 0) + 1
                
                # 按频率排序
                keywords = sorted(word_freq.keys(), key=lambda x: word_freq[x], reverse=True)[:top_n]
            
            return keywords
            
        except Exception as e:
            self.logger.error(f"提取关键词失败: {e}")
            return []
    
    def is_exact_match(self, query: str, target: str) -> bool:
        """检查是否完全匹配
        
        Args:
            query: 查询字符串
            target: 目标字符串
            
        Returns:
            bool: 是否完全匹配
        """
        if not query or not target:
            return False
        
        # 标准化文本
        query = self.normalize_text(query)
        target = self.normalize_text(target)
        
        return query == target
    
    def is_partial_match(self, query: str, target: str) -> bool:
        """检查是否部分匹配
        
        Args:
            query: 查询字符串
            target: 目标字符串
            
        Returns:
            bool: 是否部分匹配
        """
        if not query or not target:
            return False
        
        # 标准化文本
        query = self.normalize_text(query)
        target = self.normalize_text(target)
        
        return query in target
    
    def is_token_match(self, query: str, target: str) -> bool:
        """检查是否分词匹配
        
        Args:
            query: 查询字符串
            target: 目标字符串
            
        Returns:
            bool: 是否分词匹配
        """
        if not query or not target:
            return False
        
        # 分词
        query_tokens = set(self.tokenize_text(query))
        target_tokens = set(self.tokenize_text(target))
        
        # 检查是否有重叠
        return bool(query_tokens.intersection(target_tokens))
    
    def format_search_time(self, seconds: float) -> str:
        """格式化搜索时间
        
        Args:
            seconds: 搜索时间（秒）
            
        Returns:
            str: 格式化后的时间字符串
        """
        if seconds < 0.001:
            return f"{seconds * 1000000:.2f} 微秒"
        elif seconds < 1:
            return f"{seconds * 1000:.2f} 毫秒"
        else:
            return f"{seconds:.2f} 秒"