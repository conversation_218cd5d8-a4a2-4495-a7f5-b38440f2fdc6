#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征模型数据类

该模块定义了特征提取和处理相关的数据模型。
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
import numpy as np
from datetime import datetime


@dataclass
class FeatureVector:
    """特征向量数据类"""
    vector: np.ndarray
    dimension: int
    feature_type: str = "deep"  # deep, color, texture, shape
    model_name: Optional[str] = None
    extraction_time: Optional[float] = None
    
    def __post_init__(self):
        """后处理初始化"""
        if self.vector is not None:
            self.dimension = len(self.vector)
    
    def normalize(self, method: str = "l2") -> 'FeatureVector':
        """归一化特征向量
        
        Args:
            method: 归一化方法 (l2, l1, max)
            
        Returns:
            FeatureVector: 归一化后的特征向量
        """
        if method == "l2":
            norm = np.linalg.norm(self.vector)
            normalized_vector = self.vector / norm if norm > 0 else self.vector
        elif method == "l1":
            norm = np.sum(np.abs(self.vector))
            normalized_vector = self.vector / norm if norm > 0 else self.vector
        elif method == "max":
            max_val = np.max(np.abs(self.vector))
            normalized_vector = self.vector / max_val if max_val > 0 else self.vector
        else:
            normalized_vector = self.vector
        
        return FeatureVector(
            vector=normalized_vector,
            dimension=self.dimension,
            feature_type=self.feature_type,
            model_name=self.model_name,
            extraction_time=self.extraction_time
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            "vector": self.vector.tolist() if isinstance(self.vector, np.ndarray) else self.vector,
            "dimension": self.dimension,
            "feature_type": self.feature_type,
            "model_name": self.model_name,
            "extraction_time": self.extraction_time
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FeatureVector':
        """从字典创建实例
        
        Args:
            data: 数据字典
            
        Returns:
            FeatureVector: 实例
        """
        vector = data["vector"]
        if isinstance(vector, list):
            vector = np.array(vector)
        
        return cls(
            vector=vector,
            dimension=data["dimension"],
            feature_type=data.get("feature_type", "deep"),
            model_name=data.get("model_name"),
            extraction_time=data.get("extraction_time")
        )


@dataclass
class FeatureExtractionResult:
    """特征提取结果数据类"""
    image_path: str
    success: bool = True
    features: Optional[np.ndarray] = None
    feature_vectors: Dict[str, FeatureVector] = field(default_factory=dict)
    error_message: Optional[str] = None
    extraction_time: float = 0.0
    model_name: Optional[str] = None
    feature_dimension: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        """后处理初始化"""
        if self.timestamp is None:
            self.timestamp = datetime.now()
        
        # 如果有主要特征向量，设置相关属性
        if self.features is not None:
            self.feature_dimension = len(self.features)
            if "deep" not in self.feature_vectors:
                self.feature_vectors["deep"] = FeatureVector(
                    vector=self.features,
                    dimension=self.feature_dimension,
                    feature_type="deep",
                    model_name=self.model_name,
                    extraction_time=self.extraction_time
                )
    
    def add_feature_vector(self, feature_type: str, vector: np.ndarray, 
                          model_name: Optional[str] = None, 
                          extraction_time: Optional[float] = None):
        """添加特征向量
        
        Args:
            feature_type: 特征类型
            vector: 特征向量
            model_name: 模型名称
            extraction_time: 提取时间
        """
        self.feature_vectors[feature_type] = FeatureVector(
            vector=vector,
            dimension=len(vector),
            feature_type=feature_type,
            model_name=model_name,
            extraction_time=extraction_time
        )
    
    def get_feature_vector(self, feature_type: str = "deep") -> Optional[np.ndarray]:
        """获取指定类型的特征向量
        
        Args:
            feature_type: 特征类型
            
        Returns:
            Optional[np.ndarray]: 特征向量
        """
        if feature_type in self.feature_vectors:
            return self.feature_vectors[feature_type].vector
        elif feature_type == "deep" and self.features is not None:
            return self.features
        return None
    
    def get_all_features(self) -> Dict[str, np.ndarray]:
        """获取所有特征向量
        
        Returns:
            Dict[str, np.ndarray]: 所有特征向量
        """
        result = {}
        for feature_type, feature_vector in self.feature_vectors.items():
            result[feature_type] = feature_vector.vector
        
        # 兼容旧版本
        if "deep" not in result and self.features is not None:
            result["deep"] = self.features
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            "image_path": self.image_path,
            "success": self.success,
            "features": self.features.tolist() if isinstance(self.features, np.ndarray) else self.features,
            "feature_vectors": {k: v.to_dict() for k, v in self.feature_vectors.items()},
            "error_message": self.error_message,
            "extraction_time": self.extraction_time,
            "model_name": self.model_name,
            "feature_dimension": self.feature_dimension,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FeatureExtractionResult':
        """从字典创建实例
        
        Args:
            data: 数据字典
            
        Returns:
            FeatureExtractionResult: 实例
        """
        features = data.get("features")
        if isinstance(features, list):
            features = np.array(features)
        
        feature_vectors = {}
        for k, v in data.get("feature_vectors", {}).items():
            feature_vectors[k] = FeatureVector.from_dict(v)
        
        timestamp = None
        if data.get("timestamp"):
            timestamp = datetime.fromisoformat(data["timestamp"])
        
        return cls(
            image_path=data["image_path"],
            success=data.get("success", True),
            features=features,
            feature_vectors=feature_vectors,
            error_message=data.get("error_message"),
            extraction_time=data.get("extraction_time", 0.0),
            model_name=data.get("model_name"),
            feature_dimension=data.get("feature_dimension", 0),
            metadata=data.get("metadata", {}),
            timestamp=timestamp
        )


@dataclass
class BatchFeatureResult:
    """批量特征提取结果"""
    results: List[FeatureExtractionResult] = field(default_factory=list)
    total_processed: int = 0
    successful_extractions: int = 0
    failed_extractions: int = 0
    total_time: float = 0.0
    average_time_per_image: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_result(self, result: FeatureExtractionResult):
        """添加单个结果
        
        Args:
            result: 特征提取结果
        """
        self.results.append(result)
        self.total_processed += 1
        if result.success:
            self.successful_extractions += 1
        else:
            self.failed_extractions += 1
        
        self.total_time += result.extraction_time
        self.average_time_per_image = self.total_time / self.total_processed
    
    def get_successful_results(self) -> List[FeatureExtractionResult]:
        """获取成功的结果
        
        Returns:
            List[FeatureExtractionResult]: 成功的结果列表
        """
        return [r for r in self.results if r.success]
    
    def get_failed_results(self) -> List[FeatureExtractionResult]:
        """获取失败的结果
        
        Returns:
            List[FeatureExtractionResult]: 失败的结果列表
        """
        return [r for r in self.results if not r.success]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            "results": [r.to_dict() for r in self.results],
            "total_processed": self.total_processed,
            "successful_extractions": self.successful_extractions,
            "failed_extractions": self.failed_extractions,
            "total_time": self.total_time,
            "average_time_per_image": self.average_time_per_image,
            "metadata": self.metadata
        }