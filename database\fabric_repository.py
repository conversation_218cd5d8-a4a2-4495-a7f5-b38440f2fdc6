#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布料仓库统一接口

该模块提供布料数据的统一访问接口，整合了CRUD、搜索、统计等功能。
"""

import logging
import os
import hashlib
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from pathlib import Path

try:
    from PIL import Image
except ImportError:
    Image = None

from .database_manager import DatabaseManager
from .repositories import (
    FabricCrud, 
    FabricSearchRepository, 
    FabricStatisticsRepository,
    ImageFeatureRepository,
    SearchHistoryRepository,
    UserPreferencesRepository
)
from .exceptions.database_exceptions import RepositoryError
# 使用绝对导入
try:
    from models.fabric_image import FabricImage
    from models.image_feature import ImageFeature
except ImportError:
    # 尝试从相对路径导入
    try:
        from ..models.fabric_image import FabricImage
        from ..models.image_feature import ImageFeature
    except ImportError:
        # 如果导入失败，可能是因为项目结构不同
        from database.models import FabricImage, ImageFeature


class FabricRepository:
    """布料仓库统一接口类
    
    整合了布料图片的CRUD、搜索、统计等功能，提供统一的访问接口。
    """
    
    def __init__(self, db_manager: DatabaseManager = None):
        """初始化布料仓库
        
        Args:
            db_manager: 数据库管理器，如果为None则使用全局实例
        """
        if db_manager is None:
            from .database_manager import get_database_manager
            db_manager = get_database_manager()
        
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个仓库组件
        self.crud = FabricCrud(db_manager)
        self.search = FabricSearchRepository(db_manager)
        self.statistics = FabricStatisticsRepository(db_manager)
        self.image_features = ImageFeatureRepository(db_manager)
        self.search_history = SearchHistoryRepository(db_manager)
        self.user_preferences = UserPreferencesRepository(db_manager)
    
    # ==================== CRUD 操作 ====================
    
    def create(self, fabric_image: FabricImage) -> Optional[int]:
        """创建布料图片记录
        
        Args:
            fabric_image: 布料图片对象
            
        Returns:
            Optional[int]: 创建的记录ID，如果失败则返回None
        """
        return self.crud.create(fabric_image)
    
    def get_by_id(self, image_id: int) -> Optional[FabricImage]:
        """根据ID获取布料图片
        
        Args:
            image_id: 图片ID
            
        Returns:
            Optional[FabricImage]: 布料图片对象，如果不存在则返回None
        """
        return self.crud.get_by_id(image_id)
    
    def get_by_path(self, file_path: str) -> Optional[FabricImage]:
        """根据文件路径获取布料图片
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[FabricImage]: 布料图片对象，如果不存在则返回None
        """
        return self.crud.get_by_path(file_path)
    
    def get_by_hash(self, md5_hash: str) -> Optional[FabricImage]:
        """根据MD5哈希获取布料图片
        
        Args:
            md5_hash: MD5哈希值
            
        Returns:
            Optional[FabricImage]: 布料图片对象，如果不存在则返回None
        """
        return self.crud.get_by_hash(md5_hash)
    
    def update(self, fabric_image: FabricImage) -> bool:
        """更新布料图片记录
        
        Args:
            fabric_image: 布料图片对象
            
        Returns:
            bool: 更新是否成功
        """
        return self.crud.update(fabric_image)
    
    def delete(self, image_id: int, soft_delete: bool = True) -> bool:
        """删除布料图片记录
        
        Args:
            image_id: 图片ID
            soft_delete: 是否软删除
            
        Returns:
            bool: 删除是否成功
        """
        return self.crud.delete(image_id, soft_delete)
    
    def batch_create(self, fabric_images: List[FabricImage]) -> List[int]:
        """批量创建布料图片记录
        
        Args:
            fabric_images: 布料图片列表
            
        Returns:
            List[int]: 创建的记录ID列表
        """
        return self.crud.batch_create(fabric_images)
    
    def path_exists(self, file_path: str) -> bool:
        """检查文件路径是否存在
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否存在
        """
        return self.crud.exists_by_path(file_path)
    
    def hash_exists(self, md5_hash: str) -> bool:
        """检查MD5哈希是否存在
        
        Args:
            md5_hash: MD5哈希值
            
        Returns:
            bool: 是否存在
        """
        return self.crud.exists_by_hash(md5_hash)
    
    def get_all(self, include_inactive: bool = False, 
               limit: Optional[int] = None, 
               offset: int = 0) -> List[FabricImage]:
        """获取所有布料图片
        
        Args:
            include_inactive: 是否包含非活跃记录
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        return self.crud.get_all(include_inactive, limit, offset)
    
    def count(self, include_inactive: bool = False) -> int:
        """获取布料图片总数
        
        Args:
            include_inactive: 是否包含非活跃记录
            
        Returns:
            int: 布料图片总数
        """
        return self.crud.count(include_inactive)
    
    def add_fabric_image(self, fabric_image: FabricImage) -> Optional[int]:
        """添加布料图片（如果已存在则返回现有ID）
        
        Args:
            fabric_image: 布料图片对象
            
        Returns:
            Optional[int]: 图片ID
        """
        return self.crud.add_fabric_image(fabric_image)
    
    # ==================== 搜索功能 ====================
    
    def search_by_tags(self, tags: List[str], match_all: bool = False,
                      limit: int = 50, offset: int = 0) -> List[FabricImage]:
        """根据标签搜索布料图片
        
        Args:
            tags: 标签列表
            match_all: 是否匹配所有标签
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        return self.search.search_by_tags(tags, match_all, limit, offset)
    
    def search_by_text(self, query: str, limit: int = 50, 
                      offset: int = 0) -> List[FabricImage]:
        """根据文本搜索布料图片
        
        Args:
            query: 搜索查询
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        return self.search.search_by_text(query, limit, offset)
    
    def search_by_date_range(self, start_date: datetime, end_date: datetime,
                           limit: int = 50, offset: int = 0) -> List[FabricImage]:
        """根据日期范围搜索布料图片
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        return self.search.search_by_date_range(start_date, end_date, limit, offset)
    
    def advanced_search(self, filters: Dict[str, Any]) -> List[FabricImage]:
        """高级搜索
        
        Args:
            filters: 搜索过滤条件
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        return self.search.advanced_search(filters)
    
    def get_all_categories(self) -> List[str]:
        """获取所有类别
        
        Returns:
            List[str]: 类别列表
        """
        return self.search.get_all_categories()
    
    def get_all_formats(self) -> List[str]:
        """获取所有格式
        
        Returns:
            List[str]: 格式列表
        """
        return self.search.get_all_formats()
    
    def get_all_tags(self) -> List[str]:
        """获取所有标签
        
        Returns:
            List[str]: 标签列表
        """
        return self.search.get_all_tags()
    
    # ==================== 统计功能 ====================
    
    def get_total_count(self) -> int:
        """获取总数量
        
        Returns:
            int: 总数量
        """
        return self.statistics.get_total_count()
    
    def get_statistics_by_category(self) -> Dict[str, int]:
        """获取按类别统计
        
        Returns:
            Dict[str, int]: 类别统计
        """
        return self.statistics.get_statistics_by_category()
    
    def get_statistics_by_format(self) -> Dict[str, int]:
        """获取按格式统计
        
        Returns:
            Dict[str, int]: 格式统计
        """
        return self.statistics.get_statistics_by_format()
    
    def get_file_size_statistics(self) -> Dict[str, Any]:
        """获取文件大小统计
        
        Returns:
            Dict[str, Any]: 文件大小统计
        """
        return self.statistics.get_file_size_statistics()
    
    def get_recent_additions(self, days: int = 7, limit: int = 20) -> List[FabricImage]:
        """获取最近添加的图片
        
        Args:
            days: 天数
            limit: 限制数量
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        return self.statistics.get_recent_additions(days, limit)
    
    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """获取综合统计信息
        
        Returns:
            Dict[str, Any]: 综合统计信息
        """
        return self.statistics.get_comprehensive_statistics()
    
    # ==================== 图像特征管理 ====================
    
    def add_feature(self, image_id_or_feature, feature_type=None, 
                   feature_data=None, model_name=None) -> Optional[int]:
        """添加特征到图像
        
        Args:
            image_id_or_feature: 图像ID或ImageFeature对象
            feature_type: 特征类型
            feature_data: 特征数据
            model_name: 模型名称
            
        Returns:
            Optional[int]: 特征ID
        """
        return self.image_features.add_feature(
            image_id_or_feature, feature_type, feature_data, model_name
        )
    
    def get_feature_by_image_id(self, image_id: int, 
                               feature_type: str = None) -> Optional[ImageFeature]:
        """根据图像ID获取特征
        
        Args:
            image_id: 图像ID
            feature_type: 特征类型
            
        Returns:
            Optional[ImageFeature]: 特征对象
        """
        return self.image_features.get_feature_by_image_id(image_id, feature_type)
    
    def get_features_by_image_id(self, image_id: int) -> List[ImageFeature]:
        """根据图像ID获取所有特征
        
        Args:
            image_id: 图像ID
            
        Returns:
            List[ImageFeature]: 特征列表
        """
        return self.image_features.get_features_by_image_id(image_id)
    
    def update_feature(self, feature: ImageFeature) -> bool:
        """更新特征
        
        Args:
            feature: 特征对象
            
        Returns:
            bool: 更新是否成功
        """
        return self.image_features.update_feature(feature)
    
    def delete_feature(self, feature_id: int) -> bool:
        """删除特征
        
        Args:
            feature_id: 特征ID
            
        Returns:
            bool: 删除是否成功
        """
        return self.image_features.delete_feature(feature_id)
        
    def update_or_create_feature(self, image_id: int, feature_type: str, 
                            feature_data: bytes, model_name: str = None) -> Optional[int]:
        """更新或创建特征
        
        Args:
            image_id: 图像ID
            feature_type: 特征类型
            feature_data: 特征数据
            model_name: 模型名称
            
        Returns:
            Optional[int]: 特征ID
        """
        return self.image_features.update_or_create_feature(
            image_id, feature_type, feature_data, model_name
        )
    
    def delete_feature_by_image_id(self, image_id: int, 
                                  feature_type: str = None) -> bool:
        """根据图像ID删除特征
        
        Args:
            image_id: 图像ID
            feature_type: 特征类型
            
        Returns:
            bool: 删除是否成功
        """
        return self.image_features.delete_feature_by_image_id(image_id, feature_type)
    
    # ==================== 搜索历史管理 ====================
    
    def add_search_record(self, search_query: str, search_type: str,
                         search_params: Dict[str, Any] = None,
                         search_results: List[Dict[str, Any]] = None,
                         result_count: int = 0,
                         execution_time: float = 0.0) -> Optional[int]:
        """添加搜索记录
        
        Args:
            search_query: 搜索查询
            search_type: 搜索类型
            search_params: 搜索参数
            search_results: 搜索结果
            result_count: 结果数量
            execution_time: 执行时间
            
        Returns:
            Optional[int]: 记录ID
        """
        return self.search_history.add_search_record(
            search_query, search_type, search_params, 
            search_results, result_count, execution_time
        )
    
    def get_recent_searches(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取最近的搜索记录
        
        Args:
            limit: 限制数量
            
        Returns:
            List[Dict[str, Any]]: 搜索记录列表
        """
        return self.search_history.get_recent_searches(limit)
    
    def get_popular_searches(self, limit: int = 20, 
                           days: int = 30) -> List[Dict[str, Any]]:
        """获取热门搜索
        
        Args:
            limit: 限制数量
            days: 统计天数
            
        Returns:
            List[Dict[str, Any]]: 热门搜索列表
        """
        return self.search_history.get_popular_searches(limit, days)
    
    # ==================== 用户偏好管理 ====================
    
    def set_preference(self, key: str, value: Any, 
                      description: str = None, category: str = None) -> bool:
        """设置用户偏好
        
        Args:
            key: 偏好键
            value: 偏好值
            description: 描述
            category: 分类
            
        Returns:
            bool: 设置是否成功
        """
        return self.user_preferences.set_preference(key, value, description, category)
    
    def get_preference(self, key: str, default: Any = None) -> Any:
        """获取用户偏好
        
        Args:
            key: 偏好键
            default: 默认值
            
        Returns:
            Any: 偏好值
        """
        return self.user_preferences.get_preference(key, default)
    
    def get_all_preferences(self, category: str = None) -> Dict[str, Any]:
        """获取所有用户偏好
        
        Args:
            category: 分类过滤
            
        Returns:
            Dict[str, Any]: 偏好字典
        """
        return self.user_preferences.get_all_preferences(category)
    
    def delete_preference(self, key: str) -> bool:
        """删除用户偏好
        
        Args:
            key: 偏好键
            
        Returns:
            bool: 删除是否成功
        """
        return self.user_preferences.delete_preference(key)
    
    # ==================== 兼容性方法 ====================
    # 为了保持与旧代码的兼容性，保留一些常用的方法别名
    
    def add_image(self, fabric_image: FabricImage) -> Optional[int]:
        """添加图像（兼容性方法）"""
        return self.add_fabric_image(fabric_image)
    
    def get_image_by_id(self, image_id: int) -> Optional[FabricImage]:
        """根据ID获取图像（兼容性方法）"""
        return self.get_by_id(image_id)
    
    def get_image_by_path(self, file_path: str) -> Optional[FabricImage]:
        """根据路径获取图像（兼容性方法）"""
        return self.get_by_path(file_path)
    
    def get_image_by_hash(self, md5_hash: str) -> Optional[FabricImage]:
        """根据哈希获取图像（兼容性方法）"""
        return self.get_by_hash(md5_hash)
    
    def update_image(self, fabric_image: FabricImage) -> bool:
        """更新图像（兼容性方法）"""
        return self.update(fabric_image)
    
    def delete_image(self, image_id: int) -> bool:
        """删除图像（兼容性方法）"""
        return self.delete(image_id)
    
    def batch_add_images(self, fabric_images: List[FabricImage]) -> List[int]:
        """批量添加图像（兼容性方法）"""
        return self.batch_create(fabric_images)
    
    def create_fabric_image(self, image_path: str, metadata: Dict[str, Any] = None) -> Optional[FabricImage]:
        """从图像路径创建FabricImage对象
        
        Args:
            image_path: 图像文件路径
            metadata: 元数据
            
        Returns:
            Optional[FabricImage]: 创建的FabricImage对象，如果失败则返回None
        """
        try:
            from utils.image_utils import ImageProcessor
            from utils.file_utils import FileManager
            from pathlib import Path
            import json
            
            # 获取图像信息
            image_processor = ImageProcessor()
            image_info = image_processor.get_image_info(image_path)
            
            if not image_info:
                self.logger.error(f"无法获取图像信息: {image_path}")
                return None
            
            # 获取文件信息
            file_manager = FileManager()
            file_info = file_manager.get_file_info(image_path)
            
            if not file_info:
                self.logger.error(f"无法获取文件信息: {image_path}")
                return None
            
            # 计算文件哈希值
            hash_md5 = file_manager.calculate_file_hash(image_path)
            
            # 检查是否已存在相同哈希值的图像
            if hash_md5 and self.hash_exists(hash_md5):
                self.logger.warning(f"图像已存在(相同哈希值): {image_path}")
                return self.get_by_hash(hash_md5)
            
            # 创建FabricImage对象
            fabric_image = FabricImage(
                id=None,
                file_path=str(image_path),
                file_name=Path(image_path).name,
                file_size=file_info.size if hasattr(file_info, 'size') else 0,
                width=image_info.get('width', 0) if isinstance(image_info, dict) else getattr(image_info, 'width', 0),
                height=image_info.get('height', 0) if isinstance(image_info, dict) else getattr(image_info, 'height', 0),
                channels=image_info.get('channels', 3) if isinstance(image_info, dict) else getattr(image_info, 'channels', 3),
                format=image_info.get('format', '') if isinstance(image_info, dict) else getattr(image_info, 'format', ''),
                hash_md5=hash_md5 or "",
                features=None,
                thumbnail_path=None,
                tags=json.dumps([]),
                category="",
                color_info=json.dumps({}),
                texture_info=json.dumps({}),
                pattern_info=json.dumps({}),
                material_info=json.dumps({}),
                description="",
                metadata=json.dumps(metadata or {}),
                indexed_at=datetime.now(),
                is_active=True
            )
            
            # 创建记录
            fabric_id = self.create(fabric_image)
            if fabric_id:
                fabric_image.id = fabric_id
                self.logger.info(f"创建FabricImage成功: {fabric_image.file_name}")
                return fabric_image
            else:
                self.logger.error(f"创建FabricImage记录失败: {image_path}")
                return None
                
        except Exception as e:
            self.logger.error(f"创建FabricImage失败: {e}")
            return None
    
    def file_path_exists(self, file_path: str) -> bool:
        """检查文件路径是否存在（兼容性方法）"""
        return self.path_exists(file_path)
    
    def exists_by_path(self, file_path: str) -> bool:
        """检查文件路径是否存在（兼容性方法）"""
        return self.path_exists(file_path)
    
    def md5_exists(self, md5_hash: str) -> bool:
        """检查MD5是否存在（兼容性方法）"""
        return self.hash_exists(md5_hash)
    
    # ==================== 工具方法 ====================
    
    def close(self):
        """关闭数据库连接"""
        if hasattr(self.db_manager, 'close'):
            self.db_manager.close()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


# 全局实例
_fabric_repository = None


def get_fabric_repository() -> FabricRepository:
    """获取全局布料仓库实例
    
    Returns:
        FabricRepository: 布料仓库实例
    """
    global _fabric_repository
    if _fabric_repository is None:
        _fabric_repository = FabricRepository()
    return _fabric_repository


def close_fabric_repository():
    """关闭全局布料仓库实例"""
    global _fabric_repository
    if _fabric_repository is not None:
        _fabric_repository.close()
        _fabric_repository = None