"""
结果面板主模块

包含结果面板的主要实现
"""

import os
import math
from datetime import datetime
from pathlib import Path
from typing import List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QStackedWidget
)
from PyQt6.QtCore import Qt, pyqtSignal

from database.models import FabricImage
from utils.logger_mixin import LoggerMixin
from gui.config.result_config import ViewMode, ResultConfig
from gui.widgets.widget_factory import WidgetFactory
from gui.widgets.base import ButtonStyle, ButtonSize
from gui.panels.grid_view import GridView
from gui.panels.list_view import ListView


class ResultPanel(QWidget, LoggerMixin):
    """结果面板"""
    
    # 信号
    itemSelected = pyqtSignal(FabricImage)  # 项目选择
    itemDoubleClicked = pyqtSignal(FabricImage)  # 项目双击
    selectionChanged = pyqtSignal(list)  # 选择变更
    viewModeChanged = pyqtSignal(ViewMode)  # 视图模式变更
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化组件
        self.widget_factory = WidgetFactory()
        self.result_config = ResultConfig()
        
        # 结果数据
        self.search_results = []
        self.current_page = 1
        self.total_pages = 1
        
        # 视图组件
        self.grid_view = None
        self.list_view = None
        self.current_view = None
        self.view_mode = ViewMode.GRID  # 添加view_mode属性
        
        # 设置界面
        self.setup_ui()
        self.create_views()
        self.connect_signals()
        
        # 确保面板可见
        self.setVisible(True)
        
        # 初始化视图模式
        self.set_view_mode(ViewMode.GRID)
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setContentsMargins(10, 5, 10, 5)
        toolbar_layout.setSpacing(10)
        
        # 结果统计
        self.result_label = self.widget_factory.create_label("共 0 个结果")
        toolbar_layout.addWidget(self.result_label)
        
        toolbar_layout.addStretch()
        
        # 视图模式按钮
        self.grid_btn = self.widget_factory.create_tool_button(
            "网格视图", icon_name="grid", tooltip="网格视图", click_handler=lambda: self.set_view_mode(ViewMode.GRID)
        )
        self.grid_btn.setCheckable(True)
        toolbar_layout.addWidget(self.grid_btn)
        
        self.list_btn = self.widget_factory.create_tool_button(
            "列表视图", icon_name="list", tooltip="列表视图", click_handler=lambda: self.set_view_mode(ViewMode.LIST)
        )
        self.list_btn.setCheckable(True)
        toolbar_layout.addWidget(self.list_btn)
        
        # 缩略图大小滑块（仅网格视图）
        self.size_label = self.widget_factory.create_label("大小:")
        toolbar_layout.addWidget(self.size_label)
        
        self.size_slider = self.widget_factory.create_slider(
            minimum=100, maximum=300, value=150,
            orientation=Qt.Orientation.Horizontal
        )
        self.size_slider.setFixedWidth(100)
        toolbar_layout.addWidget(self.size_slider)
        
        # 排序方式
        self.sort_label = self.widget_factory.create_label("排序:")
        toolbar_layout.addWidget(self.sort_label)
        
        self.sort_combo = self.widget_factory.create_combo_box(
            items=["相关性", "日期降序", "日期升序", "大小降序", "大小升序", "名称升序", "名称降序"]
        )
        toolbar_layout.addWidget(self.sort_combo)
        
        layout.addLayout(toolbar_layout)
        
        # 分隔线
        separator = self.widget_factory.create_separator()
        layout.addWidget(separator)
        
        # 视图区域
        self.view_stack = QStackedWidget()
        layout.addWidget(self.view_stack)
        
        # 分页控件
        pagination_layout = QHBoxLayout()
        pagination_layout.setContentsMargins(10, 5, 10, 5)
        pagination_layout.setSpacing(10)
        
        self.prev_btn = self.widget_factory.create_button(
            "上一页", "prev", None, ButtonStyle.SECONDARY, ButtonSize.SMALL,
            click_handler=self.prev_page
        )
        self.prev_btn.setEnabled(False)
        pagination_layout.addWidget(self.prev_btn)
        
        self.page_label = self.widget_factory.create_label("第 1 页，共 1 页")
        pagination_layout.addWidget(self.page_label)
        
        self.next_btn = self.widget_factory.create_button(
            "下一页", "next", None, ButtonStyle.SECONDARY, ButtonSize.SMALL,
            click_handler=self.next_page
        )
        self.next_btn.setEnabled(False)
        pagination_layout.addWidget(self.next_btn)
        
        pagination_layout.addStretch()
        
        # 每页显示数量
        self.per_page_label = self.widget_factory.create_label("每页:")
        pagination_layout.addWidget(self.per_page_label)
        
        self.per_page_combo = self.widget_factory.create_combo_box(
            items=["25", "50", "100", "200"]
        )
        self.per_page_combo.setCurrentText(str(self.result_config.items_per_page))
        pagination_layout.addWidget(self.per_page_combo)
        
        layout.addLayout(pagination_layout)
    
    def create_views(self):
        """创建视图"""
        try:
            # 网格视图
            self.grid_view = GridView()
            self.view_stack.addWidget(self.grid_view)
            
            # 列表视图
            self.list_view = ListView()
            self.view_stack.addWidget(self.list_view)
            
        except Exception as e:
            self.logger.error(f"创建视图失败: {e}")
    
    def connect_signals(self):
        """连接信号"""
        try:
            # 缩略图大小
            self.size_slider.valueChanged.connect(self.on_thumbnail_size_changed)
            
            # 排序方式
            self.sort_combo.currentTextChanged.connect(self.on_sort_changed)
            
            # 每页显示数量
            self.per_page_combo.currentTextChanged.connect(self.on_per_page_changed)
            
            # 视图信号
            if self.grid_view:
                self.grid_view.itemClicked.connect(self.itemSelected.emit)
                self.grid_view.itemDoubleClicked.connect(self.itemDoubleClicked.emit)
                self.grid_view.selectionChanged.connect(self.selectionChanged.emit)
            
            if self.list_view:
                self.list_view.itemClicked.connect(self.itemSelected.emit)
                self.list_view.itemDoubleClicked.connect(self.itemDoubleClicked.emit)
                self.list_view.selectionChanged.connect(self.selectionChanged.emit)
            
        except Exception as e:
            self.logger.error(f"连接信号失败: {e}")

    
    def set_view_mode(self, mode: ViewMode):
        """设置视图模式
        
        Args:
            mode: 视图模式
        """
        try:
            self.result_config.view_mode = mode
            self.view_mode = mode  # 更新view_mode属性
            
            # 更新按钮状态
            self.grid_btn.setChecked(mode == ViewMode.GRID)
            self.list_btn.setChecked(mode == ViewMode.LIST)
            
            # 切换视图
            if mode == ViewMode.GRID:
                self.view_stack.setCurrentWidget(self.grid_view)
                self.current_view = self.grid_view
                # 显示缩略图大小控件
                self.size_label.setVisible(True)
                self.size_slider.setVisible(True)
            elif mode == ViewMode.LIST:
                self.view_stack.setCurrentWidget(self.list_view)
                self.current_view = self.list_view
                # 隐藏缩略图大小控件
                self.size_label.setVisible(False)
                self.size_slider.setVisible(False)
            
            # 更新显示
            self.update_display()
            
            # 发送视图模式变更信号（只发送一次）
            self.viewModeChanged.emit(mode)
            
            self.logger.debug(f"视图模式已切换到: {mode.value}")
            
        except Exception as e:
            self.logger.error(f"设置视图模式失败: {e}")
    
    def set_results(self, results):
        """设置搜索结果
        
        Args:
            results: 搜索结果列表，可以是List[FabricImage]、List[SearchResult]或List[Dict]
        """
        try:
            from search.search_strategies import SearchResult as StrategySearchResult
            from search.search_engine import SearchResult as EngineSearchResult
            from database.models import FabricImage
            
            self.logger.info(f"=== 开始处理搜索结果 ===")
            self.logger.info(f"接收到的结果类型: {type(results)}")
            self.logger.info(f"结果数量: {len(results) if hasattr(results, '__len__') else 'N/A'}")

            # 增强异常日志，打印results的内容和类型
            if hasattr(results, '__dict__'):
                self.logger.info(f"results对象属性: {list(results.__dict__.keys())}")

            # 获取当前搜索的特征权重（用于重新计算显示的特征分数）
            current_weights = self._get_current_search_weights()
            self.logger.info(f"当前搜索权重: {current_weights}")

            # 处理不同类型的结果
            if not results:
                self.search_results = []
                self.logger.info("结果为空，设置空列表")
            else:
                self.search_results = []
                
                # 处理SearchResult对象（来自search_engine.py）
                if isinstance(results, EngineSearchResult):
                    self.logger.info("处理EngineSearchResult对象")
                    # 保存总结果数
                    total_results = results.total_results if hasattr(results, 'total_results') else 0
                    self.logger.info(f"EngineSearchResult总结果数: {total_results}")
                    
                    # 详细日志：打印results对象的内容
                    if hasattr(results, 'results'):
                        self.logger.info(f"results.results类型: {type(results.results)}")
                        self.logger.info(f"results.results长度: {len(results.results) if results.results else 0}")
                        if results.results:
                            self.logger.info(f"第一个结果类型: {type(results.results[0])}")
                            if hasattr(results.results[0], '__dict__'):
                                self.logger.info(f"第一个结果属性: {list(results.results[0].__dict__.keys())}")
                    
                    if hasattr(results, 'results') and results.results:
                        for i, item in enumerate(results.results):
                            try:
                                self.logger.info(f"处理第{i+1}个结果，类型: {type(item)}")
                                
                                # 优先处理(FabricImage, float)元组格式
                                if isinstance(item, tuple) and len(item) >= 2:
                                    self.logger.info(f"第{i+1}个结果是元组，长度: {len(item)}")
                                    if isinstance(item[0], FabricImage):
                                        # 将相似度分数添加到FabricImage对象
                                        fabric_image = item[0]
                                        similarity_score = item[1]
                                        # 使用setattr确保similarity_score一定赋值
                                        setattr(fabric_image, 'similarity_score', similarity_score)
                                        self.logger.info(f"为FabricImage设置相似度分数: {similarity_score}")

                                        # 重新计算显示的特征分数
                                        self._update_display_feature_scores(fabric_image, current_weights)

                                        self.search_results.append(fabric_image)
                                    else:
                                        self.logger.warning(f"元组第一个元素不是FabricImage: {type(item[0])}")
                                elif isinstance(item, FabricImage):
                                    self.logger.info(f"第{i+1}个结果是FabricImage: {item.file_path}")
                                    # 确保有similarity_score属性
                                    if not hasattr(item, 'similarity_score'):
                                        setattr(item, 'similarity_score', 1.0)

                                    # 重新计算显示的特征分数
                                    self._update_display_feature_scores(item, current_weights)

                                    self.search_results.append(item)
                                elif isinstance(item, dict):
                                    self.logger.info(f"第{i+1}个结果是字典: {list(item.keys())}")
                                    # 使用FabricImage.from_dict创建对象，自动过滤多余字段
                                    try:
                                        # 过滤字典，只保留FabricImage需要的字段
                                        fabric_fields = {
                                            'id', 'file_path', 'file_name', 'file_size', 'width', 'height', 
                                            'channels', 'format', 'hash_md5', 'features', 'thumbnail_path', 
                                            'tags', 'category', 'color_info', 'texture_info', 'pattern_info', 
                                            'material_info', 'description', 'metadata', 'created_at', 
                                            'updated_at', 'indexed_at', 'is_active'
                                        }
                                        filtered_dict = {k: v for k, v in item.items() if k in fabric_fields}
                                        
                                        # 确保必要字段存在
                                        if 'file_path' not in filtered_dict and 'path' in item:
                                            filtered_dict['file_path'] = item['path']
                                        if 'category' not in filtered_dict:
                                            filtered_dict['category'] = item.get('category', '')
                                        
                                        fabric_image = FabricImage.from_dict(filtered_dict)
                                        
                                        # 设置相似度分数
                                        if 'similarity' in item:
                                            setattr(fabric_image, 'similarity_score', item['similarity'])
                                        elif 'similarity_score' in item:
                                            setattr(fabric_image, 'similarity_score', item['similarity_score'])
                                        else:
                                            setattr(fabric_image, 'similarity_score', 0.0)

                                        # 重新计算显示的特征分数
                                        self._update_display_feature_scores(fabric_image, current_weights)

                                        self.search_results.append(fabric_image)
                                        self.logger.info(f"从字典创建FabricImage成功: {fabric_image.file_path}")
                                    except Exception as dict_error:
                                        self.logger.error(f"从字典创建FabricImage失败: {dict_error}")
                                        self.logger.error(f"字典内容: {item}")
                                else:
                                    self.logger.warning(f"第{i+1}个结果类型未知: {type(item)}")
                                    if hasattr(item, '__dict__'):
                                        self.logger.warning(f"对象属性: {list(item.__dict__.keys())}")
                            except Exception as item_error:
                                self.logger.error(f"处理第{i+1}个结果时出错: {item_error}")
                                self.logger.error(f"结果内容: {item}")
                                continue
                    
                    # 如果没有提取到结果但有总结果数，创建占位符结果
                    if len(self.search_results) == 0 and total_results > 0:
                        self.logger.warning(f"EngineSearchResult有{total_results}个结果，但未能提取到FabricImage对象")
                        self.logger.warning(f"results.results内容: {results.results}")
                    
                    self.logger.info(f"处理EngineSearchResult对象，提取了 {len(self.search_results)} 个FabricImage，总结果数: {total_results}")

                # 处理StrategySearchResult对象
                elif isinstance(results, StrategySearchResult):
                    self.logger.info("处理StrategySearchResult对象")
                    if hasattr(results, 'results') and results.results:
                        for item in results.results:
                            if isinstance(item, FabricImage):
                                self.search_results.append(item)
                            elif isinstance(item, tuple) and len(item) >= 2 and isinstance(item[0], FabricImage):
                                self.search_results.append(item[0])
                    self.logger.info(f"处理StrategySearchResult对象，提取了 {len(self.search_results)} 个FabricImage")
                # 处理列表类型的结果
                elif isinstance(results, list):
                    self.logger.info(f"处理列表类型结果，包含 {len(results)} 个项目")
                    for i, result in enumerate(results):
                        try:
                            self.logger.info(f"处理第 {i+1} 个结果，类型: {type(result)}")
                            
                            # 检查是否是FabricImage对象
                            if isinstance(result, FabricImage):
                                self.logger.info(f"第 {i+1} 个结果是FabricImage对象: {result.file_path}")
                                self.search_results.append(result)
                            # 检查是否是字典格式（来自search_worker.py）
                            elif isinstance(result, dict) and 'path' in result and 'similarity' in result:
                                self.logger.info(f"第 {i+1} 个结果是字典格式，路径: {result['path']}, 相似度: {result['similarity']}")
                                
                                # 检查文件是否存在
                                file_path = result['path']
                                self.logger.info(f"处理文件路径: {file_path}")
                                
                                # 处理相对路径
                                if not os.path.isabs(file_path):
                                    # 使用当前工作目录作为基准路径
                                    abs_path = os.path.join(os.getcwd(), file_path)
                                    self.logger.info(f"转换相对路径为绝对路径: {file_path} -> {abs_path}")
                                    file_path = abs_path
                                    result['path'] = file_path
                                
                                # 如果文件仍然不存在，尝试使用test_images目录
                                if not os.path.exists(file_path) and 'test_images' in file_path:
                                    base_name = os.path.basename(file_path)
                                    alt_path = os.path.join(os.getcwd(), 'test_images', base_name)
                                    self.logger.info(f"尝试替代路径: {alt_path}")
                                    if os.path.exists(alt_path):
                                        file_path = alt_path
                                        result['path'] = file_path
                                        self.logger.info(f"使用替代路径: {file_path}")
                                
                                # 即使文件不存在也继续处理，让ResultItem处理文件不存在的情况
                                if not os.path.exists(file_path):
                                    self.logger.warning(f"文件不存在，但仍继续处理: {file_path}")
                                    # 不要跳过，继续处理
                                
                                # 创建FabricImage对象
                                from database.models import FabricImage
                                from pathlib import Path
                                fabric_image = FabricImage(
                                    id=result.get('id'),
                                    file_path=result['path'],
                                    file_name=result.get('file_name', Path(result['path']).name),
                                    width=result.get('width', 0),
                                    height=result.get('height', 0),
                                    format=result.get('format', ''),
                                    category=result.get('category', '')
                                )
                                # 添加相似度分数
                                fabric_image.similarity_score = result['similarity']

                                # 重新计算显示的特征分数
                                self._update_display_feature_scores(fabric_image, current_weights)

                                self.search_results.append(fabric_image)
                                self.logger.info(f"从字典创建FabricImage成功: {fabric_image.file_path}")
                            # 检查是否有fabric_image属性（SearchResult对象）
                            elif hasattr(result, 'fabric_image') and result.fabric_image is not None:
                                self.logger.info(f"第 {i+1} 个结果有fabric_image属性")
                                # 将feature_scores添加到fabric_image对象中，以便在列表视图中显示
                                if hasattr(result, 'feature_scores'):
                                    result.fabric_image.feature_scores = result.feature_scores
                                if hasattr(result, 'similarity_score'):
                                    result.fabric_image.similarity_score = result.similarity_score

                                # 重新计算显示的特征分数
                                self._update_display_feature_scores(result.fabric_image, current_weights)

                                self.search_results.append(result.fabric_image)
                            # 检查是否是元组格式 (fabric_image, score)
                            elif isinstance(result, tuple) and len(result) >= 2:
                                self.logger.info(f"第 {i+1} 个结果是元组格式")
                                fabric_image = result[0]
                                if isinstance(fabric_image, FabricImage):
                                    # 添加相似度分数
                                    fabric_image.similarity_score = result[1]

                                    # 重新计算显示的特征分数
                                    self._update_display_feature_scores(fabric_image, current_weights)

                                    self.search_results.append(fabric_image)
                                    self.logger.info(f"从元组提取FabricImage成功: {fabric_image.file_path}")
                                else:
                                    self.logger.warning(f"元组中的第一个元素不是FabricImage: {type(fabric_image)}")
                            # 检查是否有file_path属性（可能是FabricImage的其他实现）
                            elif hasattr(result, 'file_path'):
                                self.logger.info(f"第 {i+1} 个结果有file_path属性: {result.file_path}")

                                # 重新计算显示的特征分数
                                self._update_display_feature_scores(result, current_weights)

                                self.search_results.append(result)
                            else:
                                self.logger.warning(f"第 {i+1} 个结果无法识别，类型: {type(result)}, 属性: {dir(result) if hasattr(result, '__dict__') else 'N/A'}")
                        except Exception as item_error:
                            self.logger.error(f"处理第 {i+1} 个搜索结果时出错: {item_error}, 结果类型: {type(result)}")
                            continue
                else:
                    self.logger.warning(f"未知的结果类型: {type(results)}")
            
            self.logger.info(f"最终处理得到 {len(self.search_results)} 个FabricImage对象")
            
            # 重置分页
            self.current_page = 1
            self.calculate_pagination()
            
            # 更新显示
            self.logger.info("开始更新显示")
            self.update_display()
            
            # 更新统计信息
            self.update_result_label()
            
            self.logger.info(f"=== 搜索结果处理完成 ===")
            
        except Exception as e:
            self.logger.error(f"设置搜索结果失败: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            
            # 增强异常日志，打印results的内容和类型
            self.logger.error(f"异常时results类型: {type(results)}")
            if hasattr(results, '__len__'):
                self.logger.error(f"异常时results长度: {len(results)}")
            if hasattr(results, '__dict__'):
                self.logger.error(f"异常时results属性: {list(results.__dict__.keys())}")
            self.logger.error(f"异常时results内容: {results}")
            
            # 设置空结果以避免界面错误
            self.search_results = []
            self.current_page = 1
            self.calculate_pagination()
            self.update_display()
            self.update_result_label()
    
    def calculate_pagination(self):
        """计算分页信息"""
        try:
            total_items = len(self.search_results)
            items_per_page = self.result_config.items_per_page
            
            # 确保items_per_page不为0，避免除零错误
            if items_per_page <= 0:
                self.logger.warning(f"每页显示数量为0或负数: {items_per_page}，已重置为默认值25")
                items_per_page = 25
                self.result_config.items_per_page = 25
            
            self.total_pages = max(1, math.ceil(total_items / items_per_page))
            
            # 确保当前页在有效范围内
            self.current_page = max(1, min(self.current_page, self.total_pages))
            
            self.logger.info(f"分页计算完成 - 总项目: {total_items}, 每页: {items_per_page}, 总页数: {self.total_pages}, 当前页: {self.current_page}")
            
        except Exception as e:
            self.logger.error(f"计算分页信息失败: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            # 设置默认值避免程序崩溃
            self.total_pages = 1
            self.current_page = 1
            if self.result_config.items_per_page <= 0:
                self.result_config.items_per_page = 25
    
    def update_display(self):
        """更新显示"""
        try:
            self.logger.info(f"=== 开始更新显示 ===")
            self.logger.info(f"当前视图模式: {self.view_mode}")
            self.logger.info(f"总结果数: {len(self.search_results)}")
            
            # 确保items_per_page不为0，避免计算错误
            items_per_page = self.result_config.items_per_page
            if items_per_page <= 0:
                self.logger.warning(f"更新显示时发现每页显示数量为0或负数: {items_per_page}，已重置为默认值25")
                items_per_page = 25
                self.result_config.items_per_page = 25
                # 重新计算分页信息
                self.calculate_pagination()
            
            self.logger.info(f"当前页: {self.current_page}, 每页显示: {items_per_page}")
            
            # 获取当前页的结果
            start_idx = (self.current_page - 1) * items_per_page
            end_idx = start_idx + items_per_page
            current_results = self.search_results[start_idx:end_idx]
            
            self.logger.info(f"当前页显示范围: {start_idx}-{end_idx}, 实际结果数: {len(current_results)}")
            
            # 根据视图模式更新显示
            if self.view_mode == ViewMode.GRID:
                self.logger.info("更新网格视图")
                self.grid_view.set_results(current_results)
                self.list_view.set_results([])  # 清空列表视图
            else:
                self.logger.info("更新列表视图")
                self.list_view.set_results(current_results)
                self.grid_view.set_results([])  # 清空网格视图
            
            # 更新分页控件
            self.update_pagination_controls()
            
            self.logger.info(f"=== 显示更新完成 ===")
            
        except Exception as e:
            self.logger.error(f"更新显示失败: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
    
    def update_pagination_controls(self):
        """更新分页控件"""
        try:
            # 更新按钮状态
            self.prev_btn.setEnabled(self.current_page > 1)
            self.next_btn.setEnabled(self.current_page < self.total_pages)
            
            # 更新页码标签
            self.page_label.setText(f"第 {self.current_page} 页，共 {self.total_pages} 页")
            
        except Exception as e:
            self.logger.error(f"更新分页控件失败: {e}")
    
    def update_result_label(self):
        """更新结果标签"""
        try:
            total_count = len(self.search_results)
            self.result_label.setText(f"共 {total_count} 个结果")
            
        except Exception as e:
            self.logger.error(f"更新结果标签失败: {e}")
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.update_display()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.update_display()
    
    def on_thumbnail_size_changed(self, size: int):
        """缩略图大小变更"""
        try:
            self.result_config.thumbnail_size = size
            
            if self.grid_view:
                self.grid_view.set_thumbnail_size(size)
            
        except Exception as e:
            self.logger.error(f"缩略图大小变更失败: {e}")
    
    def on_sort_changed(self, sort_text: str):
        """排序方式变更"""
        try:
            # 实现排序逻辑
            if not self.search_results:
                return
            
            # 根据排序方式对结果进行排序
            if sort_text == "相关性":
                # 相关性排序保持原顺序，因为结果已经按相关性排序
                pass
            elif sort_text == "日期降序":
                self.search_results.sort(key=lambda x: x.created_at or datetime.min, reverse=True)
            elif sort_text == "日期升序":
                self.search_results.sort(key=lambda x: x.created_at or datetime.min)
            elif sort_text == "大小降序":
                self.search_results.sort(key=lambda x: getattr(x, 'file_size', 0) or 0, reverse=True)
            elif sort_text == "大小升序":
                self.search_results.sort(key=lambda x: getattr(x, 'file_size', 0) or 0)
            elif sort_text == "名称升序":
                self.search_results.sort(key=lambda x: x.file_name.lower() if hasattr(x, 'file_name') else '')
            elif sort_text == "名称降序":
                self.search_results.sort(key=lambda x: x.file_name.lower() if hasattr(x, 'file_name') else '', reverse=True)
            
            # 重置到第一页
            self.current_page = 1
            self.total_pages = (len(self.search_results) + self.result_config.items_per_page - 1) // self.result_config.items_per_page
            
            # 更新显示
            self.update_display()
            
            self.logger.debug(f"排序方式变更: {sort_text}")
            
        except Exception as e:
            self.logger.error(f"排序方式变更失败: {e}")
    
    def on_per_page_changed(self, per_page_text: str):
        """每页显示数量变更"""
        try:
            per_page = int(per_page_text)
            # 确保per_page不为0或负数
            if per_page <= 0:
                self.logger.warning(f"尝试将每页显示数量设置为无效值: {per_page}，已重置为默认值25")
                per_page = 25
                
            self.result_config.items_per_page = per_page
            
            # 重新计算分页
            self.calculate_pagination()
            self.update_display()
            
        except Exception as e:
            self.logger.error(f"每页显示数量变更失败: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
    
    def clear_results(self):
        """清除结果"""
        try:
            self.search_results.clear()
            
            if self.current_view:
                self.current_view.set_results([])
            
            self.current_page = 1
            self.total_pages = 1
            
            self.update_result_label()
            self.update_pagination_controls()
            
            self.logger.debug("结果已清除")
            
        except Exception as e:
            self.logger.error(f"清除结果失败: {e}")
    
    def get_selected_items(self) -> List[FabricImage]:
        """获取选中的项目
        
        Returns:
            List[FabricImage]: 选中的项目列表
        """
        if self.current_view and hasattr(self.current_view, 'get_selected_items'):
            return self.current_view.get_selected_items()
        return []
    
    def get_result_config(self) -> ResultConfig:
        """获取结果配置
        
        Returns:
            ResultConfig: 结果配置
        """
        return self.result_config
    
    def set_result_config(self, config: ResultConfig):
        """设置结果配置
        
        Args:
            config: 结果配置
        """
        try:
            # 确保config.items_per_page不为0或负数
            if config.items_per_page <= 0:
                self.logger.warning(f"结果配置中每页显示数量为无效值: {config.items_per_page}，已重置为默认值25")
                config.items_per_page = 25
                
            self.result_config = config
            
            # 更新界面
            self.set_view_mode(config.view_mode)
            self.size_slider.setValue(config.thumbnail_size)
            self.per_page_combo.setCurrentText(str(config.items_per_page))
            
            # 更新显示
            self.calculate_pagination()
            self.update_display()
            
        except Exception as e:
            self.logger.error(f"设置结果配置失败: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
    
    def update_results(self, results):
        """更新搜索结果
        
        Args:
            results: 搜索结果列表
        """
        try:
            from search.search_engine import SearchResult as EngineSearchResult
            
            # 检查是否是SearchResult对象
            if isinstance(results, EngineSearchResult):
                self.logger.info(f"接收到SearchResult对象，总结果数: {results.total_results}，当前页结果数: {len(results.results)}")
                # 直接传递SearchResult对象
                self.set_results(results)
            else:
                # 处理列表类型的结果
                self.logger.info(f"更新搜索结果: {len(results)} 个")
                self.set_results(results)
        except Exception as e:
            self.logger.error(f"更新搜索结果失败: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
    
    def _get_current_search_weights(self):
        """获取当前搜索的特征权重"""
        try:
            # 尝试从主窗口获取当前搜索权重
            if hasattr(self, 'parent') and self.parent:
                main_window = self.parent
                if hasattr(main_window, 'search_panel') and main_window.search_panel:
                    # 直接从相似度搜索组件获取当前权重
                    similarity_widget = main_window.search_panel.similarity_widget
                    if hasattr(similarity_widget, 'feature_weights_widget'):
                        current_weights = similarity_widget.feature_weights_widget.get_weights()
                        self.logger.info(f"从GUI获取的当前权重: {current_weights}")
                        return current_weights

            # 如果无法获取，返回默认权重
            self.logger.warning("无法获取当前搜索权重，使用默认权重")
            return {'deep_learning': 0.25, 'color': 0.25, 'texture': 0.25, 'shape': 0.25}
        except Exception as e:
            self.logger.error(f"获取当前搜索权重失败: {e}")
            return {'deep_learning': 0.25, 'color': 0.25, 'texture': 0.25, 'shape': 0.25}

    def _update_display_feature_scores(self, fabric_image, current_weights):
        """根据当前权重更新显示的特征分数

        Args:
            fabric_image: FabricImage对象
            current_weights: 当前搜索权重字典
        """
        try:
            if not current_weights:
                return

            # 获取原始特征分数（这些是实际的相似度分数）
            original_scores = {}
            if hasattr(fabric_image, 'feature_scores') and fabric_image.feature_scores:
                original_scores = fabric_image.feature_scores.copy()
                self.logger.info(f"原始特征分数: {original_scores}")

            # 如果没有原始特征分数，保持不变
            if not original_scores:
                self.logger.warning("没有找到原始特征分数，跳过更新")
                return

            # 根据当前权重决定显示哪些特征分数
            display_scores = {}

            # 检查哪些特征权重大于0
            active_features = [k for k, v in current_weights.items() if v > 0]
            self.logger.info(f"激活的特征: {active_features}")

            # 为每个特征设置显示分数
            for feature_type in ['deep_learning', 'color', 'texture', 'shape']:
                # 将权重键名映射到分数键名
                score_key = feature_type if feature_type != 'deep_learning' else 'deep'

                if feature_type in current_weights:
                    weight = current_weights[feature_type]

                    if weight > 0:
                        # 如果权重大于0，显示原始特征分数
                        if score_key in original_scores:
                            display_scores[score_key] = original_scores[score_key]
                        else:
                            # 如果没有对应的原始分数，使用相似度分数
                            display_scores[score_key] = getattr(fabric_image, 'similarity_score', 0.0)
                    else:
                        # 如果权重为0，显示0分数（表示该特征未参与搜索）
                        display_scores[score_key] = 0.0
                else:
                    # 如果权重字典中没有该特征，使用原始分数
                    if score_key in original_scores:
                        display_scores[score_key] = original_scores[score_key]
                    else:
                        display_scores[score_key] = 0.0

            # 更新fabric_image的feature_scores用于显示
            fabric_image.feature_scores = display_scores

            self.logger.info(f"更新显示特征分数: {display_scores}")

        except Exception as e:
            self.logger.error(f"更新显示特征分数失败: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")

    def cleanup(self):
        """清理资源"""
        try:
            self.clear_results()
            self.logger.debug("结果面板资源清理完成")
        except Exception as e:
            self.logger.error(f"结果面板资源清理失败: {e}")