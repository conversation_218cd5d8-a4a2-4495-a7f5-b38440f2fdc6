#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证模块

提供各种数据验证功能，包括：
- 图像路径验证
- 特征数据验证
- 搜索请求验证
- 配置验证
"""

import os
import numpy as np
from pathlib import Path
from typing import Any, Dict, List, Optional, Union


def validate_image_path(image_path: Union[str, Path]) -> bool:
    """
    验证图像路径是否有效
    
    Args:
        image_path: 图像文件路径
        
    Returns:
        bool: 路径是否有效
    """
    if not image_path:
        return False
        
    path = Path(image_path)
    
    # 检查文件是否存在
    if not path.exists():
        return False
        
    # 检查是否为文件
    if not path.is_file():
        return False
        
    # 检查文件扩展名
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    if path.suffix.lower() not in valid_extensions:
        return False
        
    return True


def validate_features(features: Any) -> bool:
    """
    验证特征数据是否有效
    
    Args:
        features: 特征数据
        
    Returns:
        bool: 特征数据是否有效
    """
    if features is None:
        return False
        
    # 如果是numpy数组
    if isinstance(features, np.ndarray):
        # 检查维度
        if features.ndim == 0 or features.size == 0:
            return False
        # 检查是否包含NaN或无穷大
        if np.any(np.isnan(features)) or np.any(np.isinf(features)):
            return False
        return True
        
    # 如果是列表
    if isinstance(features, (list, tuple)):
        if len(features) == 0:
            return False
        # 检查所有元素是否为数字
        try:
            features_array = np.array(features, dtype=float)
            return validate_features(features_array)
        except (ValueError, TypeError):
            return False
            
    return False


def validate_search_request(request: Dict[str, Any]) -> bool:
    """
    验证搜索请求是否有效
    
    Args:
        request: 搜索请求字典
        
    Returns:
        bool: 请求是否有效
    """
    if not isinstance(request, dict):
        return False
        
    # 检查必需字段
    required_fields = ['query_type']
    for field in required_fields:
        if field not in request:
            return False
            
    query_type = request.get('query_type')
    
    # 根据查询类型验证特定字段
    if query_type == 'image':
        if 'image_path' not in request:
            return False
        if not validate_image_path(request['image_path']):
            return False
    elif query_type == 'features':
        if 'features' not in request:
            return False
        if not validate_features(request['features']):
            return False
    else:
        return False
        
    # 验证可选参数
    if 'top_n' in request:
        top_n = request['top_n']
        if not isinstance(top_n, int) or top_n <= 0:
            return False
            
    if 'threshold' in request:
        threshold = request['threshold']
        if not isinstance(threshold, (int, float)) or threshold < 0 or threshold > 1:
            return False
            
    return True


def validate_config(config: Dict[str, Any]) -> bool:
    """
    验证配置是否有效
    
    Args:
        config: 配置字典
        
    Returns:
        bool: 配置是否有效
    """
    if not isinstance(config, dict):
        return False
        
    # 验证模型配置
    if 'model' in config:
        model_config = config['model']
        if not isinstance(model_config, dict):
            return False
            
        # 检查模型名称
        if 'name' in model_config:
            if not isinstance(model_config['name'], str) or not model_config['name']:
                return False
                
        # 检查设备配置
        if 'device' in model_config:
            device = model_config['device']
            if device not in ['cpu', 'cuda', 'auto']:
                return False
                
    # 验证数据库配置
    if 'database' in config:
        db_config = config['database']
        if not isinstance(db_config, dict):
            return False
            
        # 检查数据库路径
        if 'path' in db_config:
            if not isinstance(db_config['path'], str) or not db_config['path']:
                return False
                
    # 验证搜索配置
    if 'search' in config:
        search_config = config['search']
        if not isinstance(search_config, dict):
            return False
            
        # 检查默认top_n
        if 'default_top_n' in search_config:
            top_n = search_config['default_top_n']
            if not isinstance(top_n, int) or top_n <= 0:
                return False
                
        # 检查默认阈值
        if 'default_threshold' in search_config:
            threshold = search_config['default_threshold']
            if not isinstance(threshold, (int, float)) or threshold < 0 or threshold > 1:
                return False
                
    return True


def validate_batch_size(batch_size: int) -> bool:
    """
    验证批处理大小是否有效
    
    Args:
        batch_size: 批处理大小
        
    Returns:
        bool: 批处理大小是否有效
    """
    return isinstance(batch_size, int) and batch_size > 0


def validate_model_name(model_name: str) -> bool:
    """
    验证模型名称是否有效
    
    Args:
        model_name: 模型名称
        
    Returns:
        bool: 模型名称是否有效
    """
    if not isinstance(model_name, str) or not model_name:
        return False
        
    # 支持的模型列表
    supported_models = {
        'resnet50', 'resnet101', 'resnet152',
        'vgg16', 'vgg19',
        'densenet121', 'densenet169', 'densenet201',
        'efficientnet_b0', 'efficientnet_b1', 'efficientnet_b2',
        'mobilenet_v2', 'mobilenet_v3_large', 'mobilenet_v3_small'
    }
    
    return model_name.lower() in supported_models


def validate_similarity_threshold(threshold: float) -> bool:
    """
    验证相似度阈值是否有效
    
    Args:
        threshold: 相似度阈值
        
    Returns:
        bool: 阈值是否有效
    """
    return isinstance(threshold, (int, float)) and 0.0 <= threshold <= 1.0