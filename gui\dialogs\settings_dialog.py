#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置对话框

该模块提供应用程序设置对话框的主要实现。
"""

import os
from typing import Dict, Any, Optional, List, Tuple

from PyQt6.QtWidgets import (
    QDialog, QTabWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QDialogButtonBox, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon

from config.config_manager import ConfigManager
from utils.logger_mixin import LoggerMixin
from gui.helpers.message_helper import MessageHelper
from gui.helpers.gui_utils import center_window

from .settings_base import SettingsCategory, SettingsData, BaseSettingsWidget
from .settings_general import GeneralSettingsWidget
from .settings_search import SearchSettingsWidget
from .settings_display import DisplaySettingsWidget
from .settings_performance import PerformanceSettingsWidget
from .settings_advanced import AdvancedSettingsWidget


class SettingsDialog(QDialog, LoggerMixin):
    """设置对话框"""
    
    # 信号
    settingsChanged = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config_manager = ConfigManager()
        self.message_helper = MessageHelper()
        
        # 设置数据
        self.settings_data = SettingsData()
        self.original_settings = {}
        
        # 设置页面
        self.settings_widgets = {}
        
        self.setup_ui()
        self.load_settings()
        
        # 设置窗口属性
        self.setWindowTitle("设置")
        self.setMinimumSize(600, 500)
        self.resize(700, 550)
        center_window(self)
    
    def setup_ui(self):
        """设置界面"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 选项卡
        self.tab_widget = QTabWidget()
        
        # 创建设置页面
        self.create_settings_widgets()
        
        # 添加选项卡
        self.add_settings_tabs()
        
        layout.addWidget(self.tab_widget)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        # 恢复默认按钮
        self.reset_button = QPushButton("恢复默认")
        self.reset_button.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        # 标准按钮
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel |
            QDialogButtonBox.StandardButton.Apply
        )
        
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.button_box.button(QDialogButtonBox.StandardButton.Apply).clicked.connect(self.apply_settings)
        
        button_layout.addWidget(self.button_box)
        
        layout.addLayout(button_layout)
    
    def create_settings_widgets(self):
        """创建设置页面组件"""
        self.settings_widgets = {
            SettingsCategory.GENERAL: GeneralSettingsWidget(self),
            SettingsCategory.SEARCH: SearchSettingsWidget(self),
            SettingsCategory.DISPLAY: DisplaySettingsWidget(self),
            SettingsCategory.PERFORMANCE: PerformanceSettingsWidget(self),
            SettingsCategory.ADVANCED: AdvancedSettingsWidget(self)
        }
    
    def add_settings_tabs(self):
        """添加设置选项卡"""
        # 定义选项卡顺序和图标
        tab_info = [
            (SettingsCategory.GENERAL, "常规", "icons/settings/general.png"),
            (SettingsCategory.SEARCH, "搜索", "icons/settings/search.png"),
            (SettingsCategory.DISPLAY, "显示", "icons/settings/display.png"),
            (SettingsCategory.PERFORMANCE, "性能", "icons/settings/performance.png"),
            (SettingsCategory.ADVANCED, "高级", "icons/settings/advanced.png")
        ]
        
        # 添加选项卡
        for category, title, icon_path in tab_info:
            widget = self.settings_widgets.get(category)
            if widget:
                icon = QIcon(icon_path) if os.path.exists(icon_path) else QIcon()
                self.tab_widget.addTab(widget, icon, title)
    
    def load_settings(self):
        """加载设置"""
        try:
            # 从配置文件加载设置
            config = self.config_manager.get_config()
            
            # 将SystemConfig对象转换为字典
            config_dict = {}
            for key in dir(config):
                # 跳过私有属性和方法
                if not key.startswith('_') and not callable(getattr(config, key)):
                    config_dict[key] = getattr(config, key)
            
            # 将配置转换为设置数据
            self.settings_data = SettingsData.from_dict(config_dict)
            
            # 保存原始设置用于比较
            self.original_settings = self.settings_data.to_dict()
            
            # 更新各设置页面
            self.update_settings_widgets()
            
            self.logger.debug("设置已加载")
        except Exception as e:
            self.logger.error(f"加载设置时出错: {e}")
            self.message_helper.show_error(self, "加载设置失败", f"无法加载设置: {str(e)}")
    
    def update_settings_widgets(self):
        """更新设置页面组件"""
        settings_dict = self.settings_data.to_dict()
        
        for category, widget in self.settings_widgets.items():
            try:
                widget.set_settings(settings_dict)
            except Exception as e:
                self.logger.error(f"更新{category.name}设置页面时出错: {e}")
    
    def set_settings_data(self, settings_data):
        """设置设置数据
        
        Args:
            settings_data: 设置数据对象
        """
        self.settings_data = settings_data
        self.original_settings = settings_data.to_dict()
        self.update_settings_widgets()
    
    def collect_settings_from_widgets(self) -> Dict[str, Any]:
        """从设置页面组件收集设置"""
        settings = {}
        
        for category, widget in self.settings_widgets.items():
            try:
                widget_settings = widget.get_settings()
                settings.update(widget_settings)
            except Exception as e:
                self.logger.error(f"从{category.name}设置页面收集设置时出错: {e}")
        
        return settings
    
    def apply_settings(self):
        """应用设置"""
        try:
            # 收集设置
            settings = self.collect_settings_from_widgets()
            
            # 更新设置数据
            self.settings_data = SettingsData.from_dict(settings)
            
            # 保存设置
            self.save_settings()
            
            # 发送设置已更改信号
            self.settingsChanged.emit(settings)
            
            self.logger.debug("设置已应用")
        except Exception as e:
            self.logger.error(f"应用设置时出错: {e}")
            self.message_helper.show_error(self, "应用设置失败", f"无法应用设置: {str(e)}")
    
    def save_settings(self):
        """保存设置"""
        try:
            # 获取设置数据
            settings = self.settings_data.to_dict()
            
            # 保存到配置文件
            self.config_manager.update_config(settings)
            self.config_manager.save_config()
            
            # 更新原始设置
            self.original_settings = settings.copy()
            
            self.logger.debug("设置已保存")
        except Exception as e:
            self.logger.error(f"保存设置时出错: {e}")
            self.message_helper.show_error(self, "保存设置失败", f"无法保存设置: {str(e)}")
    
    def reset_to_defaults(self):
        """恢复默认设置"""
        # 确认对话框
        reply = self.message_helper.show_question(
            self,
            "恢复默认设置",
            "确定要将所有设置恢复为默认值吗？这将丢失所有自定义设置。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 创建默认设置数据
                self.settings_data = SettingsData()
                
                # 更新设置页面
                self.update_settings_widgets()
                
                self.logger.debug("设置已恢复为默认值")
            except Exception as e:
                self.logger.error(f"恢复默认设置时出错: {e}")
                self.message_helper.show_error(self, "恢复默认设置失败", f"无法恢复默认设置: {str(e)}")
    
    def accept(self):
        """确定按钮处理"""
        # 应用设置
        self.apply_settings()
        
        # 关闭对话框
        super().accept()
    
    def reject(self):
        """取消按钮处理"""
        # 检查设置是否已更改
        current_settings = self.collect_settings_from_widgets()
        if current_settings != self.original_settings:
            # 确认对话框
            reply = self.message_helper.show_question(
                self,
                "放弃更改",
                "您有未保存的更改。确定要放弃这些更改吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.No:
                return
        
        # 关闭对话框
        super().reject()