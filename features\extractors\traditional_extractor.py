"""
传统特征提取器模块

该模块提供传统图像特征提取功能，包括颜色、纹理和形状特征。
重构后使用模块化设计，将不同类型的特征提取器分离到独立模块中。
"""

from .traditional_extractor_core import TraditionalFeatureExtractor as CoreExtractor
from .color_extractor import ColorFeatureExtractor
from .texture_extractor import TextureFeatureExtractor
from .shape_extractor import ShapeFeatureExtractor


class TraditionalFeatureExtractor:
    """传统特征提取器主类（向后兼容包装器）"""
    
    def __init__(self, config):
        """初始化传统特征提取器
        
        Args:
            config: 传统特征配置
        """
        self._core = CoreExtractor(config)
        self.config = config
    
    def extract_features(self, image):
        """提取所有传统特征
        
        Args:
            image: 输入图像
            
        Returns:
            Dict[str, np.ndarray]: 特征字典
        """
        return self._core.extract_features(image)
    
    def extract_combined_features(self, image):
        """提取合并的传统特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 合并的特征向量
        """
        return self._core.extract_combined_features(image)
    
    @property
    def color_extractor(self):
        """获取颜色特征提取器"""
        return self._core.color_extractor
    
    @property
    def texture_extractor(self):
        """获取纹理特征提取器"""
        return self._core.texture_extractor
    
    @property
    def shape_extractor(self):
        """获取形状特征提取器"""
        return self._core.shape_extractor


# 导出所有类以保持向后兼容性
__all__ = [
    'TraditionalFeatureExtractor',
    'ColorFeatureExtractor', 
    'TextureFeatureExtractor',
    'ShapeFeatureExtractor'
]