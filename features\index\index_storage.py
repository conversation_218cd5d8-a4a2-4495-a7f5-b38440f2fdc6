#!/usr/bin/env python3
"""
索引存储模块

提供特征索引的持久化存储功能，支持多种存储格式和压缩选项。
"""

import os
import json
import pickle
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
from datetime import datetime

import numpy as np

from .feature_index import FeatureIndex


logger = logging.getLogger(__name__)


class IndexStorage:
    """
    索引存储管理器
    
    负责特征索引的保存、加载和管理，支持多种存储格式。
    """
    
    SUPPORTED_FORMATS = ['json', 'pickle', 'npz']
    DEFAULT_FORMAT = 'pickle'
    
    def __init__(self, storage_dir: str, format: str = DEFAULT_FORMAT):
        """
        初始化索引存储管理器
        
        Args:
            storage_dir: 存储目录路径
            format: 存储格式 ('json', 'pickle', 'npz')
        """
        self.storage_dir = Path(storage_dir)
        self.format = format.lower()
        
        if self.format not in self.SUPPORTED_FORMATS:
            raise ValueError(f"不支持的存储格式: {format}. 支持的格式: {self.SUPPORTED_FORMATS}")
        
        # 创建存储目录
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # 存储元数据
        self.metadata_file = self.storage_dir / "metadata.json"
        
        logger.info(f"初始化索引存储管理器: {self.storage_dir}, 格式: {self.format}")
    
    def save_index(self, index: FeatureIndex, name: str, 
                   metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        保存特征索引
        
        Args:
            index: 要保存的特征索引
            name: 索引名称
            metadata: 额外的元数据
            
        Returns:
            保存的文件路径
        """
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{name}_{timestamp}"
            
            if self.format == 'json':
                filepath = self._save_as_json(index, filename)
            elif self.format == 'pickle':
                filepath = self._save_as_pickle(index, filename)
            elif self.format == 'npz':
                filepath = self._save_as_npz(index, filename)
            else:
                raise ValueError(f"不支持的存储格式: {self.format}")
            
            # 保存元数据
            self._save_metadata(name, filepath, index, metadata)
            
            logger.info(f"成功保存索引: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"保存索引失败: {e}")
            raise
    
    def load_index(self, name: str, version: Optional[str] = None) -> FeatureIndex:
        """
        加载特征索引
        
        Args:
            name: 索引名称
            version: 版本标识，如果为None则加载最新版本
            
        Returns:
            加载的特征索引
        """
        try:
            # 获取文件路径
            filepath = self._get_index_filepath(name, version)
            
            if not filepath.exists():
                raise FileNotFoundError(f"索引文件不存在: {filepath}")
            
            # 根据格式加载
            if self.format == 'json':
                index = self._load_from_json(filepath)
            elif self.format == 'pickle':
                index = self._load_from_pickle(filepath)
            elif self.format == 'npz':
                index = self._load_from_npz(filepath)
            else:
                raise ValueError(f"不支持的存储格式: {self.format}")
            
            logger.info(f"成功加载索引: {filepath}")
            return index
            
        except Exception as e:
            logger.error(f"加载索引失败: {e}")
            raise
    
    def list_indices(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有可用的索引
        
        Returns:
            索引信息字典
        """
        try:
            if not self.metadata_file.exists():
                return {}
            
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            return metadata.get('indices', {})
            
        except Exception as e:
            logger.error(f"列出索引失败: {e}")
            return {}
    
    def delete_index(self, name: str, version: Optional[str] = None) -> bool:
        """
        删除索引
        
        Args:
            name: 索引名称
            version: 版本标识，如果为None则删除所有版本
            
        Returns:
            是否删除成功
        """
        try:
            if version:
                # 删除特定版本
                filepath = self._get_index_filepath(name, version)
                if filepath.exists():
                    filepath.unlink()
                    self._remove_from_metadata(name, version)
                    logger.info(f"删除索引版本: {name}_{version}")
                    return True
            else:
                # 删除所有版本
                indices = self.list_indices()
                if name in indices:
                    for version_info in indices[name].values():
                        filepath = Path(version_info['filepath'])
                        if filepath.exists():
                            filepath.unlink()
                    self._remove_from_metadata(name)
                    logger.info(f"删除索引所有版本: {name}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"删除索引失败: {e}")
            return False
    
    def get_storage_info(self) -> Dict[str, Any]:
        """
        获取存储信息
        
        Returns:
            存储信息字典
        """
        try:
            indices = self.list_indices()
            total_size = 0
            file_count = 0
            
            for index_name, versions in indices.items():
                for version, info in versions.items():
                    filepath = Path(info['filepath'])
                    if filepath.exists():
                        total_size += filepath.stat().st_size
                        file_count += 1
            
            return {
                'storage_dir': str(self.storage_dir),
                'format': self.format,
                'total_indices': len(indices),
                'total_files': file_count,
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2)
            }
            
        except Exception as e:
            logger.error(f"获取存储信息失败: {e}")
            return {}
    
    def _save_as_json(self, index: FeatureIndex, filename: str) -> Path:
        """保存为JSON格式"""
        filepath = self.storage_dir / f"{filename}.json"
        
        # 转换为可序列化的格式
        data = index.to_dict()
        
        # 处理numpy数组
        for entry_id, entry_data in data['entries'].items():
            if 'features' in entry_data and isinstance(entry_data['features'], np.ndarray):
                entry_data['features'] = entry_data['features'].tolist()
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        return filepath
    
    def _save_as_pickle(self, index: FeatureIndex, filename: str) -> Path:
        """保存为Pickle格式"""
        filepath = self.storage_dir / f"{filename}.pkl"
        
        with open(filepath, 'wb') as f:
            pickle.dump(index, f, protocol=pickle.HIGHEST_PROTOCOL)
        
        return filepath
    
    def _save_as_npz(self, index: FeatureIndex, filename: str) -> Path:
        """保存为NPZ格式"""
        filepath = self.storage_dir / f"{filename}.npz"
        
        # 准备数据
        data = index.to_dict()
        features_dict = {}
        metadata_dict = {}
        
        for entry_id, entry_data in data['entries'].items():
            if 'features' in entry_data and isinstance(entry_data['features'], np.ndarray):
                features_dict[f"features_{entry_id}"] = entry_data['features']
            
            # 保存其他元数据
            metadata_dict[entry_id] = {
                k: v for k, v in entry_data.items() if k != 'features'
            }
        
        # 保存特征矩阵和元数据
        np.savez_compressed(
            filepath,
            metadata=json.dumps({
                'index_metadata': data['metadata'],
                'entries_metadata': metadata_dict
            }),
            **features_dict
        )
        
        return filepath
    
    def _load_from_json(self, filepath: Path) -> FeatureIndex:
        """从JSON格式加载"""
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 转换特征数据
        for entry_id, entry_data in data['entries'].items():
            if 'features' in entry_data and isinstance(entry_data['features'], list):
                entry_data['features'] = np.array(entry_data['features'])
        
        return FeatureIndex.from_dict(data)
    
    def _load_from_pickle(self, filepath: Path) -> FeatureIndex:
        """从Pickle格式加载"""
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    
    def _load_from_npz(self, filepath: Path) -> FeatureIndex:
        """从NPZ格式加载"""
        data = np.load(filepath, allow_pickle=True)
        
        # 加载元数据
        metadata_str = str(data['metadata'])
        metadata = json.loads(metadata_str)
        
        # 重建索引
        index = FeatureIndex()
        index.metadata = metadata['index_metadata']
        
        # 重建条目
        for entry_id, entry_metadata in metadata['entries_metadata'].items():
            features_key = f"features_{entry_id}"
            if features_key in data:
                entry_metadata['features'] = data[features_key]
            
            from .feature_index import IndexEntry
            entry = IndexEntry(**entry_metadata)
            index.entries[entry_id] = entry
        
        return index
    
    def _get_index_filepath(self, name: str, version: Optional[str] = None) -> Path:
        """获取索引文件路径"""
        indices = self.list_indices()
        
        if name not in indices:
            raise ValueError(f"索引不存在: {name}")
        
        if version:
            if version not in indices[name]:
                raise ValueError(f"索引版本不存在: {name}_{version}")
            return Path(indices[name][version]['filepath'])
        else:
            # 获取最新版本
            versions = indices[name]
            if not versions:
                raise ValueError(f"索引没有可用版本: {name}")
            
            latest_version = max(versions.keys(), key=lambda v: versions[v]['created_at'])
            return Path(versions[latest_version]['filepath'])
    
    def _save_metadata(self, name: str, filepath: Path, index: FeatureIndex, 
                      metadata: Optional[Dict[str, Any]] = None):
        """保存元数据"""
        # 加载现有元数据
        if self.metadata_file.exists():
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                all_metadata = json.load(f)
        else:
            all_metadata = {'indices': {}}
        
        # 生成版本标识
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 更新元数据
        if name not in all_metadata['indices']:
            all_metadata['indices'][name] = {}
        
        all_metadata['indices'][name][timestamp] = {
            'filepath': str(filepath),
            'format': self.format,
            'created_at': datetime.now().isoformat(),
            'entry_count': len(index.entries),
            'feature_dimensions': index.get_feature_dimensions(),
            'metadata': metadata or {}
        }
        
        # 保存元数据
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(all_metadata, f, ensure_ascii=False, indent=2)
    
    def _remove_from_metadata(self, name: str, version: Optional[str] = None):
        """从元数据中移除索引信息"""
        if not self.metadata_file.exists():
            return
        
        with open(self.metadata_file, 'r', encoding='utf-8') as f:
            all_metadata = json.load(f)
        
        if name in all_metadata['indices']:
            if version:
                all_metadata['indices'][name].pop(version, None)
                if not all_metadata['indices'][name]:
                    all_metadata['indices'].pop(name)
            else:
                all_metadata['indices'].pop(name)
        
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(all_metadata, f, ensure_ascii=False, indent=2)