#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像信息显示组件

该模块提供图像信息的显示组件。
"""

from typing import Optional
from PyQt6.QtWidgets import QWidget, QHBoxLayout, QLabel
from PyQt6.QtCore import Qt

from gui.widgets.widget_factory import WidgetFactory
from .compare_config import ImageInfo


class ImageInfoWidget(QWidget):
    """图像信息显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.widget_factory = WidgetFactory()
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # 图像1信息
        self.image1_label = self.widget_factory.create_label("图像1: 未选择")
        layout.addWidget(self.image1_label)
        
        # 弹性空间
        layout.addStretch()
        
        # 图像2信息
        self.image2_label = self.widget_factory.create_label("图像2: 未选择")
        layout.addWidget(self.image2_label)
    
    def update_image_info(self, index: int, image_info: Optional[ImageInfo]):
        """更新图像信息
        
        Args:
            index: 图像索引 (0或1)
            image_info: 图像信息对象
        """
        if image_info:
            display_text = f"图像{index + 1}: {image_info.get_display_text()}"
        else:
            display_text = f"图像{index + 1}: 未选择或文件不存在"
        
        if index == 0:
            self.image1_label.setText(display_text)
        elif index == 1:
            self.image2_label.setText(display_text)
    
    def clear_info(self):
        """清空信息"""
        self.image1_label.setText("图像1: 未选择")
        self.image2_label.setText("图像2: 未选择")