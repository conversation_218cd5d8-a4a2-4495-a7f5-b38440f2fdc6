import pytest
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication, QProgressDialog
from PyQt5.QtCore import QThread

from ui.database_panel import DatabasePanel

class TestDatabaseManagement:
    """数据库管理功能测试"""
    
    @pytest.fixture
    def app(self):
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def db_panel(self, app):
        panel = DatabasePanel()
        yield panel
        
    def test_panel_initialization(self, db_panel):
        """测试数据库面板初始化"""
        assert hasattr(db_panel, 'create_db_btn')
        assert hasattr(db_panel, 'sync_db_btn')
        assert hasattr(db_panel, 'db_path_edit')
        assert hasattr(db_panel, 'folder_path_edit')
        
    @patch('PyQt5.QtWidgets.QFileDialog.getExistingDirectory')
    def test_folder_selection(self, mock_dialog, db_panel):
        """测试文件夹选择"""
        mock_dialog.return_value = '/test/images'
        
        db_panel.select_folder_btn.click()
        assert db_panel.folder_path_edit.text() == '/test/images'
        
    @patch('PyQt5.QtWidgets.QFileDialog.getSaveFileName')
    def test_database_path_selection(self, mock_dialog, db_panel):
        """测试数据库路径选择"""
        mock_dialog.return_value = ('/test/database.db', 'Database Files (*.db)')
        
        db_panel.select_db_btn.click()
        assert db_panel.db_path_edit.text() == '/test/database.db'
        
    @patch('database.feature_database.FeatureDatabase')
    def test_create_database(self, mock_db_class, db_panel):
        """测试创建数据库"""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        
        # 设置必要的路径
        db_panel.folder_path_edit.setText('/test/images')
        db_panel.db_path_edit.setText('/test/database.db')
        
        # 模拟创建数据库
        with patch.object(db_panel, 'show_progress_dialog'):
            db_panel.create_database()
            
        # 验证数据库创建被调用
        mock_db_class.assert_called_once()
        
    @patch('database.feature_database.FeatureDatabase')
    def test_sync_database(self, mock_db_class, db_panel):
        """测试同步数据库"""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        
        # 设置必要的路径
        db_panel.folder_path_edit.setText('/test/images')
        db_panel.db_path_edit.setText('/test/database.db')
        
        # 模拟同步数据库
        with patch.object(db_panel, 'show_progress_dialog'):
            db_panel.sync_database()
            
        # 验证同步被调用
        mock_db.sync_with_folder.assert_called_once()
        
    def test_progress_dialog(self, db_panel):
        """测试进度对话框"""
        progress_dialog = db_panel.show_progress_dialog("测试操作")
        
        assert isinstance(progress_dialog, QProgressDialog)
        assert progress_dialog.labelText() == "测试操作"
        
    def test_model_selection(self, db_panel):
        """测试模型选择"""
        model_combo = db_panel.model_combo
        expected_models = ["resnet50", "vgg16", "efficientnet", "vit", "swin"]
        
        actual_models = [model_combo.itemText(i) for i in range(model_combo.count())]
        for model in expected_models:
            assert model in actual_models
            
    def test_gpu_option(self, db_panel):
        """测试GPU选项"""
        gpu_checkbox = db_panel.use_gpu_checkbox
        
        # 测试启用GPU
        gpu_checkbox.setChecked(True)
        assert gpu_checkbox.isChecked() == True
        
        # 测试禁用GPU
        gpu_checkbox.setChecked(False)
        assert gpu_checkbox.isChecked() == False