# 布料图片相似度搜索系统

## 项目概述

布料图片相似度搜索系统是一个专为纺织行业设计的图像检索工具，能够基于多种特征维度（颜色、纹理、形状和深度学习特征）对布料图片进行相似度搜索。系统采用现代化的图形用户界面，支持GPU加速，提供高效、精准的布料图像检索服务。

## 功能特性

### 核心功能

- **多维度特征提取**：同时提取并分析布料图片的颜色、纹理、形状和深度学习特征
- **多模型支持**：支持多种深度学习模型，包括ResNet50、VGG16、EfficientNet、ViT等
- **GPU加速**：支持GPU加速特征提取和相似度计算，大幅提升处理速度
- **自适应权重**：根据查询图像特点自动调整各特征维度的权重
- **查询扩展**：通过初步搜索结果扩展查询，提高检索准确性
- **用户反馈学习**：根据用户对搜索结果的反馈优化后续搜索

### 数据库功能

- **多种数据库类型**：支持标准数据库、分块数据库和向量数据库
- **增量同步**：支持增量更新特征数据库，避免重复提取特征
- **兼容性检查**：自动检查数据库与当前模型的兼容性
- **数据压缩**：支持特征数据压缩，减少存储空间占用

### 用户界面

- **直观的图形界面**：提供用户友好的操作界面
- **多视图显示**：支持详细视图和网格视图两种结果展示方式
- **实时进度反馈**：在耗时操作中提供实时进度反馈
- **结果排序**：支持按不同相似度指标对结果进行排序
- **图像比较**：支持并排比较查询图像和搜索结果
- **结果导出**：支持将搜索结果导出为HTML报告

## 系统架构

系统采用模块化设计，主要包括以下几个核心模块：

### 1. 特征提取模块

- **深度学习特征提取**：使用预训练的深度学习模型提取图像高级特征
- **传统特征提取**：提取图像的颜色、纹理和形状特征
- **批处理优化**：支持批量特征提取，提高处理效率
- **PCA降维**：支持对高维特征进行PCA降维，提高检索效率

### 2. 数据库模块

- **特征存储**：高效存储和管理图像特征数据
- **增量更新**：支持增量更新特征数据库
- **向量索引**：支持Faiss向量索引，加速相似度搜索
- **用户反馈存储**：记录和利用用户反馈信息

### 3. 搜索引擎模块

- **多策略搜索**：支持加权搜索、自适应搜索和查询扩展搜索
- **相似度计算**：高效计算不同特征维度的相似度
- **结果融合**：智能融合多维度特征的相似度结果
- **GPU加速**：支持GPU加速相似度计算

### 4. 用户界面模块

- **主界面**：提供直观的操作界面
- **搜索配置**：支持灵活配置搜索参数
- **结果展示**：支持多种方式展示搜索结果
- **数据库管理**：提供数据库创建和同步功能
- **导出功能**：支持导出搜索结果

## 安装指南

### 系统要求

- Python 3.8或更高版本
- CUDA支持（可选，用于GPU加速）
- 足够的存储空间用于特征数据库

### 依赖安装

```bash
pip install -r requirements.txt
```

### GPU加速配置（可选）

如需启用GPU加速，请确保已安装支持CUDA的PyTorch版本：

```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 使用说明

### 图形界面模式

1. 运行启动脚本：
   - 普通模式：双击`启动布料搜索.bat`
   - GPU加速模式：双击`启动布料搜索(GPU加速).bat`

2. 在主界面上：
   - 选择或创建特征数据库
   - 选择查询图像
   - 配置搜索参数（可选）
   - 点击"搜索"按钮开始搜索

### 命令行模式

系统也支持命令行操作，适合批处理或集成到其他系统中：

```bash
# 创建特征数据库
python main.py --mode create_db --folder 图像文件夹路径 --db 数据库路径 --model resnet50 --use-gpu

# 同步特征数据库
python main.py --mode sync_db --folder 图像文件夹路径 --db 数据库路径 --use-gpu

# 搜索图像
python main.py --mode search --query 查询图像路径 --db 数据库路径 --top-n 10 --use-gpu
```

## 高级功能

### 模型微调

系统支持对预训练模型进行微调，以适应特定类型的布料图像：

```bash
python main.py --tune_model --folder 训练图像文件夹 --tuned_model_path 微调模型保存路径 --epochs 10 --learning_rate 0.001
```

### 向量数据库配置

对于大规模图像库，可以使用向量数据库加速搜索：

```bash
python main.py --mode create_db --folder 图像文件夹路径 --db_type vector --vector_index_type IVF100,Flat --vector_metric L2
```

### 查询扩展搜索

使用查询扩展提高搜索准确性：

```bash
python main.py --mode search --query 查询图像路径 --expansion --expansion-count 3
```

## 常见问题解答

**Q: 如何选择合适的特征提取模型？**

A: 不同模型在不同类型的布料图像上表现各异。一般来说，ResNet50是一个较好的通用选择，而对于细节丰富的布料图像，VGG16可能表现更好。您可以通过实验比较不同模型的效果。

**Q: 系统支持哪些图像格式？**

A: 系统支持常见的图像格式，包括JPG、PNG、BMP等。

**Q: 如何提高搜索准确性？**

A: 可以尝试以下方法：
- 调整各特征维度的权重
- 启用自适应权重功能
- 使用查询扩展功能
- 对模型进行微调
- 提供用户反馈

**Q: 如何处理大规模图像库？**

A: 对于大规模图像库，建议：
- 使用向量数据库类型
- 启用GPU加速
- 考虑使用PCA降维
- 使用分块数据库减少内存占用

## 开发者信息

### 代码结构

系统代码采用模块化设计，主要包括以下目录：

- `config/`: 配置文件和接口
- `database/`: 数据库相关功能
- `models/`: 特征提取模型
- `search/`: 搜索引擎功能
- `ui/`: 用户界面组件
- `utils/`: 工具函数

### 扩展开发

系统设计支持灵活扩展：

- 添加新的特征提取模型
- 实现新的搜索策略
- 开发新的数据库类型
- 扩展用户界面功能

## 性能优化

系统已进行多方面的性能优化：

- 批量特征提取和相似度计算
- GPU加速支持
- 向量索引加速搜索
- 特征缓存减少重复计算
- 多线程处理提高响应速度
- 增量数据库更新避免全量重建

## 未来计划

- 支持更多深度学习模型
- 增强用户反馈学习能力
- 添加基于语义的搜索功能
- 优化大规模图像库的处理效率
- 提供云端部署方案
- 开发移动端应用

---

© 2025 布料图片相似度搜索系统 - 保留所有权利