import pytest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt, QTimer

from ui.main_window import MainWindow

class TestIntegration:
    """集成测试"""
    
    @pytest.fixture
    def app(self):
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def main_window(self, app):
        window = MainWindow()
        yield window
        window.close()
        
    @pytest.fixture
    def temp_files(self):
        """创建临时测试文件"""
        temp_dir = tempfile.mkdtemp()
        test_image = os.path.join(temp_dir, 'test.jpg')
        test_db = os.path.join(temp_dir, 'test.db')
        
        # 创建虚拟图像文件
        with open(test_image, 'wb') as f:
            f.write(b'fake_image_data')
            
        yield {
            'temp_dir': temp_dir,
            'test_image': test_image,
            'test_db': test_db
        }
        
        # 清理
        import shutil
        shutil.rmtree(temp_dir)
        
    def test_complete_search_workflow(self, main_window, temp_files):
        """测试完整搜索工作流"""
        with patch('models.feature_extractor.FeatureExtractor') as mock_extractor:
            with patch('database.feature_database.FeatureDatabase') as mock_db:
                with patch('search.search_engine.SearchEngine') as mock_engine:
                    
                    # 配置模拟对象
                    mock_extractor_instance = Mock()
                    mock_extractor.return_value = mock_extractor_instance
                    
                    mock_db_instance = Mock()
                    mock_db.return_value = mock_db_instance
                    
                    mock_engine_instance = Mock()
                    mock_engine.return_value = mock_engine_instance
                    mock_engine_instance.search.return_value = [
                        {'image_path': 'result1.jpg', 'similarity': 0.95},
                        {'image_path': 'result2.jpg', 'similarity': 0.87}
                    ]
                    
                    # 1. 加载查询图像
                    main_window.search_panel.query_image_path = temp_files['test_image']
                    
                    # 2. 配置搜索参数
                    main_window.search_panel.search_mode_combo.setCurrentText("混合搜索")
                    main_window.search_panel.top_n_spin.setValue(10)
                    
                    # 3. 执行搜索
                    main_window.search_panel.perform_search()
                    
                    # 4. 验证搜索结果
                    results = main_window.result_display.current_results
                    assert len(results) == 2
                    assert results[0]['similarity'] == 0.95
                    
    def test_database_creation_workflow(self, main_window, temp_files):
        """测试数据库创建工作流"""
        with patch('database.feature_database.FeatureDatabase') as mock_db:
            with patch('models.feature_extractor.FeatureExtractor') as mock_extractor:
                
                # 配置模拟对象
                mock_db_instance = Mock()
                mock_db.return_value = mock_db_instance
                
                # 1. 设置文件夹路径
                main_window.db_panel.folder_path_edit.setText(temp_files['temp_dir'])
                
                # 2. 设置数据库路径
                main_window.db_panel.db_path_edit.setText(temp_files['test_db'])
                
                # 3. 选择模型
                main_window.db_panel.model_combo.setCurrentText("resnet50")
                
                # 4. 创建数据库
                main_window.db_panel.create_database()
                
                # 5. 验证数据库创建
                mock_db.assert_called_once()
                
    def test_error_handling(self, main_window):
        """测试错误处理"""
        with patch('PyQt5.QtWidgets.QMessageBox.critical') as mock_msgbox:
            
            # 测试无效图像路径
            main_window.search_panel.query_image_path = 'invalid_path.jpg'
            main_window.search_panel.perform_search()
            
            # 验证错误消息显示
            mock_msgbox.assert_called()
            
    def test_ui_responsiveness(self, main_window):
        """测试UI响应性"""
        # 测试长时间操作时UI不冻结
        with patch('PyQt5.QtCore.QThread') as mock_thread:
            mock_thread_instance = Mock()
            mock_thread.return_value = mock_thread_instance
            
            # 模拟长时间操作
            main_window.search_panel.perform_search()
            
            # 验证使用了线程
            mock_thread.assert_called()
            
    def test_memory_management(self, main_window, temp_files):
        """测试内存管理"""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # 执行多次搜索操作
        for i in range(10):
            with patch('search.search_engine.SearchEngine') as mock_engine:
                mock_engine_instance = Mock()
                mock_engine.return_value = mock_engine_instance
                mock_engine_instance.search.return_value = [
                    {'image_path': f'result{j}.jpg', 'similarity': 0.9 - j*0.1}
                    for j in range(100)
                ]
                
                main_window.search_panel.query_image_path = temp_files['test_image']
                main_window.search_panel.perform_search()
                
        # 强制垃圾回收
        gc.collect()
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 验证内存增长在合理范围内（小于100MB）
        assert memory_increase < 100 * 1024 * 1024