#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志混入类模块

提供可以被其他类继承的日志功能。
"""

import logging
from typing import Optional


class LoggerMixin:
    """
    日志混入类，为继承的类提供日志功能
    
    使用方法：
    class MyClass(LoggerMixin):
        def __init__(self):
            # 可以直接使用self.logger
            self.logger.info("初始化完成")
    """
    
    @property
    def logger(self) -> logging.Logger:
        """
        获取当前类的日志器
        
        如果日志器不存在，则创建一个新的日志器
        
        Returns:
            logging.Logger: 日志器实例
        """
        if not hasattr(self, '_logger'):
            self._logger = self.get_logger()
        return self._logger
    
    def get_logger(self, name: Optional[str] = None) -> logging.Logger:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称，默认为类名
            
        Returns:
            logging.Logger: 日志器实例
        """
        if name is None:
            name = self.__class__.__name__
        return logging.getLogger(name)
    
    def log_debug(self, message: str, *args, **kwargs):
        """记录调试信息"""
        self.logger.debug(message, *args, **kwargs)
    
    def log_info(self, message: str, *args, **kwargs):
        """记录信息"""
        self.logger.info(message, *args, **kwargs)
    
    def log_warning(self, message: str, *args, **kwargs):
        """记录警告"""
        self.logger.warning(message, *args, **kwargs)
    
    def log_error(self, message: str, *args, **kwargs):
        """记录错误"""
        self.logger.error(message, *args, **kwargs)
    
    def log_critical(self, message: str, *args, **kwargs):
        """记录严重错误"""
        self.logger.critical(message, *args, **kwargs)
    
    def log_exception(self, message: str, *args, **kwargs):
        """记录异常信息"""
        self.logger.exception(message, *args, **kwargs)