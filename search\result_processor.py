#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索结果处理模块

该模块处理搜索结果的过滤、排序和分页等操作。
"""

from typing import List, Dict, Any, Optional, Tuple
from database.models import FabricImage
from utils.log_utils import LoggerMixin
from .models import SearchQuery
from .search_filters import FilterEngine


class ResultProcessor(LoggerMixin):
    """搜索结果处理器"""
    
    def __init__(self, filter_engine: FilterEngine):
        """初始化结果处理器
        
        Args:
            filter_engine: 过滤引擎
        """
        super().__init__()
        self.filter_engine = filter_engine
    
    def apply_filters(self, results: List[Tuple[FabricImage, float]], 
                      query: SearchQuery, progress_callback=None) -> List[Tuple[FabricImage, float]]:
        """应用过滤器
        
        Args:
            results: 原始结果
            query: 搜索查询
            progress_callback: 进度回调函数
            
        Returns:
            List[Tuple[FabricImage, float]]: 过滤后的结果
        """
        try:
            if not query.use_filters:
                return results
            
            if progress_callback:
                progress_callback(0.6, "准备应用过滤器...")
                
            # 提取图片列表
            images = [result[0] for result in results]
            
            # 应用过滤器
            if progress_callback:
                progress_callback(0.65, "正在应用过滤器...")
                
            filtered_images = self.filter_engine.apply_filters(
                images, query.active_filters
            )
            
            # 重新构建结果，保持分数
            filtered_results = []
            image_score_map = {result[0].id: result[1] for result in results}
            
            for image in filtered_images:
                if image.id in image_score_map:
                    filtered_results.append((image, image_score_map[image.id]))
            
            return filtered_results
            
        except Exception as e:
            self.logger.error(f"应用过滤器失败: {e}")
            return results
    
    def sort_results(self, results: List[Tuple[FabricImage, float]], 
                     query: SearchQuery, progress_callback=None) -> List[Tuple[FabricImage, float]]:
        """排序结果
        
        Args:
            results: 结果列表
            query: 搜索查询
            progress_callback: 进度回调函数
            
        Returns:
            List[Tuple[FabricImage, float]]: 排序后的结果
        """
        try:
            if progress_callback:
                progress_callback(0.75, "正在排序结果...")
                
            reverse = query.sort_order.lower() == "desc"
            
            if query.sort_by == "similarity":
                # 按相似度排序
                results.sort(key=lambda x: x[1], reverse=reverse)
            elif query.sort_by == "date":
                # 按日期排序
                results.sort(key=lambda x: x[0].created_at, reverse=reverse)
            elif query.sort_by == "name":
                # 按文件名排序
                results.sort(key=lambda x: x[0].file_name, reverse=reverse)
            elif query.sort_by == "size":
                # 按文件大小排序
                results.sort(key=lambda x: x[0].file_size or 0, reverse=reverse)
            
            return results
            
        except Exception as e:
            self.logger.error(f"排序结果失败: {e}")
            return results
    
    def paginate_results(self, results: List[Tuple[FabricImage, float]], 
                         query: SearchQuery, progress_callback=None) -> Tuple[List[Tuple[FabricImage, float]], Dict[str, Any]]:
        """分页结果
        
        Args:
            results: 结果列表
            query: 搜索查询
            progress_callback: 进度回调函数
            
        Returns:
            Tuple[List[Tuple[FabricImage, float]], Dict[str, Any]]: (分页结果, 分页信息)
        """
        try:
            if progress_callback:
                progress_callback(0.85, "正在分页结果...")
                
            total_results = len(results)
            total_pages = max(1, (total_results + query.page_size - 1) // query.page_size)
            
            # 计算分页范围
            start_idx = (query.page - 1) * query.page_size
            end_idx = start_idx + query.page_size
            
            paginated_results = results[start_idx:end_idx]
            
            pagination_info = {
                'current_page': query.page,
                'total_pages': total_pages,
                'has_next_page': query.page < total_pages,
                'has_prev_page': query.page > 1,
                'total_results': total_results,
                'page_size': query.page_size
            }
            
            return paginated_results, pagination_info
            
        except Exception as e:
            self.logger.error(f"分页结果失败: {e}")
            return results, {
                'current_page': 1,
                'total_pages': 1,
                'has_next_page': False,
                'has_prev_page': False,
                'total_results': len(results),
                'page_size': query.page_size
            }
    
    def generate_search_statistics(self, raw_results: List[Tuple[FabricImage, float]],
                                  filtered_results: List[Tuple[FabricImage, float]],
                                  query: SearchQuery) -> Dict[str, Any]:
        """生成搜索统计信息
        
        Args:
            raw_results: 原始结果
            filtered_results: 过滤后结果
            query: 搜索查询
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {
                'raw_result_count': len(raw_results),
                'filtered_result_count': len(filtered_results),
                'filter_reduction_rate': 0.0,
                'score_distribution': {},
                'category_distribution': {},
                'tag_distribution': {}
            }
            
            if len(raw_results) > 0:
                stats['filter_reduction_rate'] = (
                    (len(raw_results) - len(filtered_results)) / len(raw_results)
                )
            
            # 分数分布
            if filtered_results:
                scores = [result[1] for result in filtered_results]
                stats['score_distribution'] = {
                    'min': min(scores),
                    'max': max(scores),
                    'avg': sum(scores) / len(scores),
                    'median': sorted(scores)[len(scores) // 2]
                }
            
            # 类别分布
            category_counts = {}
            tag_counts = {}
            
            for fabric_image, _ in filtered_results:
                # 类别统计
                if fabric_image.category:
                    category_counts[fabric_image.category] = (
                        category_counts.get(fabric_image.category, 0) + 1
                    )
                
                # 标签统计
                if fabric_image.tags:
                    for tag in fabric_image.tags:
                        tag_counts[tag] = tag_counts.get(tag, 0) + 1
            
            stats['category_distribution'] = category_counts
            stats['tag_distribution'] = tag_counts
            
            return stats
            
        except Exception as e:
            self.logger.error(f"生成搜索统计信息失败: {e}")
            return {}