#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题加载器模块

该模块负责加载和管理主题文件。
"""

import os
import json
from typing import Dict, Optional, List

from utils.log_utils import LoggerMixin
from utils.file_utils import FileManager
from gui.themes.schemes import Theme, ThemeType, ColorScheme, FontScheme


class ThemeLoader(LoggerMixin):
    """主题加载器
    
    负责加载内置主题和自定义主题。
    """
    
    def __init__(self, themes_dir: str = "themes"):
        """初始化主题加载器
        
        Args:
            themes_dir: 主题目录
        """
        super().__init__()
        
        self.themes_dir = themes_dir
        self.file_manager = FileManager()
        
        # 主题存储
        self.themes: Dict[str, Theme] = {}
        
        # 初始化
        self._ensure_themes_dir()
        self._load_builtin_themes()
        self._load_custom_themes()
        
        self.logger.info("主题加载器初始化完成")
    
    def _ensure_themes_dir(self):
        """确保主题目录存在"""
        try:
            self.file_manager.ensure_dir(self.themes_dir)
            
            # 创建子目录
            subdirs = ['light', 'dark', 'custom']
            for subdir in subdirs:
                subdir_path = os.path.join(self.themes_dir, subdir)
                self.file_manager.ensure_dir(subdir_path)
                
        except Exception as e:
            self.logger.error(f"创建主题目录失败: {e}")
    
    def _load_builtin_themes(self):
        """加载内置主题"""
        try:
            # 浅色主题
            light_theme = self._create_light_theme()
            self.themes[light_theme.name] = light_theme
            
            # 深色主题
            dark_theme = self._create_dark_theme()
            self.themes[dark_theme.name] = dark_theme
            
            self.logger.info(f"加载内置主题: {len(self.themes)} 个")
            
        except Exception as e:
            self.logger.error(f"加载内置主题失败: {e}")
    
    def _create_light_theme(self) -> Theme:
        """创建浅色主题"""
        color_scheme = ColorScheme(
            primary="#2196F3",
            secondary="#FFC107",
            success="#4CAF50",
            warning="#FF9800",
            error="#F44336",
            info="#00BCD4",
            background="#FFFFFF",
            surface="#F5F5F5",
            card="#FFFFFF",
            text_primary="#212121",
            text_secondary="#757575",
            text_disabled="#BDBDBD",
            border="#E0E0E0",
            divider="#EEEEEE",
            hover="#F0F0F0",
            selected="#E3F2FD",
            focus="#BBDEFB"
        )
        
        font_scheme = FontScheme()
        
        return Theme(
            name="light",
            display_name="浅色主题",
            theme_type=ThemeType.LIGHT,
            color_scheme=color_scheme,
            font_scheme=font_scheme,
            stylesheet=""
        )
    
    def _create_dark_theme(self) -> Theme:
        """创建深色主题"""
        color_scheme = ColorScheme(
            primary="#1976D2",
            secondary="#FFA000",
            success="#388E3C",
            warning="#F57C00",
            error="#D32F2F",
            info="#0097A7",
            background="#121212",
            surface="#1E1E1E",
            card="#2D2D2D",
            text_primary="#FFFFFF",
            text_secondary="#AAAAAA",
            text_disabled="#666666",
            border="#404040",
            divider="#333333",
            hover="#2A2A2A",
            selected="#1565C0",
            focus="#1976D2"
        )
        
        font_scheme = FontScheme()
        
        return Theme(
            name="dark",
            display_name="深色主题",
            theme_type=ThemeType.DARK,
            color_scheme=color_scheme,
            font_scheme=font_scheme,
            stylesheet=""
        )
    
    def _load_custom_themes(self):
        """加载自定义主题"""
        try:
            custom_dir = os.path.join(self.themes_dir, 'custom')
            
            if not os.path.exists(custom_dir):
                return
            
            for file_name in os.listdir(custom_dir):
                if file_name.endswith('.json'):
                    file_path = os.path.join(custom_dir, file_name)
                    theme = self._load_theme_from_file(file_path)
                    
                    if theme:
                        self.themes[theme.name] = theme
            
            self.logger.info(f"加载自定义主题: {len([t for t in self.themes.values() if t.theme_type == ThemeType.CUSTOM])} 个")
            
        except Exception as e:
            self.logger.error(f"加载自定义主题失败: {e}")
    
    def _load_theme_from_file(self, file_path: str) -> Optional[Theme]:
        """从文件加载主题
        
        Args:
            file_path: 主题文件路径
            
        Returns:
            Optional[Theme]: 主题对象
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            theme = Theme.from_dict(data)
            return theme
            
        except Exception as e:
            self.logger.error(f"从文件加载主题失败 {file_path}: {e}")
            return None
    
    def save_theme_to_file(self, theme: Theme) -> bool:
        """保存主题到文件
        
        Args:
            theme: 主题对象
            
        Returns:
            bool: 是否保存成功
        """
        try:
            custom_dir = os.path.join(self.themes_dir, 'custom')
            file_path = os.path.join(custom_dir, f"{theme.name}.json")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(theme.to_dict(), f, indent=2, ensure_ascii=False)
            
            return True
                
        except Exception as e:
            self.logger.error(f"保存主题到文件失败: {e}")
            return False
    
    def get_available_themes(self) -> List[str]:
        """获取可用主题列表
        
        Returns:
            List[str]: 主题名称列表
        """
        return list(self.themes.keys())
    
    def get_theme(self, theme_name: str) -> Optional[Theme]:
        """获取主题
        
        Args:
            theme_name: 主题名称
            
        Returns:
            Optional[Theme]: 主题对象
        """
        return self.themes.get(theme_name)
    
    def import_theme(self, import_path: str) -> Optional[Theme]:
        """导入主题
        
        Args:
            import_path: 导入路径
            
        Returns:
            Optional[Theme]: 导入的主题对象
        """
        try:
            theme = self._load_theme_from_file(import_path)
            
            if not theme:
                return None
            
            # 确保主题名称唯一
            original_name = theme.name
            counter = 1
            
            while theme.name in self.themes:
                theme.name = f"{original_name}_{counter}"
                counter += 1
            
            # 设置为自定义主题
            theme.theme_type = ThemeType.CUSTOM
            
            # 添加主题
            self.themes[theme.name] = theme
            
            # 保存到文件
            self.save_theme_to_file(theme)
            
            self.logger.info(f"主题导入成功: {theme.display_name}")
            return theme
            
        except Exception as e:
            self.logger.error(f"导入主题失败: {e}")
            return None
    
    def delete_theme(self, theme_name: str) -> bool:
        """删除主题
        
        Args:
            theme_name: 主题名称
            
        Returns:
            bool: 是否删除成功
        """
        try:
            if theme_name not in self.themes:
                return False
            
            theme = self.themes[theme_name]
            
            # 只能删除自定义主题
            if theme.theme_type != ThemeType.CUSTOM:
                self.logger.warning(f"不能删除内置主题: {theme_name}")
                return False
            
            # 删除主题
            del self.themes[theme_name]
            
            # 删除文件
            custom_dir = os.path.join(self.themes_dir, 'custom')
            file_path = os.path.join(custom_dir, f"{theme_name}.json")
            
            if os.path.exists(file_path):
                os.remove(file_path)
            
            self.logger.info(f"自定义主题删除成功: {theme_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除自定义主题失败: {e}")
            return False