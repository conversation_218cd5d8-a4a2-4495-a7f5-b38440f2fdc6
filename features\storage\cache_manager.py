#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存管理器

提供特征提取结果的缓存管理功能。
"""

import os
import hashlib
import logging
from typing import Optional

from ..data_models.feature_models import FeatureExtractionResult
from ..utils.feature_utils import save_features, load_features
from ..utils.file_utils import ensure_dir

logger = logging.getLogger(__name__)


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_dir: Optional[str] = None, use_cache: bool = True):
        """初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录
            use_cache: 是否使用缓存
        """
        self.cache_dir = cache_dir
        self.use_cache = use_cache
        
        if self.cache_dir and self.use_cache:
            ensure_dir(self.cache_dir)
    
    def _get_cache_path(self, image_path: str) -> str:
        """生成缓存文件路径
        
        Args:
            image_path: 图像路径
            
        Returns:
            str: 缓存文件路径
        """
        try:
            if not self.cache_dir:
                return ""
            
            if not image_path:
                return ""
            
            # 使用图像路径的哈希值作为缓存文件名
            path_hash = hashlib.md5(image_path.encode('utf-8')).hexdigest()
            cache_filename = f"{path_hash}.cache"
            cache_path = os.path.join(self.cache_dir, cache_filename)
            
            return cache_path
            
        except Exception as e:
            logger.warning(f"生成缓存路径异常: {str(e)}")
            return ""
    
    def save_to_cache(self, image_path: str, result: FeatureExtractionResult) -> bool:
        """保存结果到缓存
        
        Args:
            image_path: 图像路径
            result: 特征提取结果
            
        Returns:
            bool: 是否保存成功
        """
        try:
            if not self.use_cache or not self.cache_dir:
                return False
            
            # 输入参数验证
            if not image_path:
                logger.warning("图像路径为空，跳过缓存保存")
                return False
            
            if not isinstance(image_path, str):
                logger.warning("图像路径不是字符串类型，跳过缓存保存")
                return False
            
            if result is None:
                logger.warning("特征提取结果为None，跳过缓存保存")
                return False
            
            if not hasattr(result, 'to_dict'):
                logger.warning("特征提取结果没有to_dict方法，跳过缓存保存")
                return False
            
            # 确保缓存目录存在
            try:
                ensure_dir(self.cache_dir)
            except Exception as e:
                logger.warning(f"创建缓存目录失败: {str(e)}")
                return False
            
            # 获取缓存路径
            cache_path = self._get_cache_path(image_path)
            if not cache_path:
                logger.warning("无法生成缓存路径")
                return False
            
            # 转换结果为字典
            try:
                result_dict = result.to_dict()
                if not result_dict:
                    logger.warning("特征提取结果转换为字典失败")
                    return False
            except Exception as e:
                logger.warning(f"特征提取结果转换为字典异常: {str(e)}")
                return False
            
            # 保存到缓存
            save_features(result_dict, cache_path)
            logger.debug(f"特征缓存保存成功: {cache_path}")
            return True
            
        except Exception as e:
            logger.warning(f"缓存保存失败: {str(e)}")
            return False
    
    def load_from_cache(self, image_path: str) -> Optional[FeatureExtractionResult]:
        """从缓存加载结果
        
        Args:
            image_path: 图像路径
            
        Returns:
            Optional[FeatureExtractionResult]: 缓存的结果
        """
        try:
            if not self.use_cache or not self.cache_dir:
                return None
            
            # 输入参数验证
            if not image_path:
                logger.debug("图像路径为空，跳过缓存加载")
                return None
            
            if not isinstance(image_path, str):
                logger.debug("图像路径不是字符串类型，跳过缓存加载")
                return None
            
            # 检查缓存目录
            if not os.path.exists(self.cache_dir):
                logger.debug("缓存目录不存在，跳过缓存加载")
                return None
            
            # 获取缓存路径
            cache_path = self._get_cache_path(image_path)
            if not cache_path:
                logger.debug("无法生成缓存路径")
                return None
            
            if not os.path.exists(cache_path):
                logger.debug(f"缓存文件不存在: {cache_path}")
                return None
            
            # 检查原图像文件是否存在
            if not os.path.exists(image_path):
                logger.debug(f"原图像文件不存在，删除缓存: {image_path}")
                try:
                    os.remove(cache_path)
                except Exception as e:
                    logger.warning(f"删除无效缓存失败: {str(e)}")
                return None
            
            # 检查文件修改时间
            try:
                cache_mtime = os.path.getmtime(cache_path)
                image_mtime = os.path.getmtime(image_path)
                
                if cache_mtime <= image_mtime:
                    logger.debug(f"缓存文件过期: {cache_path}")
                    try:
                        os.remove(cache_path)
                    except Exception as e:
                        logger.warning(f"删除过期缓存失败: {str(e)}")
                    return None
            except OSError as e:
                logger.warning(f"检查文件修改时间失败: {str(e)}")
                return None
            
            # 加载缓存数据
            try:
                cached_data = load_features(cache_path)
                if not cached_data:
                    logger.debug(f"缓存数据为空: {cache_path}")
                    return None
                
                if not isinstance(cached_data, dict):
                    logger.warning(f"缓存数据格式无效: {cache_path}")
                    try:
                        os.remove(cache_path)
                    except Exception as e:
                        logger.warning(f"删除无效缓存失败: {str(e)}")
                    return None
                
                # 从字典创建结果对象
                result = FeatureExtractionResult.from_dict(cached_data)
                if result is None:
                    logger.warning(f"从缓存数据创建结果对象失败: {cache_path}")
                    try:
                        os.remove(cache_path)
                    except Exception as e:
                        logger.warning(f"删除无效缓存失败: {str(e)}")
                    return None
                
                # 验证缓存结果的特征
                if (not hasattr(result, 'features') or 
                    result.features is None or 
                    len(result.features) == 0):
                    logger.warning(f"缓存结果特征无效: {cache_path}")
                    try:
                        os.remove(cache_path)
                    except Exception as e:
                        logger.warning(f"删除无效缓存失败: {str(e)}")
                    return None
                
                logger.debug(f"缓存加载成功: {cache_path}")
                return result
                
            except Exception as e:
                logger.warning(f"加载缓存数据异常: {str(e)}")
                try:
                    os.remove(cache_path)
                except Exception as e2:
                    logger.warning(f"删除损坏缓存失败: {str(e2)}")
                return None
            
        except Exception as e:
            logger.warning(f"缓存加载失败: {str(e)}")
            return None
    
    def clear_cache(self) -> bool:
        """清空缓存
        
        Returns:
            bool: 是否清空成功
        """
        try:
            if not self.cache_dir or not os.path.exists(self.cache_dir):
                return True
            
            # 删除缓存目录中的所有文件
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.cache'):
                    file_path = os.path.join(self.cache_dir, filename)
                    try:
                        os.remove(file_path)
                    except Exception as e:
                        logger.warning(f"删除缓存文件失败: {file_path} - {str(e)}")
            
            logger.info("缓存清空完成")
            return True
            
        except Exception as e:
            logger.error(f"清空缓存失败: {str(e)}")
            return False