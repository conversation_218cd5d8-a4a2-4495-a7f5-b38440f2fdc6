#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输入验证器

提供输入参数验证功能。
"""

import os
import logging
from typing import List, Optional, Union

from ..data_models.feature_models import FeatureExtractionResult

logger = logging.getLogger(__name__)


class InputValidator:
    """输入参数验证器"""
    
    # 支持的图像格式
    ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
    
    # 文件大小限制（100MB）
    MAX_FILE_SIZE = 100 * 1024 * 1024
    
    @staticmethod
    def validate_image_path(image_path: str) -> FeatureExtractionResult:
        """验证图像路径
        
        Args:
            image_path: 图像路径
            
        Returns:
            FeatureExtractionResult: 验证结果，如果成功则success=True
        """
        try:
            # 空值检查
            if not image_path:
                return FeatureExtractionResult(
                    image_path="",
                    success=False,
                    error_message="图像路径不能为空"
                )
            
            # 类型检查
            if not isinstance(image_path, str):
                return FeatureExtractionResult(
                    image_path=str(image_path),
                    success=False,
                    error_message="图像路径必须是字符串类型"
                )
            
            # 路径规范化
            image_path = os.path.normpath(image_path)
            
            # 文件存在性检查
            if not os.path.exists(image_path):
                return FeatureExtractionResult(
                    image_path=image_path,
                    success=False,
                    error_message=f"图像文件不存在: {image_path}"
                )
            
            # 文件类型检查
            if not os.path.isfile(image_path):
                return FeatureExtractionResult(
                    image_path=image_path,
                    success=False,
                    error_message=f"路径不是文件: {image_path}"
                )
            
            # 文件大小检查
            try:
                file_size = os.path.getsize(image_path)
                if file_size > InputValidator.MAX_FILE_SIZE:
                    return FeatureExtractionResult(
                        image_path=image_path,
                        success=False,
                        error_message=f"图像文件过大: {file_size / (1024*1024):.1f}MB > 100MB"
                    )
                if file_size == 0:
                    return FeatureExtractionResult(
                        image_path=image_path,
                        success=False,
                        error_message="图像文件为空"
                    )
            except OSError as e:
                return FeatureExtractionResult(
                    image_path=image_path,
                    success=False,
                    error_message=f"无法获取文件大小: {str(e)}"
                )
            
            # 文件扩展名检查
            file_ext = os.path.splitext(image_path)[1].lower()
            if file_ext not in InputValidator.ALLOWED_EXTENSIONS:
                return FeatureExtractionResult(
                    image_path=image_path,
                    success=False,
                    error_message=f"不支持的图像格式: {file_ext}"
                )
            
            # 验证通过
            return FeatureExtractionResult(
                image_path=image_path,
                success=True
            )
            
        except Exception as e:
            logger.error(f"输入验证异常: {str(e)}")
            return FeatureExtractionResult(
                image_path=image_path if 'image_path' in locals() else "",
                success=False,
                error_message=f"输入验证异常: {str(e)}"
            )
    
    @staticmethod
    def validate_image_paths_batch(image_paths: List[str]) -> List[str]:
        """批量验证图像路径
        
        Args:
            image_paths: 图像路径列表
            
        Returns:
            List[str]: 有效的图像路径列表
        """
        try:
            # 输入参数验证
            if not image_paths:
                logger.warning("图像路径列表为空")
                return []
            
            if not isinstance(image_paths, (list, tuple)):
                logger.error("图像路径必须是列表或元组类型")
                return []
            
            # 过滤有效路径
            valid_paths = []
            for path in image_paths:
                if path and isinstance(path, str):
                    validation_result = InputValidator.validate_image_path(path)
                    if validation_result.success:
                        valid_paths.append(path)
                    else:
                        logger.warning(f"跳过无效路径: {path} - {validation_result.error_message}")
                else:
                    logger.warning(f"跳过无效路径: {path}")
            
            return valid_paths
            
        except Exception as e:
            logger.error(f"批量路径验证异常: {str(e)}")
            return []
    
    @staticmethod
    def validate_config(config) -> bool:
        """验证配置对象
        
        Args:
            config: 配置对象
            
        Returns:
            bool: 是否有效
        """
        try:
            if config is None:
                logger.error("配置对象不能为None")
                return False
            
            # 检查必要属性
            required_attrs = ['model_name', 'use_cache', 'cache_dir']
            for attr in required_attrs:
                if not hasattr(config, attr):
                    logger.error(f"配置对象缺少必要属性: {attr}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"配置验证异常: {str(e)}")
            return False