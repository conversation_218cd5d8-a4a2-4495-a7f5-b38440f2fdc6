#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的性能优化验证脚本

验证我们实施的性能优化是否正确配置。
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_config_optimizations():
    """测试配置优化"""
    logger.info("测试配置优化...")
    
    try:
        # 测试批处理配置
        from models.batch.config import BatchConfig
        
        config = BatchConfig()
        logger.info(f"批处理配置:")
        logger.info(f"  batch_size: {config.batch_size}")
        logger.info(f"  max_workers: {config.max_workers}")
        logger.info(f"  enable_batch_insert: {config.enable_batch_insert}")
        logger.info(f"  db_batch_size: {config.db_batch_size}")
        
        # 验证优化值
        assert config.batch_size >= 32, f"batch_size应该>=32，实际值: {config.batch_size}"
        assert config.max_workers >= 8, f"max_workers应该>=8，实际值: {config.max_workers}"
        
        logger.info("✓ 批处理配置优化验证通过")
        return True
        
    except Exception as e:
        logger.error(f"配置优化测试失败: {e}")
        return False


def test_system_config_optimizations():
    """测试系统配置优化"""
    logger.info("测试系统配置优化...")
    
    try:
        from config.system_config import PerformanceConfig
        
        config = PerformanceConfig()
        logger.info(f"性能配置:")
        logger.info(f"  max_memory_usage_mb: {config.max_memory_usage_mb}")
        logger.info(f"  max_processes: {config.max_processes}")
        logger.info(f"  max_threads: {config.max_threads}")
        
        # 验证优化值
        assert config.max_memory_usage_mb >= 4096, f"max_memory_usage_mb应该>=4096，实际值: {config.max_memory_usage_mb}"
        assert config.max_processes >= 8, f"max_processes应该>=8，实际值: {config.max_processes}"
        assert config.max_threads >= 16, f"max_threads应该>=16，实际值: {config.max_threads}"
        
        logger.info("✓ 系统配置优化验证通过")
        return True
        
    except Exception as e:
        logger.error(f"系统配置优化测试失败: {e}")
        return False


def test_feature_cache_optimization():
    """测试特征缓存优化"""
    logger.info("测试特征缓存优化...")
    
    try:
        from features.storage.feature_cache import FeatureCache
        import numpy as np
        
        # 创建优化的缓存实例
        cache = FeatureCache(
            max_size=100,
            max_memory_mb=50,
            enable_compression=True
        )
        
        # 测试基本功能
        test_key = "test_feature"
        test_data = np.random.rand(512).astype(np.float32)
        
        # 测试存储
        cache.put(test_key, test_data)
        
        # 测试检索
        retrieved_data = cache.get(test_key)
        assert retrieved_data is not None, "缓存检索失败"
        assert np.allclose(test_data, retrieved_data), "缓存数据不匹配"
        
        # 测试统计信息
        stats = cache.get_memory_usage()
        logger.info(f"缓存统计:")
        logger.info(f"  当前内存使用: {stats['current_memory_mb']:.2f}MB")
        logger.info(f"  缓存大小: {stats['cache_size']}")
        logger.info(f"  命中率: {stats['hit_rate']:.2f}")
        logger.info(f"  压缩比: {stats['compression_ratio']:.2f}")
        
        logger.info("✓ 特征缓存优化验证通过")
        return True
        
    except Exception as e:
        logger.error(f"特征缓存优化测试失败: {e}")
        return False


def test_database_optimization():
    """测试数据库优化"""
    logger.info("测试数据库优化...")
    
    try:
        # 检查数据库CRUD是否有批量插入方法
        from database.repositories.fabric_crud import FabricCRUD
        
        # 检查是否有batch_create方法
        assert hasattr(FabricCRUD, 'batch_create'), "FabricCRUD缺少batch_create方法"
        
        logger.info("✓ 数据库优化验证通过")
        return True
        
    except Exception as e:
        logger.error(f"数据库优化测试失败: {e}")
        return False


def test_performance_modules():
    """测试性能模块是否可用"""
    logger.info("测试性能模块...")
    
    modules_to_test = [
        'utils.memory_manager',
        'utils.async_io_manager', 
        'utils.performance_monitor'
    ]
    
    available_modules = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            available_modules.append(module_name)
            logger.info(f"✓ {module_name} 可用")
        except ImportError as e:
            logger.warning(f"✗ {module_name} 不可用: {e}")
    
    logger.info(f"可用性能模块: {len(available_modules)}/{len(modules_to_test)}")
    return len(available_modules) > 0


def test_feature_manager_integration():
    """测试特征管理器集成"""
    logger.info("测试特征管理器集成...")
    
    try:
        # 检查特征管理器是否包含性能优化
        from features.core.manager import FeatureManager
        
        # 检查是否有性能优化相关的方法
        methods_to_check = [
            'get_performance_statistics',
            'optimize_performance',
            '_cleanup_caches'
        ]
        
        for method_name in methods_to_check:
            assert hasattr(FeatureManager, method_name), f"FeatureManager缺少{method_name}方法"
        
        logger.info("✓ 特征管理器集成验证通过")
        return True
        
    except Exception as e:
        logger.error(f"特征管理器集成测试失败: {e}")
        return False


def run_verification_tests():
    """运行所有验证测试"""
    logger.info("开始运行性能优化验证测试...")
    logger.info("="*60)
    
    tests = [
        ("配置优化", test_config_optimizations),
        ("系统配置优化", test_system_config_optimizations),
        ("特征缓存优化", test_feature_cache_optimization),
        ("数据库优化", test_database_optimization),
        ("性能模块", test_performance_modules),
        ("特征管理器集成", test_feature_manager_integration),
    ]
    
    results = {}
    total_time = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*40}")
        logger.info(f"测试: {test_name}")
        logger.info(f"{'='*40}")
        
        start_time = time.time()
        try:
            success = test_func()
            duration = time.time() - start_time
            total_time += duration
            
            results[test_name] = {
                'success': success,
                'duration': duration
            }
            
            status = "通过" if success else "失败"
            logger.info(f"{test_name} - {status} (耗时: {duration:.3f}s)")
            
        except Exception as e:
            duration = time.time() - start_time
            total_time += duration
            logger.error(f"{test_name} - 异常: {e}")
            results[test_name] = {
                'success': False,
                'duration': duration,
                'error': str(e)
            }
    
    # 输出总结
    logger.info(f"\n{'='*60}")
    logger.info("验证测试总结")
    logger.info(f"{'='*60}")
    
    total_tests = len(tests)
    passed_tests = sum(1 for r in results.values() if r['success'])
    
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {total_tests - passed_tests}")
    logger.info(f"成功率: {passed_tests/total_tests*100:.1f}%")
    logger.info(f"总耗时: {total_time:.3f}s")
    
    # 详细结果
    logger.info(f"\n详细结果:")
    for test_name, result in results.items():
        status = "✓" if result['success'] else "✗"
        logger.info(f"  {status} {test_name}: {result['duration']:.3f}s")
        if 'error' in result:
            logger.info(f"    错误: {result['error']}")
    
    return results


if __name__ == "__main__":
    results = run_verification_tests()
    
    # 如果所有测试都通过，返回0；否则返回1
    all_passed = all(r['success'] for r in results.values())
    sys.exit(0 if all_passed else 1)
