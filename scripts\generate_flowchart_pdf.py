#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成GUI参数传递与搜索策略流程图PDF

该脚本使用matplotlib和graphviz生成详细的流程图PDF文档
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np
from pathlib import Path
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_flowchart_pdf():
    """创建流程图PDF"""

    # 创建图形和子图
    fig = plt.figure(figsize=(16, 20))
    ax = fig.add_subplot(111)

    # 设置坐标范围
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 20)
    ax.axis('off')

    # 定义颜色方案
    colors = {
        'gui': '#e1f5fe',           # 浅蓝色 - GUI层
        'widget': '#f3e5f5',        # 浅紫色 - 组件层
        'config': '#e8f5e8',        # 浅绿色 - 配置层
        'strategy': '#fff3e0',      # 浅橙色 - 策略层
        'compute': '#fce4ec',       # 浅粉色 - 计算层
        'result': '#f1f8e9'         # 浅黄绿色 - 结果层
    }
    
    # 定义节点位置和内容
    nodes = [
        # GUI层
        {'pos': (8, 19), 'text': '用户界面层\nGUI Interface', 'color': colors['gui'], 'size': (3, 0.8)},

        # 参数设置层
        {'pos': (4, 17.5), 'text': '相似度搜索参数\nSimilarity Search', 'color': colors['widget'], 'size': (2.5, 0.6)},
        {'pos': (8, 17.5), 'text': '高级搜索参数\nAdvanced Search', 'color': colors['widget'], 'size': (2.5, 0.6)},
        {'pos': (12, 17.5), 'text': '文本搜索参数\nText Search', 'color': colors['widget'], 'size': (2.5, 0.6)},

        # 组件层
        {'pos': (2, 16), 'text': 'FeatureWeightsWidget\n特征权重设置', 'color': colors['widget'], 'size': (2.6, 0.6)},
        {'pos': (4.5, 16), 'text': 'FeatureParamsWidget\n传统特征参数', 'color': colors['widget'], 'size': (2.6, 0.6)},
        {'pos': (7, 16), 'text': 'SimilaritySearchWidget\n相似度阈值等', 'color': colors['widget'], 'size': (2.6, 0.6)},
        {'pos': (10, 16), 'text': 'AdvancedSearchWidget\n高级过滤参数', 'color': colors['widget'], 'size': (2.6, 0.6)},
        
        # 方法层
        {'pos': (2, 18.5), 'text': 'get_weights()\n归一化权重', 'color': colors['config'], 'size': (2.2, 0.5)},
        {'pos': (5, 18.5), 'text': 'get_params()\n特征提取参数', 'color': colors['config'], 'size': (2.2, 0.5)},
        {'pos': (8, 18.5), 'text': 'get_search_params()\n搜索参数', 'color': colors['config'], 'size': (2.2, 0.5)},
        {'pos': (12, 18.5), 'text': 'get_search_params()\n过滤参数', 'color': colors['config'], 'size': (2.2, 0.5)},
        
        # 参数输出层
        {'pos': (2, 17), 'text': '特征权重字典\ndeep_learning: 0.4\ncolor: 0.3\ntexture: 0.2\nshape: 0.1', 'color': colors['config'], 'size': (2.5, 1)},
        {'pos': (5, 17), 'text': '传统特征参数\nhist_bins: 32\nlbp_radius: 3\nfourier_desc: 64', 'color': colors['config'], 'size': (2.5, 1)},
        {'pos': (8, 17), 'text': '搜索参数\nsimilarity_threshold: 0.7\nmax_results: 50', 'color': colors['config'], 'size': (2.5, 1)},
        {'pos': (12, 17), 'text': '过滤参数\nfile_filters\ndate_filter\ncontent_filters', 'color': colors['config'], 'size': (2.5, 1)},
        
        # 配置聚合层
        {'pos': (10, 15), 'text': 'SearchPanel.get_search_config()\n搜索配置聚合', 'color': colors['config'], 'size': (4, 0.6)},
        {'pos': (10, 13.5), 'text': 'SearchConfig对象\nmode: SearchMode\nfeature_weights: Dict\nfeature_extraction_params: Dict\nmax_results: int', 'color': colors['config'], 'size': (4.5, 1.2)},
        
        # 处理层
        {'pos': (10, 12), 'text': 'SearchHandler.start_search()\n搜索处理器', 'color': colors['strategy'], 'size': (3.5, 0.6)},
        {'pos': (10, 10.8), 'text': 'SearchHandler._config_to_query()\n配置转换', 'color': colors['strategy'], 'size': (3.5, 0.6)},
        
        # 查询对象层
        {'pos': (10, 9.5), 'text': 'SearchQuery对象创建\n参数映射与转换', 'color': colors['strategy'], 'size': (3.5, 0.8)},
        
        # 搜索引擎层
        {'pos': (10, 8), 'text': 'SearchEngine.search()\n搜索引擎', 'color': colors['strategy'], 'size': (3, 0.6)},
        {'pos': (10, 6.8), 'text': 'FeatureManager特征管理\n特征提取 + 搜索策略选择', 'color': colors['strategy'], 'size': (4, 0.8)},
        
        # 策略选择层
        {'pos': (10, 5.5), 'text': '搜索策略类型选择', 'color': colors['strategy'], 'size': (3, 0.5)},
        
        # 具体策略层
        {'pos': (3, 4), 'text': 'WeightedSearchStrategy\n加权搜索策略\n加权融合计算', 'color': colors['strategy'], 'size': (2.8, 1)},
        {'pos': (6.5, 4), 'text': 'AdaptiveSearchStrategy\n自适应搜索策略\n自适应权重分析', 'color': colors['strategy'], 'size': (2.8, 1)},
        {'pos': (10, 4), 'text': 'QueryExpansionStrategy\n查询扩展策略\n两轮搜索扩展', 'color': colors['strategy'], 'size': (2.8, 1)},
        {'pos': (13.5, 4), 'text': 'HybridSearchStrategy\n混合搜索策略\n多策略融合', 'color': colors['strategy'], 'size': (2.8, 1)},
        {'pos': (17, 4), 'text': 'SingleFeatureStrategy\n单特征搜索策略\n单特征计算', 'color': colors['strategy'], 'size': (2.8, 1)},
        
        # 计算层
        {'pos': (10, 2.5), 'text': '相似度计算\nFAISS索引搜索 + NumPy计算', 'color': colors['compute'], 'size': (4, 0.8)},
        
        # 结果处理层
        {'pos': (10, 1.2), 'text': 'ResultProcessor结果处理\n过滤器应用 + 结果排序 + 分页处理', 'color': colors['result'], 'size': (5, 0.8)},
        
        # 最终结果
        {'pos': (10, 0.3), 'text': 'SearchResult对象\n返回GUI显示', 'color': colors['result'], 'size': (3, 0.6)}
    ]
    
    # 绘制节点
    for node in nodes:
        x, y = node['pos']
        width, height = node['size']
        
        # 创建圆角矩形
        box = FancyBboxPatch(
            (x - width/2, y - height/2), width, height,
            boxstyle="round,pad=0.1",
            facecolor=node['color'],
            edgecolor='black',
            linewidth=1
        )
        ax.add_patch(box)
        
        # 添加文本
        ax.text(x, y, node['text'], 
               ha='center', va='center', 
               fontsize=8, fontweight='bold',
               wrap=True)
    
    # 定义连接线
    connections = [
        # GUI到参数设置
        ((10, 22.6), (5, 22.1)),
        ((10, 22.6), (10, 22.1)),
        ((10, 22.6), (15, 22.1)),
        
        # 参数设置到组件
        ((5, 21.2), (2, 20.6)),
        ((5, 21.2), (5, 20.6)),
        ((5, 21.2), (8, 20.6)),
        ((10, 21.2), (12, 20.6)),
        
        # 组件到方法
        ((2, 19.4), (2, 19)),
        ((5, 19.4), (5, 19)),
        ((8, 19.4), (8, 19)),
        ((12, 19.4), (12, 19)),
        
        # 方法到参数输出
        ((2, 18), (2, 17.5)),
        ((5, 18), (5, 17.5)),
        ((8, 18), (8, 17.5)),
        ((12, 18), (12, 17.5)),
        
        # 参数输出到配置聚合
        ((2, 16.5), (8.5, 15.3)),
        ((5, 16.5), (9, 15.3)),
        ((8, 16.5), (10, 15.3)),
        ((12, 16.5), (11.5, 15.3)),
        
        # 配置聚合到SearchConfig
        ((10, 14.7), (10, 14.1)),
        
        # SearchConfig到处理层
        ((10, 12.9), (10, 12.3)),
        ((10, 11.7), (10, 11.1)),
        
        # 处理层到查询对象
        ((10, 10.5), (10, 10.1)),
        
        # 查询对象到搜索引擎
        ((10, 9.1), (10, 8.3)),
        ((10, 7.7), (10, 7.2)),
        
        # 搜索引擎到策略选择
        ((10, 6.4), (10, 6)),
        
        # 策略选择到具体策略
        ((8.5, 5.2), (3, 4.5)),
        ((9, 5.2), (6.5, 4.5)),
        ((10, 5.2), (10, 4.5)),
        ((11, 5.2), (13.5, 4.5)),
        ((11.5, 5.2), (17, 4.5)),
        
        # 策略到计算层
        ((3, 3.5), (8.5, 2.9)),
        ((6.5, 3.5), (9, 2.9)),
        ((10, 3.5), (10, 2.9)),
        ((13.5, 3.5), (11, 2.9)),
        ((17, 3.5), (11.5, 2.9)),
        
        # 计算层到结果处理
        ((10, 2.1), (10, 1.6)),
        
        # 结果处理到最终结果
        ((10, 0.8), (10, 0.6))
    ]
    
    # 绘制连接线
    for start, end in connections:
        arrow = ConnectionPatch(start, end, "data", "data",
                              arrowstyle="->", shrinkA=5, shrinkB=5,
                              mutation_scale=20, fc="black", alpha=0.7)
        ax.add_patch(arrow)
    
    # 添加标题
    ax.text(10, 23.8, 'Fabric Search - GUI参数传递与搜索策略流程图', 
           ha='center', va='center', fontsize=16, fontweight='bold')
    
    # 添加图例
    legend_elements = [
        mpatches.Patch(color=colors['gui'], label='用户界面层'),
        mpatches.Patch(color=colors['widget'], label='组件参数层'),
        mpatches.Patch(color=colors['config'], label='配置转换层'),
        mpatches.Patch(color=colors['strategy'], label='搜索策略层'),
        mpatches.Patch(color=colors['compute'], label='计算执行层'),
        mpatches.Patch(color=colors['result'], label='结果处理层')
    ]
    
    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98))
    
    # 保存PDF
    output_dir = Path('docs/diagrams')
    output_dir.mkdir(exist_ok=True)
    
    pdf_path = output_dir / 'GUI参数传递与搜索策略流程图.pdf'
    plt.savefig(pdf_path, format='pdf', bbox_inches='tight', dpi=300, 
                facecolor='white', edgecolor='none')
    
    print(f"PDF流程图已生成: {pdf_path}")
    
    # 同时保存PNG格式
    png_path = output_dir / 'GUI参数传递与搜索策略流程图.png'
    plt.savefig(png_path, format='png', bbox_inches='tight', dpi=300,
                facecolor='white', edgecolor='none')
    
    print(f"PNG流程图已生成: {png_path}")
    
    plt.close()

if __name__ == "__main__":
    create_flowchart_pdf()
