#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控件基础类模块

该模块定义控件工厂的基础类和枚举。
"""

from typing import Optional, Tuple
from enum import Enum
from dataclasses import dataclass

from PyQt6.QtWidgets import QWidget, QSizePolicy
from PyQt6.QtCore import QSize


class ButtonStyle(Enum):
    """按钮样式"""
    PRIMARY = "primary"
    SECONDARY = "secondary"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    INFO = "info"
    OUTLINE = "outline"
    TEXT = "text"


class ButtonSize(Enum):
    """按钮尺寸"""
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"


class InputType(Enum):
    """输入框类型"""
    TEXT = "text"
    PASSWORD = "password"
    EMAIL = "email"
    NUMBER = "number"
    SEARCH = "search"
    URL = "url"


@dataclass
class WidgetConfig:
    """控件配置"""
    # 基本属性
    enabled: bool = True
    visible: bool = True
    tooltip: Optional[str] = None
    
    # 尺寸属性
    width: Optional[int] = None
    height: Optional[int] = None
    min_width: Optional[int] = None
    min_height: Optional[int] = None
    max_width: Optional[int] = None
    max_height: Optional[int] = None
    
    # 样式属性
    style_class: Optional[str] = None
    custom_style: Optional[str] = None
    
    # 布局属性
    size_policy: Optional[Tuple[QSizePolicy.Policy, QSizePolicy.Policy]] = None
    
    def apply_to_widget(self, widget: QWidget):
        """应用配置到控件
        
        Args:
            widget: 目标控件
        """
        widget.setEnabled(self.enabled)
        widget.setVisible(self.visible)
        
        if self.tooltip:
            widget.setToolTip(self.tooltip)
        
        if self.width is not None:
            widget.setFixedWidth(self.width)
        
        if self.height is not None:
            widget.setFixedHeight(self.height)
        
        if self.min_width is not None:
            widget.setMinimumWidth(self.min_width)
        
        if self.min_height is not None:
            widget.setMinimumHeight(self.min_height)
        
        if self.max_width is not None:
            widget.setMaximumWidth(self.max_width)
        
        if self.max_height is not None:
            widget.setMaximumHeight(self.max_height)
        
        if self.size_policy:
            policy = QSizePolicy(*self.size_policy)
            widget.setSizePolicy(policy)
        
        if self.style_class:
            widget.setProperty("class", self.style_class)
        
        if self.custom_style:
            widget.setStyleSheet(self.custom_style)