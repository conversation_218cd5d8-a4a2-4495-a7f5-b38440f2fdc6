"""
纹理特征提取器模块

该模块负责提取图像的纹理特征，包括：
- LBP（局部二值模式）特征
- GLCM（灰度共生矩阵）特征
"""

import numpy as np
from PIL import Image
from skimage.feature import local_binary_pattern, graycomatrix, graycoprops
import logging

from ..config import TraditionalFeatureConfig

logger = logging.getLogger(__name__)


class TextureFeatureExtractor:
    """纹理特征提取器"""
    
    def __init__(self, config: TraditionalFeatureConfig):
        """初始化纹理特征提取器
        
        Args:
            config: 传统特征配置
        """
        if config is None:
            raise ValueError("Config cannot be None")
        
        self.config = config
        
        # 验证配置参数
        if not hasattr(config, 'lbp_n_points') or config.lbp_n_points <= 0:
            logger.warning("Invalid lbp_n_points, using default value 8")
            self.config.lbp_n_points = 8
            
        if not hasattr(config, 'lbp_radius') or config.lbp_radius <= 0:
            logger.warning("Invalid lbp_radius, using default value 1")
            self.config.lbp_radius = 1
    
    def extract_lbp_features(self, image: Image.Image) -> np.ndarray:
        """提取LBP（局部二值模式）特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: LBP特征
        """
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
            
            # 检查图像尺寸
            if image.size[0] == 0 or image.size[1] == 0:
                logger.error("Image has zero dimensions")
                return np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
            
            # 转换为灰度图像
            try:
                if image.mode != 'L':
                    gray_image = image.convert('L')
                else:
                    gray_image = image
            except Exception as e:
                logger.error(f"Failed to convert image to grayscale: {str(e)}")
                return np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
            
            # 转换为numpy数组
            try:
                img_array = np.array(gray_image)
            except Exception as e:
                logger.error(f"Failed to convert image to numpy array: {str(e)}")
                return np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
            
            # 验证数组
            if img_array.size == 0:
                logger.error("Image array is empty")
                return np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
            
            # 检查图像尺寸是否足够大
            min_size = 2 * self.config.lbp_radius + 1
            if img_array.shape[0] < min_size or img_array.shape[1] < min_size:
                logger.warning(f"Image too small for LBP radius {self.config.lbp_radius}")
                return np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
            
            # 计算LBP
            try:
                lbp = local_binary_pattern(
                    img_array, 
                    self.config.lbp_n_points, 
                    self.config.lbp_radius, 
                    method='uniform'
                )
                
                # 验证LBP结果
                if lbp is None or lbp.size == 0:
                    logger.error("LBP computation failed")
                    return np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
                
                # 检查并处理异常值
                lbp = np.nan_to_num(lbp, nan=0.0, posinf=0.0, neginf=0.0)
                
            except Exception as e:
                logger.error(f"Error computing LBP: {str(e)}")
                return np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
            
            # 计算LBP直方图
            try:
                n_bins = self.config.lbp_n_points + 2  # uniform模式的bin数量
                hist, _ = np.histogram(lbp.ravel(), bins=n_bins, range=(0, n_bins))
                
                # 验证直方图
                if hist is None or len(hist) == 0:
                    logger.error("LBP histogram computation failed")
                    return np.zeros(n_bins, dtype=np.float32)
                
                # 归一化
                hist = hist.astype(np.float64)  # 使用更高精度
                hist_sum = hist.sum()
                
                if hist_sum > 0:
                    hist = hist / hist_sum
                else:
                    logger.warning("LBP histogram sum is zero")
                    hist = np.zeros_like(hist)
                
                # 检查并处理异常值
                hist = np.nan_to_num(hist, nan=0.0, posinf=1.0, neginf=0.0)
                
                result = hist.astype(np.float32)
                
                # 最终验证
                if result.size != n_bins:
                    logger.error(f"Unexpected result size: {result.size}, expected: {n_bins}")
                    return np.zeros(n_bins, dtype=np.float32)
                
                return result
                
            except Exception as e:
                logger.error(f"Error computing LBP histogram: {str(e)}")
                return np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error extracting LBP features: {str(e)}")
            return np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
    
    def extract_glcm_features(self, image: Image.Image) -> np.ndarray:
        """提取GLCM（灰度共生矩阵）特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: GLCM特征
        """
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return np.zeros(32, dtype=np.float32)  # 4 properties * 2 distances * 4 angles
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return np.zeros(32, dtype=np.float32)
            
            # 检查图像尺寸
            if image.size[0] == 0 or image.size[1] == 0:
                logger.error("Image has zero dimensions")
                return np.zeros(32, dtype=np.float32)
            
            # 转换为灰度图像
            try:
                if image.mode != 'L':
                    gray_image = image.convert('L')
                else:
                    gray_image = image
            except Exception as e:
                logger.error(f"Failed to convert image to grayscale: {str(e)}")
                return np.zeros(32, dtype=np.float32)
            
            # 转换为numpy数组
            try:
                img_array = np.array(gray_image)
            except Exception as e:
                logger.error(f"Failed to convert image to numpy array: {str(e)}")
                return np.zeros(32, dtype=np.float32)
            
            # 验证数组
            if img_array.size == 0:
                logger.error("Image array is empty")
                return np.zeros(32, dtype=np.float32)
            
            # 检查图像尺寸是否足够大
            if img_array.shape[0] < 3 or img_array.shape[1] < 3:
                logger.warning("Image too small for GLCM computation")
                return np.zeros(32, dtype=np.float32)
            
            # 量化灰度级别以减少计算复杂度
            try:
                img_array = (img_array // 32).astype(np.uint8)  # 256 -> 8 levels
                
                # 确保量化后的值在有效范围内
                img_array = np.clip(img_array, 0, 7)
                
                # 检查量化结果
                if img_array.size == 0:
                    logger.error("Image quantization failed")
                    return np.zeros(32, dtype=np.float32)
                
            except Exception as e:
                logger.error(f"Error quantizing image: {str(e)}")
                return np.zeros(32, dtype=np.float32)
            
            # 计算GLCM
            try:
                distances = [1, 2]
                angles = [0, np.pi/4, np.pi/2, 3*np.pi/4]
                
                glcm = graycomatrix(
                    img_array, 
                    distances=distances, 
                    angles=angles, 
                    levels=8,
                    symmetric=True, 
                    normed=True
                )
                
                # 验证GLCM结果
                if glcm is None or glcm.size == 0:
                    logger.error("GLCM computation failed")
                    return np.zeros(32, dtype=np.float32)
                
                # 检查并处理异常值
                glcm = np.nan_to_num(glcm, nan=0.0, posinf=1.0, neginf=0.0)
                
            except Exception as e:
                logger.error(f"Error computing GLCM: {str(e)}")
                return np.zeros(32, dtype=np.float32)
            
            # 提取GLCM属性
            try:
                properties = ['contrast', 'dissimilarity', 'homogeneity', 'energy']
                features = []
                
                for prop in properties:
                    try:
                        prop_values = graycoprops(glcm, prop)
                        
                        # 验证属性值
                        if prop_values is None or prop_values.size == 0:
                            logger.warning(f"GLCM property {prop} computation failed")
                            prop_values = np.zeros((len(distances), len(angles)))
                        
                        # 检查并处理异常值
                        prop_values = np.nan_to_num(prop_values, nan=0.0, posinf=1.0, neginf=0.0)
                        
                        # 确保值在合理范围内
                        prop_values = np.clip(prop_values, -1e6, 1e6)
                        
                        features.extend(prop_values.flatten())
                        
                    except Exception as e:
                        logger.warning(f"Error computing GLCM property {prop}: {str(e)}")
                        # 添加默认值
                        features.extend(np.zeros(len(distances) * len(angles)))
                
                result = np.array(features, dtype=np.float32)
                
                # 最终验证
                expected_size = len(properties) * len(distances) * len(angles)
                if result.size != expected_size:
                    logger.error(f"Unexpected result size: {result.size}, expected: {expected_size}")
                    return np.zeros(32, dtype=np.float32)
                
                # 最终清理异常值
                result = np.nan_to_num(result, nan=0.0, posinf=1.0, neginf=0.0)
                
                return result
                
            except Exception as e:
                logger.error(f"Error extracting GLCM properties: {str(e)}")
                return np.zeros(32, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error extracting GLCM features: {str(e)}")
            return np.zeros(32, dtype=np.float32)  # 4 properties * 2 distances * 4 angles
    
    def extract_features(self, image: Image.Image) -> np.ndarray:
        """提取所有纹理特征
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 纹理特征向量
        """
        try:
            # 输入验证
            if image is None:
                logger.error("Input image is None")
                return np.zeros(self.config.lbp_n_points + 2 + 32, dtype=np.float32)
                
            if not isinstance(image, Image.Image):
                logger.error(f"Invalid image type: {type(image)}")
                return np.zeros(self.config.lbp_n_points + 2 + 32, dtype=np.float32)
            
            features = []
            
            # LBP特征
            try:
                lbp_features = self.extract_lbp_features(image)
                
                # 验证LBP特征
                if lbp_features is None or lbp_features.size == 0:
                    logger.warning("LBP features extraction failed, using zeros")
                    lbp_features = np.zeros(self.config.lbp_n_points + 2, dtype=np.float32)
                
                # 检查并处理异常值
                lbp_features = np.nan_to_num(lbp_features, nan=0.0, posinf=1.0, neginf=0.0)
                
                features.extend(lbp_features)
                
            except Exception as e:
                logger.error(f"Error extracting LBP features: {str(e)}")
                features.extend(np.zeros(self.config.lbp_n_points + 2, dtype=np.float32))
            
            # GLCM特征
            try:
                glcm_features = self.extract_glcm_features(image)
                
                # 验证GLCM特征
                if glcm_features is None or glcm_features.size == 0:
                    logger.warning("GLCM features extraction failed, using zeros")
                    glcm_features = np.zeros(32, dtype=np.float32)
                
                # 检查并处理异常值
                glcm_features = np.nan_to_num(glcm_features, nan=0.0, posinf=1.0, neginf=0.0)
                
                features.extend(glcm_features)
                
            except Exception as e:
                logger.error(f"Error extracting GLCM features: {str(e)}")
                features.extend(np.zeros(32, dtype=np.float32))
            
            # 转换为numpy数组
            try:
                result = np.array(features, dtype=np.float32)
                
                # 最终验证
                if result.size == 0:
                    logger.error("Final texture features array is empty")
                    return np.zeros(self.config.lbp_n_points + 2 + 32, dtype=np.float32)
                
                # 最终清理异常值
                result = np.nan_to_num(result, nan=0.0, posinf=1.0, neginf=0.0)
                
                return result
                
            except Exception as e:
                logger.error(f"Error creating final texture features array: {str(e)}")
                return np.zeros(self.config.lbp_n_points + 2 + 32, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error extracting texture features: {str(e)}")
            return np.zeros(self.config.lbp_n_points + 2 + 32, dtype=np.float32)