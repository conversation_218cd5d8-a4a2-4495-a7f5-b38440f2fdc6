#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息提示辅助模块

该模块提供各种消息对话框的辅助功能。
"""

from PyQt6.QtWidgets import QWidget, QMessageBox


class MessageHelper:
    """消息提示辅助类"""
    
    @staticmethod
    def show_info(parent: QWidget, title: str, message: str) -> int:
        """显示信息对话框
        
        Args:
            parent: 父控件
            title: 标题
            message: 消息内容
            
        Returns:
            int: 用户选择的按钮
        """
        return QMessageBox.information(parent, title, message)
    
    @staticmethod
    def show_warning(parent: QWidget, title: str, message: str) -> int:
        """显示警告对话框
        
        Args:
            parent: 父控件
            title: 标题
            message: 消息内容
            
        Returns:
            int: 用户选择的按钮
        """
        return QMessageBox.warning(parent, title, message)
    
    @staticmethod
    def show_error(parent: QWidget, title: str, message: str) -> int:
        """显示错误对话框
        
        Args:
            parent: 父控件
            title: 标题
            message: 消息内容
            
        Returns:
            int: 用户选择的按钮
        """
        return QMessageBox.critical(parent, title, message)
    
    @staticmethod
    def show_question(parent: QWidget, title: str, message: str,
                     buttons: QMessageBox.StandardButton = None) -> int:
        """显示询问对话框
        
        Args:
            parent: 父控件
            title: 标题
            message: 消息内容
            buttons: 按钮组合
            
        Returns:
            int: 用户选择的按钮
        """
        if buttons is None:
            buttons = (QMessageBox.StandardButton.Yes | 
                      QMessageBox.StandardButton.No)
        
        return QMessageBox.question(parent, title, message, buttons)
    
    @staticmethod
    def show_custom_message(parent: QWidget, title: str, message: str,
                          icon: QMessageBox.Icon, 
                          buttons: QMessageBox.StandardButton,
                          default_button: QMessageBox.StandardButton = None) -> int:
        """显示自定义消息对话框
        
        Args:
            parent: 父控件
            title: 标题
            message: 消息内容
            icon: 图标类型
            buttons: 按钮组合
            default_button: 默认按钮
            
        Returns:
            int: 用户选择的按钮
        """
        msg_box = QMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.setStandardButtons(buttons)
        
        if default_button:
            msg_box.setDefaultButton(default_button)
        
        return msg_box.exec()