#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征提取器工厂

提供创建特征提取器的便捷方法。
"""

import logging
from typing import Optional

from ..config.feature_config import FeatureExtractorConfig, TraditionalFeatureConfig

logger = logging.getLogger(__name__)


class ExtractorFactory:
    """特征提取器工厂"""
    
    @staticmethod
    def create_feature_extractor(model_name: str = "resnet50", 
                                use_gpu: bool = True,
                                extract_traditional: bool = True,
                                cache_dir: Optional[str] = None,
                                use_cache: bool = True):
        """创建特征提取器的便捷函数
        
        Args:
            model_name: 模型名称
            use_gpu: 是否使用GPU
            extract_traditional: 是否提取传统特征
            cache_dir: 缓存目录
            use_cache: 是否使用缓存
            
        Returns:
            FeatureExtractor: 特征提取器实例
        """
        try:
            # 延迟导入避免循环依赖
            from ..core.feature_extractor import FeatureExtractor
            
            # 创建配置
            config = FeatureExtractorConfig(
                model_name=model_name,
                use_gpu=use_gpu,
                cache_dir=cache_dir,
                use_cache=use_cache
            )
            
            # 创建传统特征配置
            traditional_config = None
            if extract_traditional:
                traditional_config = TraditionalFeatureConfig()
            
            # 创建特征提取器
            extractor = FeatureExtractor(config, traditional_config)
            
            logger.info(f"创建特征提取器成功: {model_name}")
            return extractor
            
        except Exception as e:
            logger.error(f"创建特征提取器失败: {str(e)}")
            raise
    
    @staticmethod
    def create_batch_extractor(feature_extractor):
        """创建批量特征提取器
        
        Args:
            feature_extractor: 特征提取器实例
            
        Returns:
            BatchExtractor: 批量特征提取器实例
        """
        try:
            from ..batch.batch_extractor import BatchExtractor
            
            batch_extractor = BatchExtractor(feature_extractor)
            logger.info("创建批量特征提取器成功")
            return batch_extractor
            
        except Exception as e:
            logger.error(f"创建批量特征提取器失败: {str(e)}")
            raise