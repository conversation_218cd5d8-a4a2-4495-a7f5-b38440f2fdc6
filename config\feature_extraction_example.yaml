# 高级特征提取配置示例
# 本文件展示如何配置传统特征提取的各种参数

# 应用程序配置
app:
  name: "Fabric Search"
  version: "2.0.0"
  debug: false

# 特征提取配置
feature_extraction:
  # 基本开关 - 控制是否提取各类特征
  extract_color: true      # 是否提取颜色特征
  extract_texture: true    # 是否提取纹理特征
  extract_shape: true      # 是否提取形状特征
  
  # 颜色特征参数
  hist_bins: 32           # 颜色直方图分箱数 (推荐: 16-128)
  n_dominant_colors: 5    # 主要颜色数量 (推荐: 3-10)
  
  # 纹理特征参数
  lbp_radius: 3          # LBP半径 (推荐: 1-8)
  lbp_n_points: 24       # LBP采样点数 (推荐: 8-48)
  
  # 形状特征参数
  n_fourier_descriptors: 32  # 傅里叶描述子数量 (推荐: 16-128)

# 不同场景的配置示例
configurations:
  # 高精度配置 - 适用于对准确性要求高的场景
  high_precision:
    feature_extraction:
      extract_color: true
      extract_texture: true
      extract_shape: true
      hist_bins: 128
      n_dominant_colors: 10
      lbp_radius: 8
      lbp_n_points: 48
      n_fourier_descriptors: 128
  
  # 快速配置 - 适用于对速度要求高的场景
  fast:
    feature_extraction:
      extract_color: true
      extract_texture: true
      extract_shape: false  # 禁用形状特征以提高速度
      hist_bins: 16
      n_dominant_colors: 3
      lbp_radius: 1
      lbp_n_points: 8
      n_fourier_descriptors: 16
  
  # 颜色专用配置 - 只关注颜色特征
  color_only:
    feature_extraction:
      extract_color: true
      extract_texture: false
      extract_shape: false
      hist_bins: 64
      n_dominant_colors: 8
  
  # 纹理专用配置 - 只关注纹理特征
  texture_only:
    feature_extraction:
      extract_color: false
      extract_texture: true
      extract_shape: false
      lbp_radius: 5
      lbp_n_points: 32
  
  # 形状专用配置 - 只关注形状特征
  shape_only:
    feature_extraction:
      extract_color: false
      extract_texture: false
      extract_shape: true
      n_fourier_descriptors: 64

# 参数说明
parameter_descriptions:
  hist_bins: |
    颜色直方图分箱数
    - 较小值(16-32): 计算快，特征维度低，可能丢失细节
    - 较大值(64-128): 计算慢，特征维度高，保留更多细节
  
  n_dominant_colors: |
    主要颜色数量
    - 较小值(3-5): 提取最主要的颜色，适合简单图像
    - 较大值(8-10): 提取更多颜色信息，适合复杂图像
  
  lbp_radius: |
    LBP(局部二值模式)半径
    - 较小值(1-3): 捕获细粒度纹理，计算快
    - 较大值(5-8): 捕获粗粒度纹理，计算慢
  
  lbp_n_points: |
    LBP采样点数
    - 较小值(8-16): 计算快，纹理描述简单
    - 较大值(24-48): 计算慢，纹理描述详细
  
  n_fourier_descriptors: |
    傅里叶描述子数量
    - 较小值(16-32): 形状描述简单，计算快
    - 较大值(64-128): 形状描述详细，计算慢

# 使用建议
usage_recommendations:
  - "对于面料搜索，建议启用所有特征类型以获得最佳匹配效果"
  - "如果处理速度是关键因素，可以禁用形状特征或减少参数值"
  - "对于颜色敏感的搜索，增加hist_bins和n_dominant_colors"
  - "对于纹理敏感的搜索，增加lbp_radius和lbp_n_points"
  - "对于形状敏感的搜索，增加n_fourier_descriptors"
  - "建议根据实际数据集进行参数调优"