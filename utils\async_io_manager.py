#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步I/O管理模块

提供高效的异步文件I/O操作，支持批量读写和压缩。
"""

import asyncio
import aiofiles
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Union, Callable, Tuple
from pathlib import Path
import numpy as np
import pickle
import lz4.frame
import logging
from dataclasses import dataclass


@dataclass
class IOTask:
    """I/O任务"""
    operation: str              # 操作类型: 'read', 'write'
    file_path: Union[str, Path] # 文件路径
    data: Any = None           # 写入数据
    callback: Optional[Callable] = None  # 完成回调
    compress: bool = False     # 是否压缩
    
    
@dataclass
class IOResult:
    """I/O结果"""
    task: IOTask
    success: bool
    data: Any = None
    error: Optional[str] = None
    duration: float = 0.0


class AsyncIOManager:
    """异步I/O管理器"""
    
    def __init__(self, 
                 max_workers: int = 8,
                 buffer_size: int = 65536,
                 enable_compression: bool = True,
                 compression_level: int = 3):
        """初始化异步I/O管理器
        
        Args:
            max_workers: 最大工作线程数
            buffer_size: I/O缓冲区大小
            enable_compression: 是否启用压缩
            compression_level: 压缩级别
        """
        self.max_workers = max_workers
        self.buffer_size = buffer_size
        self.enable_compression = enable_compression
        self.compression_level = compression_level
        
        self.logger = logging.getLogger(__name__)
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._pending_tasks: List[IOTask] = []
        self._lock = threading.RLock()
        
        # 统计信息
        self._total_reads = 0
        self._total_writes = 0
        self._total_bytes_read = 0
        self._total_bytes_written = 0
        self._total_time = 0.0
    
    async def read_file_async(self, file_path: Union[str, Path], 
                            decompress: bool = False) -> Optional[bytes]:
        """异步读取文件
        
        Args:
            file_path: 文件路径
            decompress: 是否解压缩
            
        Returns:
            Optional[bytes]: 文件内容，失败返回None
        """
        try:
            start_time = time.time()
            
            async with aiofiles.open(file_path, 'rb') as f:
                data = await f.read()
            
            if decompress and data:
                try:
                    data = lz4.frame.decompress(data)
                except Exception as e:
                    self.logger.warning(f"解压缩失败: {e}")
            
            duration = time.time() - start_time
            self._update_stats('read', len(data) if data else 0, duration)
            
            return data
            
        except Exception as e:
            self.logger.error(f"异步读取文件失败 {file_path}: {e}")
            return None
    
    async def write_file_async(self, file_path: Union[str, Path], 
                             data: bytes, compress: bool = False) -> bool:
        """异步写入文件
        
        Args:
            file_path: 文件路径
            data: 要写入的数据
            compress: 是否压缩
            
        Returns:
            bool: 是否成功
        """
        try:
            start_time = time.time()
            
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 压缩数据
            if compress:
                try:
                    data = lz4.frame.compress(data, compression_level=self.compression_level)
                except Exception as e:
                    self.logger.warning(f"压缩失败: {e}")
            
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(data)
            
            duration = time.time() - start_time
            self._update_stats('write', len(data), duration)
            
            return True
            
        except Exception as e:
            self.logger.error(f"异步写入文件失败 {file_path}: {e}")
            return False
    
    def read_file_sync(self, file_path: Union[str, Path], 
                      decompress: bool = False) -> Optional[bytes]:
        """同步读取文件
        
        Args:
            file_path: 文件路径
            decompress: 是否解压缩
            
        Returns:
            Optional[bytes]: 文件内容
        """
        try:
            start_time = time.time()
            
            with open(file_path, 'rb', buffering=self.buffer_size) as f:
                data = f.read()
            
            if decompress and data:
                try:
                    data = lz4.frame.decompress(data)
                except Exception as e:
                    self.logger.warning(f"解压缩失败: {e}")
            
            duration = time.time() - start_time
            self._update_stats('read', len(data) if data else 0, duration)
            
            return data
            
        except Exception as e:
            self.logger.error(f"同步读取文件失败 {file_path}: {e}")
            return None
    
    def write_file_sync(self, file_path: Union[str, Path], 
                       data: bytes, compress: bool = False) -> bool:
        """同步写入文件
        
        Args:
            file_path: 文件路径
            data: 要写入的数据
            compress: 是否压缩
            
        Returns:
            bool: 是否成功
        """
        try:
            start_time = time.time()
            
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 压缩数据
            if compress:
                try:
                    data = lz4.frame.compress(data, compression_level=self.compression_level)
                except Exception as e:
                    self.logger.warning(f"压缩失败: {e}")
            
            with open(file_path, 'wb', buffering=self.buffer_size) as f:
                f.write(data)
            
            duration = time.time() - start_time
            self._update_stats('write', len(data), duration)
            
            return True
            
        except Exception as e:
            self.logger.error(f"同步写入文件失败 {file_path}: {e}")
            return False
    
    def batch_read_files(self, file_paths: List[Union[str, Path]], 
                        decompress: bool = False) -> List[Tuple[Union[str, Path], Optional[bytes]]]:
        """批量读取文件
        
        Args:
            file_paths: 文件路径列表
            decompress: 是否解压缩
            
        Returns:
            List[Tuple[Union[str, Path], Optional[bytes]]]: (文件路径, 内容)列表
        """
        results = []
        
        # 使用线程池并行读取
        future_to_path = {
            self._executor.submit(self.read_file_sync, path, decompress): path
            for path in file_paths
        }
        
        for future in as_completed(future_to_path):
            path = future_to_path[future]
            try:
                data = future.result()
                results.append((path, data))
            except Exception as e:
                self.logger.error(f"批量读取文件失败 {path}: {e}")
                results.append((path, None))
        
        return results
    
    def batch_write_files(self, file_data: List[Tuple[Union[str, Path], bytes]], 
                         compress: bool = False) -> List[Tuple[Union[str, Path], bool]]:
        """批量写入文件
        
        Args:
            file_data: (文件路径, 数据)列表
            compress: 是否压缩
            
        Returns:
            List[Tuple[Union[str, Path], bool]]: (文件路径, 是否成功)列表
        """
        results = []
        
        # 使用线程池并行写入
        future_to_path = {
            self._executor.submit(self.write_file_sync, path, data, compress): path
            for path, data in file_data
        }
        
        for future in as_completed(future_to_path):
            path = future_to_path[future]
            try:
                success = future.result()
                results.append((path, success))
            except Exception as e:
                self.logger.error(f"批量写入文件失败 {path}: {e}")
                results.append((path, False))
        
        return results
    
    def save_numpy_array(self, file_path: Union[str, Path], 
                        array: np.ndarray, compress: bool = True) -> bool:
        """保存NumPy数组
        
        Args:
            file_path: 文件路径
            array: NumPy数组
            compress: 是否压缩
            
        Returns:
            bool: 是否成功
        """
        try:
            # 转换为字节
            data = array.tobytes()
            
            # 保存形状和数据类型信息
            metadata = {
                'shape': array.shape,
                'dtype': str(array.dtype)
            }
            metadata_bytes = pickle.dumps(metadata)
            
            # 组合数据
            combined_data = len(metadata_bytes).to_bytes(4, 'little') + metadata_bytes + data
            
            return self.write_file_sync(file_path, combined_data, compress)
            
        except Exception as e:
            self.logger.error(f"保存NumPy数组失败 {file_path}: {e}")
            return False
    
    def load_numpy_array(self, file_path: Union[str, Path], 
                        decompress: bool = True) -> Optional[np.ndarray]:
        """加载NumPy数组
        
        Args:
            file_path: 文件路径
            decompress: 是否解压缩
            
        Returns:
            Optional[np.ndarray]: NumPy数组
        """
        try:
            data = self.read_file_sync(file_path, decompress)
            if data is None:
                return None
            
            # 解析元数据
            metadata_size = int.from_bytes(data[:4], 'little')
            metadata_bytes = data[4:4+metadata_size]
            array_data = data[4+metadata_size:]
            
            metadata = pickle.loads(metadata_bytes)
            
            # 重建数组
            array = np.frombuffer(array_data, dtype=metadata['dtype'])
            return array.reshape(metadata['shape'])
            
        except Exception as e:
            self.logger.error(f"加载NumPy数组失败 {file_path}: {e}")
            return None
    
    def _update_stats(self, operation: str, bytes_count: int, duration: float) -> None:
        """更新统计信息"""
        with self._lock:
            if operation == 'read':
                self._total_reads += 1
                self._total_bytes_read += bytes_count
            elif operation == 'write':
                self._total_writes += 1
                self._total_bytes_written += bytes_count
            
            self._total_time += duration
    
    def get_stats(self) -> Dict[str, Any]:
        """获取I/O统计信息"""
        with self._lock:
            return {
                'total_reads': self._total_reads,
                'total_writes': self._total_writes,
                'total_bytes_read': self._total_bytes_read,
                'total_bytes_written': self._total_bytes_written,
                'total_bytes_read_mb': self._total_bytes_read / 1024 / 1024,
                'total_bytes_written_mb': self._total_bytes_written / 1024 / 1024,
                'total_time': self._total_time,
                'avg_read_speed_mbps': (self._total_bytes_read / 1024 / 1024) / max(self._total_time, 0.001),
                'avg_write_speed_mbps': (self._total_bytes_written / 1024 / 1024) / max(self._total_time, 0.001)
            }
    
    def shutdown(self) -> None:
        """关闭I/O管理器"""
        self._executor.shutdown(wait=True)


# 全局I/O管理器实例
_io_manager: Optional[AsyncIOManager] = None


def get_io_manager() -> AsyncIOManager:
    """获取全局I/O管理器实例"""
    global _io_manager
    if _io_manager is None:
        _io_manager = AsyncIOManager()
    return _io_manager
