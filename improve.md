1. 增强异常日志，定位具体出错点
在 ResultPanel.set_results 的 except 里，打印 results 的内容和类型，便于定位。
2. 修复 FabricImage 构造兼容性
FabricImage 的 category 字段是 Optional[str]，但构造时用 result.get('category', '')，如果 dict 里是 None 也没问题。
但如果 dict 里有多余字段，FabricImage(**dict) 可能会报错。
建议用 FabricImage.from_dict(result) 替换手动构造，自动过滤多余字段。
3. 保证 similarity_score 一定赋值
有些地方直接 fabric_image.similarity_score = result['similarity']，但 FabricImage 没有这个字段，应该用 setattr 或扩展 dataclass。
4. 分页配置检查
确认 self.result_config.items_per_page 不为0。