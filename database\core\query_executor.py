#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查询执行器

该模块提供数据库查询的执行功能。
"""

import sqlite3
import logging
from typing import Optional, Tuple, List, Any, Dict

from .connection_manager import ConnectionManager


class QueryExecutor:
    """数据库查询执行器"""
    
    def __init__(self, connection_manager: ConnectionManager):
        """初始化查询执行器
        
        Args:
            connection_manager: 连接管理器
        """
        self.connection_manager = connection_manager
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def execute_query(self, sql: str, params: Optional[Tuple] = None) -> List[sqlite3.Row]:
        """执行查询
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            List[sqlite3.Row]: 查询结果
        """
        with self.connection_manager.connection_context() as conn:
            cursor = conn.cursor()
            
            try:
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                return cursor.fetchall()
                
            except Exception as e:
                self.logger.error(f"执行查询失败: {e}, SQL: {sql}")
                raise
            finally:
                cursor.close()
    
    def execute_update(self, sql: str, params: Optional[Tuple] = None) -> int:
        """执行更新操作
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            int: 影响的行数
        """
        with self.connection_manager.connection_context() as conn:
            cursor = conn.cursor()
            
            try:
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                conn.commit()
                return cursor.rowcount
                
            except Exception as e:
                self.logger.error(f"执行更新失败: {e}, SQL: {sql}")
                conn.rollback()
                raise
            finally:
                cursor.close()
    
    def execute_insert(self, sql: str, params: Optional[Tuple] = None) -> int:
        """执行插入操作
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            int: 插入记录的ID
        """
        with self.connection_manager.connection_context() as conn:
            cursor = conn.cursor()
            
            try:
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                conn.commit()
                return cursor.lastrowid
                
            except Exception as e:
                self.logger.error(f"执行插入失败: {e}, SQL: {sql}")
                conn.rollback()
                raise
            finally:
                cursor.close()
    
    def execute_batch(self, sql: str, params_list: List[Tuple]) -> int:
        """执行批量操作
        
        Args:
            sql: SQL语句
            params_list: 参数列表
            
        Returns:
            int: 影响的行数
        """
        with self.connection_manager.connection_context() as conn:
            cursor = conn.cursor()
            
            try:
                cursor.executemany(sql, params_list)
                conn.commit()
                return cursor.rowcount
                
            except Exception as e:
                self.logger.error(f"执行批量操作失败: {e}, SQL: {sql}")
                conn.rollback()
                raise
            finally:
                cursor.close()
    
    def execute_script(self, script: str):
        """执行SQL脚本
        
        Args:
            script: SQL脚本
        """
        with self.connection_manager.connection_context() as conn:
            cursor = conn.cursor()
            
            try:
                cursor.executescript(script)
                conn.commit()
                
            except Exception as e:
                self.logger.error(f"执行脚本失败: {e}")
                conn.rollback()
                raise
            finally:
                cursor.close()
    
    def get_table_info(self, table_name: str) -> List[sqlite3.Row]:
        """获取表信息
        
        Args:
            table_name: 表名
            
        Returns:
            List[sqlite3.Row]: 表信息
        """
        sql = "PRAGMA table_info(?)"
        return self.execute_query(sql, (table_name,))
    
    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 表是否存在
        """
        sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
        result = self.execute_query(sql, (table_name,))
        return len(result) > 0
    
    def get_database_size(self) -> int:
        """获取数据库大小（字节）
        
        Returns:
            int: 数据库大小
        """
        sql = "SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()"
        result = self.execute_query(sql)
        return result[0][0] if result else 0
    
    def vacuum_database(self):
        """清理数据库"""
        with self.connection_manager.connection_context() as conn:
            conn.execute("VACUUM")
            conn.commit()
    
    def analyze_database(self):
        """分析数据库"""
        with self.connection_manager.connection_context() as conn:
            conn.execute("ANALYZE")
            conn.commit()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {}
        
        # 获取数据库大小
        stats['database_size'] = self.get_database_size()
        
        # 获取表统计
        tables_sql = "SELECT name FROM sqlite_master WHERE type='table'"
        tables = self.execute_query(tables_sql)
        
        table_stats = {}
        for table in tables:
            table_name = table[0]
            count_sql = f"SELECT COUNT(*) FROM {table_name}"
            count_result = self.execute_query(count_sql)
            table_stats[table_name] = count_result[0][0] if count_result else 0
        
        stats['table_counts'] = table_stats
        
        return stats