#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本搜索组件

该模块提供基于文本的搜索功能界面。
"""

from typing import Dict, Any, List

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QCheckBox,
    QGroupBox, QSpinBox, QFormLayout
)
from PyQt6.QtCore import pyqtSignal

from utils.log_utils import LoggerMixin
from gui.helpers.message_helper import MessageHelper
from gui.widget_factory import WidgetFactory, ButtonStyle, ButtonSize


class TextSearchWidget(QWidget, LoggerMixin):
    """文本搜索组件"""
    
    # 信号
    searchRequested = pyqtSignal(str, dict)  # 搜索请求
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.widget_factory = WidgetFactory()
        
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 搜索输入
        self._setup_search_input(layout)
        
        # 搜索选项
        self._setup_search_options(layout)
        
        # 搜索范围
        self._setup_search_scope(layout)
        
        # 搜索参数
        self._setup_search_params(layout)
        
        # 搜索按钮
        self.search_btn = self.widget_factory.create_button(
            "开始搜索", "search", None, ButtonStyle.SUCCESS, ButtonSize.LARGE,
            click_handler=self.start_search
        )
        layout.addWidget(self.search_btn)
        
        layout.addStretch()
    
    def _setup_search_input(self, layout):
        """设置搜索输入"""
        input_group = self.widget_factory.create_group_box("搜索关键词")
        input_layout = QVBoxLayout(input_group)
        
        # 搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("请输入搜索关键词...")
        self.search_input.setMinimumHeight(30)
        input_layout.addWidget(self.search_input)
        
        layout.addWidget(input_group)
    
    def _setup_search_options(self, layout):
        """设置搜索选项"""
        options_group = self.widget_factory.create_group_box("搜索选项")
        options_layout = QVBoxLayout(options_group)
        
        # 第一行选项
        row1_layout = QHBoxLayout()
        self.case_sensitive_cb = self.widget_factory.create_checkbox("区分大小写")
        self.whole_word_cb = self.widget_factory.create_checkbox("全词匹配")
        row1_layout.addWidget(self.case_sensitive_cb)
        row1_layout.addWidget(self.whole_word_cb)
        row1_layout.addStretch()
        options_layout.addLayout(row1_layout)
        
        # 第二行选项
        row2_layout = QHBoxLayout()
        self.regex_cb = self.widget_factory.create_checkbox("正则表达式")
        self.fuzzy_cb = self.widget_factory.create_checkbox("模糊匹配")
        row2_layout.addWidget(self.regex_cb)
        row2_layout.addWidget(self.fuzzy_cb)
        row2_layout.addStretch()
        options_layout.addLayout(row2_layout)
        
        layout.addWidget(options_group)
    
    def _setup_search_scope(self, layout):
        """设置搜索范围"""
        scope_group = self.widget_factory.create_group_box("搜索范围")
        scope_layout = QVBoxLayout(scope_group)
        
        # 第一行范围
        row1_layout = QHBoxLayout()
        self.filename_cb = self.widget_factory.create_checkbox("文件名", checked=True)
        self.description_cb = self.widget_factory.create_checkbox("描述", checked=True)
        row1_layout.addWidget(self.filename_cb)
        row1_layout.addWidget(self.description_cb)
        row1_layout.addStretch()
        scope_layout.addLayout(row1_layout)
        
        # 第二行范围
        row2_layout = QHBoxLayout()
        self.tags_cb = self.widget_factory.create_checkbox("标签", checked=True)
        self.category_cb = self.widget_factory.create_checkbox("类别", checked=True)
        row2_layout.addWidget(self.tags_cb)
        row2_layout.addWidget(self.category_cb)
        row2_layout.addStretch()
        scope_layout.addLayout(row2_layout)
        
        layout.addWidget(scope_group)
    
    def _setup_search_params(self, layout):
        """设置搜索参数"""
        params_group = self.widget_factory.create_group_box("搜索参数")
        params_layout = QFormLayout(params_group)
        
        # 最大结果数
        try:
            from config.config_manager import ConfigManager
            config_manager = ConfigManager()
            default_max_results = config_manager.config.search.top_k
        except Exception:
            default_max_results = 50
            
        self.max_results_spin = self.widget_factory.create_spin_box(
            minimum=1, maximum=1000, value=default_max_results
        )
        params_layout.addRow("最大结果数:", self.max_results_spin)
        
        layout.addWidget(params_group)
    
    def connect_signals(self):
        """连接信号"""
        # 搜索输入框回车事件
        self.search_input.returnPressed.connect(self.start_search)
        
        # 正则表达式和模糊匹配互斥
        self.regex_cb.stateChanged.connect(self._on_regex_changed)
        self.fuzzy_cb.stateChanged.connect(self._on_fuzzy_changed)
    
    def _on_regex_changed(self, state):
        """正则表达式选项变化"""
        if state and self.fuzzy_cb.isChecked():
            self.fuzzy_cb.setChecked(False)
    
    def _on_fuzzy_changed(self, state):
        """模糊匹配选项变化"""
        if state and self.regex_cb.isChecked():
            self.regex_cb.setChecked(False)
    
    def start_search(self):
        """开始搜索"""
        try:
            query = self.search_input.text().strip()
            if not query:
                MessageHelper.show_warning(self, "警告", "请输入搜索关键词")
                return
            
            # 检查搜索范围
            if not self._has_search_scope():
                MessageHelper.show_warning(self, "警告", "请至少选择一个搜索范围")
                return
            
            # 构建搜索参数
            params = self.get_search_params()
            
            # 发送搜索请求
            self.searchRequested.emit(query, params)
            
        except Exception as e:
            self.logger.error(f"开始文本搜索失败: {e}")
            MessageHelper.show_error(self, "错误", f"开始搜索失败: {e}")
    
    def _has_search_scope(self) -> bool:
        """检查是否有选择搜索范围"""
        return any([
            self.filename_cb.isChecked(),
            self.description_cb.isChecked(),
            self.tags_cb.isChecked(),
            self.category_cb.isChecked()
        ])
    
    def get_search_params(self) -> Dict[str, Any]:
        """获取搜索参数"""
        # 获取搜索范围
        search_fields = []
        if self.filename_cb.isChecked():
            search_fields.append("filename")
        if self.description_cb.isChecked():
            search_fields.append("description")
        if self.tags_cb.isChecked():
            search_fields.append("tags")
        if self.category_cb.isChecked():
            search_fields.append("category")
        
        return {
            "max_results": self.max_results_spin.value(),
            "case_sensitive": self.case_sensitive_cb.isChecked(),
            "whole_word": self.whole_word_cb.isChecked(),
            "use_regex": self.regex_cb.isChecked(),
            "fuzzy_search": self.fuzzy_cb.isChecked(),
            "search_fields": search_fields
        }
    
    def get_search_query(self) -> str:
        """获取搜索查询"""
        return self.search_input.text().strip()
    
    def set_search_query(self, query: str):
        """设置搜索查询"""
        self.search_input.setText(query)
    
    def clear_search(self):
        """清除搜索"""
        self.search_input.clear()
        
    def set_searching(self, is_searching: bool):
        """设置搜索状态
        
        Args:
            is_searching: 是否正在搜索
        """
        try:
            # 更新搜索按钮状态
            if hasattr(self, 'search_btn'):
                self.search_btn.setEnabled(not is_searching)
                self.search_btn.setText("停止搜索" if is_searching else "搜索")
            
            # 更新搜索输入框状态
            if hasattr(self, 'search_input'):
                self.search_input.setEnabled(not is_searching)
            
            # 记录日志
            self.logger.debug(f"文本搜索组件状态已更新: {'搜索中' if is_searching else '空闲'}")
            
        except Exception as e:
            self.logger.error(f"设置搜索状态失败: {e}")