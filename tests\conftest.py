import pytest
import sys
import os
from unittest.mock import Mock, patch
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

@pytest.fixture(scope="session")
def qapp():
    """创建QApplication实例用于整个测试会话"""
    if not QApplication.instance():
        app = QApplication([])
    else:
        app = QApplication.instance()
    yield app

@pytest.fixture
def mock_feature_extractor():
    """模拟特征提取器"""
    with patch('models.feature_extractor.FeatureExtractor') as mock:
        mock_instance = Mock()
        mock_instance.extract_features.return_value = {
            'deep': [0.1, 0.2, 0.3],
            'color': [0.4, 0.5, 0.6],
            'texture': [0.7, 0.8, 0.9],
            'shape': [0.1, 0.1, 0.1]
        }
        mock.return_value = mock_instance
        yield mock_instance

@pytest.fixture
def mock_database():
    """模拟特征数据库"""
    with patch('database.feature_database.FeatureDatabase') as mock:
        mock_instance = Mock()
        mock_instance.get_all_features.return_value = {
            'image1.jpg': {'deep': [0.1, 0.2], 'color': [0.3, 0.4]},
            'image2.jpg': {'deep': [0.5, 0.6], 'color': [0.7, 0.8]}
        }
        mock.return_value = mock_instance
        yield mock_instance

@pytest.fixture
def mock_search_engine():
    """模拟搜索引擎"""
    with patch('search.search_engine.SearchEngine') as mock:
        mock_instance = Mock()
        mock_instance.search.return_value = [
            {'image_path': 'result1.jpg', 'similarity': 0.95},
            {'image_path': 'result2.jpg', 'similarity': 0.87},
            {'image_path': 'result3.jpg', 'similarity': 0.76}
        ]
        mock.return_value = mock_instance
        yield mock_instance

@pytest.fixture
def sample_image_data():
    """示例图像数据"""
    return {
        'path': 'test_image.jpg',
        'features': {
            'deep': [0.1, 0.2, 0.3, 0.4],
            'color': [0.5, 0.6, 0.7],
            'texture': [0.8, 0.9, 1.0],
            'shape': [0.2, 0.3, 0.4]
        },
        'category': 'cotton'
    }