#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控件工厂 (导入转发模块)

该模块提供标准化的GUI控件创建功能。
为保持向后兼容性，该模块将导入转发到新的模块结构。
"""

# 导入基础类和枚举
from .widgets.base import WidgetConfig, ButtonStyle, ButtonSize, InputType

# 导入工厂类
from .widgets.widget_factory import WidgetFactory


# 为保持向后兼容性，导出所有类和枚举
__all__ = [
    # 基础类
    'WidgetConfig',
    'ButtonStyle',
    'ButtonSize', 
    'InputType',
    
    # 工厂类
    'WidgetFactory'
]


# 导入完成