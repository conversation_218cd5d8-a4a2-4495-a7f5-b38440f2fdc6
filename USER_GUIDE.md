# Fabric Search v2 用户操作指南

## 目录

1. [系统概述](#系统概述)
2. [安装与启动](#安装与启动)
3. [GUI界面使用](#gui界面使用)
4. [搜索功能详解](#搜索功能详解)
5. [批量处理](#批量处理)
6. [API接口使用](#api接口使用)
7. [配置管理](#配置管理)
8. [常见问题](#常见问题)
9. [高级功能](#高级功能)
10. [最佳实践](#最佳实践)

## 系统概述

Fabric Search v2 是一个基于深度学习的智能面料图像搜索系统，提供以下核心功能：

- **图像相似度搜索**：通过上传查询图像，找到相似的面料图像
- **多维度特征搜索**：支持颜色、纹理、图案等特征搜索
- **标签和分类管理**：自定义标签和分类系统
- **批量处理**：高效的批量图像导入和特征提取
- **数据管理**：完整的图像数据库管理功能

## 安装与启动

### 系统要求

- **操作系统**：Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Python**：3.8 或更高版本
- **内存**：建议 8GB 以上
- **存储**：至少 2GB 可用空间
- **显卡**：可选，支持CUDA的NVIDIA显卡可提升性能

### 快速安装

1. **下载并解压项目文件**
2. **打开命令行/终端，进入项目目录**
3. **安装依赖**：
   ```bash
   pip install -r requirements.txt
   ```
4. **初始化环境**：
   ```bash
   python main.py setup
   ```
5. **启动应用**：
   ```bash
   python main.py gui
   ```

### 启动模式

系统支持多种启动模式：

#### GUI模式（推荐新手使用）
```bash
python main.py gui
```

#### API服务器模式
```bash
python main.py server --host 0.0.0.0 --port 5000
```

#### 批量处理模式
```bash
python main.py batch /path/to/images
```

#### 配置管理模式
```bash
python main.py config list
```

## GUI界面使用

### 主界面布局

GUI界面采用三栏布局：

```
┌─────────────────────────────────────────────────────────┐
│ 菜单栏                                                   │
├─────────────────────────────────────────────────────────┤
│ 工具栏                                                   │
├──────────────┬──────────────────────┬──────────────────┤
│              │                      │                  │
│   搜索面板    │      结果展示区       │   图像详情面板    │
│              │                      │                  │
│  - 查询设置   │   - 搜索结果列表      │  - 图像预览      │
│  - 过滤条件   │   - 缩略图网格        │  - 详细信息      │
│  - 搜索历史   │   - 分页控制         │  - 标签编辑      │
│              │                      │                  │
├──────────────┴──────────────────────┴──────────────────┤
│ 状态栏                                                   │
└─────────────────────────────────────────────────────────┘
```

### 首次使用

#### 1. 导入图像数据

**方法一：通过菜单导入**
1. 点击菜单栏 "文件" → "打开文件夹"
2. 选择包含面料图像的文件夹
3. 系统会自动扫描支持的图像格式（.jpg, .jpeg, .png, .bmp, .tiff）
4. 等待特征提取完成

**方法二：拖拽导入**
1. 直接将图像文件或文件夹拖拽到主窗口
2. 系统会自动开始处理

**方法三：批量导入**
1. 点击工具栏的"批量导入"按钮
2. 选择源文件夹和处理选项
3. 点击"开始处理"

#### 2. 等待索引建立

- 首次导入时，系统需要为每张图像提取特征
- 进度会在状态栏显示
- 可以在"任务管理"中查看详细进度
- 建议在索引完成后再进行搜索

### 搜索面板详解

#### 查询图像选择

1. **本地图像**：
   - 点击"选择查询图像"按钮
   - 浏览并选择要搜索的图像
   - 支持预览功能

2. **数据库图像**：
   - 在结果列表中右键点击图像
   - 选择"以此图像搜索"

3. **拖拽上传**：
   - 直接将图像拖拽到查询区域

#### 搜索参数设置

**基本参数**：
- **返回数量**：设置搜索结果的最大数量（1-1000）
- **相似度阈值**：设置最低相似度要求（0.0-1.0）
- **搜索模式**：选择搜索算法（精确/快速）

**高级参数**：
- **特征权重**：调整不同特征的重要性
  - 深度特征：基于深度学习的整体特征
  - 颜色特征：主色调、色彩分布
  - 纹理特征：纹理模式、粗糙度
  - 形状特征：轮廓、几何特征

#### 过滤条件

**标签过滤**：
- 选择一个或多个标签
- 支持"包含所有"或"包含任一"模式

**分类过滤**：
- 按面料类型筛选
- 支持多级分类

**属性过滤**：
- 图像尺寸范围
- 文件大小范围
- 创建时间范围
- 颜色主调

### 结果展示区

#### 显示模式

**网格模式**（默认）：
- 缩略图网格显示
- 显示相似度分数
- 支持多选操作

**列表模式**：
- 详细信息列表
- 包含文件名、尺寸、标签等
- 支持排序功能

**大图模式**：
- 大尺寸图像预览
- 适合详细查看

#### 结果操作

**单个图像操作**：
- 左键点击：选择图像，在详情面板显示
- 双击：在外部程序中打开
- 右键菜单：
  - 以此图像搜索
  - 编辑标签
  - 查看详细信息
  - 导出图像
  - 从数据库删除

**批量操作**：
- Ctrl+点击：多选
- Shift+点击：范围选择
- Ctrl+A：全选
- 批量操作菜单：
  - 批量标签编辑
  - 批量导出
  - 批量删除

#### 分页控制

- 页码导航
- 每页显示数量设置
- 快速跳转到指定页

### 图像详情面板

#### 基本信息
- **文件信息**：文件名、路径、大小、格式
- **图像信息**：尺寸、色彩模式、DPI
- **索引信息**：添加时间、最后更新时间

#### 标签管理
- **查看标签**：显示当前图像的所有标签
- **添加标签**：输入新标签或从建议列表选择
- **删除标签**：点击标签旁的删除按钮
- **标签建议**：基于相似图像的智能标签建议

#### 特征信息
- **颜色分析**：主色调、色彩分布图
- **纹理分析**：纹理类型、方向性
- **相似度分布**：与其他图像的相似度统计

## 搜索功能详解

### 图像相似度搜索

这是系统的核心功能，基于深度学习特征进行图像检索。

#### 搜索流程

1. **选择查询图像**
   - 支持常见图像格式
   - 自动调整图像尺寸
   - 预处理优化

2. **特征提取**
   - 使用预训练深度学习模型
   - 提取高维特征向量
   - 特征标准化处理

3. **相似度计算**
   - 余弦相似度（推荐）
   - 欧几里得距离
   - 曼哈顿距离

4. **结果排序**
   - 按相似度降序排列
   - 应用过滤条件
   - 分页显示

#### 搜索技巧

**提高搜索准确性**：
- 使用高质量的查询图像
- 确保查询图像清晰、光照良好
- 避免包含过多背景干扰
- 选择具有代表性的图像区域

**调整搜索参数**：
- 降低相似度阈值可获得更多结果
- 提高阈值可获得更精确的结果
- 根据数据集大小调整返回数量

### 多维度特征搜索

#### 颜色搜索

**主色调搜索**：
1. 在搜索面板选择"颜色搜索"模式
2. 使用颜色选择器选择目标颜色
3. 调整颜色容差范围
4. 执行搜索

**色彩分布搜索**：
- 基于颜色直方图的相似度
- 考虑颜色的空间分布
- 适合查找色彩风格相似的图像

#### 纹理搜索

**纹理模式**：
- 基于Gabor滤波器的纹理特征
- 支持方向性纹理检测
- 适合查找具有相似纹理的面料

**纹理粗糙度**：
- 基于局部二值模式(LBP)
- 量化纹理的粗糙程度
- 适合按纹理细腻程度分类

#### 形状搜索

**轮廓特征**：
- 基于边缘检测的形状特征
- 适合查找具有相似图案的面料

**几何特征**：
- 基于几何矩的形状描述
- 对旋转和缩放具有一定不变性

### 组合搜索

可以同时使用多种搜索条件：

1. **设置主要搜索模式**（如图像相似度）
2. **添加辅助条件**（如颜色、标签过滤）
3. **调整各特征权重**
4. **执行组合搜索**

权重设置建议：
- 深度特征：70-80%（主要特征）
- 颜色特征：10-20%（辅助特征）
- 纹理特征：5-15%（细节特征）
- 其他特征：5-10%（补充特征）

### 搜索历史

系统自动记录搜索历史，方便重复使用：

**查看历史**：
- 在搜索面板点击"搜索历史"标签
- 显示最近的搜索记录
- 包含查询图像、参数、时间等信息

**重用历史**：
- 点击历史记录可快速重复搜索
- 支持修改参数后重新搜索
- 可以收藏常用的搜索配置

**管理历史**：
- 删除不需要的历史记录
- 导出搜索历史
- 清空所有历史

## 批量处理

### GUI批量处理

#### 批量导入

1. **启动批量导入**：
   - 点击工具栏"批量导入"按钮
   - 或菜单"工具" → "批量处理"

2. **选择源目录**：
   - 浏览选择包含图像的文件夹
   - 支持选择多个文件夹
   - 可以选择是否递归处理子目录

3. **设置处理选项**：
   - **递归处理**：是否处理子目录
   - **跳过已存在**：避免重复处理
   - **更新已存在**：重新提取已有图像的特征
   - **并行线程数**：根据CPU核心数设置
   - **批处理大小**：每批处理的图像数量

4. **开始处理**：
   - 点击"开始处理"按钮
   - 在进度对话框中监控处理状态
   - 可以随时暂停或取消处理

#### 进度监控

**实时进度**：
- 总体进度条
- 当前处理的文件名
- 处理速度（图像/秒）
- 预计剩余时间

**详细统计**：
- 已处理数量
- 成功数量
- 失败数量
- 跳过数量

**错误处理**：
- 显示处理失败的文件
- 错误原因说明
- 支持重试失败的文件

### 命令行批量处理

#### 基本用法

```bash
# 处理单个目录
python main.py batch /path/to/images

# 递归处理子目录
python main.py batch /path/to/images --recursive

# 设置并行线程数
python main.py batch /path/to/images --max-workers 8

# 设置批处理大小
python main.py batch /path/to/images --batch-size 64
```

#### 高级选项

```bash
# 跳过已存在的图像
python main.py batch /path/to/images --skip-existing

# 强制更新已存在的图像
python main.py batch /path/to/images --update-existing

# 不递归处理子目录
python main.py batch /path/to/images --no-recursive
```

#### 批量处理最佳实践

**性能优化**：
- 根据CPU核心数设置合适的线程数
- 使用SSD存储可提高I/O性能
- 启用GPU加速（如果可用）

**内存管理**：
- 大数据集时适当减小批处理大小
- 监控内存使用情况
- 必要时分批处理

**错误处理**：
- 检查图像文件完整性
- 确保有足够的磁盘空间
- 定期备份数据库

## API接口使用

### 启动API服务器

```bash
# 默认配置启动
python main.py server

# 自定义主机和端口
python main.py server --host 0.0.0.0 --port 8080

# 启用调试模式
python main.py server --debug
```

### API端点概览

#### 系统信息
```http
GET /api/info
```
返回系统版本、状态等基本信息。

#### 图像管理

**上传单个图像**：
```http
POST /api/images/upload
Content-Type: multipart/form-data

file: <image_file>
tags: ["tag1", "tag2"]  # 可选
category: "fabric_type"  # 可选
description: "图像描述"  # 可选
```

**批量上传图像**：
```http
POST /api/images/batch_upload
Content-Type: multipart/form-data

files: <multiple_files>
```

**获取图像信息**：
```http
GET /api/images/{image_id}
```

**更新图像信息**：
```http
PUT /api/images/{image_id}
Content-Type: application/json

{
  "tags": ["new_tag1", "new_tag2"],
  "category": "new_category",
  "description": "新的描述"
}
```

**删除图像**：
```http
DELETE /api/images/{image_id}
```

**获取图像列表**：
```http
GET /api/images?page=1&page_size=20&category=fabric&tags=cotton,blue
```

#### 搜索功能

**相似图像搜索**：
```http
POST /api/search/similar
Content-Type: application/json

{
  "query_image_path": "path/to/query.jpg",
  "top_k": 20,
  "similarity_threshold": 0.5,
  "filters": {
    "tags": ["cotton"],
    "category": "fabric"
  }
}
```

**通过上传图像搜索**：
```http
POST /api/search/upload_and_search
Content-Type: multipart/form-data

file: <query_image>
top_k: 20
similarity_threshold: 0.5
```

**标签搜索**：
```http
POST /api/search/tags
Content-Type: application/json

{
  "tags": ["cotton", "blue"],
  "match_all": true,
  "top_k": 50
}
```

**颜色搜索**：
```http
POST /api/search/color
Content-Type: application/json

{
  "color": "#FF0000",
  "tolerance": 0.1,
  "top_k": 30
}
```

#### 批量操作

**批量特征提取**：
```http
POST /api/batch/extract_features
Content-Type: application/json

{
  "image_ids": [1, 2, 3, 4, 5],
  "force_update": false
}
```

**获取批量任务状态**：
```http
GET /api/batch/status/{task_id}
```

#### 数据导出

**导出搜索结果**：
```http
POST /api/export/search_results
Content-Type: application/json

{
  "search_results": [1, 2, 3, 4, 5],
  "format": "json",  # json, csv, excel
  "include_images": true
}
```

**导出数据库**：
```http
GET /api/export/database?format=json
```

### API使用示例

#### Python客户端示例

```python
import requests
import json

# API基础URL
base_url = "http://localhost:5000/api"

# 上传图像
def upload_image(image_path, tags=None, category=None):
    url = f"{base_url}/images/upload"
    
    with open(image_path, 'rb') as f:
        files = {'file': f}
        data = {}
        if tags:
            data['tags'] = json.dumps(tags)
        if category:
            data['category'] = category
        
        response = requests.post(url, files=files, data=data)
        return response.json()

# 相似图像搜索
def search_similar(query_image_path, top_k=20, threshold=0.5):
    url = f"{base_url}/search/similar"
    
    payload = {
        "query_image_path": query_image_path,
        "top_k": top_k,
        "similarity_threshold": threshold
    }
    
    response = requests.post(url, json=payload)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 上传图像
    result = upload_image(
        "sample.jpg", 
        tags=["cotton", "blue"], 
        category="fabric"
    )
    print(f"上传结果: {result}")
    
    # 搜索相似图像
    search_result = search_similar("query.jpg", top_k=10)
    print(f"搜索到 {len(search_result['results'])} 个相似图像")
```

#### JavaScript客户端示例

```javascript
// API基础URL
const baseUrl = 'http://localhost:5000/api';

// 上传图像
async function uploadImage(file, tags = [], category = null) {
    const formData = new FormData();
    formData.append('file', file);
    
    if (tags.length > 0) {
        formData.append('tags', JSON.stringify(tags));
    }
    
    if (category) {
        formData.append('category', category);
    }
    
    const response = await fetch(`${baseUrl}/images/upload`, {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
}

// 通过上传搜索
async function uploadAndSearch(file, topK = 20, threshold = 0.5) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('top_k', topK);
    formData.append('similarity_threshold', threshold);
    
    const response = await fetch(`${baseUrl}/search/upload_and_search`, {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
}

// 使用示例
document.getElementById('upload-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const fileInput = document.getElementById('file-input');
    const file = fileInput.files[0];
    
    if (file) {
        try {
            const result = await uploadAndSearch(file, 10, 0.6);
            console.log('搜索结果:', result);
            displayResults(result.results);
        } catch (error) {
            console.error('搜索失败:', error);
        }
    }
});
```

## 配置管理

### 配置文件结构

系统使用YAML格式的配置文件（`config.yaml`），主要包含以下部分：

```yaml
# 基本配置
app_name: Fabric Search
version: 2.0.0
debug: false

# 目录配置
data_dir: data
model_dir: models
cache_dir: cache
log_dir: logs
temp_dir: temp

# 数据库配置
database:
  host: localhost
  port: 5432
  database: fabric_search
  username: postgres
  password: ""

# 特征提取配置
feature_extraction:
  model_name: resnet50
  device: auto
  batch_size: 32
  image_size: [224, 224]

# 搜索配置
search:
  index_type: faiss
  similarity_metric: cosine
  top_k: 50
  use_gpu: true

# 其他配置...
```

### 命令行配置管理

#### 查看配置

```bash
# 查看所有配置
python main.py config list

# 查看特定配置项
python main.py config get feature_extraction.model_name
python main.py config get search.top_k
```

#### 修改配置

```bash
# 设置配置值
python main.py config set search.top_k 100
python main.py config set feature_extraction.device cuda
python main.py config set debug true
```

#### 导入导出配置

```bash
# 导出配置到文件
python main.py config export config_backup.json
python main.py config export config_backup.yaml --format yaml

# 从文件导入配置
python main.py config import config_backup.json
```

### GUI配置管理

#### 设置对话框

1. **打开设置**：
   - 菜单"工具" → "设置"
   - 或按快捷键 Ctrl+,

2. **配置分类**：
   - **常规设置**：基本应用设置
   - **搜索设置**：搜索算法和参数
   - **模型设置**：深度学习模型配置
   - **界面设置**：GUI主题和布局
   - **性能设置**：GPU、内存等性能选项

3. **应用设置**：
   - 点击"应用"保存设置
   - 点击"确定"保存并关闭
   - 点击"取消"放弃更改

#### 常用配置项

**搜索设置**：
- 默认返回数量
- 相似度阈值
- 搜索算法选择
- 特征权重配置

**性能设置**：
- GPU使用设置
- 并行线程数
- 内存限制
- 缓存大小

**界面设置**：
- 主题选择（明亮/暗黑/自动）
- 字体大小
- 缩略图尺寸
- 界面语言

### 环境变量配置

系统支持通过环境变量覆盖配置文件设置：

```bash
# 数据库配置
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=fabric_search
export DB_USER=postgres
export DB_PASSWORD=your_password

# GPU配置
export CUDA_VISIBLE_DEVICES=0

# 调试模式
export DEBUG=true

# 日志级别
export LOG_LEVEL=DEBUG
```

## 常见问题

### 安装问题

#### Q: 安装PyQt6时出错
**A**: 
```bash
# 尝试使用conda安装
conda install pyqt

# 或者安装特定版本
pip install PyQt6==6.4.0

# 如果仍有问题，可以使用tkinter版本
# 系统会自动检测并使用可用的GUI框架
```

#### Q: CUDA相关错误
**A**: 
```bash
# 检查CUDA是否正确安装
nvidia-smi

# 安装对应版本的PyTorch
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# 如果仍有问题，可以使用CPU模式
# 在配置中设置 device: cpu
```

#### Q: 内存不足错误
**A**: 
- 减小批处理大小（batch_size）
- 降低图像分辨率
- 关闭其他占用内存的程序
- 使用交换文件/虚拟内存

### 使用问题

#### Q: 搜索结果不准确
**A**: 
1. 检查查询图像质量
2. 调整相似度阈值
3. 尝试不同的搜索模型
4. 增加训练数据的多样性
5. 调整特征权重配置

#### Q: 处理速度慢
**A**: 
1. 启用GPU加速
2. 增加并行线程数
3. 使用SSD存储
4. 优化图像尺寸
5. 启用特征缓存

#### Q: 数据库连接失败
**A**: 
1. 检查数据库服务状态
2. 验证连接参数
3. 检查网络连接
4. 确认用户权限
5. 尝试使用SQLite作为备选

#### Q: GUI界面无法启动
**A**: 
1. 检查PyQt6安装
2. 更新显卡驱动
3. 尝试不同的GUI框架
4. 检查系统兼容性
5. 查看错误日志

### 性能问题

#### Q: 内存使用过高
**A**: 
1. 减小批处理大小
2. 启用内存优化选项
3. 定期清理缓存
4. 使用内存映射文件
5. 分批处理大数据集

#### Q: GPU利用率低
**A**: 
1. 增加批处理大小
2. 检查GPU内存使用
3. 优化数据加载流程
4. 使用混合精度训练
5. 检查CPU瓶颈

## 高级功能

### 自定义模型

#### 添加新的预训练模型

1. **下载模型文件**：
   - 将模型文件放置在 `models/` 目录
   - 支持PyTorch、ONNX等格式

2. **配置模型**：
   ```yaml
   feature_extraction:
     model_name: custom_model
     model_path: models/custom_model.pth
     feature_dim: 1024
   ```

3. **实现模型加载器**：
   - 在 `models/model_loader.py` 中添加自定义加载逻辑
   - 实现特征提取接口

#### 微调模型

1. **准备训练数据**：
   - 收集标注数据
   - 按类别组织图像

2. **配置训练参数**：
   ```yaml
   training:
     learning_rate: 0.001
     batch_size: 32
     epochs: 100
     validation_split: 0.2
   ```

3. **启动训练**：
   ```bash
   python scripts/train_model.py --config training_config.yaml
   ```

### 插件系统

#### 开发自定义插件

1. **创建插件目录**：
   ```
   plugins/
   └── my_plugin/
       ├── __init__.py
       ├── plugin.py
       └── config.yaml
   ```

2. **实现插件接口**：
   ```python
   from plugins.base import BasePlugin
   
   class MyPlugin(BasePlugin):
       def __init__(self):
           super().__init__()
           self.name = "My Plugin"
           self.version = "1.0.0"
       
       def process_image(self, image):
           # 自定义图像处理逻辑
           return processed_image
       
       def extract_features(self, image):
           # 自定义特征提取逻辑
           return features
   ```

3. **注册插件**：
   - 在配置文件中启用插件
   - 系统会自动加载和初始化

### 分布式部署

#### 多节点部署

1. **配置主节点**：
   ```yaml
   cluster:
     mode: master
     nodes:
       - host: worker1.example.com
         port: 5001
       - host: worker2.example.com
         port: 5001
   ```

2. **配置工作节点**：
   ```yaml
   cluster:
     mode: worker
     master_host: master.example.com
     master_port: 5000
   ```

3. **启动集群**：
   ```bash
   # 主节点
   python main.py server --cluster-mode master
   
   # 工作节点
   python main.py server --cluster-mode worker
   ```

#### 负载均衡

使用Nginx或HAProxy进行负载均衡：

```nginx
upstream fabric_search {
    server 127.0.0.1:5000;
    server 127.0.0.1:5001;
    server 127.0.0.1:5002;
}

server {
    listen 80;
    server_name fabric-search.example.com;
    
    location / {
        proxy_pass http://fabric_search;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 数据备份与恢复

#### 自动备份

1. **配置备份策略**：
   ```yaml
   backup:
     enabled: true
     schedule: "0 2 * * *"  # 每天凌晨2点
     retention_days: 30
     backup_dir: backups/
   ```

2. **手动备份**：
   ```bash
   python main.py backup create --name manual_backup
   ```

#### 数据恢复

```bash
# 列出可用备份
python main.py backup list

# 恢复指定备份
python main.py backup restore --name backup_20240101

# 验证恢复结果
python main.py backup verify
```

## 最佳实践

### 数据组织

#### 目录结构建议

```
fabric_images/
├── cotton/
│   ├── plain/
│   ├── striped/
│   └── printed/
├── silk/
│   ├── solid/
│   └── patterned/
└── wool/
    ├── fine/
    └── coarse/
```

#### 文件命名规范

- 使用描述性文件名
- 包含关键属性信息
- 避免特殊字符和空格
- 示例：`cotton_blue_striped_001.jpg`

### 标签管理

#### 标签体系设计

**层次化标签**：
```
材质:
  - 天然纤维:
    - 棉
    - 丝
    - 羊毛
  - 合成纤维:
    - 聚酯
    - 尼龙

颜色:
  - 基础色:
    - 红
    - 蓝
    - 绿
  - 复合色:
    - 紫
    - 橙
    - 黄

图案:
  - 几何:
    - 条纹
    - 格子
    - 圆点
  - 自然:
    - 花卉
    - 叶子
```

#### 标签使用建议

1. **保持一致性**：使用统一的标签词汇
2. **避免冗余**：不要使用过于相似的标签
3. **定期整理**：清理无用或错误的标签
4. **建立词典**：维护标准标签词典

### 性能优化

#### 硬件配置建议

**开发环境**：
- CPU: Intel i5/AMD Ryzen 5 或更高
- 内存: 16GB 或更多
- 存储: SSD 500GB 或更多
- GPU: GTX 1060/RTX 3060 或更高（可选）

**生产环境**：
- CPU: Intel Xeon/AMD EPYC 多核处理器
- 内存: 32GB 或更多
- 存储: NVMe SSD 1TB 或更多
- GPU: RTX 4080/A100 或更高（推荐）

#### 软件优化

1. **使用GPU加速**：
   - 启用CUDA支持
   - 使用混合精度计算
   - 优化批处理大小

2. **内存优化**：
   - 启用特征缓存
   - 使用内存映射
   - 定期清理缓存

3. **I/O优化**：
   - 使用SSD存储
   - 启用并行加载
   - 优化数据库索引

### 安全考虑

#### 数据安全

1. **访问控制**：
   - 启用API密钥认证
   - 设置用户权限
   - 限制文件访问路径

2. **数据加密**：
   - 数据库连接加密
   - 敏感数据加密存储
   - 传输层安全(TLS)

3. **备份安全**：
   - 定期备份数据
   - 异地备份存储
   - 备份文件加密

#### 系统安全

1. **网络安全**：
   - 防火墙配置
   - 端口访问限制
   - DDoS防护

2. **应用安全**：
   - 输入验证
   - SQL注入防护
   - 文件上传安全

### 监控与维护

#### 系统监控

1. **性能监控**：
   - CPU、内存使用率
   - GPU利用率
   - 磁盘I/O
   - 网络流量

2. **应用监控**：
   - 请求响应时间
   - 错误率统计
   - 搜索准确率
   - 用户活跃度

3. **日志监控**：
   - 错误日志分析
   - 性能日志统计
   - 用户行为日志

#### 定期维护

1. **数据库维护**：
   - 索引优化
   - 数据清理
   - 统计信息更新

2. **系统维护**：
   - 软件更新
   - 安全补丁
   - 配置优化

3. **数据维护**：
   - 重复数据清理
   - 无效数据删除
   - 特征重新提取

---

本指南涵盖了Fabric Search v2的主要功能和使用方法。如有其他问题，请参考[README.md](README.md)或联系技术支持。