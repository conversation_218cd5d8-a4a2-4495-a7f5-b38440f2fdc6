#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库异常类

该模块定义数据库相关的异常类。
"""


class DatabaseError(Exception):
    """数据库基础异常"""
    pass


class ConnectionError(DatabaseError):
    """数据库连接异常"""
    pass


class TransactionError(DatabaseError):
    """事务异常"""
    pass


class SchemaError(DatabaseError):
    """架构异常"""
    pass


class QueryError(DatabaseError):
    """查询异常"""
    pass


class ValidationError(DatabaseError):
    """验证异常"""
    pass


class RepositoryError(DatabaseError):
    """仓库异常"""
    pass