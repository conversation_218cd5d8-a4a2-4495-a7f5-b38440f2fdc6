#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索引擎主模块

该模块整合了搜索引擎的所有功能，包括搜索、缓存、统计等。
"""

import time
from typing import Dict, Any, List, Optional, Tuple, Union, Callable

from database.models import FabricImage
from database.fabric_repository import FabricRepository
from features.core.manager import FeatureManager
from utils.log_utils import LoggerMixin
from utils.file_utils import validate_image_file

from .models import SearchQuery, SearchResult
from .search_methods import SearchMethods
from .result_processor import ResultProcessor
from .cache_manager import CacheManager
from .statistics_manager import StatisticsManager
from .search_filters import FilterEngine, SearchFilter
from .search_history import SearchHistoryManager, SearchType
from .config import ConfigManager, SearchConfig
from .utils import SearchUtils


class SearchEngine(LoggerMixin):
    """搜索引擎类
    
    整合了搜索、缓存、统计等功能的主类。
    """
    
    def __init__(self, app_config=None, fabric_repository: FabricRepository=None, 
                 feature_manager: FeatureManager=None,
                 search_history_manager=None, config_path: Optional[str] = None):
        """初始化搜索引擎
        
        Args:
            app_config: 应用配置字典或对象
            fabric_repository: 布料数据仓库
            feature_manager: 特征管理器
            search_history_manager: 搜索历史管理器
            config_path: 配置文件路径
        """
        super().__init__()
        
        # 如果app_config是字典，则尝试从中获取必要的组件
        if isinstance(app_config, dict):
            # 尝试从app_config中获取fabric_repository和feature_manager
            if fabric_repository is None and 'fabric_repository' in app_config:
                fabric_repository = app_config.get('fabric_repository')
            
            if feature_manager is None and 'feature_manager' in app_config:
                feature_manager = app_config.get('feature_manager')
                self.logger.info(f"从app_config中获取feature_manager: {feature_manager is not None}")
            
            # 如果config_path为None，尝试从app_config中获取
            if config_path is None and 'config_path' in app_config:
                config_path = app_config.get('config_path')
        
        self.fabric_repository = fabric_repository
        
        # 使用传入的feature_manager实例，避免重复初始化
        self.feature_manager = feature_manager
        
        # 验证feature_manager是否存在
        if self.feature_manager is None:
            self.logger.error("SearchEngine初始化失败：feature_manager为None")
            raise ValueError("feature_manager不能为None")
        
        # 添加调试日志
        self.logger.info(f"SearchEngine初始化 - feature_manager状态: {self.feature_manager is not None}")
        self.logger.info(f"feature_manager类型: {type(self.feature_manager)}")
        self.logger.info(f"feature_manager ID: {id(self.feature_manager)}")
        
        # 加载配置
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_config()
        
        # 初始化组件
        self.filter_engine = FilterEngine(fabric_repository)
        
        # 创建SearchHistoryRepository实例
        from database.repositories.search_history_repository import SearchHistoryRepository
        search_repository = SearchHistoryRepository(fabric_repository.db_manager) if fabric_repository else None
        
        # 只有在search_repository可用时才初始化SearchHistoryManager
        if search_history_manager:
            self.search_history = search_history_manager
        elif search_repository:
            self.search_history = SearchHistoryManager(
                search_repository=search_repository,
                max_history_days=getattr(self.config, 'max_history_days', 90),
                max_sessions_per_user=getattr(self.config, 'max_sessions_per_user', 100)
            )
        else:
            self.search_history = None
            self.logger.warning("SearchHistoryManager未初始化：缺少必要的依赖项")
        self.cache_manager = CacheManager(
            cache_ttl=self.config.cache_ttl,
            max_cache_size=self.config.max_cache_size
        )
        self.result_processor = ResultProcessor(self.filter_engine)
        # 只有在必要组件可用时才初始化StatisticsManager
        if fabric_repository and self.search_history:
            self.statistics_manager = StatisticsManager(
                fabric_repository=fabric_repository,
                history_manager=self.search_history,
                max_suggestions=self.config.max_suggestions
            )
        else:
            self.statistics_manager = None
            self.logger.warning("StatisticsManager未初始化：缺少必要的依赖项")
        self.search_utils = SearchUtils()
        
        # 初始化搜索方法
        # 确保feature_manager不为None，避免在SearchMethods中重复创建
        self.search_methods = SearchMethods(
            fabric_repository=fabric_repository,
            feature_manager=feature_manager,  # 直接传递，即使为None也让SearchMethods处理
            search_utils=self.search_utils,
            config=self.config
        )
        
        self.logger.info("搜索引擎初始化完成")
    
    def search(self, query: SearchQuery, 
               use_cache: bool = True,
               progress_callback: Optional[Callable] = None) -> SearchResult:
        """执行搜索
        
        Args:
            query: 搜索查询
            use_cache: 是否使用缓存
            progress_callback: 进度回调函数
            
        Returns:
            SearchResult: 搜索结果
        """
        start_time = time.time()
        
        try:
            # 应用默认值
            self._apply_default_values(query)
            
            # 检查缓存
            if use_cache and self.config.cache_enabled:
                cached_result = self.cache_manager.get_cached_result(query)
                if cached_result:
                    self.logger.info(f"从缓存中获取结果，查询类型: {query.query_type}")
                    return cached_result
            
            # 执行搜索
            result = self._execute_search(query, progress_callback)
            
            # 记录搜索历史
            if self.config.log_searches and self.statistics_manager:
                self.statistics_manager.record_search_history(query, result)
            
            # 缓存结果
            if use_cache and self.config.cache_enabled:
                self.cache_manager.cache_result(query, result)
            
            # 计算搜索时间
            search_time = time.time() - start_time
            result.search_time = search_time
            
            self.logger.info(f"搜索完成，查询类型: {query.query_type}，结果数: {result.total_results}，耗时: {self.search_utils.format_search_time(search_time)}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
            # 返回空结果
            return SearchResult(
                query=query,
                results=[],
                total_results=0,
                search_time=time.time() - start_time,
                error_message=str(e)
            )
    
    def _apply_default_values(self, query: SearchQuery) -> None:
        """应用默认值
        
        Args:
            query: 搜索查询
        """
        # 应用默认分页参数
        if query.page <= 0:
            query.page = 1
            
        if query.page_size <= 0:
            query.page_size = self.config.default_page_size
        elif query.page_size > self.config.max_page_size:
            query.page_size = self.config.max_page_size
        
        # 应用默认排序参数
        if not query.sort_by:
            query.sort_by = self.config.default_sort_by
            
        if not query.sort_order:
            query.sort_order = self.config.default_sort_order
    
    def _execute_search(self, query: SearchQuery, 
                        progress_callback: Optional[Callable] = None) -> SearchResult:
        """执行搜索
        
        Args:
            query: 搜索查询
            progress_callback: 进度回调函数
            
        Returns:
            SearchResult: 搜索结果
        """
        if progress_callback:
            progress_callback(0.1, "开始搜索...")
        
        # 根据查询类型执行不同的搜索
        raw_results = []
        
        if query.query_type == SearchType.IMAGE_SIMILARITY:
            # 图像相似度搜索
            if progress_callback:
                progress_callback(0.2, "执行图像相似度搜索...")
            raw_results = self.search_methods.search_by_image_similarity(query, progress_callback)
            
        elif query.query_type == SearchType.TEXT_SEARCH:
            # 文本搜索
            if progress_callback:
                progress_callback(0.2, "执行文本搜索...")
            raw_results = self.search_methods.search_by_text(query, progress_callback)
            
        elif query.query_type == SearchType.CATEGORY_BROWSE:
            # 分类浏览
            if progress_callback:
                progress_callback(0.2, "执行分类浏览...")
            raw_results = self.search_methods.search_by_category(query, progress_callback)
            
        elif query.query_type == SearchType.TAG_SEARCH:
            # 标签搜索
            if progress_callback:
                progress_callback(0.2, "执行标签搜索...")
            raw_results = self.search_methods.search_by_tags(query, progress_callback)
            
        elif query.query_type == SearchType.ADVANCED_FILTER:
            # 高级过滤
            if progress_callback:
                progress_callback(0.2, "执行高级过滤...")
            raw_results = self.search_methods.search_by_advanced_filter(query, progress_callback)
            
        else:
            self.logger.warning(f"未知的查询类型: {query.query_type}")
            return SearchResult(
                query=query,
                results=[],
                total_results=0,
                error_message=f"未知的查询类型: {query.query_type}"
            )
        
        # 应用过滤器
        filtered_results = self.result_processor.apply_filters(raw_results, query, progress_callback)
        
        # 排序结果
        sorted_results = self.result_processor.sort_results(filtered_results, query, progress_callback)
        
        # 分页结果
        paginated_results, pagination_info = self.result_processor.paginate_results(sorted_results, query, progress_callback)
        
        # 生成统计信息
        stats = {}
        if self.config.enable_statistics:
            stats = self.result_processor.generate_search_statistics(raw_results, filtered_results, query)
        
        # 构建结果
        result = SearchResult(
            query=query,
            results=paginated_results,  # 返回(图片对象,分数)元组列表
            total_results=len(sorted_results),
            current_page=query.page,
            total_pages=pagination_info.get('total_pages', 1),
            has_next_page=pagination_info.get('has_next', False),
            has_prev_page=pagination_info.get('has_prev', False),
            statistics=stats
        )
        
        if progress_callback:
            progress_callback(1.0, "搜索完成")
        
        return result
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self.cache_manager.clear_cache()
    
    def get_search_suggestions(self, query_prefix: str = "", 
                              max_results: int = 10) -> List[Dict[str, Any]]:
        """获取搜索建议
        
        Args:
            query_prefix: 查询前缀
            max_results: 最大结果数
            
        Returns:
            List[Dict[str, Any]]: 搜索建议列表
        """
        return self.statistics_manager.get_search_suggestions(
            query_prefix=query_prefix,
            max_results=max_results
        )
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """获取搜索统计信息
        
        Returns:
            Dict[str, Any]: 搜索统计信息
        """
        # 获取搜索统计
        search_stats = self.statistics_manager.get_search_statistics()
        
        # 获取缓存统计
        cache_stats = self.cache_manager.get_cache_stats()
        
        # 合并统计信息
        return {
            'search': search_stats,
            'cache': cache_stats,
            'config': self.config.to_dict()
        }
    
    def add_image(self, image_path: str, metadata: Optional[Dict[str, Any]] = None) -> Optional[FabricImage]:
        """添加图像
        
        Args:
            image_path: 图像路径
            metadata: 元数据
            
        Returns:
            Optional[FabricImage]: 添加的图像对象
        """
        try:
            # 验证文件
            if not validate_image_file(image_path):
                self.logger.error(f"无效的图像文件: {image_path}")
                return None
            
            # 创建FabricImage对象
            fabric_image = self.fabric_repository.create_fabric_image(image_path, metadata)
            
            if not fabric_image:
                self.logger.error(f"创建FabricImage对象失败: {image_path}")
                return None
            
            # 添加到特征管理器
            self.feature_manager.add_image(fabric_image)
            
            # 清除缓存
            self.clear_cache()
            
            self.logger.info(f"图像添加成功: {fabric_image.file_name}")
            return fabric_image
            
        except Exception as e:
            self.logger.error(f"添加图像失败: {e}")
            return None
    
    def update_config(self, config_dict: Dict[str, Any], save: bool = True) -> bool:
        """更新配置
        
        Args:
            config_dict: 配置字典
            save: 是否保存到文件
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 更新配置
            self.config_manager.update_config(config_dict)
            self.config = self.config_manager.get_config()
            
            # 更新组件配置
            self.cache_manager.cache_ttl = self.config.cache_ttl
            self.cache_manager.max_cache_size = self.config.max_cache_size
            self.search_history.max_size = self.config.max_history_size
            
            # 保存配置
            if save:
                self.config_manager.save_config()
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
            return False
    
    def rebuild_index(self, force: bool = True) -> bool:
        """重建搜索索引
        
        Args:
            force: 是否强制重建
            
        Returns:
            bool: 重建是否成功
        """
        try:
            if not self.feature_manager:
                self.logger.error("特征管理器未初始化，无法重建索引")
                return False
            
            self.logger.info("开始重建搜索索引...")
            
            # 调用特征管理器的索引构建方法
            success = self.feature_manager.build_search_index(force_rebuild=force)
            
            if success:
                # 清除缓存，确保使用新索引
                self.clear_cache()
                self.logger.info("搜索索引重建完成")
            else:
                self.logger.error("搜索索引重建失败")
            
            return success
            
        except Exception as e:
            self.logger.error(f"重建索引过程中发生错误: {e}")
            return False
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            # 保存搜索历史
            if hasattr(self, 'search_history') and self.search_history:
                self.search_history.save()
            
            # 清理其他资源
            if hasattr(self, 'cache_manager') and self.cache_manager:
                self.clear_cache()
            
            # 注意：不在这里清理特征管理器，避免重复清理
            # 特征管理器由ComponentManager统一管理和清理
            
            self.logger.info("搜索引擎资源已清理")
            
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")