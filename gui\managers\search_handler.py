#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索处理器

该模块负责处理所有搜索相关的逻辑。
"""

from typing import Optional, Dict, Any, List
from PyQt6.QtCore import QObject, pyqtSignal, QThread
from PyQt6.QtWidgets import QMessageBox, QMainWindow

from utils.logger_mixin import LoggerMixin
from search import SearchQuery, SearchConfig, SearchEngine
from search.search_history import SearchType
from search.search_worker import SearchWorker


class SearchHandler(QObject, LoggerMixin):
    """搜索处理器"""
    
    # 信号
    searchStarted = pyqtSignal()
    searchFinished = pyqtSignal()
    searchError = pyqtSignal(str)
    searchResultsUpdated = pyqtSignal(object)  # 修改为object类型，以支持SearchResult对象和list
    searchProgress = pyqtSignal(int, str)
    
    def __init__(self, main_window: QMainWindow):
        super().__init__()
        self.main_window = main_window
        self.search_engine: Optional[SearchEngine] = None
        self.search_worker: Optional[SearchWorker] = None
        self.search_thread: Optional[QThread] = None
        self.is_searching = False
        self.current_task_id: Optional[str] = None  # 当前搜索任务ID
        
    def initialize_search_engine(self, config: Dict[str, Any]):
        """初始化搜索引擎
        
        Args:
            config: 配置字典
        """
        try:
            # 从配置字典中提取参数
            fabric_repository = config.get('fabric_repository')
            feature_manager = config.get('feature_manager')
            
            # 验证必要组件
            self.logger.info(f"SearchHandler.initialize_search_engine - feature_manager状态: {feature_manager is not None}")
            if feature_manager is None:
                self.logger.error("SearchHandler初始化失败：feature_manager为None")
                self.searchError.emit("搜索引擎初始化失败：缺少特征管理器")
                return
            
            self.logger.info(f"feature_manager类型: {type(feature_manager)}")
            self.logger.info(f"feature_manager ID: {id(feature_manager)}")
            
            # 创建应用配置对象
            app_config = {
                'database_path': config.get('database_path'),
                'max_results': config.get('max_results', 100),
                'similarity_threshold': config.get('similarity_threshold', 0.8),
                'enable_cache': config.get('enable_cache', True),
                'cache_size': config.get('cache_size', 1000),
                'search_timeout': config.get('search_timeout', 30),
                # 添加data_dir属性，从config中获取
                'data_dir': config.get('data_dir', './data'),
                # 确保fabric_repository和feature_manager也被包含在app_config中
                'fabric_repository': fabric_repository,
                'feature_manager': feature_manager
            }
            
            # 初始化搜索引擎，传递正确的参数
            self.search_engine = SearchEngine(
                app_config=app_config,
                fabric_repository=fabric_repository,
                feature_manager=feature_manager
            )
            self.logger.info("搜索引擎初始化完成")
        except Exception as e:
            self.logger.error(f"搜索引擎初始化失败: {e}")
            self.searchError.emit(f"搜索引擎初始化失败: {e}")
    
    def start_search(self, search_config: SearchConfig):
        """开始搜索
        
        Args:
            search_config: 搜索配置
        """
        if self.is_searching:
            self.logger.warning("搜索正在进行中，忽略新的搜索请求")
            return
        
        if not self.search_engine:
            self.searchError.emit("搜索引擎未初始化")
            return
        
        try:
            # 获取任务管理器
            task_manager = getattr(self.main_window, 'task_manager', None)
            if not task_manager:
                self.logger.warning("任务管理器未找到，搜索将不会在任务管理器中显示")
            
            # 创建搜索任务
            search_mode = getattr(search_config, 'mode', None)
            search_query = getattr(search_config, 'query', '')
            
            if task_manager:
                task_name = f"搜索 - {search_mode.value if search_mode else '未知模式'}"
                if search_query:
                    task_name += f": {search_query[:50]}{'...' if len(search_query) > 50 else ''}"
                
                task_metadata = {
                    'type': 'search',
                    'search_mode': search_mode.value if search_mode else 'unknown',
                    'query': search_query
                }
                self.current_task_id = task_manager.create_task(task_name, task_metadata)
            
            # 转换搜索配置为搜索查询
            search_query = self._config_to_query(search_config)
            
            # 创建搜索工作线程
            self.search_thread = QThread()
            self.search_worker = SearchWorker(self.search_engine, search_query, task_manager, self.current_task_id)
            self.search_worker.moveToThread(self.search_thread)
            
            # 连接信号
            self.search_thread.started.connect(self.search_worker.run)
            self.search_worker.finished.connect(self._on_search_finished)
            self.search_worker.error.connect(self._on_search_error)
            self.search_worker.progress.connect(self.searchProgress.emit)
            # 注意：不直接连接results信号，而是在finished信号处理中统一发射searchResultsUpdated
            
            # 启动搜索
            self.is_searching = True
            self.searchStarted.emit()
            self.search_thread.start()
            
            self.logger.info("搜索已开始")
            
        except Exception as e:
            self.logger.error(f"启动搜索失败: {e}")
            self.searchError.emit(f"启动搜索失败: {e}")
            
            # 如果创建了任务但启动失败，标记任务为失败
            if self.current_task_id and task_manager:
                task_manager.complete_task(self.current_task_id, error=str(e))
                self.current_task_id = None
    
    def stop_search(self):
        """停止搜索"""
        if not self.is_searching:
            return
        
        try:
            if self.search_worker:
                self.search_worker.stop()
            
            if self.search_thread and self.search_thread.isRunning():
                self.search_thread.quit()
                self.search_thread.wait(3000)  # 等待3秒
                
                if self.search_thread.isRunning():
                    self.logger.warning("线程未能正常退出，强制终止")
                    self.search_thread.terminate()
                    self.search_thread.wait(1000)
            
            # 清理线程和工作器
            self._cleanup_search_resources()
            
            # 标记任务为取消
            if self.current_task_id:
                task_manager = getattr(self.main_window, 'task_manager', None)
                if task_manager:
                    task_manager.update_task_status(self.current_task_id, "failed", "用户取消搜索")
                self.current_task_id = None
            
            self.is_searching = False
            self.searchFinished.emit()
            
            self.logger.info("搜索已停止")
            
        except Exception as e:
            self.logger.error(f"停止搜索失败: {e}")
            # 确保即使出错也要清理资源
            self._cleanup_search_resources()
            self.is_searching = False
    
    def rebuild_index(self):
        """重建索引"""
        if not self.search_engine:
            self.searchError.emit("搜索引擎未初始化")
            return
        
        try:
            # 显示确认对话框
            reply = QMessageBox.question(
                self.main_window,
                "确认重建索引",
                "重建索引将删除现有索引并重新构建，这可能需要较长时间。\n\n确定要继续吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.search_engine.rebuild_index()
                self.logger.info("索引重建完成")
                
        except Exception as e:
            self.logger.error(f"重建索引失败: {e}")
            self.searchError.emit(f"重建索引失败: {e}")
    
    def clear_cache(self):
        """清除缓存"""
        if not self.search_engine:
            self.searchError.emit("搜索引擎未初始化")
            return
        
        try:
            # 显示确认对话框
            reply = QMessageBox.question(
                self.main_window,
                "确认清除缓存",
                "清除缓存将删除所有缓存文件，下次搜索可能会较慢。\n\n确定要继续吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.search_engine.clear_cache()
                self.logger.info("缓存清除完成")
                
        except Exception as e:
            self.logger.error(f"清除缓存失败: {e}")
            self.searchError.emit(f"清除缓存失败: {e}")
    
    def _config_to_query(self, config: SearchConfig) -> SearchQuery:
        """将搜索配置转换为搜索查询
        
        Args:
            config: 搜索配置
            
        Returns:
            SearchQuery: 搜索查询对象
        """
        # 确定查询类型
        query_type = SearchType.TEXT_SEARCH  # 默认值
        
        # 从SearchMode枚举获取搜索模式
        search_mode = config.mode.value if hasattr(config, 'mode') else "text"
        
        if search_mode == "similarity":
            query_type = SearchType.IMAGE_SIMILARITY
        elif search_mode == "text":
            query_type = SearchType.TEXT_SEARCH
        elif search_mode == "category":
            query_type = SearchType.CATEGORY_BROWSE
        elif search_mode == "tag":
            query_type = SearchType.TAG_SEARCH
        elif search_mode == "advanced":
            query_type = SearchType.ADVANCED_FILTER
        
        # 创建查询对象，确保提供必要的query_type参数
        query = SearchQuery(query_type=query_type)
        
        # 设置搜索参数
        if search_mode == "similarity":
            # 获取图像路径
            if hasattr(config, 'query') and config.query:
                query.query_image_path = config.query
            elif hasattr(config, 'image_path') and config.image_path:
                query.query_image_path = config.image_path
        elif search_mode == "text":
            # 获取文本查询
            if hasattr(config, 'query') and config.query:
                query.text_query = config.query
            elif hasattr(config, 'text_query') and config.text_query:
                query.text_query = config.text_query
        elif search_mode == "category":
            # 获取类别
            if hasattr(config, 'category') and config.category:
                query.categories = [config.category]
        elif search_mode == "tag":
            # 获取标签
            if hasattr(config, 'tags') and config.tags:
                query.tags = config.tags
        elif search_mode == "advanced":
            # 设置高级过滤器参数
            if hasattr(config, 'advanced_filters'):
                if not query.metadata:
                    query.metadata = {}
                query.metadata['advanced_filters'] = config.advanced_filters
        
        # 设置相似度阈值
        if hasattr(config, 'similarity_threshold'):
            query.similarity_threshold = config.similarity_threshold
            
        # 设置特征权重
        if hasattr(config, 'feature_weights') and config.feature_weights:
            query.feature_weights = config.feature_weights
        
        # 设置特征提取参数
        if hasattr(config, 'feature_extraction_params'):
            query.feature_extraction_params = config.feature_extraction_params
        
        # 设置搜索策略
        if hasattr(config, 'search_strategy'):
            query.search_strategy = config.search_strategy
        
        # 设置排序参数
        if hasattr(config, 'sort_by'):
            query.sort_by = config.sort_by
        else:
            query.sort_by = 'similarity'  # 默认按相似度排序
        
        if hasattr(config, 'sort_ascending'):
            query.sort_order = 'asc' if config.sort_ascending else 'desc'
        else:
            query.sort_order = 'desc'
        
        # 设置结果限制
        if hasattr(config, 'max_results'):
            query.top_k = config.max_results
        else:
            query.top_k = 100
        
        return query
    
    def _on_search_finished(self, results: List[Dict[str, Any]]):
        """搜索完成处理
        
        Args:
            results: 搜索结果
        """
        self.is_searching = False
        self.searchFinished.emit()
        
        # 验证结果
        if self._validate_search_results(results):
            # 获取原始的SearchResult对象
            search_result = None
            if self.search_worker and hasattr(self.search_worker, 'search_result'):
                search_result = self.search_worker.search_result
            
            # 如果有SearchResult对象，直接发送它
            if search_result:
                self.logger.info(f"发送SearchResult对象，总结果数: {search_result.total_results}")
                self.searchResultsUpdated.emit(search_result)
            else:
                # 否则发送字典列表
                self.logger.info(f"发送结果列表，结果数: {len(results)}")
                self.searchResultsUpdated.emit(results)
            
            # 完成任务
            if self.current_task_id:
                task_manager = getattr(self.main_window, 'task_manager', None)
                if task_manager:
                    task_manager.complete_task(self.current_task_id, {
                        'result_count': len(results),
                        'results': results[:10]  # 只保存前10个结果作为示例
                    })
        else:
            error_msg = "搜索结果格式无效"
            self.searchError.emit(error_msg)
            
            # 标记任务失败
            if self.current_task_id:
                task_manager = getattr(self.main_window, 'task_manager', None)
                if task_manager:
                    task_manager.complete_task(self.current_task_id, error=error_msg)
        
        # 清理任务ID
        self.current_task_id = None
        
        # 清理线程
        if self.search_thread:
            self.search_thread.quit()
            self.search_thread.wait()
            self.search_thread = None
            self.search_worker = None
    
    def _on_search_error(self, error_message: str):
        """搜索错误处理
        
        Args:
            error_message: 错误消息
        """
        self.is_searching = False
        self.searchFinished.emit()
        self.searchError.emit(error_message)
        
        # 标记任务失败
        if self.current_task_id:
            task_manager = getattr(self.main_window, 'task_manager', None)
            if task_manager:
                task_manager.complete_task(self.current_task_id, error=error_message)
        
        # 清理任务ID
        self.current_task_id = None
        
        # 清理线程
        if self.search_thread:
            self.search_thread.quit()
            self.search_thread.wait()
            self.search_thread = None
            self.search_worker = None
    
    def _validate_search_results(self, results: List[Dict[str, Any]]) -> bool:
        """验证搜索结果格式
        
        Args:
            results: 搜索结果
            
        Returns:
            bool: 是否有效
        """
        if not isinstance(results, list):
            return False
        
        for result in results:
            if not isinstance(result, dict):
                return False
            
            # 检查必需的字段
            required_fields = ['path', 'similarity']
            for field in required_fields:
                if field not in result:
                    return False
        
        return True
    
    def _cleanup_search_resources(self):
        """清理搜索相关资源"""
        try:
            if self.search_worker:
                self.search_worker.deleteLater()
                self.search_worker = None
            
            if self.search_thread:
                self.search_thread.deleteLater()
                self.search_thread = None
                
        except Exception as e:
            self.logger.error(f"清理搜索资源失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        self.stop_search()
        
        # 注意：不在这里调用search_engine.cleanup()，避免重复清理
        # search_engine由ComponentManager统一管理和清理
        self.search_engine = None