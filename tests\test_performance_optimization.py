#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化测试脚本

测试特征提取和存储的性能优化效果。
"""

import time
import numpy as np
import tempfile
import shutil
from pathlib import Path
from typing import List, Dict, Any
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_feature_cache_performance():
    """测试特征缓存性能"""
    logger.info("开始测试特征缓存性能...")

    try:
        from features.storage.feature_cache import FeatureCache

        # 创建缓存实例
        cache = FeatureCache(
            max_size=1000,
            max_memory_mb=100,
            enable_compression=True
        )

        # 生成测试数据
        test_data = []
        for i in range(100):
            features = np.random.rand(512).astype(np.float32)
            test_data.append((f"test_key_{i}", features))

        # 测试写入性能
        start_time = time.time()
        for key, features in test_data:
            cache.put(key, features)
        write_time = time.time() - start_time

        # 测试读取性能
        start_time = time.time()
        hit_count = 0
        for key, _ in test_data:
            if cache.get(key) is not None:
                hit_count += 1
        read_time = time.time() - start_time

        # 获取统计信息
        stats = cache.get_memory_usage()

        logger.info(f"缓存性能测试结果:")
        logger.info(f"  写入时间: {write_time:.3f}s")
        logger.info(f"  读取时间: {read_time:.3f}s")
        logger.info(f"  命中率: {hit_count/len(test_data)*100:.1f}%")
        logger.info(f"  内存使用: {stats['current_memory_mb']:.1f}MB")
        logger.info(f"  压缩比: {stats['compression_ratio']:.2f}")

        return True

    except Exception as e:
        logger.error(f"特征缓存性能测试失败: {e}")
        return False


def test_memory_management():
    """测试内存管理"""
    logger.info("开始测试内存管理...")

    try:
        from utils.memory_manager import get_memory_manager

        memory_manager = get_memory_manager()

        # 获取初始内存状态
        initial_stats = memory_manager.get_memory_stats()
        logger.info(f"初始内存使用: {initial_stats.process_memory_mb:.1f}MB")

        # 创建大量数据模拟内存压力
        large_arrays = []
        for i in range(10):
            arr = np.random.rand(1000, 1000).astype(np.float32)
            large_arrays.append(arr)

        # 检查内存使用
        after_stats = memory_manager.get_memory_stats()
        logger.info(f"分配后内存使用: {after_stats.process_memory_mb:.1f}MB")

        # 执行内存清理
        memory_manager.force_cleanup()

        # 清理数据
        del large_arrays

        # 再次检查内存
        final_stats = memory_manager.get_memory_stats()
        logger.info(f"清理后内存使用: {final_stats.process_memory_mb:.1f}MB")

        return True

    except Exception as e:
        logger.error(f"内存管理测试失败: {e}")
        return False


def test_io_performance():
    """测试I/O性能"""
    logger.info("开始测试I/O性能...")

    try:
        from utils.async_io_manager import get_io_manager

        io_manager = get_io_manager()

        # 创建临时目录
        temp_dir = Path(tempfile.mkdtemp())

        try:
            # 生成测试数据
            test_files = []
            for i in range(10):
                data = np.random.rand(1000).astype(np.float32)
                file_path = temp_dir / f"test_array_{i}.npy"
                test_files.append((file_path, data))

            # 测试批量写入
            start_time = time.time()
            file_data = [(path, data.tobytes()) for path, data in test_files]
            results = io_manager.batch_write_files(file_data, compress=True)
            write_time = time.time() - start_time

            success_count = sum(1 for _, success in results if success)
            logger.info(f"批量写入: {success_count}/{len(test_files)} 成功, 耗时 {write_time:.3f}s")

            # 测试批量读取
            start_time = time.time()
            file_paths = [path for path, _ in test_files]
            read_results = io_manager.batch_read_files(file_paths, decompress=True)
            read_time = time.time() - start_time

            read_success_count = sum(1 for _, data in read_results if data is not None)
            logger.info(f"批量读取: {read_success_count}/{len(test_files)} 成功, 耗时 {read_time:.3f}s")

            # 获取I/O统计
            stats = io_manager.get_stats()
            logger.info(f"I/O统计: {stats}")

        finally:
            # 清理临时文件
            shutil.rmtree(temp_dir)

        return True

    except Exception as e:
        logger.error(f"I/O性能测试失败: {e}")
        return False


def test_performance_monitoring():
    """测试性能监控"""
    logger.info("开始测试性能监控...")

    try:
        from utils.performance_monitor import get_performance_monitor

        monitor = get_performance_monitor()

        # 测试计时功能
        with monitor.measure("test_operation"):
            time.sleep(0.1)  # 模拟操作

        # 测试计数器
        monitor.increment_counter("test_counter", 5)

        # 测试仪表
        monitor.set_gauge("test_gauge", 42.0)

        # 获取统计信息
        timing_stats = monitor.get_timing_stats("test_operation")
        system_stats = monitor.get_system_stats()

        logger.info(f"操作统计: {timing_stats}")
        logger.info(f"系统统计: CPU {system_stats.get('cpu_percent', 0):.1f}%, "
                   f"内存 {system_stats.get('memory_percent', 0):.1f}%")

        return True

    except Exception as e:
        logger.error(f"性能监控测试失败: {e}")
        return False


def run_all_tests():
    """运行所有性能测试"""
    logger.info("开始运行性能优化测试套件...")

    tests = [
        ("特征缓存性能", test_feature_cache_performance),
        ("内存管理", test_memory_management),
        ("I/O性能", test_io_performance),
        ("性能监控", test_performance_monitoring),
    ]

    results = {}

    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试: {test_name}")
        logger.info(f"{'='*50}")

        try:
            start_time = time.time()
            success = test_func()
            duration = time.time() - start_time

            results[test_name] = {
                'success': success,
                'duration': duration
            }

            status = "通过" if success else "失败"
            logger.info(f"{test_name} - {status} (耗时: {duration:.3f}s)")

        except Exception as e:
            logger.error(f"{test_name} - 异常: {e}")
            results[test_name] = {
                'success': False,
                'duration': 0,
                'error': str(e)
            }

    # 输出总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结")
    logger.info(f"{'='*50}")

    total_tests = len(tests)
    passed_tests = sum(1 for r in results.values() if r['success'])

    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {total_tests - passed_tests}")
    logger.info(f"成功率: {passed_tests/total_tests*100:.1f}%")

    return results


if __name__ == "__main__":
    results = run_all_tests()
