"""
结果项模块

包含搜索结果项的显示组件
"""

import os
from typing import Dict, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QMenu, QMessageBox, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QPixmap, QFont, QAction

from database.models import FabricImage
from utils.logger_mixin import LoggerMixin
from gui.helpers.message_helper import MessageHelper


class ResultItem(QWidget, LoggerMixin):
    """搜索结果项组件"""
    
    # 信号
    itemClicked = pyqtSignal(FabricImage)
    itemDoubleClicked = pyqtSignal(FabricImage)
    item_deleted = pyqtSignal(int)  # 删除项目信号
    
    def __init__(self, fabric_image: FabricImage, thumbnail_size: int = 150, 
                 feature_scores: Dict[str, float] = None, parent=None):
        super().__init__(parent)
        
        self.fabric_image = fabric_image
        self.thumbnail_size = thumbnail_size
        self.feature_scores = feature_scores or {}
        self.is_selected = False
        
        self.logger.info(f"=== 创建ResultItem ===")
        self.logger.info(f"文件路径: {fabric_image.file_path}")
        self.logger.info(f"缩略图大小: {thumbnail_size}")
        self.logger.info(f"特征分数: {feature_scores}")
        self.logger.info(f"文件是否存在: {os.path.exists(fabric_image.file_path)}")
        
        self.setup_ui()
        self.load_thumbnail()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 缩略图
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setFixedSize(self.thumbnail_size, self.thumbnail_size)
        self.thumbnail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.thumbnail_label.setStyleSheet(
            "border: 1px solid #ddd; background-color: #f9f9f9;"
        )
        layout.addWidget(self.thumbnail_label)
        
        # 文件名
        filename = os.path.basename(self.fabric_image.file_path)
        if len(filename) > 20:
            filename = filename[:17] + "..."
        
        self.name_label = QLabel(filename)
        self.name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.name_label.setStyleSheet("font-size: 11px; color: #333;")
        self.name_label.setWordWrap(True)
        layout.addWidget(self.name_label)
        
        # 详细信息
        self.info_label = QLabel()
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.info_label.setStyleSheet("font-size: 10px; color: #666;")
        layout.addWidget(self.info_label)
        
        # 加权相似度总分标签
        self.weighted_score_label = QLabel()
        self.weighted_score_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.weighted_score_label.setStyleSheet(
            "font-size: 11px; font-weight: bold; color: #d2691e; "
            "background-color: #fff8dc; border: 1px solid #daa520; "
            "border-radius: 3px; padding: 2px;"
        )
        layout.addWidget(self.weighted_score_label)
        
        # 更新信息
        self.update_info()
        self.update_weighted_score()
        
        # 设置固定大小
        extra_height = 30  # 为加权相似度总分预留空间
        self.setFixedSize(self.thumbnail_size + 20, self.thumbnail_size + 80 + extra_height)
        
        # 设置样式
        self.setStyleSheet("""
            ResultItem {
                border: 2px solid transparent;
                border-radius: 5px;
                background-color: white;
            }
            ResultItem:hover {
                border-color: #87CEEB;
                background-color: #f0f8ff;
            }
        """)
    
    def load_thumbnail(self):
        """加载缩略图"""
        try:
            self.logger.info(f"=== 开始加载缩略图 ===")
            self.logger.info(f"文件路径: {self.fabric_image.file_path}")
            self.logger.info(f"文件路径类型: {type(self.fabric_image.file_path)}")
            self.logger.info(f"缩略图大小: {self.thumbnail_size}")
            
            # 检查文件路径是否为相对路径，如果是，转换为绝对路径
            file_path = self.fabric_image.file_path
            if not os.path.isabs(file_path):
                # 使用当前工作目录作为基准路径
                file_path = os.path.join(os.getcwd(), file_path)
                self.logger.info(f"转换为绝对路径: {file_path}")
                
                # 如果文件仍然不存在，尝试使用test_images目录
                if not os.path.exists(file_path) and file_path.find('test_images') != -1:
                    base_name = os.path.basename(file_path)
                    alt_path = os.path.join(os.getcwd(), 'test_images', base_name)
                    self.logger.info(f"尝试替代路径: {alt_path}")
                    if os.path.exists(alt_path):
                        file_path = alt_path
                        self.logger.info(f"使用替代路径: {file_path}")
                        # 更新FabricImage对象中的路径
                        self.fabric_image.file_path = file_path
            
            # 检查文件是否存在
            if os.path.exists(file_path):
                self.logger.info(f"文件存在，开始加载图片: {file_path}")
                
                # 尝试使用QPixmap加载图片
                pixmap = QPixmap(file_path)
                self.logger.info(f"QPixmap创建结果: {not pixmap.isNull()}")
                
                if not pixmap.isNull():
                    self.logger.info(f"图片加载成功，原始尺寸: {pixmap.width()}x{pixmap.height()}")
                    
                    # 缩放图片
                    scaled_pixmap = pixmap.scaled(
                        self.thumbnail_size, self.thumbnail_size,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.logger.info(f"图片缩放成功，缩放后尺寸: {scaled_pixmap.width()}x{scaled_pixmap.height()}")
                    
                    # 设置缩略图
                    self.thumbnail_label.setPixmap(scaled_pixmap)
                    self.logger.info("缩略图设置成功")
                else:
                    self.logger.error(f"QPixmap加载失败，图片为空: {file_path}")
                    # 尝试获取更多信息
                    self.logger.error(f"文件大小: {os.path.getsize(file_path) if os.path.exists(file_path) else 'N/A'}")
                    self.logger.error(f"文件扩展名: {os.path.splitext(file_path)[1]}")
                    self.thumbnail_label.setText("无法加载")
            else:
                self.logger.error(f"文件不存在: {file_path}")
                # 检查目录是否存在
                dir_path = os.path.dirname(file_path)
                self.logger.error(f"目录是否存在: {os.path.exists(dir_path)}, 目录: {dir_path}")
                
                # 创建一个占位图像
                placeholder_pixmap = QPixmap(self.thumbnail_size, self.thumbnail_size)
                placeholder_pixmap.fill(Qt.GlobalColor.lightGray)
                self.thumbnail_label.setPixmap(placeholder_pixmap)
                
                # 在占位图像上显示文本
                self.thumbnail_label.setText("文件不存在")
                self.thumbnail_label.setStyleSheet(
                    "border: 1px solid #ddd; background-color: #f9f9f9; color: red; "
                    "font-weight: bold; qproperty-alignment: AlignCenter;"
                )
                
        except Exception as e:
            self.logger.error(f"加载缩略图失败: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            self.thumbnail_label.setText("加载失败")
    
    def update_info(self):
        """更新详细信息"""
        try:
            info_parts = []
            
            # 文件大小
            if self.fabric_image.file_size:
                size_mb = self.fabric_image.file_size / (1024 * 1024)
                if size_mb >= 1:
                    info_parts.append(f"{size_mb:.1f}MB")
                else:
                    size_kb = self.fabric_image.file_size / 1024
                    info_parts.append(f"{size_kb:.1f}KB")
            
            # 图像尺寸
            if self.fabric_image.width and self.fabric_image.height:
                info_parts.append(f"{self.fabric_image.width}x{self.fabric_image.height}")
            
            # 类别
            if self.fabric_image.category:
                info_parts.append(self.fabric_image.category)
            
            self.info_label.setText(" | ".join(info_parts))
            
        except Exception as e:
            self.logger.error(f"更新信息失败: {e}")
    
    def update_weighted_score(self):
        """更新加权相似度总分显示"""
        try:
            # 检查是否有加权总分
            weighted_score = None
            
            # 优先查找加权分数
            if self.feature_scores:
                for key in ["加权", "weighted", "总分"]:
                    if key in self.feature_scores:
                        weighted_score = self.feature_scores[key]
                        break
            
            # 如果没有加权分数，查找相似度分数
            if weighted_score is None and hasattr(self.fabric_image, 'similarity_score'):
                weighted_score = self.fabric_image.similarity_score
            
            # 显示分数
            if weighted_score is not None:
                self.weighted_score_label.setText(f"相似度: {weighted_score:.4f}")
                self.weighted_score_label.setVisible(True)
            else:
                self.weighted_score_label.setVisible(False)
                
        except Exception as e:
            self.logger.error(f"更新加权相似度总分失败: {e}")
            self.weighted_score_label.setVisible(False)
    
    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.is_selected = selected
        
        if selected:
            self.setStyleSheet("""
                ResultItem {
                    border: 2px solid #4169E1;
                    border-radius: 5px;
                    background-color: #e6f3ff;
                }
            """)
        else:
            self.setStyleSheet("""
                ResultItem {
                    border: 2px solid transparent;
                    border-radius: 5px;
                    background-color: white;
                }
                ResultItem:hover {
                    border-color: #87CEEB;
                    background-color: #f0f8ff;
                }
            """)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.itemClicked.emit(self.fabric_image)
        super().mousePressEvent(event)
    
    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.itemDoubleClicked.emit(self.fabric_image)
        super().mouseDoubleClickEvent(event)
    
    def contextMenuEvent(self, event):
        """右键菜单事件"""
        try:
            menu = QMenu(self)
            
            # 查看详情
            detail_action = QAction("查看详情", self)
            detail_action.triggered.connect(self.show_detail)
            menu.addAction(detail_action)
            
            # 对比图像
            compare_action = QAction("对比图像", self)
            compare_action.triggered.connect(self.compare_image)
            menu.addAction(compare_action)
            
            # 以此图像搜索
            search_action = QAction("以此图像搜索", self)
            search_action.triggered.connect(self.search_with_image)
            menu.addAction(search_action)
            
            menu.addSeparator()
            
            # 在文件管理器中显示
            show_action = QAction("在文件管理器中显示", self)
            show_action.triggered.connect(self.show_in_explorer)
            menu.addAction(show_action)
            
            # 复制路径
            copy_action = QAction("复制路径", self)
            copy_action.triggered.connect(self.copy_path)
            menu.addAction(copy_action)
            
            menu.addSeparator()
            
            # 删除
            delete_action = QAction("删除", self)
            delete_action.triggered.connect(self.delete_item)
            menu.addAction(delete_action)
            
            menu.exec(event.globalPos())
            
        except Exception as e:
            self.logger.error(f"显示右键菜单失败: {e}")
    
    def show_detail(self):
        """显示详情"""
        try:
            # 获取主窗口
            main_window = self.window()
            if main_window and hasattr(main_window, 'show_image_detail'):
                main_window.show_image_detail(self.fabric_image)
            else:
                MessageHelper.show_info(self, "提示", "详情功能暂未实现")
        except Exception as e:
            self.logger.error(f"显示详情失败: {e}")
    
    def search_with_image(self):
        """以此图像搜索"""
        try:
            # 获取主窗口
            main_window = self.window()
            if main_window and hasattr(main_window, 'search_with_image'):
                main_window.search_with_image(self.fabric_image.file_path)
            else:
                MessageHelper.show_info(self, "提示", "搜索功能暂未实现")
        except Exception as e:
            self.logger.error(f"以此图像搜索失败: {e}")
    
    def show_in_explorer(self):
        """在文件管理器中显示"""
        try:
            import subprocess
            import platform
            
            if platform.system() == "Windows":
                subprocess.run(["explorer", "/select,", self.fabric_image.file_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", "-R", self.fabric_image.file_path])
            else:  # Linux
                subprocess.run(["xdg-open", os.path.dirname(self.fabric_image.file_path)])
                
        except Exception as e:
            self.logger.error(f"在文件管理器中显示失败: {e}")
            MessageHelper.show_error(self, "错误", f"无法打开文件管理器: {e}")
    
    def copy_path(self):
        """复制路径"""
        try:
            clipboard = QApplication.clipboard()
            clipboard.setText(self.fabric_image.file_path)
            MessageHelper.show_info(self, "提示", "路径已复制到剪贴板")
        except Exception as e:
            self.logger.error(f"复制路径失败: {e}")
    
    def compare_image(self):
        """对比图像"""
        try:
            # 获取主窗口
            main_window = self.window()
            if main_window:
                # 获取搜索图像
                search_image = None
                if hasattr(main_window, 'get_search_image'):
                    search_image = main_window.get_search_image()
                
                if search_image:
                    # 调用主窗口的对比图像方法
                    if hasattr(main_window, 'compare_images'):
                        main_window.compare_images(search_image, self.fabric_image.file_path)
                    else:
                        self.logger.error("主窗口没有compare_images方法")
                        MessageHelper.show_error(self, "错误", "无法对比图像，功能不可用")
                else:
                    self.logger.error("无法获取搜索图像")
                    MessageHelper.show_error(self, "错误", "请先设置搜索图像")
            else:
                self.logger.error("无法找到主窗口")
                MessageHelper.show_error(self, "错误", "无法对比图像")
                
        except Exception as e:
            self.logger.error(f"对比图像失败: {e}")
            MessageHelper.show_error(self, "错误", f"对比图像失败: {e}")
    
    def delete_item(self):
        """删除项目"""
        try:
            reply = MessageHelper.show_question(
                self, "确认删除", 
                f"确定要删除文件 '{os.path.basename(self.fabric_image.file_path)}' 吗？"
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 实现删除逻辑
                try:
                    import os
                    from PyQt6.QtWidgets import QMessageBox
                    
                    # 删除物理文件
                    if os.path.exists(self.fabric_image.file_path):
                        os.remove(self.fabric_image.file_path)
                        self.logger.info(f"已删除物理文件: {self.fabric_image.file_path}")
                    
                    # 从数据库删除记录
                    if hasattr(self.parent(), 'search_engine') and self.parent().search_engine:
                        try:
                            self.parent().search_engine.remove_image(self.fabric_image.id)
                            self.logger.info(f"已从数据库删除记录: {self.fabric_image.id}")
                        except Exception as db_error:
                            self.logger.error(f"从数据库删除记录失败: {db_error}")
                    
                    # 发送删除信号
                    if hasattr(self, 'item_deleted'):
                        self.item_deleted.emit(self.fabric_image.id)
                    
                    # 显示成功消息
                    QMessageBox.information(
                        self, "删除成功", 
                        f"文件 {self.fabric_image.file_name} 已成功删除"
                    )
                    
                except Exception as delete_error:
                    self.logger.error(f"删除操作失败: {delete_error}")
                    QMessageBox.critical(
                        self, "删除失败", 
                        f"删除文件时发生错误: {delete_error}"
                    )
                
                self.logger.info(f"删除文件: {self.fabric_image.file_path}")
                
        except Exception as e:
            self.logger.error(f"删除项目失败: {e}")