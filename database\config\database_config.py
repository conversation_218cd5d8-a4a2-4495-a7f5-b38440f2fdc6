#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置模块

该模块提供数据库配置相关的类和函数。
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class DatabaseConfig:
    """数据库配置"""
    db_path: str
    timeout: float = 30.0
    check_same_thread: bool = False
    isolation_level: Optional[str] = None
    enable_foreign_keys: bool = True
    enable_wal_mode: bool = True
    cache_size: int = -2000  # 2MB cache
    synchronous: str = 'NORMAL'


def get_default_config(db_path: str = "data/fabric_search.db") -> DatabaseConfig:
    """获取默认数据库配置
    
    Args:
        db_path: 数据库文件路径
        
    Returns:
        DatabaseConfig: 默认配置
    """
    return DatabaseConfig(db_path=db_path)