#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按钮工厂模块

该模块提供各种按钮控件的创建功能。
"""

from typing import Callable, Union
from PyQt6.QtWidgets import QPushButton, QToolButton
from PyQt6.QtCore import QSize
from PyQt6.QtGui import QIcon

from utils.log_utils import LoggerMixin
from ..helpers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>con<PERSON>elper
from .base import WidgetConfig, ButtonStyle, ButtonSize


class ButtonFactory(LoggerMixin):
    """按钮工厂"""
    
    def __init__(self):
        super().__init__()
        
        # 默认配置
        self.default_icon_size = QSize(16, 16)
        
        # 样式映射
        self.button_styles = {
            ButtonStyle.PRIMARY: "QPushButton { background-color: #2196F3; color: white; }",
            ButtonStyle.SECONDARY: "QPushButton { background-color: #6c757d; color: white; }",
            ButtonStyle.SUCCESS: "QPushButton { background-color: #28a745; color: white; }",
            ButtonStyle.WARNING: "QPushButton { background-color: #ffc107; color: black; }",
            ButtonStyle.ERROR: "QPushButton { background-color: #dc3545; color: white; }",
            ButtonStyle.INFO: "QPushButton { background-color: #17a2b8; color: white; }",
            ButtonStyle.OUTLINE: "QPushButton { background-color: transparent; border: 1px solid #2196F3; color: #2196F3; }",
            ButtonStyle.TEXT: "QPushButton { background-color: transparent; border: none; color: #2196F3; }"
        }
        
        self.button_sizes = {
            ButtonSize.SMALL: ("padding: 4px 8px;", 8),
            ButtonSize.MEDIUM: ("padding: 6px 12px;", 9),
            ButtonSize.LARGE: ("padding: 8px 16px;", 10)
        }
    
    def create_button(self, text: str = "", icon: Union[str, QIcon] = None,
                     icon_name: str = None, style: ButtonStyle = ButtonStyle.PRIMARY,
                     size: ButtonSize = ButtonSize.MEDIUM,
                     tooltip: str = "",
                     config: WidgetConfig = None,
                     click_handler: Callable = None) -> QPushButton:
        """创建按钮
        
        Args:
            text: 按钮文本
            icon: 图标对象或图标名称
            icon_name: 图标名称（兼容旧版本）
            style: 按钮样式
            size: 按钮尺寸
            tooltip: 工具提示
            config: 控件配置
            click_handler: 点击事件处理器
            
        Returns:
            QPushButton: 按钮控件
        """
        try:
            button = QPushButton(text)
            
            # 设置图标
            if icon_name:
                button.setIcon(IconHelper.load_icon(icon_name, self.default_icon_size))
            elif icon is not None:
                if isinstance(icon, str):
                    button.setIcon(IconHelper.load_icon(icon, self.default_icon_size))
                elif isinstance(icon, QIcon):
                    button.setIcon(icon)
            
            # 设置工具提示
            if tooltip:
                button.setToolTip(tooltip)
            
            # 应用样式
            style_sheet = self.button_styles.get(style, "")
            size_style, font_size = self.button_sizes.get(size, ("", 9))
            
            if style_sheet and size_style:
                combined_style = style_sheet.replace("}", f" {size_style} }}")
                button.setStyleSheet(combined_style)
            
            # 设置字体
            font = FontHelper.get_default_font(font_size)
            button.setFont(font)
            
            # 应用配置
            if config:
                config.apply_to_widget(button)
            
            # 连接事件
            if click_handler:
                button.clicked.connect(click_handler)
            
            return button
            
        except Exception as e:
            self.logger.error(f"创建按钮失败: {e}")
            return QPushButton(text)
    
    def create_tool_button(
        self, 
        text: str = "", 
        icon_name: str = None, 
        tooltip: str = "",
        config: WidgetConfig = None,
        click_handler: Callable = None
    ) -> QToolButton:
        """创建工具按钮
        
        Args:
            text: 按钮文本
            icon_name: 图标名称
            tooltip: 工具提示
            config: 控件配置
            click_handler: 点击事件处理器
            
        Returns:
            QToolButton: 工具按钮
        """
        try:
            button = QToolButton()
            
            # 设置文本
            if text:
                button.setText(text)
            
            # 设置图标
            if icon_name:
                button.setIcon(IconHelper.load_icon(icon_name, self.default_icon_size))
            
            # 设置工具提示
            if tooltip:
                button.setToolTip(tooltip)
            
            # 设置样式
            button.setAutoRaise(True)
            button.setIconSize(self.default_icon_size)
            
            # 应用配置
            if config:
                config.apply_to_widget(button)
            
            # 连接事件
            if click_handler:
                button.clicked.connect(click_handler)
            
            return button
            
        except Exception as e:
            self.logger.error(f"创建工具按钮失败: {e}")
            return QToolButton()
    
    def set_default_icon_size(self, size: QSize):
        """设置默认图标尺寸
        
        Args:
            size: 图标尺寸
        """
        self.default_icon_size = size