#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成综合PDF文档

该脚本将所有流程图和说明文档合并为一个综合PDF
"""

import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, FancyArrowPatch
import numpy as np
from pathlib import Path
import textwrap

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_title_page(pdf):
    """创建标题页"""
    fig, ax = plt.subplots(1, 1, figsize=(8.5, 11))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # 主标题
    ax.text(5, 8, 'Fabric Search 项目', 
           ha='center', va='center', fontsize=24, fontweight='bold')
    
    # 副标题
    ax.text(5, 7.3, 'GUI参数传递与搜索策略', 
           ha='center', va='center', fontsize=20, fontweight='bold')
    
    ax.text(5, 6.8, '详细技术文档', 
           ha='center', va='center', fontsize=18)
    
    # 版本信息
    ax.text(5, 5.5, '版本: v2.0', 
           ha='center', va='center', fontsize=14)
    
    ax.text(5, 5.1, '日期: 2025年8月', 
           ha='center', va='center', fontsize=14)
    
    # 内容概述
    content_text = """
本文档详细描述了Fabric Search项目中：

1. GUI搜索参数的传递流程
   - 特征权重设置
   - 传统特征参数
   - 高级搜索参数

2. 搜索策略的工作机制
   - 加权搜索策略
   - 自适应搜索策略
   - 查询扩展策略
   - 混合搜索策略
   - 单特征搜索策略

3. 系统架构设计
   - 分层架构
   - 设计模式
   - 性能优化
    """
    
    ax.text(5, 3.5, content_text, 
           ha='center', va='center', fontsize=12,
           bbox=dict(boxstyle="round,pad=0.5", facecolor='#f0f0f0', alpha=0.8))
    
    # 底部信息
    ax.text(5, 1, '© 2025 Fabric Search Project', 
           ha='center', va='center', fontsize=10, style='italic')
    
    pdf.savefig(fig, bbox_inches='tight')
    plt.close(fig)

def create_overview_page(pdf):
    """创建概览页"""
    fig, ax = plt.subplots(1, 1, figsize=(8.5, 11))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # 页面标题
    ax.text(5, 11.5, '系统架构概览', 
           ha='center', va='center', fontsize=18, fontweight='bold')
    
    # 架构层次
    layers = [
        {'name': '用户界面层 (GUI Layer)', 'desc': '用户交互界面，参数设置组件', 'y': 10.5, 'color': '#e3f2fd'},
        {'name': '参数收集层 (Parameter Collection)', 'desc': '特征权重、参数设置、过滤条件', 'y': 9.5, 'color': '#f3e5f5'},
        {'name': '配置转换层 (Configuration Layer)', 'desc': 'SearchConfig → SearchQuery 转换', 'y': 8.5, 'color': '#e8f5e8'},
        {'name': '搜索引擎层 (Search Engine)', 'desc': '搜索逻辑协调，策略选择', 'y': 7.5, 'color': '#fff3e0'},
        {'name': '策略执行层 (Strategy Layer)', 'desc': '具体搜索算法实现', 'y': 6.5, 'color': '#fce4ec'},
        {'name': '计算层 (Computation Layer)', 'desc': '相似度计算，FAISS索引', 'y': 5.5, 'color': '#ffebee'},
        {'name': '结果处理层 (Result Layer)', 'desc': '结果排序、过滤、分页', 'y': 4.5, 'color': '#f9fbe7'}
    ]
    
    for layer in layers:
        # 绘制层次框
        box = FancyBboxPatch(
            (1, layer['y'] - 0.3), 8, 0.6,
            boxstyle="round,pad=0.1",
            facecolor=layer['color'],
            edgecolor='black',
            linewidth=1
        )
        ax.add_patch(box)
        
        # 添加层次名称
        ax.text(2, layer['y'], layer['name'], 
               ha='left', va='center', fontsize=12, fontweight='bold')
        
        # 添加描述
        ax.text(2, layer['y'] - 0.15, layer['desc'], 
               ha='left', va='center', fontsize=10, style='italic')
    
    # 添加箭头
    for i in range(len(layers) - 1):
        arrow = FancyArrowPatch((5, layers[i]['y'] - 0.35), (5, layers[i+1]['y'] + 0.35),
                              arrowstyle='->', mutation_scale=20,
                              color='#333333', linewidth=2)
        ax.add_patch(arrow)
    
    # 关键特性
    features_text = """
关键技术特性：

• 模块化设计：清晰的层次分离，便于维护和扩展
• 策略模式：支持多种搜索算法，可插拔设计
• 工厂模式：动态策略创建和配置
• 性能优化：FAISS向量索引，缓存机制，并行处理
• 错误处理：完善的异常处理和日志记录
• 可扩展性：支持新策略和特征类型的添加
    """
    
    ax.text(5, 3, features_text, 
           ha='center', va='top', fontsize=11,
           bbox=dict(boxstyle="round,pad=0.3", facecolor='#f8f9fa', alpha=0.9))
    
    pdf.savefig(fig, bbox_inches='tight')
    plt.close(fig)

def create_parameter_flow_page(pdf):
    """创建参数传递流程页"""
    fig, ax = plt.subplots(1, 1, figsize=(8.5, 11))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # 页面标题
    ax.text(5, 11.5, 'GUI参数传递流程详解', 
           ha='center', va='center', fontsize=18, fontweight='bold')
    
    # 流程步骤
    steps = [
        {
            'title': '1. GUI参数收集',
            'content': '• FeatureWeightsWidget: 特征权重设置\n• FeatureParamsWidget: 传统特征参数\n• AdvancedSearchWidget: 高级搜索选项',
            'y': 10.5,
            'color': '#e3f2fd'
        },
        {
            'title': '2. 参数标准化',
            'content': '• get_weights(): 权重归一化\n• get_params(): 参数验证\n• get_search_params(): 搜索配置',
            'y': 9,
            'color': '#f3e5f5'
        },
        {
            'title': '3. 配置对象创建',
            'content': '• SearchConfig对象聚合所有参数\n• 统一的配置接口\n• 参数验证和默认值设置',
            'y': 7.5,
            'color': '#e8f5e8'
        },
        {
            'title': '4. 查询对象转换',
            'content': '• SearchHandler._config_to_query()\n• GUI配置 → 搜索引擎格式\n• 参数映射和类型转换',
            'y': 6,
            'color': '#fff3e0'
        },
        {
            'title': '5. 搜索执行',
            'content': '• SearchEngine接收SearchQuery\n• 策略选择和参数传递\n• 搜索结果生成',
            'y': 4.5,
            'color': '#fce4ec'
        }
    ]
    
    for step in steps:
        # 绘制步骤框
        box = FancyBboxPatch(
            (0.5, step['y'] - 0.6), 9, 1.2,
            boxstyle="round,pad=0.1",
            facecolor=step['color'],
            edgecolor='black',
            linewidth=1
        )
        ax.add_patch(box)
        
        # 添加标题
        ax.text(1, step['y'] + 0.2, step['title'], 
               ha='left', va='center', fontsize=14, fontweight='bold')
        
        # 添加内容
        ax.text(1, step['y'] - 0.2, step['content'], 
               ha='left', va='center', fontsize=10)
    
    # 添加箭头
    for i in range(len(steps) - 1):
        arrow = FancyArrowPatch((5, steps[i]['y'] - 0.65), (5, steps[i+1]['y'] + 0.65),
                              arrowstyle='->', mutation_scale=20,
                              color='#333333', linewidth=2)
        ax.add_patch(arrow)
    
    # 代码示例
    code_text = """
参数传递示例代码：

# GUI参数收集
weights = feature_weights_widget.get_weights()
params = feature_params_widget.get_params()

# 配置对象创建
config = SearchConfig(
    feature_weights=weights,
    feature_extraction_params=params
)

# 查询对象转换
query = search_handler._config_to_query(config)

# 搜索执行
results = search_engine.search(query)
    """
    
    ax.text(5, 2.5, code_text, 
           ha='center', va='top', fontsize=9,
           bbox=dict(boxstyle="round,pad=0.3", facecolor='#f8f9fa', alpha=0.9),
           family='monospace')
    
    pdf.savefig(fig, bbox_inches='tight')
    plt.close(fig)

def create_comprehensive_pdf():
    """创建综合PDF文档"""
    output_dir = Path('docs/diagrams')
    output_dir.mkdir(exist_ok=True)
    
    pdf_path = output_dir / 'Fabric_Search_GUI参数传递与搜索策略_综合文档.pdf'
    
    with PdfPages(pdf_path) as pdf:
        # 创建标题页
        create_title_page(pdf)
        
        # 创建概览页
        create_overview_page(pdf)
        
        # 创建参数传递流程页
        create_parameter_flow_page(pdf)
        
        # 导入已生成的流程图
        try:
            # 简化版流程图
            from scripts.generate_simple_flowchart_pdf import create_simple_flowchart_pdf
            fig1 = plt.figure(figsize=(11, 14))
            # 重新创建简化流程图但不保存，直接添加到PDF
            create_simple_flowchart_pdf()
            
            # 策略详细流程图
            from scripts.generate_strategy_flowchart_pdf import create_strategy_flowchart_pdf
            fig2 = plt.figure(figsize=(11, 16))
            # 重新创建策略流程图但不保存，直接添加到PDF
            create_strategy_flowchart_pdf()
            
        except Exception as e:
            print(f"导入流程图时出错: {e}")
    
    print(f"综合PDF文档已生成: {pdf_path}")

if __name__ == "__main__":
    create_comprehensive_pdf()
