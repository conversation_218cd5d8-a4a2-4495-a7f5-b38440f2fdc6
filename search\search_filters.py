#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索过滤器模块

该模块定义各种搜索过滤条件。
"""

import logging
from typing import List, Dict, Any, Optional, Union, Set
from datetime import datetime, date
from enum import Enum
from dataclasses import dataclass, field
import re

from database.models import FabricImage
from utils.log_utils import LoggerMixin


class FilterType(Enum):
    """过滤器类型"""
    CATEGORY = "category"
    TAGS = "tags"
    COLOR = "color"
    SIZE = "size"
    DATE_RANGE = "date_range"
    FILE_TYPE = "file_type"
    RESOLUTION = "resolution"
    FILE_SIZE = "file_size"
    SIMILARITY_THRESHOLD = "similarity_threshold"
    TEXT_SEARCH = "text_search"
    CUSTOM = "custom"


@dataclass
class FilterCondition:
    """过滤条件"""
    field: str
    operator: str  # eq, ne, gt, lt, ge, le, in, not_in, contains, not_contains, regex
    value: Any
    case_sensitive: bool = False


@dataclass
class SearchFilter:
    """搜索过滤器"""
    filter_type: FilterType
    name: str
    description: str = ""
    enabled: bool = True
    conditions: List[FilterCondition] = field(default_factory=list)
    logic_operator: str = "AND"  # AND, OR
    priority: int = 0  # 优先级，数字越大优先级越高
    
    def add_condition(self, field: str, operator: str, value: Any, case_sensitive: bool = False):
        """添加过滤条件
        
        Args:
            field: 字段名
            operator: 操作符
            value: 值
            case_sensitive: 是否区分大小写
        """
        condition = FilterCondition(
            field=field,
            operator=operator,
            value=value,
            case_sensitive=case_sensitive
        )
        self.conditions.append(condition)
    
    def remove_condition(self, index: int) -> bool:
        """移除过滤条件
        
        Args:
            index: 条件索引
            
        Returns:
            bool: 是否成功移除
        """
        try:
            if 0 <= index < len(self.conditions):
                self.conditions.pop(index)
                return True
            return False
        except Exception:
            return False
    
    def clear_conditions(self):
        """清除所有条件"""
        self.conditions.clear()


class FilterEngine(LoggerMixin):
    """过滤引擎"""
    
    def __init__(self, fabric_repository=None):
        """初始化过滤引擎
        
        Args:
            fabric_repository: 布料数据仓库
        """
        super().__init__()
        self.fabric_repository = fabric_repository
        self.filters: Dict[str, SearchFilter] = {}
        
        # 创建预定义过滤器
        self.predefined_filters = self._create_predefined_filters()
        self.filters.update(self.predefined_filters)
        
        self.logger.info(f"过滤引擎初始化完成，加载 {len(self.filters)} 个过滤器")
    
    def _create_predefined_filters(self) -> Dict[str, SearchFilter]:
        """创建预定义过滤器
        
        Returns:
            Dict[str, SearchFilter]: 预定义过滤器字典
        """
        filters = {}
        
        # 类别过滤器
        category_filter = SearchFilter(
            filter_type=FilterType.CATEGORY,
            name="category_filter",
            description="按类别过滤",
            priority=10
        )
        filters["category"] = category_filter
        
        # 标签过滤器
        tags_filter = SearchFilter(
            filter_type=FilterType.TAGS,
            name="tags_filter",
            description="按标签过滤",
            priority=9
        )
        filters["tags"] = tags_filter
        
        # 颜色过滤器
        color_filter = SearchFilter(
            filter_type=FilterType.COLOR,
            name="color_filter",
            description="按颜色过滤",
            priority=8
        )
        filters["color"] = color_filter
        
        # 日期范围过滤器
        date_filter = SearchFilter(
            filter_type=FilterType.DATE_RANGE,
            name="date_range_filter",
            description="按日期范围过滤",
            priority=7
        )
        filters["date_range"] = date_filter
        
        # 文件类型过滤器
        file_type_filter = SearchFilter(
            filter_type=FilterType.FILE_TYPE,
            name="file_type_filter",
            description="按文件类型过滤",
            priority=6
        )
        filters["file_type"] = file_type_filter
        
        # 分辨率过滤器
        resolution_filter = SearchFilter(
            filter_type=FilterType.RESOLUTION,
            name="resolution_filter",
            description="按分辨率过滤",
            priority=5
        )
        filters["resolution"] = resolution_filter
        
        # 文件大小过滤器
        file_size_filter = SearchFilter(
            filter_type=FilterType.FILE_SIZE,
            name="file_size_filter",
            description="按文件大小过滤",
            priority=4
        )
        filters["file_size"] = file_size_filter
        
        # 相似度阈值过滤器
        similarity_filter = SearchFilter(
            filter_type=FilterType.SIMILARITY_THRESHOLD,
            name="similarity_threshold_filter",
            description="按相似度阈值过滤",
            priority=3
        )
        filters["similarity_threshold"] = similarity_filter
        
        # 文本搜索过滤器
        text_filter = SearchFilter(
            filter_type=FilterType.TEXT_SEARCH,
            name="text_search_filter",
            description="文本搜索过滤",
            priority=2
        )
        filters["text_search"] = text_filter
        
        return filters
    
    def add_filter(self, filter_name: str, search_filter: SearchFilter) -> bool:
        """添加过滤器
        
        Args:
            filter_name: 过滤器名称
            search_filter: 搜索过滤器
            
        Returns:
            bool: 是否成功添加
        """
        try:
            self.filters[filter_name] = search_filter
            self.logger.debug(f"添加过滤器: {filter_name}")
            return True
        except Exception as e:
            self.logger.error(f"添加过滤器失败 {filter_name}: {e}")
            return False
    
    def remove_filter(self, filter_name: str) -> bool:
        """移除过滤器
        
        Args:
            filter_name: 过滤器名称
            
        Returns:
            bool: 是否成功移除
        """
        try:
            if filter_name in self.filters:
                del self.filters[filter_name]
                self.logger.debug(f"移除过滤器: {filter_name}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"移除过滤器失败 {filter_name}: {e}")
            return False
    
    def get_filter(self, filter_name: str) -> Optional[SearchFilter]:
        """获取过滤器
        
        Args:
            filter_name: 过滤器名称
            
        Returns:
            Optional[SearchFilter]: 过滤器对象
        """
        return self.filters.get(filter_name)
    
    def get_enabled_filters(self) -> List[SearchFilter]:
        """获取启用的过滤器
        
        Returns:
            List[SearchFilter]: 启用的过滤器列表
        """
        enabled_filters = [f for f in self.filters.values() if f.enabled]
        # 按优先级排序
        enabled_filters.sort(key=lambda x: x.priority, reverse=True)
        return enabled_filters
    
    def apply_filters(self, fabric_images: List[FabricImage],
                     active_filters: Optional[List[str]] = None) -> List[FabricImage]:
        """应用过滤器
        
        Args:
            fabric_images: 布料图片列表
            active_filters: 活跃的过滤器名称列表，None表示使用所有启用的过滤器
            
        Returns:
            List[FabricImage]: 过滤后的图片列表
        """
        try:
            if not fabric_images:
                return []
            
            # 确定要应用的过滤器
            if active_filters is None:
                filters_to_apply = self.get_enabled_filters()
            else:
                filters_to_apply = [
                    self.filters[name] for name in active_filters 
                    if name in self.filters and self.filters[name].enabled
                ]
            
            if not filters_to_apply:
                return fabric_images
            
            # 应用过滤器
            filtered_images = fabric_images.copy()
            
            for search_filter in filters_to_apply:
                if search_filter.conditions:
                    filtered_images = self._apply_single_filter(
                        filtered_images, search_filter
                    )
            
            self.logger.debug(
                f"过滤完成: {len(fabric_images)} -> {len(filtered_images)}"
            )
            
            return filtered_images
            
        except Exception as e:
            self.logger.error(f"应用过滤器失败: {e}")
            return fabric_images
    
    def _apply_single_filter(self, fabric_images: List[FabricImage],
                           search_filter: SearchFilter) -> List[FabricImage]:
        """应用单个过滤器
        
        Args:
            fabric_images: 布料图片列表
            search_filter: 搜索过滤器
            
        Returns:
            List[FabricImage]: 过滤后的图片列表
        """
        try:
            filtered_images = []
            
            for fabric_image in fabric_images:
                if self._evaluate_filter_conditions(fabric_image, search_filter):
                    filtered_images.append(fabric_image)
            
            return filtered_images
            
        except Exception as e:
            self.logger.error(f"应用单个过滤器失败 {search_filter.name}: {e}")
            return fabric_images
    
    def _evaluate_filter_conditions(self, fabric_image: FabricImage,
                                  search_filter: SearchFilter) -> bool:
        """评估过滤条件
        
        Args:
            fabric_image: 布料图片
            search_filter: 搜索过滤器
            
        Returns:
            bool: 是否满足条件
        """
        try:
            if not search_filter.conditions:
                return True
            
            results = []
            
            for condition in search_filter.conditions:
                result = self._evaluate_single_condition(fabric_image, condition)
                results.append(result)
            
            # 根据逻辑操作符组合结果
            if search_filter.logic_operator.upper() == "OR":
                return any(results)
            else:  # AND
                return all(results)
                
        except Exception as e:
            self.logger.error(f"评估过滤条件失败: {e}")
            return True
    
    def _evaluate_single_condition(self, fabric_image: FabricImage,
                                 condition: FilterCondition) -> bool:
        """评估单个条件
        
        Args:
            fabric_image: 布料图片
            condition: 过滤条件
            
        Returns:
            bool: 是否满足条件
        """
        try:
            # 获取字段值
            field_value = self._get_field_value(fabric_image, condition.field)
            
            if field_value is None:
                return False
            
            # 处理大小写敏感性
            if isinstance(field_value, str) and not condition.case_sensitive:
                field_value = field_value.lower()
            
            if isinstance(condition.value, str) and not condition.case_sensitive:
                condition_value = condition.value.lower()
            else:
                condition_value = condition.value
            
            # 根据操作符评估
            operator = condition.operator.lower()
            
            if operator == "eq":
                return field_value == condition_value
            elif operator == "ne":
                return field_value != condition_value
            elif operator == "gt":
                return field_value > condition_value
            elif operator == "lt":
                return field_value < condition_value
            elif operator == "ge":
                return field_value >= condition_value
            elif operator == "le":
                return field_value <= condition_value
            elif operator == "in":
                if isinstance(condition_value, (list, tuple, set)):
                    return field_value in condition_value
                return False
            elif operator == "not_in":
                if isinstance(condition_value, (list, tuple, set)):
                    return field_value not in condition_value
                return True
            elif operator == "contains":
                if isinstance(field_value, str):
                    return str(condition_value) in field_value
                elif isinstance(field_value, (list, tuple)):
                    return condition_value in field_value
                return False
            elif operator == "not_contains":
                if isinstance(field_value, str):
                    return str(condition_value) not in field_value
                elif isinstance(field_value, (list, tuple)):
                    return condition_value not in field_value
                return True
            elif operator == "regex":
                if isinstance(field_value, str):
                    pattern = re.compile(
                        str(condition_value),
                        re.IGNORECASE if not condition.case_sensitive else 0
                    )
                    return bool(pattern.search(field_value))
                return False
            else:
                self.logger.warning(f"未知操作符: {operator}")
                return True
                
        except Exception as e:
            self.logger.error(f"评估单个条件失败: {e}")
            return True
    
    def _get_field_value(self, fabric_image: FabricImage, field: str) -> Any:
        """获取字段值
        
        Args:
            fabric_image: 布料图片
            field: 字段名
            
        Returns:
            Any: 字段值
        """
        try:
            # 处理嵌套字段（如 metadata.width）
            if '.' in field:
                parts = field.split('.')
                value = fabric_image
                for part in parts:
                    if hasattr(value, part):
                        value = getattr(value, part)
                    elif isinstance(value, dict) and part in value:
                        value = value[part]
                    else:
                        return None
                return value
            else:
                # 直接字段访问
                if hasattr(fabric_image, field):
                    return getattr(fabric_image, field)
                return None
                
        except Exception as e:
            self.logger.error(f"获取字段值失败 {field}: {e}")
            return None
    
    def create_category_filter(self, categories: List[str]) -> SearchFilter:
        """创建类别过滤器
        
        Args:
            categories: 类别列表
            
        Returns:
            SearchFilter: 类别过滤器
        """
        filter_obj = SearchFilter(
            filter_type=FilterType.CATEGORY,
            name="category_filter",
            description=f"类别过滤: {', '.join(categories)}"
        )
        filter_obj.add_condition("category", "in", categories)
        return filter_obj
    
    def create_tags_filter(self, tags: List[str], match_all: bool = False) -> SearchFilter:
        """创建标签过滤器
        
        Args:
            tags: 标签列表
            match_all: 是否匹配所有标签
            
        Returns:
            SearchFilter: 标签过滤器
        """
        filter_obj = SearchFilter(
            filter_type=FilterType.TAGS,
            name="tags_filter",
            description=f"标签过滤: {', '.join(tags)}",
            logic_operator="AND" if match_all else "OR"
        )
        
        for tag in tags:
            filter_obj.add_condition("tags", "contains", tag)
        
        return filter_obj
    
    def create_date_range_filter(self, start_date: Optional[datetime] = None,
                                end_date: Optional[datetime] = None) -> SearchFilter:
        """创建日期范围过滤器
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            SearchFilter: 日期范围过滤器
        """
        filter_obj = SearchFilter(
            filter_type=FilterType.DATE_RANGE,
            name="date_range_filter",
            description="日期范围过滤"
        )
        
        if start_date:
            filter_obj.add_condition("created_at", "ge", start_date)
        
        if end_date:
            filter_obj.add_condition("created_at", "le", end_date)
        
        return filter_obj
    
    def create_file_size_filter(self, min_size: Optional[int] = None,
                               max_size: Optional[int] = None) -> SearchFilter:
        """创建文件大小过滤器
        
        Args:
            min_size: 最小文件大小（字节）
            max_size: 最大文件大小（字节）
            
        Returns:
            SearchFilter: 文件大小过滤器
        """
        filter_obj = SearchFilter(
            filter_type=FilterType.FILE_SIZE,
            name="file_size_filter",
            description="文件大小过滤"
        )
        
        if min_size is not None:
            filter_obj.add_condition("file_size", "ge", min_size)
        
        if max_size is not None:
            filter_obj.add_condition("file_size", "le", max_size)
        
        return filter_obj
    
    def create_resolution_filter(self, min_width: Optional[int] = None,
                               max_width: Optional[int] = None,
                               min_height: Optional[int] = None,
                               max_height: Optional[int] = None) -> SearchFilter:
        """创建分辨率过滤器
        
        Args:
            min_width: 最小宽度
            max_width: 最大宽度
            min_height: 最小高度
            max_height: 最大高度
            
        Returns:
            SearchFilter: 分辨率过滤器
        """
        filter_obj = SearchFilter(
            filter_type=FilterType.RESOLUTION,
            name="resolution_filter",
            description="分辨率过滤"
        )
        
        if min_width is not None:
            filter_obj.add_condition("width", "ge", min_width)
        
        if max_width is not None:
            filter_obj.add_condition("width", "le", max_width)
        
        if min_height is not None:
            filter_obj.add_condition("height", "ge", min_height)
        
        if max_height is not None:
            filter_obj.add_condition("height", "le", max_height)
        
        return filter_obj
    
    def create_text_search_filter(self, query: str, fields: Optional[List[str]] = None) -> SearchFilter:
        """创建文本搜索过滤器
        
        Args:
            query: 搜索查询
            fields: 搜索字段列表
            
        Returns:
            SearchFilter: 文本搜索过滤器
        """
        if fields is None:
            fields = ["file_name", "description", "tags", "category"]
        
        filter_obj = SearchFilter(
            filter_type=FilterType.TEXT_SEARCH,
            name="text_search_filter",
            description=f"文本搜索: {query}",
            logic_operator="OR"
        )
        
        for field in fields:
            filter_obj.add_condition(field, "contains", query, case_sensitive=False)
        
        return filter_obj
    
    def get_filter_statistics(self) -> Dict[str, Any]:
        """获取过滤器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {
                'total_filters': len(self.filters),
                'enabled_filters': len([f for f in self.filters.values() if f.enabled]),
                'filter_types': {},
                'filters_with_conditions': 0
            }
            
            # 按类型统计
            for filter_obj in self.filters.values():
                filter_type = filter_obj.filter_type.value
                if filter_type not in stats['filter_types']:
                    stats['filter_types'][filter_type] = 0
                stats['filter_types'][filter_type] += 1
                
                if filter_obj.conditions:
                    stats['filters_with_conditions'] += 1
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取过滤器统计信息失败: {e}")
            return {}
    
    def reset_filters(self):
        """重置所有过滤器"""
        try:
            self.filters.clear()
            self.filters.update(self.predefined_filters)
            self.logger.info("过滤器已重置")
        except Exception as e:
            self.logger.error(f"重置过滤器失败: {e}")
    
    def export_filters(self) -> Dict[str, Any]:
        """导出过滤器配置
        
        Returns:
            Dict[str, Any]: 过滤器配置
        """
        try:
            export_data = {}
            
            for name, filter_obj in self.filters.items():
                export_data[name] = {
                    'filter_type': filter_obj.filter_type.value,
                    'name': filter_obj.name,
                    'description': filter_obj.description,
                    'enabled': filter_obj.enabled,
                    'logic_operator': filter_obj.logic_operator,
                    'priority': filter_obj.priority,
                    'conditions': [
                        {
                            'field': c.field,
                            'operator': c.operator,
                            'value': c.value,
                            'case_sensitive': c.case_sensitive
                        }
                        for c in filter_obj.conditions
                    ]
                }
            
            return export_data
            
        except Exception as e:
            self.logger.error(f"导出过滤器配置失败: {e}")
            return {}
    
    def import_filters(self, filter_data: Dict[str, Any]) -> bool:
        """导入过滤器配置
        
        Args:
            filter_data: 过滤器配置数据
            
        Returns:
            bool: 导入是否成功
        """
        try:
            for name, data in filter_data.items():
                filter_type = FilterType(data['filter_type'])
                
                search_filter = SearchFilter(
                    filter_type=filter_type,
                    name=data['name'],
                    description=data.get('description', ''),
                    enabled=data.get('enabled', True),
                    logic_operator=data.get('logic_operator', 'AND'),
                    priority=data.get('priority', 0)
                )
                
                # 添加条件
                for condition_data in data.get('conditions', []):
                    search_filter.add_condition(
                        field=condition_data['field'],
                        operator=condition_data['operator'],
                        value=condition_data['value'],
                        case_sensitive=condition_data.get('case_sensitive', False)
                    )
                
                self.filters[name] = search_filter
            
            self.logger.info(f"导入过滤器配置成功: {len(filter_data)} 个过滤器")
            return True
            
        except Exception as e:
            self.logger.error(f"导入过滤器配置失败: {e}")
            return False