#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型选择对话框 (导入转发模块)

该模块提供用户选择特征提取模型的界面。
为保持向后兼容性，该模块将导入转发到新的模块结构。
"""

# 从新模块导入类，以保持向后兼容性
from gui.dialogs.model_selector_dialog import ModelSelectorDialog
from gui.dialogs.model_config import ModelType, ModelDisplayInfo, ModelSelectorConfig
from gui.dialogs.model_selector import ModelSelector
from gui.dialogs.model_details import ModelDetailsWidget

# 导出所有类，使旧代码可以继续工作
__all__ = [
    'ModelSelectorDialog',
    'ModelType',
    'ModelDisplayInfo',
    'ModelSelectorConfig',
    'ModelSelector',
    'ModelDetailsWidget'
]