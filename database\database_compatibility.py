#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库兼容性检查模块

该模块提供数据库与模型兼容性检查和版本管理功能。
"""

import json
import hashlib
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

from utils.log_utils import LoggerMixin
from config.model_config import ModelConfig


@dataclass
class DatabaseVersion:
    """数据库版本信息"""
    version: str
    model_name: str
    model_version: str
    feature_dimension: int
    feature_type: str
    created_at: str
    updated_at: str
    checksum: str
    metadata: Dict[str, Any]


@dataclass
class CompatibilityResult:
    """兼容性检查结果"""
    is_compatible: bool
    issues: List[str]
    warnings: List[str]
    recommendations: List[str]
    migration_required: bool
    backup_recommended: bool


class DatabaseCompatibilityChecker(LoggerMixin):
    """数据库兼容性检查器"""
    
    def __init__(self):
        super().__init__()
        self.current_version = "2.0"
    
    def check_compatibility(self, db_path: Path, 
                          model_config: ModelConfig) -> CompatibilityResult:
        """检查数据库与模型的兼容性
        
        Args:
            db_path: 数据库路径
            model_config: 模型配置
            
        Returns:
            CompatibilityResult: 兼容性检查结果
        """
        try:
            issues = []
            warnings = []
            recommendations = []
            migration_required = False
            backup_recommended = False
            
            # 检查数据库是否存在
            if not self._database_exists(db_path):
                return CompatibilityResult(
                    is_compatible=True,
                    issues=[],
                    warnings=["数据库不存在，将创建新数据库"],
                    recommendations=["建议在首次使用前进行特征提取"],
                    migration_required=False,
                    backup_recommended=False
                )
            
            # 读取数据库版本信息
            db_version = self._read_database_version(db_path)
            
            if db_version is None:
                issues.append("无法读取数据库版本信息")
                recommendations.append("建议重新创建数据库")
                backup_recommended = True
                migration_required = True
            else:
                # 检查版本兼容性
                version_check = self._check_version_compatibility(db_version)
                if not version_check[0]:
                    issues.extend(version_check[1])
                    migration_required = True
                    backup_recommended = True
                
                # 检查模型兼容性
                model_check = self._check_model_compatibility(db_version, model_config)
                if not model_check[0]:
                    issues.extend(model_check[1])
                    if model_check[2]:  # 需要迁移
                        migration_required = True
                        backup_recommended = True
                    else:
                        warnings.extend(model_check[1])
                
                # 检查特征维度兼容性
                dimension_check = self._check_dimension_compatibility(db_version, model_config)
                if not dimension_check[0]:
                    issues.extend(dimension_check[1])
                    migration_required = True
                    backup_recommended = True
                
                # 检查数据完整性
                integrity_check = self._check_data_integrity(db_path, db_version)
                if not integrity_check[0]:
                    warnings.extend(integrity_check[1])
                    recommendations.append("建议运行数据库修复工具")
            
            # 生成建议
            if migration_required:
                recommendations.append("需要进行数据库迁移")
            if backup_recommended:
                recommendations.append("建议在操作前备份数据库")
            
            is_compatible = len(issues) == 0
            
            result = CompatibilityResult(
                is_compatible=is_compatible,
                issues=issues,
                warnings=warnings,
                recommendations=recommendations,
                migration_required=migration_required,
                backup_recommended=backup_recommended
            )
            
            self.logger.info(f"兼容性检查完成: {db_path}, 兼容: {is_compatible}")
            return result
            
        except Exception as e:
            self.logger.error(f"兼容性检查失败: {e}")
            return CompatibilityResult(
                is_compatible=False,
                issues=[f"兼容性检查失败: {e}"],
                warnings=[],
                recommendations=["建议检查数据库文件是否损坏"],
                migration_required=True,
                backup_recommended=True
            )
    
    def create_database_version(self, db_path: Path, 
                              model_config: ModelConfig,
                              feature_dimension: int,
                              feature_type: str = "deep_learning") -> bool:
        """创建数据库版本信息
        
        Args:
            db_path: 数据库路径
            model_config: 模型配置
            feature_dimension: 特征维度
            feature_type: 特征类型
            
        Returns:
            bool: 创建是否成功
        """
        try:
            now = datetime.now().isoformat()
            
            # 计算校验和
            checksum = self._calculate_database_checksum(db_path)
            
            version_info = DatabaseVersion(
                version=self.current_version,
                model_name=model_config.model_name,
                model_version=model_config.model_version,
                feature_dimension=feature_dimension,
                feature_type=feature_type,
                created_at=now,
                updated_at=now,
                checksum=checksum,
                metadata={
                    "model_path": model_config.model_path,
                    "input_size": model_config.input_size,
                    "preprocessing": model_config.preprocessing_config
                }
            )
            
            # 保存版本信息
            version_path = self._get_version_file_path(db_path)
            with open(version_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(version_info), f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"数据库版本信息创建成功: {version_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建数据库版本信息失败: {e}")
            return False
    
    def update_database_version(self, db_path: Path, 
                              model_config: Optional[ModelConfig] = None) -> bool:
        """更新数据库版本信息
        
        Args:
            db_path: 数据库路径
            model_config: 模型配置
            
        Returns:
            bool: 更新是否成功
        """
        try:
            version_path = self._get_version_file_path(db_path)
            
            if not version_path.exists():
                self.logger.warning("版本文件不存在，无法更新")
                return False
            
            # 读取现有版本信息
            with open(version_path, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
            
            # 更新时间戳和校验和
            version_data['updated_at'] = datetime.now().isoformat()
            version_data['checksum'] = self._calculate_database_checksum(db_path)
            
            # 如果提供了新的模型配置，更新模型信息
            if model_config:
                version_data['model_name'] = model_config.model_name
                version_data['model_version'] = model_config.model_version
                version_data['metadata'].update({
                    "model_path": model_config.model_path,
                    "input_size": model_config.input_size,
                    "preprocessing": model_config.preprocessing_config
                })
            
            # 保存更新后的版本信息
            with open(version_path, 'w', encoding='utf-8') as f:
                json.dump(version_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"数据库版本信息更新成功: {version_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新数据库版本信息失败: {e}")
            return False
    
    def _database_exists(self, db_path: Path) -> bool:
        """检查数据库是否存在"""
        if db_path.is_file():
            return True
        elif db_path.is_dir():
            # 检查分块数据库
            return (db_path / 'index.json').exists()
        return False
    
    def _read_database_version(self, db_path: Path) -> Optional[DatabaseVersion]:
        """读取数据库版本信息"""
        try:
            version_path = self._get_version_file_path(db_path)
            
            if not version_path.exists():
                return None
            
            with open(version_path, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
            
            return DatabaseVersion(**version_data)
            
        except Exception as e:
            self.logger.error(f"读取数据库版本信息失败: {e}")
            return None
    
    def _get_version_file_path(self, db_path: Path) -> Path:
        """获取版本文件路径"""
        if db_path.is_file():
            return db_path.with_suffix('.version')
        else:
            return db_path / 'version.json'
    
    def _check_version_compatibility(self, db_version: DatabaseVersion) -> Tuple[bool, List[str]]:
        """检查版本兼容性"""
        issues = []
        
        try:
            db_ver = float(db_version.version)
            current_ver = float(self.current_version)
            
            if db_ver > current_ver:
                issues.append(f"数据库版本 {db_version.version} 高于当前支持版本 {self.current_version}")
            elif db_ver < 1.0:
                issues.append(f"数据库版本 {db_version.version} 过旧，需要升级")
            
        except ValueError:
            issues.append(f"无效的数据库版本格式: {db_version.version}")
        
        return len(issues) == 0, issues
    
    def _check_model_compatibility(self, db_version: DatabaseVersion, 
                                 model_config: ModelConfig) -> Tuple[bool, List[str], bool]:
        """检查模型兼容性
        
        Returns:
            Tuple[bool, List[str], bool]: (是否兼容, 问题列表, 是否需要迁移)
        """
        issues = []
        needs_migration = False
        
        # 检查模型名称
        if db_version.model_name != model_config.model_name:
            issues.append(f"模型不匹配: 数据库使用 {db_version.model_name}, 当前使用 {model_config.model_name}")
            needs_migration = True
        
        # 检查模型版本
        elif db_version.model_version != model_config.model_version:
            issues.append(f"模型版本不匹配: 数据库使用 {db_version.model_version}, 当前使用 {model_config.model_version}")
            # 版本不匹配可能不需要完全迁移，取决于具体情况
        
        return len(issues) == 0, issues, needs_migration
    
    def _check_dimension_compatibility(self, db_version: DatabaseVersion, 
                                     model_config: ModelConfig) -> Tuple[bool, List[str]]:
        """检查特征维度兼容性"""
        issues = []
        
        # 这里需要根据模型配置获取特征维度
        # 暂时跳过具体实现，因为需要模型加载
        
        return True, issues
    
    def _check_data_integrity(self, db_path: Path, 
                            db_version: DatabaseVersion) -> Tuple[bool, List[str]]:
        """检查数据完整性"""
        warnings = []
        
        try:
            # 计算当前校验和
            current_checksum = self._calculate_database_checksum(db_path)
            
            if current_checksum != db_version.checksum:
                warnings.append("数据库校验和不匹配，可能存在数据损坏")
            
        except Exception as e:
            warnings.append(f"无法验证数据完整性: {e}")
        
        return len(warnings) == 0, warnings
    
    def _calculate_database_checksum(self, db_path: Path) -> str:
        """计算数据库校验和"""
        try:
            hasher = hashlib.md5()
            
            if db_path.is_file():
                # 单文件数据库
                with open(db_path, 'rb') as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hasher.update(chunk)
            elif db_path.is_dir():
                # 分块数据库
                for file_path in sorted(db_path.glob('**/*')):
                    if file_path.is_file() and not file_path.name.endswith('.version'):
                        with open(file_path, 'rb') as f:
                            for chunk in iter(lambda: f.read(4096), b""):
                                hasher.update(chunk)
            
            return hasher.hexdigest()
            
        except Exception as e:
            self.logger.error(f"计算校验和失败: {e}")
            return ""