#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库事务管理器

该模块提供数据库事务的管理功能。
"""

import sqlite3
import logging
from contextlib import contextmanager
from typing import Optional, Any, Dict, List

from .connection_manager import ConnectionManager


class TransactionManager:
    """数据库事务管理器"""
    
    def __init__(self, connection_manager: ConnectionManager):
        """初始化事务管理器
        
        Args:
            connection_manager: 连接管理器
        """
        self.connection_manager = connection_manager
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @contextmanager
    def transaction(self):
        """事务上下文管理器
        
        Yields:
            sqlite3.Connection: 数据库连接
        """
        conn = self.connection_manager.get_connection()
        
        try:
            # 开始事务
            conn.execute("BEGIN")
            self.logger.debug("事务已开始")
            
            yield conn
            
            # 提交事务
            conn.commit()
            self.logger.debug("事务已提交")
            
        except Exception as e:
            # 回滚事务
            conn.rollback()
            self.logger.error(f"事务回滚: {e}")
            raise
    
    def execute_in_transaction(self, operations: List[Dict[str, Any]]) -> List[Any]:
        """在事务中执行多个操作
        
        Args:
            operations: 操作列表，每个操作包含sql和params
            
        Returns:
            List[Any]: 执行结果列表
            
        Example:
            operations = [
                {'sql': 'INSERT INTO table1 VALUES (?, ?)', 'params': (1, 'test')},
                {'sql': 'UPDATE table2 SET col1 = ? WHERE id = ?', 'params': ('value', 1)}
            ]
        """
        results = []
        
        with self.transaction() as conn:
            cursor = conn.cursor()
            
            try:
                for operation in operations:
                    sql = operation.get('sql')
                    params = operation.get('params', ())
                    
                    if not sql:
                        raise ValueError("操作必须包含SQL语句")
                    
                    cursor.execute(sql, params)
                    
                    # 收集结果
                    if sql.strip().upper().startswith('SELECT'):
                        results.append(cursor.fetchall())
                    elif sql.strip().upper().startswith('INSERT'):
                        results.append(cursor.lastrowid)
                    else:
                        results.append(cursor.rowcount)
                
                return results
                
            finally:
                cursor.close()
    
    def execute_batch_in_transaction(self, sql: str, params_list: List[tuple]) -> int:
        """在事务中执行批量操作
        
        Args:
            sql: SQL语句
            params_list: 参数列表
            
        Returns:
            int: 影响的行数
        """
        with self.transaction() as conn:
            cursor = conn.cursor()
            
            try:
                cursor.executemany(sql, params_list)
                return cursor.rowcount
                
            finally:
                cursor.close()