#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
传统特征参数设置组件

该模块提供传统特征参数的设置界面，包括颜色、纹理和形状特征的参数配置。
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSlider, QPushButton,
    QGroupBox, QGridLayout
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont


class FeatureParamsWidget(QWidget):
    """传统特征参数设置组件"""
    
    # 信号定义
    paramsChanged = pyqtSignal(dict)  # 参数变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        self._connect_signals()
        
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建分组框
        group_box = QGroupBox("传统特征参数")
        group_layout = QVBoxLayout(group_box)
        
        # 颜色特征参数
        self._setup_color_params(group_layout)
        
        # 纹理特征参数
        self._setup_texture_params(group_layout)
        
        # 形状特征参数
        self._setup_shape_params(group_layout)
        
        # 重置按钮
        reset_layout = QHBoxLayout()
        reset_layout.addStretch()
        self.reset_btn = QPushButton("重置所有参数")
        self.reset_btn.setMaximumWidth(120)
        reset_layout.addWidget(self.reset_btn)
        group_layout.addLayout(reset_layout)
        
        layout.addWidget(group_box)
        
    def _setup_color_params(self, layout):
        """设置颜色特征参数"""
        color_layout = QGridLayout()
        
        # 颜色直方图分箱数
        self.color_bins_label = QLabel("颜色直方图分箱数: 32")
        self.color_bins_slider = QSlider(Qt.Orientation.Horizontal)
        self.color_bins_slider.setRange(8, 64)
        self.color_bins_slider.setValue(32)
        self.color_bins_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.color_bins_slider.setTickInterval(8)
        
        color_layout.addWidget(self.color_bins_label, 0, 0)
        color_layout.addWidget(self.color_bins_slider, 0, 1)
        
        # 主要颜色数量
        self.dominant_colors_label = QLabel("主要颜色数量: 5")
        self.dominant_colors_slider = QSlider(Qt.Orientation.Horizontal)
        self.dominant_colors_slider.setRange(3, 10)
        self.dominant_colors_slider.setValue(5)
        self.dominant_colors_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.dominant_colors_slider.setTickInterval(1)
        
        color_layout.addWidget(self.dominant_colors_label, 1, 0)
        color_layout.addWidget(self.dominant_colors_slider, 1, 1)
        
        layout.addLayout(color_layout)
        
    def _setup_texture_params(self, layout):
        """设置纹理特征参数"""
        texture_layout = QGridLayout()
        
        # LBP半径
        self.lbp_radius_label = QLabel("LBP半径: 3")
        self.lbp_radius_slider = QSlider(Qt.Orientation.Horizontal)
        self.lbp_radius_slider.setRange(1, 5)
        self.lbp_radius_slider.setValue(3)
        self.lbp_radius_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.lbp_radius_slider.setTickInterval(1)
        
        texture_layout.addWidget(self.lbp_radius_label, 0, 0)
        texture_layout.addWidget(self.lbp_radius_slider, 0, 1)
        
        # LBP采样点数
        self.lbp_points_label = QLabel("LBP采样点数: 24")
        self.lbp_points_slider = QSlider(Qt.Orientation.Horizontal)
        self.lbp_points_slider.setRange(8, 32)
        self.lbp_points_slider.setValue(24)
        self.lbp_points_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.lbp_points_slider.setTickInterval(8)
        
        texture_layout.addWidget(self.lbp_points_label, 1, 0)
        texture_layout.addWidget(self.lbp_points_slider, 1, 1)
        
        layout.addLayout(texture_layout)
        
    def _setup_shape_params(self, layout):
        """设置形状特征参数"""
        shape_layout = QGridLayout()
        
        # 傅里叶描述子数量
        self.fourier_descriptors_label = QLabel("傅里叶描述子数量: 20")
        self.fourier_descriptors_slider = QSlider(Qt.Orientation.Horizontal)
        self.fourier_descriptors_slider.setRange(10, 50)
        self.fourier_descriptors_slider.setValue(20)
        self.fourier_descriptors_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.fourier_descriptors_slider.setTickInterval(10)
        
        shape_layout.addWidget(self.fourier_descriptors_label, 0, 0)
        shape_layout.addWidget(self.fourier_descriptors_slider, 0, 1)
        
        layout.addLayout(shape_layout)
        
    def _connect_signals(self):
        """连接信号"""
        # 参数变化信号
        self.color_bins_slider.valueChanged.connect(self._update_color_bins_label)
        self.dominant_colors_slider.valueChanged.connect(self._update_dominant_colors_label)
        self.lbp_radius_slider.valueChanged.connect(self._update_lbp_radius_label)
        self.lbp_points_slider.valueChanged.connect(self._update_lbp_points_label)
        self.fourier_descriptors_slider.valueChanged.connect(self._update_fourier_descriptors_label)
        
        # 重置按钮
        self.reset_btn.clicked.connect(self.reset_all_params)
        
        # 参数变化通知
        self.color_bins_slider.valueChanged.connect(self._emit_params_changed)
        self.dominant_colors_slider.valueChanged.connect(self._emit_params_changed)
        self.lbp_radius_slider.valueChanged.connect(self._emit_params_changed)
        self.lbp_points_slider.valueChanged.connect(self._emit_params_changed)
        self.fourier_descriptors_slider.valueChanged.connect(self._emit_params_changed)
        
    def _update_color_bins_label(self, value):
        """更新颜色直方图分箱数标签"""
        self.color_bins_label.setText(f"颜色直方图分箱数: {value}")
        
    def _update_dominant_colors_label(self, value):
        """更新主要颜色数量标签"""
        self.dominant_colors_label.setText(f"主要颜色数量: {value}")
        
    def _update_lbp_radius_label(self, value):
        """更新LBP半径标签"""
        self.lbp_radius_label.setText(f"LBP半径: {value}")
        
    def _update_lbp_points_label(self, value):
        """更新LBP采样点数标签"""
        self.lbp_points_label.setText(f"LBP采样点数: {value}")
        
    def _update_fourier_descriptors_label(self, value):
        """更新傅里叶描述子数量标签"""
        self.fourier_descriptors_label.setText(f"傅里叶描述子数量: {value}")
        
    def _emit_params_changed(self):
        """发送参数变化信号"""
        params = self.get_feature_params()
        self.paramsChanged.emit(params)
        
    def get_feature_params(self):
        """获取特征参数
        
        Returns:
            dict: 特征参数字典
        """
        return self.get_params()
        
    def get_params(self):
        """获取特征参数（标准化格式）
        
        Returns:
            dict: 特征参数字典，键名符合搜索引擎要求
        """
        return {
            'extract_color': True,  # 默认启用颜色特征
            'extract_texture': True,  # 默认启用纹理特征
            'extract_shape': True,  # 默认启用形状特征
            'hist_bins': self.color_bins_slider.value(),
            'n_dominant_colors': self.dominant_colors_slider.value(),
            'lbp_radius': self.lbp_radius_slider.value(),
            'lbp_n_points': self.lbp_points_slider.value(),
            'n_fourier_descriptors': self.fourier_descriptors_slider.value()
        }
        
    def set_feature_params(self, params):
        """设置特征参数
        
        Args:
            params (dict): 特征参数字典
        """
        if 'color_histogram_bins' in params:
            self.color_bins_slider.setValue(params['color_histogram_bins'])
        if 'dominant_colors_count' in params:
            self.dominant_colors_slider.setValue(params['dominant_colors_count'])
        if 'lbp_radius' in params:
            self.lbp_radius_slider.setValue(params['lbp_radius'])
        if 'lbp_points' in params:
            self.lbp_points_slider.setValue(params['lbp_points'])
        if 'fourier_descriptors_count' in params:
            self.fourier_descriptors_slider.setValue(params['fourier_descriptors_count'])
            
    def reset_all_params(self):
        """重置所有参数到默认值"""
        self.reset_params()
        
    def reset_params(self):
        """重置参数到默认值"""
        self.color_bins_slider.setValue(32)
        self.dominant_colors_slider.setValue(5)
        self.lbp_radius_slider.setValue(3)
        self.lbp_points_slider.setValue(24)
        self.fourier_descriptors_slider.setValue(20)