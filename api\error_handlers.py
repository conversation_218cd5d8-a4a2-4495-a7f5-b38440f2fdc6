#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API错误处理模块

提供统一的错误处理和响应格式。
"""

import traceback
from typing import Dict, Any, Tuple
from flask import Flask, jsonify, request, g
from werkzeug.exceptions import (
    HTTPException, BadRequest, Unauthorized, Forbidden, 
    NotFound, MethodNotAllowed, TooManyRequests, InternalServerError
)
import logging

from config.config_manager import get_config
from utils.logger_mixin import LoggerMixin


class ErrorHandler(LoggerMixin):
    """错误处理器"""
    
    def __init__(self):
        super().__init__()
        self.config = get_config()
    
    def format_error_response(self, error: Exception, status_code: int = 500) -> Tuple[Dict[str, Any], int]:
        """格式化错误响应
        
        Args:
            error: 错误对象
            status_code: HTTP状态码
            
        Returns:
            Tuple[Dict[str, Any], int]: (响应数据, 状态码)
        """
        # 基本错误信息
        error_response = {
            'success': False,
            'error': {
                'type': error.__class__.__name__,
                'message': str(error),
                'status_code': status_code
            },
            'timestamp': self._get_timestamp(),
            'path': request.path,
            'method': request.method
        }
        
        # 添加请求ID（如果存在）
        if hasattr(g, 'request_id'):
            error_response['request_id'] = g.request_id
        
        # 在调试模式下添加详细信息
        if self.config.debug:
            error_response['error']['traceback'] = traceback.format_exc()
            error_response['error']['request_data'] = self._get_request_data()
        
        # 记录错误日志
        self._log_error(error, status_code, error_response)
        
        return error_response, status_code
    
    def _get_timestamp(self) -> str:
        """获取时间戳
        
        Returns:
            str: ISO格式时间戳
        """
        from datetime import datetime
        return datetime.utcnow().isoformat() + 'Z'
    
    def _get_request_data(self) -> Dict[str, Any]:
        """获取请求数据（调试用）
        
        Returns:
            Dict[str, Any]: 请求数据
        """
        try:
            data = {
                'args': dict(request.args),
                'headers': dict(request.headers),
                'remote_addr': request.remote_addr,
                'user_agent': request.headers.get('User-Agent')
            }
            
            # 添加JSON数据（如果存在）
            if request.is_json:
                try:
                    data['json'] = request.get_json()
                except (ValueError, TypeError, UnicodeDecodeError) as e:
                    data['json'] = f'Invalid JSON: {str(e)}'
            
            # 添加表单数据（如果存在）
            if request.form:
                data['form'] = dict(request.form)
            
            return data
            
        except Exception as e:
            return {'error': f'Failed to get request data: {str(e)}'}
    
    def _log_error(self, error: Exception, status_code: int, error_response: Dict[str, Any]):
        """记录错误日志
        
        Args:
            error: 错误对象
            status_code: HTTP状态码
            error_response: 错误响应数据
        """
        # 根据状态码选择日志级别
        if status_code >= 500:
            log_level = logging.ERROR
        elif status_code >= 400:
            log_level = logging.WARNING
        else:
            log_level = logging.INFO
        
        # 构建日志消息
        log_message = (
            f"HTTP {status_code} - {error.__class__.__name__}: {str(error)} "
            f"[{request.method} {request.path}]"
        )
        
        # 添加请求ID
        if hasattr(g, 'request_id'):
            log_message = f"[{g.request_id}] {log_message}"
        
        # 记录日志
        self.logger.log(log_level, log_message)
        
        # 在调试模式下记录详细信息
        if self.config.debug and status_code >= 500:
            self.logger.debug(f"错误详情: {traceback.format_exc()}")


# 全局错误处理器实例
error_handler = ErrorHandler()


def register_error_handlers(app: Flask):
    """注册错误处理器
    
    Args:
        app: Flask应用实例
    """
    
    @app.errorhandler(400)
    def handle_bad_request(error: BadRequest):
        """处理400错误"""
        return jsonify(*error_handler.format_error_response(error, 400))
    
    @app.errorhandler(401)
    def handle_unauthorized(error: Unauthorized):
        """处理401错误"""
        return jsonify(*error_handler.format_error_response(error, 401))
    
    @app.errorhandler(403)
    def handle_forbidden(error: Forbidden):
        """处理403错误"""
        return jsonify(*error_handler.format_error_response(error, 403))
    
    @app.errorhandler(404)
    def handle_not_found(error: NotFound):
        """处理404错误"""
        return jsonify(*error_handler.format_error_response(error, 404))
    
    @app.errorhandler(405)
    def handle_method_not_allowed(error: MethodNotAllowed):
        """处理405错误"""
        return jsonify(*error_handler.format_error_response(error, 405))
    
    @app.errorhandler(413)
    def handle_payload_too_large(error):
        """处理413错误（文件过大）"""
        custom_error = Exception("上传文件过大，请选择较小的文件")
        return jsonify(*error_handler.format_error_response(custom_error, 413))
    
    @app.errorhandler(429)
    def handle_too_many_requests(error: TooManyRequests):
        """处理429错误"""
        return jsonify(*error_handler.format_error_response(error, 429))
    
    @app.errorhandler(500)
    def handle_internal_server_error(error: InternalServerError):
        """处理500错误"""
        return jsonify(*error_handler.format_error_response(error, 500))
    
    @app.errorhandler(HTTPException)
    def handle_http_exception(error: HTTPException):
        """处理其他HTTP异常"""
        return jsonify(*error_handler.format_error_response(error, error.code))
    
    @app.errorhandler(Exception)
    def handle_generic_exception(error: Exception):
        """处理通用异常"""
        # 检查是否为已知的应用异常
        if hasattr(error, 'status_code'):
            status_code = error.status_code
        else:
            status_code = 500
        
        return jsonify(*error_handler.format_error_response(error, status_code))
    
    # 自定义异常处理
    @app.errorhandler(ValueError)
    def handle_value_error(error: ValueError):
        """处理值错误"""
        return jsonify(*error_handler.format_error_response(error, 400))
    
    @app.errorhandler(FileNotFoundError)
    def handle_file_not_found_error(error: FileNotFoundError):
        """处理文件未找到错误"""
        custom_error = Exception("请求的文件不存在")
        return jsonify(*error_handler.format_error_response(custom_error, 404))
    
    @app.errorhandler(PermissionError)
    def handle_permission_error(error: PermissionError):
        """处理权限错误"""
        custom_error = Exception("没有权限访问请求的资源")
        return jsonify(*error_handler.format_error_response(custom_error, 403))
    
    @app.errorhandler(TimeoutError)
    def handle_timeout_error(error: TimeoutError):
        """处理超时错误"""
        custom_error = Exception("请求超时，请稍后重试")
        return jsonify(*error_handler.format_error_response(custom_error, 504))


class APIException(Exception):
    """API自定义异常基类"""
    
    def __init__(self, message: str, status_code: int = 500, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.error_code = error_code or self.__class__.__name__


class ValidationError(APIException):
    """验证错误"""
    
    def __init__(self, message: str, field: str = None):
        super().__init__(message, 400, 'VALIDATION_ERROR')
        self.field = field


class AuthenticationError(APIException):
    """认证错误"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(message, 401, 'AUTHENTICATION_ERROR')


class AuthorizationError(APIException):
    """授权错误"""
    
    def __init__(self, message: str = "没有权限执行此操作"):
        super().__init__(message, 403, 'AUTHORIZATION_ERROR')


class ResourceNotFoundError(APIException):
    """资源未找到错误"""
    
    def __init__(self, message: str = "请求的资源不存在", resource_type: str = None):
        super().__init__(message, 404, 'RESOURCE_NOT_FOUND')
        self.resource_type = resource_type


class ConflictError(APIException):
    """冲突错误"""
    
    def __init__(self, message: str = "资源冲突"):
        super().__init__(message, 409, 'CONFLICT_ERROR')


class RateLimitError(APIException):
    """速率限制错误"""
    
    def __init__(self, message: str = "请求过于频繁", retry_after: int = None):
        super().__init__(message, 429, 'RATE_LIMIT_ERROR')
        self.retry_after = retry_after


class ProcessingError(APIException):
    """处理错误"""
    
    def __init__(self, message: str = "处理请求时发生错误", details: str = None):
        super().__init__(message, 500, 'PROCESSING_ERROR')
        self.details = details


class ExternalServiceError(APIException):
    """外部服务错误"""
    
    def __init__(self, message: str = "外部服务不可用", service_name: str = None):
        super().__init__(message, 502, 'EXTERNAL_SERVICE_ERROR')
        self.service_name = service_name


class ServiceUnavailableError(APIException):
    """服务不可用错误"""
    
    def __init__(self, message: str = "服务暂时不可用", retry_after: int = None):
        super().__init__(message, 503, 'SERVICE_UNAVAILABLE')
        self.retry_after = retry_after


def register_custom_error_handlers(app: Flask):
    """注册自定义异常处理器
    
    Args:
        app: Flask应用实例
    """
    
    @app.errorhandler(APIException)
    def handle_api_exception(error: APIException):
        """处理API自定义异常"""
        response_data = {
            'success': False,
            'error': {
                'type': error.error_code,
                'message': error.message,
                'status_code': error.status_code
            },
            'timestamp': error_handler._get_timestamp(),
            'path': request.path,
            'method': request.method
        }
        
        # 添加请求ID
        if hasattr(g, 'request_id'):
            response_data['request_id'] = g.request_id
        
        # 添加特定异常的额外信息
        if isinstance(error, ValidationError) and error.field:
            response_data['error']['field'] = error.field
        elif isinstance(error, ResourceNotFoundError) and error.resource_type:
            response_data['error']['resource_type'] = error.resource_type
        elif isinstance(error, RateLimitError) and error.retry_after:
            response_data['error']['retry_after'] = error.retry_after
        elif isinstance(error, ProcessingError) and error.details:
            response_data['error']['details'] = error.details
        elif isinstance(error, ExternalServiceError) and error.service_name:
            response_data['error']['service_name'] = error.service_name
        elif isinstance(error, ServiceUnavailableError) and error.retry_after:
            response_data['error']['retry_after'] = error.retry_after
        
        # 记录错误日志
        error_handler._log_error(error, error.status_code, response_data)
        
        response = jsonify(response_data)
        response.status_code = error.status_code
        
        # 添加特定头部
        if isinstance(error, RateLimitError) and error.retry_after:
            response.headers['Retry-After'] = str(error.retry_after)
        elif isinstance(error, ServiceUnavailableError) and error.retry_after:
            response.headers['Retry-After'] = str(error.retry_after)
        
        return response


def success_response(
    data: Any = None, 
    message: str = None, 
    status_code: int = 200, 
    **kwargs
) -> Tuple[Dict[str, Any], int]:
    """创建成功响应
    
    Args:
        data: 响应数据
        message: 响应消息
        status_code: HTTP状态码
        **kwargs: 额外的响应字段
        
    Returns:
        Tuple[Dict[str, Any], int]: (响应数据, 状态码)
    """
    response_data = {
        'success': True,
        'timestamp': error_handler._get_timestamp(),
        'path': request.path,
        'method': request.method
    }
    
    if data is not None:
        response_data['data'] = data
    
    if message:
        response_data['message'] = message
    
    # 添加请求ID
    if hasattr(g, 'request_id'):
        response_data['request_id'] = g.request_id
    
    # 添加额外字段
    response_data.update(kwargs)
    
    return response_data, status_code


def paginated_response(items: list, page: int, per_page: int, total: int, **kwargs) -> Tuple[Dict[str, Any], int]:
    """创建分页响应
    
    Args:
        items: 数据项列表
        page: 当前页码
        per_page: 每页数量
        total: 总数量
        **kwargs: 额外的响应字段
        
    Returns:
        Tuple[Dict[str, Any], int]: (响应数据, 状态码)
    """
    import math
    
    total_pages = math.ceil(total / per_page) if per_page > 0 else 0
    
    pagination_info = {
        'page': page,
        'per_page': per_page,
        'total': total,
        'total_pages': total_pages,
        'has_prev': page > 1,
        'has_next': page < total_pages
    }
    
    return success_response(
        data={
            'items': items,
            'pagination': pagination_info
        },
        **kwargs
    )