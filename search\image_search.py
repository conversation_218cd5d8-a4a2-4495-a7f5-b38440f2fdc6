#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像搜索引擎模块

提供基于特征的图像搜索功能，支持多种搜索算法和相似度计算方法。
包含特征提取、索引构建、相似度计算和搜索结果排序等功能。
"""

import os
import time
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime
import pickle
import json

try:
    import torch
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False

# 使用绝对导入替代相对导入
from utils.logger_mixin import LoggerMixin
from utils.gpu_utils import get_optimal_device
from features.feature_extractor import FeatureExtractor
from database.fabric_repository import FabricRepository
from database.models import FabricImage


@dataclass
class SearchConfig:
    """搜索配置"""
    # 搜索参数
    top_k: int = 10
    similarity_threshold: float = 0.7
    search_algorithm: str = 'cosine'  # 'cosine', 'euclidean', 'faiss'
    
    # 特征处理
    normalize_features: bool = True
    feature_dimension: int = 512
    
    # 索引设置
    use_index: bool = True
    index_type: str = 'flat'  # 'flat', 'ivf', 'hnsw'
    rebuild_index: bool = False
    
    # 过滤设置
    filter_duplicates: bool = True
    min_image_size: Tuple[int, int] = (64, 64)
    max_image_size: Tuple[int, int] = (4096, 4096)
    
    # 性能优化
    use_gpu: bool = True
    batch_size: int = 32
    cache_features: bool = True


@dataclass
class SearchResult:
    """搜索结果"""
    image_id: str
    image_path: str
    similarity_score: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    features: Optional[np.ndarray] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'image_id': self.image_id,
            'image_path': self.image_path,
            'similarity_score': self.similarity_score,
            'metadata': self.metadata
        }


class FeatureIndex(LoggerMixin):
    """特征索引管理器"""
    
    def __init__(self, config: SearchConfig):
        super().__init__()
        self.config = config
        self.index = None
        self.image_ids = []
        self.features_cache = {}
        self.is_built = False
        
    def build_index(self, features: np.ndarray, image_ids: List[str]):
        """构建索引"""
        self.logger.info(f"开始构建特征索引，特征数量: {len(features)}")
        
        if not FAISS_AVAILABLE and self.config.search_algorithm == 'faiss':
            self.logger.warning("FAISS不可用，切换到余弦相似度搜索")
            self.config.search_algorithm = 'cosine'
            
        # 标准化特征
        if self.config.normalize_features:
            features = self._normalize_features(features)
            
        self.image_ids = image_ids.copy()
        
        if self.config.search_algorithm == 'faiss' and FAISS_AVAILABLE:
            self._build_faiss_index(features)
        else:
            # 使用简单的内存索引
            self.index = features.copy()
            
        self.is_built = True
        self.logger.info("特征索引构建完成")
        
    def _build_faiss_index(self, features: np.ndarray):
        """构建FAISS索引"""
        dimension = features.shape[1]
        
        if self.config.index_type == 'flat':
            # 平坦索引，精确搜索
            self.index = faiss.IndexFlatIP(dimension)  # 内积索引
        elif self.config.index_type == 'ivf':
            # IVF索引，近似搜索
            nlist = min(100, len(features) // 10)  # 聚类中心数量
            quantizer = faiss.IndexFlatIP(dimension)
            self.index = faiss.IndexIVFFlat(quantizer, dimension, nlist)
            self.index.train(features.astype(np.float32))
        elif self.config.index_type == 'hnsw':
            # HNSW索引，图搜索
            self.index = faiss.IndexHNSWFlat(dimension, 32)
            
        # 添加特征到索引
        self.index.add(features.astype(np.float32))
        
    def search(self, query_features: np.ndarray, top_k: int = None) -> List[Tuple[int, float]]:
        """搜索相似特征"""
        if not self.is_built:
            raise ValueError("索引尚未构建")
            
        top_k = top_k or self.config.top_k
        
        # 标准化查询特征
        if self.config.normalize_features:
            query_features = self._normalize_features(query_features)
            
        if self.config.search_algorithm == 'faiss' and FAISS_AVAILABLE:
            return self._search_faiss(query_features, top_k)
        else:
            return self._search_simple(query_features, top_k)
            
    def _search_faiss(self, query_features: np.ndarray, top_k: int) -> List[Tuple[int, float]]:
        """使用FAISS搜索"""
        if query_features.ndim == 1:
            query_features = query_features.reshape(1, -1)
            
        scores, indices = self.index.search(query_features.astype(np.float32), top_k)
        
        results = []
        for i, (idx, score) in enumerate(zip(indices[0], scores[0])):
            if idx != -1:  # FAISS返回-1表示无效结果
                results.append((idx, float(score)))
                
        return results
        
    def _search_simple(self, query_features: np.ndarray, top_k: int) -> List[Tuple[int, float]]:
        """简单搜索实现"""
        if query_features.ndim == 1:
            query_features = query_features.reshape(1, -1)
            
        # 计算相似度
        if self.config.search_algorithm == 'cosine':
            similarities = np.dot(self.index, query_features.T).flatten()
        elif self.config.search_algorithm == 'euclidean':
            # 欧几里得距离（转换为相似度）
            distances = np.linalg.norm(self.index - query_features, axis=1)
            similarities = 1.0 / (1.0 + distances)
        else:
            raise ValueError(f"不支持的搜索算法: {self.config.search_algorithm}")
            
        # 获取top-k结果
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            score = similarities[idx]
            if score >= self.config.similarity_threshold:
                results.append((int(idx), float(score)))
                
        return results
        
    def _normalize_features(self, features: np.ndarray) -> np.ndarray:
        """标准化特征"""
        if TORCH_AVAILABLE:
            # 使用PyTorch进行L2标准化
            features_tensor = torch.from_numpy(features)
            normalized = F.normalize(features_tensor, p=2, dim=-1)
            return normalized.numpy()
        else:
            # 使用NumPy进行L2标准化
            norms = np.linalg.norm(features, axis=-1, keepdims=True)
            norms = np.where(norms == 0, 1, norms)  # 避免除零
            return features / norms
            
    def save_index(self, filepath: str):
        """保存索引"""
        if not self.is_built:
            raise ValueError("索引尚未构建")
            
        index_data = {
            'config': self.config,
            'image_ids': self.image_ids,
            'is_built': self.is_built
        }
        
        if self.config.search_algorithm == 'faiss' and FAISS_AVAILABLE:
            # 保存FAISS索引
            faiss.write_index(self.index, f"{filepath}.faiss")
        else:
            # 保存简单索引
            index_data['features'] = self.index
            
        # 保存元数据
        with open(f"{filepath}.meta", 'wb') as f:
            pickle.dump(index_data, f)
            
        self.logger.info(f"索引已保存到: {filepath}")
        
    def load_index(self, filepath: str):
        """加载索引"""
        # 加载元数据
        with open(f"{filepath}.meta", 'rb') as f:
            index_data = pickle.load(f)
            
        self.config = index_data['config']
        self.image_ids = index_data['image_ids']
        self.is_built = index_data['is_built']
        
        if self.config.search_algorithm == 'faiss' and FAISS_AVAILABLE:
            # 加载FAISS索引
            self.index = faiss.read_index(f"{filepath}.faiss")
        else:
            # 加载简单索引
            self.index = index_data['features']
            
        self.logger.info(f"索引已从 {filepath} 加载")


class ImageSearchEngine(LoggerMixin):
    """图像搜索引擎"""
    
    def __init__(self, 
                 feature_extractor: FeatureExtractor = None,
                 fabric_repository: FabricRepository = None,
                 config: SearchConfig = None):
        super().__init__()
        
        self.feature_extractor = feature_extractor or FeatureExtractor()
        self.fabric_repository = fabric_repository or FabricRepository()
        self.config = config or SearchConfig()
        
        self.feature_index = FeatureIndex(self.config)
        self.device = get_optimal_device() if self.config.use_gpu else 'cpu'
        
        # 缓存
        self._features_cache = {}
        self._last_index_update = None
        
    def build_search_index(self, force_rebuild: bool = False):
        """构建搜索索引"""
        if (self.feature_index.is_built and not force_rebuild and 
            not self.config.rebuild_index):
            self.logger.info("搜索索引已存在，跳过构建")
            return
            
        self.logger.info("开始构建搜索索引")
        
        # 从数据库获取所有图像特征
        all_images = self.fabric_repository.get_all_images()
        
        if not all_images:
            self.logger.warning("数据库中没有图像数据")
            return
            
        # 提取特征和ID
        features_list = []
        image_ids = []
        
        for image in all_images:
            if image.features is not None:
                features_list.append(image.features)
                image_ids.append(image.id)
                
        if not features_list:
            self.logger.warning("没有可用的特征数据")
            return
            
        # 转换为numpy数组
        features_array = np.array(features_list)
        
        # 构建索引
        self.feature_index.build_index(features_array, image_ids)
        self._last_index_update = datetime.now()
        
        self.logger.info(f"搜索索引构建完成，包含 {len(image_ids)} 个图像")
        
    def search(self, 
               query_image: Union[str, np.ndarray],
               top_k: int = None,
               similarity_threshold: float = None,
               filters: Dict[str, Any] = None) -> List[SearchResult]:
        """搜索相似图像
        
        Args:
            query_image: 查询图像路径或特征向量
            top_k: 返回结果数量
            similarity_threshold: 相似度阈值
            filters: 过滤条件
            
        Returns:
            List[SearchResult]: 搜索结果列表
        """
        # 确保索引已构建
        if not self.feature_index.is_built:
            self.build_search_index()
            
        # 提取查询特征
        if isinstance(query_image, str):
            query_features = self.extract_features(query_image)
        else:
            query_features = query_image
            
        if query_features is None:
            self.logger.error("无法提取查询图像特征")
            return []
            
        # 设置搜索参数
        top_k = top_k or self.config.top_k
        similarity_threshold = similarity_threshold or self.config.similarity_threshold
        
        # 执行搜索
        search_results = self.feature_index.search(query_features, top_k)
        
        # 转换为SearchResult对象
        results = []
        for idx, score in search_results:
            if score >= similarity_threshold:
                image_id = self.feature_index.image_ids[idx]
                image = self.fabric_repository.get_by_id(image_id)
                
                if image and self._apply_filters(image, filters):
                    result = SearchResult(
                        image_id=image_id,
                        image_path=image.file_path,
                        similarity_score=score,
                        metadata={
                            'file_name': image.file_name,
                            'file_size': image.file_size,
                            'width': image.width,
                            'height': image.height,
                            'format': image.format,
                            'indexed_at': image.indexed_at.isoformat() if image.indexed_at else None
                        }
                    )
                    results.append(result)
                    
        # 去重处理
        if self.config.filter_duplicates:
            results = self._filter_duplicates(results)
            
        self.logger.info(f"搜索完成，返回 {len(results)} 个结果")
        return results
        
    def search_batch(self, 
                    query_images: List[Union[str, np.ndarray]],
                    **kwargs) -> List[List[SearchResult]]:
        """批量搜索"""
        results = []
        
        for query_image in query_images:
            try:
                search_results = self.search(query_image, **kwargs)
                results.append(search_results)
            except Exception as e:
                self.logger.error(f"批量搜索失败: {e}")
                results.append([])
                
        return results
        
    def extract_features(self, image_path: str) -> Optional[np.ndarray]:
        """提取图像特征"""
        # 检查缓存
        if self.config.cache_features and image_path in self._features_cache:
            return self._features_cache[image_path]
            
        try:
            # 使用特征提取器提取特征
            result = self.feature_extractor.extract_features(image_path)
            
            if result.success:
                features = result.features
                
                # 缓存特征
                if self.config.cache_features:
                    self._features_cache[image_path] = features
                    
                return features
            else:
                self.logger.error(f"特征提取失败: {result.error}")
                return None
                
        except Exception as e:
            self.logger.error(f"提取特征时发生异常: {e}")
            return None
            
    def _apply_filters(self, image: FabricImage, filters: Dict[str, Any]) -> bool:
        """应用过滤条件"""
        if not filters:
            return True
            
        # 尺寸过滤
        if 'min_size' in filters:
            min_w, min_h = filters['min_size']
            if image.width < min_w or image.height < min_h:
                return False
                
        if 'max_size' in filters:
            max_w, max_h = filters['max_size']
            if image.width > max_w or image.height > max_h:
                return False
                
        # 格式过滤
        if 'formats' in filters:
            if image.format not in filters['formats']:
                return False
                
        # 文件大小过滤
        if 'min_file_size' in filters:
            if image.file_size < filters['min_file_size']:
                return False
                
        if 'max_file_size' in filters:
            if image.file_size > filters['max_file_size']:
                return False
                
        return True
        
    def _filter_duplicates(self, results: List[SearchResult]) -> List[SearchResult]:
        """过滤重复结果"""
        seen_paths = set()
        filtered_results = []
        
        for result in results:
            if result.image_path not in seen_paths:
                seen_paths.add(result.image_path)
                filtered_results.append(result)
                
        return filtered_results
        
    def get_similar_images(self, 
                          image_id: str, 
                          top_k: int = None) -> List[SearchResult]:
        """获取与指定图像相似的图像"""
        # 获取图像信息
        image = self.fabric_repository.get_by_id(image_id)
        if not image or image.features is None:
            self.logger.error(f"图像不存在或没有特征: {image_id}")
            return []
            
        # 使用图像特征进行搜索
        return self.search(image.features, top_k=top_k)
        
    def update_index(self, image_ids: List[str] = None):
        """更新索引"""
        if image_ids:
            # 增量更新（简化实现，实际可能需要重建）
            self.logger.info(f"增量更新索引，图像数量: {len(image_ids)}")
            self.build_search_index(force_rebuild=True)
        else:
            # 全量更新
            self.build_search_index(force_rebuild=True)
            
    def save_index(self, filepath: str):
        """保存索引到文件"""
        self.feature_index.save_index(filepath)
        
    def load_index(self, filepath: str):
        """从文件加载索引"""
        self.feature_index.load_index(filepath)
        
    def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        return {
            'is_built': self.feature_index.is_built,
            'total_images': len(self.feature_index.image_ids),
            'last_update': self._last_index_update.isoformat() if self._last_index_update else None,
            'config': {
                'search_algorithm': self.config.search_algorithm,
                'index_type': self.config.index_type,
                'feature_dimension': self.config.feature_dimension,
                'use_gpu': self.config.use_gpu
            }
        }
        
    def clear_cache(self):
        """清空缓存"""
        self._features_cache.clear()
        self.logger.info("特征缓存已清空")


# 全局搜索引擎实例
_search_engine = None


def get_search_engine(config: SearchConfig = None) -> ImageSearchEngine:
    """获取全局搜索引擎实例"""
    global _search_engine
    if _search_engine is None:
        _search_engine = ImageSearchEngine(config=config)
    return _search_engine


def search_similar_images(query_image: Union[str, np.ndarray], 
                         top_k: int = 10,
                         **kwargs) -> List[SearchResult]:
    """搜索相似图像的便捷函数"""
    engine = get_search_engine()
    return engine.search(query_image, top_k=top_k, **kwargs)


def build_search_index(force_rebuild: bool = False):
    """构建搜索索引的便捷函数"""
    engine = get_search_engine()
    engine.build_search_index(force_rebuild=force_rebuild)