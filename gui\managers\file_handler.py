#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件操作处理器

该模块负责处理文件和文件夹相关的操作。
"""

import os
from typing import Optional, List, Dict, Any
from pathlib import Path
from PyQt6.QtWidgets import (
    QFileDialog, QMessageBox, QMainWindow, QProgressDialog
)
from PyQt6.QtCore import QObject, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QPixmap

from utils.logger_mixin import LoggerMixin
from utils.file_utils import is_image_file, FileUtils
from database.database_manager import DatabaseManager


class FileHandler(QObject, LoggerMixin):
    """文件操作处理器"""
    
    # 信号
    imageOpened = pyqtSignal(str)  # 图像路径
    folderOpened = pyqtSignal(str)  # 文件夹路径
    databaseImported = pyqtSignal(str)  # 数据库路径
    resultsExported = pyqtSignal(str)  # 导出路径
    operationError = pyqtSignal(str)  # 操作错误
    importProgress = pyqtSignal(int, str)  # 导入进度
    
    def __init__(self, main_window: QMainWindow):
        super().__init__()
        self.main_window = main_window
        self.database_manager: Optional[DatabaseManager] = None
        self.import_worker = None  # ImportWorker 实例
        self.import_thread: Optional[QThread] = None
        self.progress_dialog: Optional[QProgressDialog] = None
        
        # 支持的图像格式
        self.supported_formats = {
            '常见格式': ['jpg', 'jpeg', 'png', 'gif', 'bmp'],
            '高级格式': ['tiff', 'tif', 'webp', 'svg', 'ico']
        }
        
    def set_database_manager(self, database_manager: DatabaseManager):
        """设置数据库管理器
        
        Args:
            database_manager: 数据库管理器实例
        """
        self.database_manager = database_manager
    
    def open_image(self):
        """打开图像文件"""
        try:
            # 构建文件过滤器
            filters = self._build_image_filters()
            
            file_path, _ = QFileDialog.getOpenFileName(
                self.main_window,
                "选择图像文件",
                "",
                filters
            )
            
            if file_path:
                if self._validate_image_file(file_path):
                    self.imageOpened.emit(file_path)
                    self.logger.info(f"打开图像文件: {file_path}")
                else:
                    self.operationError.emit("不支持的图像格式")
                    
        except Exception as e:
            self.logger.error(f"打开图像文件失败: {e}")
            self.operationError.emit(f"打开图像文件失败: {e}")
    
    def open_folder(self):
        """打开文件夹"""
        try:
            folder_path = QFileDialog.getExistingDirectory(
                self.main_window,
                "选择图像文件夹"
            )
            
            if folder_path:
                # 获取任务管理器
                task_manager = getattr(self.main_window, 'task_manager', None)
                current_task_id = None
                
                # 创建任务
                if task_manager:
                    task_name = f"打开文件夹 - {os.path.basename(folder_path)}"
                    task_metadata = {
                        'type': 'folder_open',
                        'folder_path': folder_path
                    }
                    current_task_id = task_manager.create_task(task_name, task_metadata)
                    task_manager.update_task_status(current_task_id, "running")
                    task_manager.update_task_progress(current_task_id, 0.3, "正在验证文件夹...")
                
                # 更新窗口状态管理器的进度条
                window_state_manager = getattr(self.main_window, 'window_state_manager', None)
                if window_state_manager:
                    window_state_manager.show_progress(30, 100, "正在验证文件夹...")
                
                # 验证文件夹
                if self._validate_image_folder(folder_path):
                    # 更新任务进度
                    if task_manager and current_task_id:
                        task_manager.update_task_progress(current_task_id, 0.7, "正在加载文件夹...")
                    
                    # 更新进度条
                    if window_state_manager:
                        window_state_manager.update_progress(70, "正在加载文件夹...")
                    
                    # 发送文件夹打开信号
                    self.folderOpened.emit(folder_path)
                    self.logger.info(f"打开文件夹: {folder_path}")
                    
                    # 完成任务
                    if task_manager and current_task_id:
                        task_manager.complete_task(current_task_id, {"folder_path": folder_path})
                    
                    # 隐藏进度条
                    if window_state_manager:
                        window_state_manager.hide_progress()
                else:
                    # 任务失败
                    if task_manager and current_task_id:
                        task_manager.complete_task(current_task_id, error="文件夹中没有找到支持的图像文件")
                    
                    # 隐藏进度条
                    if window_state_manager:
                        window_state_manager.hide_progress()
                    
                    self.operationError.emit("文件夹中没有找到支持的图像文件")
                    
        except Exception as e:
            # 获取任务管理器和窗口状态管理器
            task_manager = getattr(self.main_window, 'task_manager', None)
            window_state_manager = getattr(self.main_window, 'window_state_manager', None)
            
            # 任务失败
            if task_manager and 'current_task_id' in locals() and current_task_id:
                task_manager.complete_task(current_task_id, error=str(e))
            
            # 隐藏进度条
            if window_state_manager:
                window_state_manager.hide_progress()
            
            self.logger.error(f"打开文件夹失败: {e}")
            self.operationError.emit(f"打开文件夹失败: {e}")
    
    def import_database(self):
        """导入数据库"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self.main_window,
                "选择数据库文件",
                "",
                "数据库文件 (*.db *.sqlite *.sqlite3);;所有文件 (*)"
            )
            
            if file_path:
                if self._validate_database_file(file_path):
                    self._start_database_import(file_path)
                else:
                    self.operationError.emit("无效的数据库文件")
                    
        except Exception as e:
            self.logger.error(f"导入数据库失败: {e}")
            self.operationError.emit(f"导入数据库失败: {e}")
    
    def export_results(self, results: List[Dict[str, Any]]):
        """导出搜索结果
        
        Args:
            results: 搜索结果列表
        """
        if not results:
            self.operationError.emit("没有可导出的搜索结果")
            return
        
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self.main_window,
                "导出搜索结果",
                "search_results.csv",
                "CSV文件 (*.csv);;JSON文件 (*.json);;所有文件 (*)"
            )
            
            if file_path:
                self._export_results_to_file(results, file_path)
                
        except Exception as e:
            self.logger.error(f"导出搜索结果失败: {e}")
            self.operationError.emit(f"导出搜索结果失败: {e}")
    
    def _build_image_filters(self) -> str:
        """构建图像文件过滤器
        
        Returns:
            str: 文件过滤器字符串
        """
        filters = []
        
        # 所有支持的图像格式
        all_formats = [ext.lstrip('.') for ext in FileUtils.IMAGE_EXTENSIONS]
        
        if all_formats:
            all_pattern = " ".join([f"*.{fmt}" for fmt in all_formats])
            filters.append(f"图像文件 ({all_pattern})")
        
        # 按类型分组
        for format_type, formats in self.supported_formats.items():
            if formats:
                pattern = " ".join([f"*.{fmt}" for fmt in formats])
                filters.append(f"{format_type} ({pattern})")
        
        filters.append("所有文件 (*)")
        
        return ";;".join(filters)
    
    def _validate_image_file(self, file_path: str) -> bool:
        """验证图像文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否为有效的图像文件
        """
        if not os.path.isfile(file_path):
            return False
        
        return is_image_file(file_path)
    
    def _validate_image_folder(self, folder_path: str) -> bool:
        """验证图像文件夹
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            bool: 文件夹中是否包含图像文件
        """
        if not os.path.isdir(folder_path):
            return False
        
        # 检查文件夹中是否有图像文件
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                if is_image_file(file_path):
                    return True
        
        return False
    
    def _validate_database_file(self, file_path: str) -> bool:
        """验证数据库文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否为有效的数据库文件
        """
        if not os.path.isfile(file_path):
            return False
        
        # 检查文件扩展名
        ext = Path(file_path).suffix.lower()
        return ext in ['.db', '.sqlite', '.sqlite3']
    
    def _start_database_import(self, file_path: str):
        """开始数据库导入
        
        Args:
            file_path: 数据库文件路径
        """
        if not self.database_manager:
            self.operationError.emit("数据库管理器未初始化")
            return
        
        try:
            # 创建进度对话框
            self.progress_dialog = QProgressDialog(
                "正在导入数据库...", "取消", 0, 100, self.main_window
            )
            self.progress_dialog.setWindowTitle("导入数据库")
            self.progress_dialog.setModal(True)
            self.progress_dialog.show()
            
            # 实现数据库导入功能
            self._import_database_file(file_path)
            
        except Exception as e:
            self.logger.error(f"启动数据库导入失败: {e}")
            self.operationError.emit(f"启动数据库导入失败: {e}")
            if self.progress_dialog:
                self.progress_dialog.close()
    
    def _import_database_file(self, file_path: str):
        """导入数据库文件
        
        Args:
            file_path: 数据库文件路径
        """
        try:
            # 模拟导入过程
            import time
            for i in range(101):
                if self.progress_dialog and self.progress_dialog.wasCanceled():
                    self._cancel_import()
                    return
                
                if self.progress_dialog:
                    self.progress_dialog.setValue(i)
                    self.progress_dialog.setLabelText(f"导入进度: {i}%")
                
                time.sleep(0.01)  # 模拟处理时间
            
            # 导入完成
            self._on_import_finished(file_path)
            
        except Exception as e:
            self._on_import_error(str(e))
    
    def _export_results_to_file(self, results: List[Dict[str, Any]], 
                               file_path: str):
        """将搜索结果导出到文件
        
        Args:
            results: 搜索结果
            file_path: 导出文件路径
        """
        ext = Path(file_path).suffix.lower()
        
        if ext == '.csv':
            self._export_to_csv(results, file_path)
        elif ext == '.json':
            self._export_to_json(results, file_path)
        else:
            # 默认导出为CSV
            self._export_to_csv(results, file_path)
        
        self.resultsExported.emit(file_path)
        self.logger.info(f"搜索结果已导出到: {file_path}")
    
    def _export_to_csv(self, results: List[Dict[str, Any]], file_path: str):
        """导出为CSV格式
        
        Args:
            results: 搜索结果
            file_path: 文件路径
        """
        import csv
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            if not results:
                return
            
            fieldnames = results[0].keys()
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for result in results:
                writer.writerow(result)
    
    def _export_to_json(self, results: List[Dict[str, Any]], file_path: str):
        """导出为JSON格式
        
        Args:
            results: 搜索结果
            file_path: 文件路径
        """
        import json
        
        with open(file_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(results, jsonfile, ensure_ascii=False, indent=2)
    
    def _on_import_progress(self, progress: int, message: str):
        """导入进度更新
        
        Args:
            progress: 进度百分比
            message: 进度消息
        """
        if self.progress_dialog:
            self.progress_dialog.setValue(progress)
            self.progress_dialog.setLabelText(message)
        
        self.importProgress.emit(progress, message)
    
    def _on_import_finished(self, database_path: str):
        """导入完成
        
        Args:
            database_path: 数据库路径
        """
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        self._cleanup_import_thread()
        
        self.databaseImported.emit(database_path)
        self.logger.info(f"数据库导入完成: {database_path}")
        
        # 显示成功消息
        QMessageBox.information(
            self.main_window,
            "导入完成",
            f"数据库已成功导入:\n{database_path}"
        )
    
    def _on_import_error(self, error_message: str):
        """导入错误
        
        Args:
            error_message: 错误消息
        """
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        self._cleanup_import_thread()
        
        self.operationError.emit(error_message)
        self.logger.error(f"数据库导入失败: {error_message}")
    
    def _cancel_import(self):
        """取消导入"""
        if self.import_worker and hasattr(self.import_worker, 'stop'):
            self.import_worker.stop()
        
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        self._cleanup_import_thread()
        
        self.logger.info("数据库导入已取消")
    
    def _cleanup_import_thread(self):
        """清理导入线程"""
        if self.import_thread and self.import_thread.isRunning():
            self.import_thread.quit()
            self.import_thread.wait()
        
        self.import_thread = None
        self.import_worker = None
    
    def cleanup(self):
        """清理资源"""
        self._cancel_import()