"""
相似度可视化组件

包含相似度条形图等可视化组件
"""

from typing import Dict
from PyQt6.QtWidgets import QWidget
from PyQt6.QtCore import Qt, QRect
from PyQt6.QtGui import QPainter, QColor, QPen, QBrush, QFont


class SimilarityBarWidget(QWidget):
    """相似度条形图组件（横向条形图）"""
    
    def __init__(self, feature_scores: Dict[str, float], parent=None):
        super().__init__(parent)
        self.feature_scores = feature_scores
        self.setMinimumHeight(150)
        self.setMinimumWidth(300)
        self.setMaximumHeight(180)
        
        # 定义特征颜色映射
        self.feature_colors = {
            "形状": QColor(34, 139, 34),      # 森林绿
            "shape": QColor(34, 139, 34),
            "纹理": QColor(30, 144, 255),     # 道奇蓝
            "texture": QColor(30, 144, 255),
            "颜色": QColor(220, 20, 60),      # 深红
            "color": QColor(220, 20, 60),
            "深度学习": QColor(138, 43, 226), # 蓝紫色
            "deep_learning": QColor(138, 43, 226),
            "加权": QColor(255, 140, 0),      # 深橙色
            "weighted": QColor(255, 140, 0),
            "总分": QColor(255, 140, 0),      # 深橙色
        }
    
    def paintEvent(self, event):
        """绘制事件（横向条形图）"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 设置背景
        painter.fillRect(self.rect(), QColor(250, 250, 250))
        
        # 如果没有特征分数，显示提示信息
        if not self.feature_scores:
            painter.setPen(QPen(Qt.GlobalColor.gray))
            painter.setFont(QFont("Arial", 10))
            painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "无相似度数据")
            return
        
        # 计算绘图区域
        rect = self.rect()
        margin = 10
        title_height = 20
        feature_name_width = 80  # 特征名称区域宽度
        
        chart_rect = QRect(
            rect.left() + margin + feature_name_width,
            rect.top() + title_height,
            rect.width() - margin * 2 - feature_name_width - 60,  # 为分数标签留空间
            rect.height() - title_height - margin
        )
        
        # 绘制标题
        title_font = QFont("Arial", 10, QFont.Weight.Bold)
        painter.setFont(title_font)
        painter.setPen(QPen(Qt.GlobalColor.black))
        painter.drawText(rect.left() + margin, rect.top() + 15, "特征相似度")
        
        # 绘制横向条形图
        if len(self.feature_scores) > 0:
            bar_height = 18  # 每个条形图的高度
            bar_spacing = 8  # 条形图之间的间距
            total_height = len(self.feature_scores) * (bar_height + bar_spacing) - bar_spacing
            
            # 计算起始Y位置（居中）
            start_y = chart_rect.top() + (chart_rect.height() - total_height) // 2
            
            for i, (feature_name, score) in enumerate(self.feature_scores.items()):
                # 获取颜色
                color = self.feature_colors.get(feature_name.lower(), QColor(100, 100, 100))
                
                # 计算条形图位置
                bar_y = start_y + i * (bar_height + bar_spacing)
                bar_width = max(2, int(score * chart_rect.width()))
                
                # 绘制特征名称
                painter.setPen(QPen(Qt.GlobalColor.black))
                painter.setFont(QFont("Arial", 9))
                
                # 简化特征名称
                display_name = feature_name
                if feature_name == "深度学习":
                    display_name = "深度学习"
                elif feature_name == "加权":
                    display_name = "总分"
                elif feature_name == "deep_learning":
                    display_name = "深度学习"
                elif feature_name == "weighted":
                    display_name = "总分"
                elif feature_name == "shape":
                    display_name = "形状"
                elif feature_name == "texture":
                    display_name = "纹理"
                elif feature_name == "color":
                    display_name = "颜色"
                
                name_rect = QRect(rect.left() + margin, bar_y, feature_name_width - 5, bar_height)
                painter.drawText(name_rect, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter, display_name)
                
                # 绘制条形图（带渐变效果）
                gradient_color = QColor(color)
                gradient_color.setAlpha(180)
                painter.setBrush(QBrush(gradient_color))
                painter.setPen(QPen(color, 1))
                painter.drawRect(chart_rect.left(), bar_y + 2, bar_width, bar_height - 4)
                
                # 绘制分数（在条形图右侧）
                painter.setPen(QPen(Qt.GlobalColor.black))
                painter.setFont(QFont("Arial", 8, QFont.Weight.Bold))
                score_text = f"{score:.3f}"
                score_rect = QRect(chart_rect.left() + bar_width + 5, bar_y, 50, bar_height)
                painter.drawText(score_rect, Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter, score_text)
                
                # 绘制背景网格线
                painter.setPen(QPen(QColor(230, 230, 230), 1))
                painter.drawLine(chart_rect.left(), bar_y + bar_height // 2, chart_rect.right(), bar_y + bar_height // 2)
        
        # 绘制边框
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        painter.setBrush(QBrush())
        painter.drawRect(chart_rect)