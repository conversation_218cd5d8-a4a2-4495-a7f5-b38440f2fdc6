#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布料图片CRUD操作模块

该模块提供布料图片的基本增删改查操作。
"""

import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional, List, Dict, Any

from .base_repository import BaseRepository
from ..database_manager import DatabaseManager
from ..utils.database_utils import serialize_json, deserialize_json
from ..exceptions.database_exceptions import RepositoryError
from ..models import FabricImage


class FabricCrud(BaseRepository):
    """布料图片CRUD操作类"""
    
    @property
    def table_name(self) -> str:
        """表名"""
        return "fabric_images"
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化布料图片CRUD操作
        
        Args:
            db_manager: 数据库管理器
        """
        super().__init__(db_manager)
    
    def _row_to_fabric_image(self, row) -> FabricImage:
        """将数据库行转换为FabricImage对象
        
        Args:
            row: 数据库行
            
        Returns:
            FabricImage: 布料图片对象
        """
        try:
            # 反序列化JSON字段
            tags = self._deserialize_field(row['tags']) if row['tags'] else []
            color_info = self._deserialize_field(row['color_info']) if row['color_info'] else {}
            texture_info = self._deserialize_field(row['texture_info']) if row['texture_info'] else {}
            pattern_info = self._deserialize_field(row['pattern_info']) if row['pattern_info'] else {}
            material_info = self._deserialize_field(row['material_info']) if row['material_info'] else {}
            metadata = self._deserialize_field(row['metadata']) if row['metadata'] else {}
            
            # 处理时间字段
            created_at = None
            if row['created_at']:
                try:
                    created_at = datetime.fromisoformat(row['created_at'].replace('Z', '+00:00'))
                except ValueError:
                    created_at = datetime.strptime(row['created_at'], '%Y-%m-%d %H:%M:%S')
            
            updated_at = None
            if row['updated_at']:
                try:
                    updated_at = datetime.fromisoformat(row['updated_at'].replace('Z', '+00:00'))
                except ValueError:
                    updated_at = datetime.strptime(row['updated_at'], '%Y-%m-%d %H:%M:%S')
            
            indexed_at = None
            if row['indexed_at']:
                try:
                    indexed_at = datetime.fromisoformat(row['indexed_at'].replace('Z', '+00:00'))
                except ValueError:
                    indexed_at = datetime.strptime(row['indexed_at'], '%Y-%m-%d %H:%M:%S')
            
            return FabricImage(
                id=row['id'],
                file_path=row['file_path'],
                file_name=row['file_name'],
                file_size=row['file_size'],
                width=row['width'],
                height=row['height'],
                channels=row['channels'] if 'channels' in row.keys() else 3,
                format=row['format'],
                hash_md5=row['hash_md5'],
                features=row['features'],
                thumbnail_path=row['thumbnail_path'],
                tags=tags,
                category=row['category'],
                color_info=color_info,
                texture_info=texture_info,
                pattern_info=pattern_info,
                material_info=material_info,
                description=row['description'],
                metadata=metadata,
                created_at=created_at,
                updated_at=updated_at,
                indexed_at=indexed_at,
                is_active=bool(row['is_active'])
            )
            
        except Exception as e:
            self.logger.error(f"转换数据库行为FabricImage对象失败: {e}")
            raise RepositoryError(f"转换数据库行为FabricImage对象失败: {e}")
    
    def create(self, fabric_image: FabricImage) -> Optional[int]:
        """创建布料图片记录

        Args:
            fabric_image: 布料图片对象

        Returns:
            Optional[int]: 创建的记录ID，如果失败则返回None
        """
        try:
            sql = """
                INSERT INTO fabric_images (
                    file_path, file_name, file_size, width, height, channels, format,
                    hash_md5, features, thumbnail_path, tags, category,
                    color_info, texture_info, pattern_info, material_info,
                    description, metadata, indexed_at, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            # 序列化复杂类型
            tags = self._serialize_field(fabric_image.tags) if isinstance(fabric_image.tags, list) else fabric_image.tags
            color_info = self._serialize_field(fabric_image.color_info) if isinstance(fabric_image.color_info, dict) else fabric_image.color_info
            texture_info = self._serialize_field(fabric_image.texture_info) if isinstance(fabric_image.texture_info, dict) else fabric_image.texture_info
            pattern_info = self._serialize_field(fabric_image.pattern_info) if isinstance(fabric_image.pattern_info, dict) else fabric_image.pattern_info
            material_info = self._serialize_field(fabric_image.material_info) if isinstance(fabric_image.material_info, dict) else fabric_image.material_info
            metadata = self._serialize_field(fabric_image.metadata) if isinstance(fabric_image.metadata, dict) else fabric_image.metadata
            indexed_at = fabric_image.indexed_at.isoformat() if isinstance(fabric_image.indexed_at, datetime) else fabric_image.indexed_at
            is_active = 1 if fabric_image.is_active else 0
            
            params = (
                fabric_image.file_path,
                fabric_image.file_name,
                fabric_image.file_size,
                fabric_image.width,
                fabric_image.height,
                fabric_image.channels,
                fabric_image.format,
                fabric_image.hash_md5,
                fabric_image.features,
                fabric_image.thumbnail_path,
                tags,
                fabric_image.category,
                color_info,
                texture_info,
                pattern_info,
                material_info,
                fabric_image.description,
                metadata,
                indexed_at,
                is_active
            )
            
            fabric_id = self.db_manager.execute_insert(sql, params)
            self.logger.debug(f"布料图片记录创建成功: {fabric_id}")
            return fabric_id
            
        except Exception as e:
            self.logger.error(f"创建布料图片记录失败: {e}")
            raise RepositoryError(f"创建布料图片记录失败: {e}")
    
    def get_by_id(self, fabric_id: int) -> Optional[FabricImage]:
        """根据ID获取布料图片
        
        Args:
            fabric_id: 布料图片ID
            
        Returns:
            Optional[FabricImage]: 布料图片对象，如果不存在则返回None
        """
        try:
            sql = "SELECT * FROM fabric_images WHERE id = ?"
            rows = self.db_manager.execute_query(sql, (fabric_id,))
            
            if rows:
                return self._row_to_fabric_image(rows[0])
            return None
            
        except Exception as e:
            self.logger.error(f"根据ID获取布料图片失败: {e}")
            raise RepositoryError(f"根据ID获取布料图片失败: {e}")
    
    def get_by_path(self, file_path: str) -> Optional[FabricImage]:
        """根据文件路径获取布料图片
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[FabricImage]: 布料图片对象，如果不存在则返回None
        """
        try:
            sql = "SELECT * FROM fabric_images WHERE file_path = ?"
            rows = self.db_manager.execute_query(sql, (file_path,))
            
            if rows:
                return self._row_to_fabric_image(rows[0])
            return None
            
        except Exception as e:
            self.logger.error(f"根据路径获取布料图片失败: {e}")
            raise RepositoryError(f"根据路径获取布料图片失败: {e}")
    
    def get_by_hash(self, hash_md5: str) -> Optional[FabricImage]:
        """根据MD5哈希获取布料图片
        
        Args:
            hash_md5: MD5哈希值
            
        Returns:
            Optional[FabricImage]: 布料图片对象，如果不存在则返回None
        """
        try:
            sql = "SELECT * FROM fabric_images WHERE hash_md5 = ?"
            rows = self.db_manager.execute_query(sql, (hash_md5,))
            
            if rows:
                return self._row_to_fabric_image(rows[0])
            return None
            
        except Exception as e:
            self.logger.error(f"根据哈希获取布料图片失败: {e}")
            raise RepositoryError(f"根据哈希获取布料图片失败: {e}")
    
    def get_all(self, include_inactive: bool = False, 
               limit: Optional[int] = None, 
               offset: int = 0) -> List[FabricImage]:
        """获取所有布料图片
        
        Args:
            include_inactive: 是否包含非活跃记录
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[FabricImage]: 布料图片列表
        """
        try:
            sql = "SELECT * FROM fabric_images"
            params = []
            
            if not include_inactive:
                sql += " WHERE is_active = 1"
            
            sql += " ORDER BY created_at DESC"
            
            if limit is not None:
                sql += " LIMIT ? OFFSET ?"
                params.extend([limit, offset])
            
            rows = self.db_manager.execute_query(sql, params)
            
            return [self._row_to_fabric_image(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"获取所有布料图片失败: {e}")
            raise RepositoryError(f"获取所有布料图片失败: {e}")
    
    def count(self, include_inactive: bool = False) -> int:
        """获取布料图片总数
        
        Args:
            include_inactive: 是否包含非活跃记录
            
        Returns:
            int: 布料图片总数
        """
        try:
            sql = "SELECT COUNT(*) as count FROM fabric_images"
            params = []
            
            if not include_inactive:
                sql += " WHERE is_active = 1"
            
            result = self.db_manager.execute_query(sql, params)
            return result[0]['count'] if result else 0
            
        except Exception as e:
            self.logger.error(f"获取布料图片总数失败: {e}")
            raise RepositoryError(f"获取布料图片总数失败: {e}")
    
    def update(self, fabric_image: FabricImage) -> bool:
        """更新布料图片记录
        
        Args:
            fabric_image: 布料图片对象
            
        Returns:
            bool: 更新是否成功
        """
        try:
            with self.db_manager.transaction():
                sql = """
                    UPDATE fabric_images SET
                        file_path = ?, file_name = ?, file_size = ?, width = ?, height = ?,
                        channels = ?, format = ?, hash_md5 = ?, features = ?, thumbnail_path = ?, 
                        tags = ?, category = ?, color_info = ?, texture_info = ?, pattern_info = ?,
                        material_info = ?, description = ?, metadata = ?, indexed_at = ?, is_active = ?
                    WHERE id = ?
                """
                
                # 序列化复杂类型
                tags = self._serialize_field(fabric_image.tags) if isinstance(fabric_image.tags, list) else fabric_image.tags
                color_info = self._serialize_field(fabric_image.color_info) if isinstance(fabric_image.color_info, dict) else fabric_image.color_info
                texture_info = self._serialize_field(fabric_image.texture_info) if isinstance(fabric_image.texture_info, dict) else fabric_image.texture_info
                pattern_info = self._serialize_field(fabric_image.pattern_info) if isinstance(fabric_image.pattern_info, dict) else fabric_image.pattern_info
                material_info = self._serialize_field(fabric_image.material_info) if isinstance(fabric_image.material_info, dict) else fabric_image.material_info
                metadata = self._serialize_field(fabric_image.metadata) if isinstance(fabric_image.metadata, dict) else fabric_image.metadata
                indexed_at = fabric_image.indexed_at.isoformat() if isinstance(fabric_image.indexed_at, datetime) else fabric_image.indexed_at
                is_active = 1 if fabric_image.is_active else 0
                
                params = (
                    fabric_image.file_path,
                    fabric_image.file_name,
                    fabric_image.file_size,
                    fabric_image.width,
                    fabric_image.height,
                    fabric_image.channels,
                    fabric_image.format,
                    fabric_image.hash_md5,
                    fabric_image.features,
                    fabric_image.thumbnail_path,
                    tags,
                    fabric_image.category,
                    color_info,
                    texture_info,
                    pattern_info,
                    material_info,
                    fabric_image.description,
                    metadata,
                    indexed_at,
                    is_active,
                    fabric_image.id
                )
                
                affected_rows = self.db_manager.execute_update(sql, params)
                return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"更新布料图片记录失败: {e}")
            raise RepositoryError(f"更新布料图片记录失败: {e}")
    
    def delete(self, fabric_id: int, soft_delete: bool = True) -> bool:
        """删除布料图片记录
        
        Args:
            fabric_id: 布料图片ID
            soft_delete: 是否软删除（设置is_active=False）
            
        Returns:
            bool: 删除是否成功
        """
        try:
            if soft_delete:
                sql = "UPDATE fabric_images SET is_active = 0 WHERE id = ?"
            else:
                sql = "DELETE FROM fabric_images WHERE id = ?"
            
            affected_rows = self.db_manager.execute_update(sql, (fabric_id,))
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"删除布料图片记录失败: {e}")
            raise RepositoryError(f"删除布料图片记录失败: {e}")
    
    def batch_create(self, fabric_images: List[FabricImage]) -> List[int]:
        """批量创建布料图片记录
        
        Args:
            fabric_images: 布料图片列表
            
        Returns:
            List[int]: 创建的记录ID列表
        """
        try:
            # 过滤掉已存在的图像
            new_images = []
            existing_ids = []
            
            for fabric_image in fabric_images:
                existing_image = self.get_by_path(fabric_image.file_path)
                if existing_image:
                    existing_ids.append(existing_image.id)
                    self.logger.debug(f"图像已存在，跳过: {fabric_image.file_path}")
                else:
                    new_images.append(fabric_image)
            
            # 如果没有新图像需要创建，返回现有ID
            if not new_images:
                return existing_ids
            
            sql = """
                INSERT INTO fabric_images (
                    file_path, file_name, file_size, width, height, channels, format,
                    hash_md5, features, thumbnail_path, tags, category,
                    color_info, texture_info, pattern_info, material_info,
                    description, metadata, indexed_at, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params_list = []
            for fabric_image in new_images:
                # 序列化复杂类型
                tags = self._serialize_field(fabric_image.tags) if isinstance(fabric_image.tags, list) else fabric_image.tags
                color_info = self._serialize_field(fabric_image.color_info) if isinstance(fabric_image.color_info, dict) else fabric_image.color_info
                texture_info = self._serialize_field(fabric_image.texture_info) if isinstance(fabric_image.texture_info, dict) else fabric_image.texture_info
                pattern_info = self._serialize_field(fabric_image.pattern_info) if isinstance(fabric_image.pattern_info, dict) else fabric_image.pattern_info
                material_info = self._serialize_field(fabric_image.material_info) if isinstance(fabric_image.material_info, dict) else fabric_image.material_info
                metadata = self._serialize_field(fabric_image.metadata) if isinstance(fabric_image.metadata, dict) else fabric_image.metadata
                indexed_at = fabric_image.indexed_at.isoformat() if isinstance(fabric_image.indexed_at, datetime) else fabric_image.indexed_at
                is_active = 1 if fabric_image.is_active else 0
                
                params = (
                    fabric_image.file_path,
                    fabric_image.file_name,
                    fabric_image.file_size,
                    fabric_image.width,
                    fabric_image.height,
                    fabric_image.channels,
                    fabric_image.format,
                    fabric_image.hash_md5,
                    fabric_image.features,
                    fabric_image.thumbnail_path,
                    tags,
                    fabric_image.category,
                    color_info,
                    texture_info,
                    pattern_info,
                    material_info,
                    fabric_image.description,
                    metadata,
                    indexed_at,
                    is_active
                )
                params_list.append(params)
            
            affected_rows = self.db_manager.execute_batch(sql, params_list)
            
            # 获取插入的ID
            last_id = self.db_manager.get_last_insert_id()
            new_ids = list(range(last_id - len(new_images) + 1, last_id + 1))
            
            # 合并现有ID和新ID
            all_ids = existing_ids + new_ids
            
            self.logger.info(f"批量创建布料图片记录成功: {len(new_ids)} 条新记录，{len(existing_ids)} 条已存在")
            return all_ids
            
        except Exception as e:
            self.logger.error(f"批量创建布料图片记录失败: {e}")
            raise RepositoryError(f"批量创建布料图片记录失败: {e}")
    
    def exists_by_path(self, file_path: str) -> bool:
        """检查文件路径是否存在
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否存在
        """
        try:
            sql = "SELECT COUNT(*) as count FROM fabric_images WHERE file_path = ?"
            result = self.db_manager.execute_query(sql, (file_path,))
            return result[0]['count'] > 0 if result else False
            
        except Exception as e:
            self.logger.error(f"检查文件路径存在性失败: {e}")
            raise RepositoryError(f"检查文件路径存在性失败: {e}")
    
    def exists_by_hash(self, hash_md5: str) -> bool:
        """检查MD5哈希是否存在
        
        Args:
            hash_md5: MD5哈希值
            
        Returns:
            bool: 是否存在
        """
        try:
            sql = "SELECT COUNT(*) as count FROM fabric_images WHERE hash_md5 = ?"
            result = self.db_manager.execute_query(sql, (hash_md5,))
            return result[0]['count'] > 0 if result else False
            
        except Exception as e:
            self.logger.error(f"检查MD5哈希存在性失败: {e}")
            raise RepositoryError(f"检查MD5哈希存在性失败: {e}")
    
    def add_fabric_image(self, file_path: str, width: int, height: int, channels: int,
                        file_size: int, file_type: str, metadata: Dict[str, Any] = None,
                        tags: List[str] = None, category: str = None,
                        description: str = None) -> Optional[int]:
        """添加布料图片
        
        Args:
            file_path: 文件路径
            width: 图像宽度
            height: 图像高度
            channels: 图像通道数
            file_size: 文件大小
            file_type: 文件类型
            metadata: 元数据
            tags: 标签列表
            category: 类别
            description: 描述
            
        Returns:
            Optional[int]: 创建的记录ID，如果失败则返回None
        """
        try:
            # 创建FabricImage对象
            fabric_image = FabricImage(
                id=None,
                file_path=file_path,
                file_name=Path(file_path).name,
                file_size=file_size,
                width=width,
                height=height,
                channels=channels,
                format=file_type,
                hash_md5="",  # 可以在这里计算MD5哈希
                features=None,
                thumbnail_path=None,
                tags=tags or [],
                category=category or "",
                color_info={},
                texture_info={},
                pattern_info={},
                material_info={},
                description=description or "",
                metadata=metadata or {},
                indexed_at=datetime.now(),
                is_active=True
            )
            
            # 创建记录
            return self.create(fabric_image)
            
        except Exception as e:
            self.logger.error(f"添加布料图片失败: {e}")
            raise RepositoryError(f"添加布料图片失败: {e}")
    
    def add_image(self, fabric_image: FabricImage) -> Optional[int]:
        """添加图像到数据库

        Args:
            fabric_image: 布料图片对象

        Returns:
            Optional[int]: 添加成功的布料图片ID，失败返回None
        """
        try:
            # 检查文件路径是否已存在
            existing_image = self.get_by_path(fabric_image.file_path)
            if existing_image:
                self.logger.debug(f"图像已存在，返回现有ID: {fabric_image.file_path}")
                fabric_image.id = existing_image.id
                return existing_image.id

            # 创建记录并获取ID
            fabric_id = self.create(fabric_image)
            if fabric_id is None:
                return None

            # 设置ID并返回ID
            fabric_image.id = fabric_id
            return fabric_id

        except Exception as e:
            self.logger.error(f"添加图像失败: {e}")
            raise RepositoryError(f"添加图像失败: {e}")

    def batch_create(self, fabric_images: List[FabricImage], batch_size: int = 100) -> List[Optional[int]]:
        """批量创建布料图片记录

        Args:
            fabric_images: 布料图片对象列表
            batch_size: 批处理大小

        Returns:
            List[Optional[int]]: 创建的记录ID列表
        """
        if not fabric_images:
            return []

        result_ids = []

        try:
            # 分批处理
            for i in range(0, len(fabric_images), batch_size):
                batch = fabric_images[i:i + batch_size]
                batch_ids = self._batch_insert_chunk(batch)
                result_ids.extend(batch_ids)

                # 设置ID到原对象
                for j, fabric_image in enumerate(batch):
                    if j < len(batch_ids) and batch_ids[j] is not None:
                        fabric_image.id = batch_ids[j]

            self.logger.info(f"批量创建完成，总数: {len(fabric_images)}, 成功: {sum(1 for id in result_ids if id is not None)}")
            return result_ids

        except Exception as e:
            self.logger.error(f"批量创建失败: {e}")
            raise RepositoryError(f"批量创建失败: {e}")

    def _batch_insert_chunk(self, fabric_images: List[FabricImage]) -> List[Optional[int]]:
        """批量插入一个块的数据

        Args:
            fabric_images: 布料图片对象列表

        Returns:
            List[Optional[int]]: 插入的记录ID列表
        """
        if not fabric_images:
            return []

        sql = """
            INSERT INTO fabric_images (
                file_path, file_name, file_size, width, height, channels, format,
                hash_md5, features, thumbnail_path, tags, category,
                color_info, texture_info, pattern_info, material_info,
                description, metadata, indexed_at, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        try:
            with self.connection_manager.connection_context() as conn:
                cursor = conn.cursor()

                # 准备批量插入数据
                batch_data = []
                for fabric_image in fabric_images:
                    data = self._prepare_insert_data(fabric_image)
                    batch_data.append(data)

                # 执行批量插入
                cursor.executemany(sql, batch_data)

                # 获取插入的ID
                result_ids = []
                first_id = cursor.lastrowid
                if first_id is not None:
                    # SQLite的lastrowid返回最后插入的ID，需要计算前面的ID
                    for i in range(len(fabric_images)):
                        result_ids.append(first_id - len(fabric_images) + 1 + i)
                else:
                    result_ids = [None] * len(fabric_images)

                conn.commit()
                return result_ids

        except Exception as e:
            self.logger.error(f"批量插入块失败: {e}")
            return [None] * len(fabric_images)

    def _prepare_insert_data(self, fabric_image: FabricImage) -> tuple:
        """准备插入数据

        Args:
            fabric_image: 布料图片对象

        Returns:
            tuple: 插入数据元组
        """
        return (
            fabric_image.file_path,
            fabric_image.file_name,
            fabric_image.file_size,
            fabric_image.width,
            fabric_image.height,
            fabric_image.channels,
            fabric_image.format,
            fabric_image.hash_md5,
            json.dumps(fabric_image.features) if fabric_image.features else None,
            fabric_image.thumbnail_path,
            json.dumps(fabric_image.tags) if fabric_image.tags else None,
            fabric_image.category,
            json.dumps(fabric_image.color_info) if fabric_image.color_info else None,
            json.dumps(fabric_image.texture_info) if fabric_image.texture_info else None,
            json.dumps(fabric_image.pattern_info) if fabric_image.pattern_info else None,
            json.dumps(fabric_image.material_info) if fabric_image.material_info else None,
            fabric_image.description,
            json.dumps(fabric_image.metadata) if fabric_image.metadata else None,
            fabric_image.indexed_at.isoformat() if fabric_image.indexed_at else None,
            fabric_image.is_active
        )
    
    # 别名方法
    def get_image_by_id(self, image_id: int) -> Optional[FabricImage]:
        """根据ID获取布料图片（get_by_id的别名）"""
        return self.get_by_id(image_id)
    
    def update_image(self, fabric_image: FabricImage) -> bool:
        """更新布料图片记录（update的别名）"""
        return self.update(fabric_image)