#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像对比控制器

该模块提供图像对比的核心逻辑控制。
"""

import os
from typing import Optional, Callable
from PyQt6.QtCore import QObject, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap

from utils.log_utils import LoggerMixin
from .compare_config import CompareConfig, ImageInfo, SyncMode


class CompareController(QObject, LoggerMixin):
    """图像对比控制器"""
    
    # 信号
    imageInfoUpdated = pyqtSignal(int, object)  # 图像索引, ImageInfo
    syncModeChanged = pyqtSignal(str)  # 同步模式
    zoomSyncRequested = pyqtSignal(int, float)  # 源图像索引, 缩放因子
    panSyncRequested = pyqtSignal(int, object)  # 源图像索引, 平移位置
    
    def __init__(self, config: Optional[CompareConfig] = None):
        super().__init__()
        
        self.config = config or CompareConfig()
        
        # 图像信息
        self.image1_info: Optional[ImageInfo] = None
        self.image2_info: Optional[ImageInfo] = None
        
        # 同步状态
        self._updating_zoom = False
        self._updating_pan = False
        
        # 回调函数
        self.zoom_callbacks = {}  # {viewer_index: callback}
        self.pan_callbacks = {}   # {viewer_index: callback}
    
    def set_images(self, image1_path: str, image2_path: str) -> bool:
        """设置要对比的图像
        
        Args:
            image1_path: 第一个图像路径
            image2_path: 第二个图像路径
            
        Returns:
            bool: 设置是否成功
        """
        try:
            # 验证文件存在
            if not os.path.exists(image1_path):
                self.logger.error(f"图像1不存在: {image1_path}")
                return False
            
            if not os.path.exists(image2_path):
                self.logger.error(f"图像2不存在: {image2_path}")
                return False
            
            # 创建图像信息
            self.image1_info = self._create_image_info(image1_path)
            self.image2_info = self._create_image_info(image2_path)
            
            # 发送更新信号
            self.imageInfoUpdated.emit(0, self.image1_info)
            self.imageInfoUpdated.emit(1, self.image2_info)
            
            self.logger.info(f"设置对比图像: {image1_path} vs {image2_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置对比图像失败: {e}")
            return False
    
    def _create_image_info(self, image_path: str) -> ImageInfo:
        """创建图像信息
        
        Args:
            image_path: 图像路径
            
        Returns:
            ImageInfo: 图像信息对象
        """
        try:
            # 获取文件大小
            file_size = os.path.getsize(image_path)
            size_mb = file_size / (1024 * 1024)
            
            # 获取图像尺寸
            pixmap = QPixmap(image_path)
            width = pixmap.width()
            height = pixmap.height()
            
            return ImageInfo(
                path=image_path,
                width=width,
                height=height,
                size_mb=size_mb
            )
            
        except Exception as e:
            self.logger.error(f"创建图像信息失败: {e}")
            return ImageInfo(path=image_path)
    
    def set_sync_zoom(self, enabled: bool):
        """设置缩放同步
        
        Args:
            enabled: 是否启用同步
        """
        self.config.sync_zoom = enabled
        self.logger.info(f"缩放同步: {enabled}")
    
    def set_sync_pan(self, enabled: bool):
        """设置平移同步
        
        Args:
            enabled: 是否启用同步
        """
        self.config.sync_pan = enabled
        self.logger.info(f"平移同步: {enabled}")
    
    def on_zoom_changed(self, viewer_index: int, zoom_factor: float):
        """处理缩放变化
        
        Args:
            viewer_index: 查看器索引 (0或1)
            zoom_factor: 缩放因子
        """
        if self.config.sync_zoom and not self._updating_zoom:
            try:
                self._updating_zoom = True
                # 同步到另一个查看器
                target_index = 1 - viewer_index
                self.zoomSyncRequested.emit(target_index, zoom_factor)
            finally:
                self._updating_zoom = False
    
    def on_pan_changed(self, viewer_index: int, pan_position):
        """处理平移变化
        
        Args:
            viewer_index: 查看器索引 (0或1)
            pan_position: 平移位置
        """
        if self.config.sync_pan and not self._updating_pan:
            try:
                self._updating_pan = True
                # 同步到另一个查看器
                target_index = 1 - viewer_index
                self.panSyncRequested.emit(target_index, pan_position)
            finally:
                self._updating_pan = False
    
    def register_zoom_callback(self, viewer_index: int, callback: Callable[[float], None]):
        """注册缩放回调
        
        Args:
            viewer_index: 查看器索引
            callback: 回调函数
        """
        self.zoom_callbacks[viewer_index] = callback
    
    def register_pan_callback(self, viewer_index: int, callback: Callable):
        """注册平移回调
        
        Args:
            viewer_index: 查看器索引
            callback: 回调函数
        """
        self.pan_callbacks[viewer_index] = callback
    
    def apply_zoom_to_all(self, zoom_factor: float):
        """对所有查看器应用缩放
        
        Args:
            zoom_factor: 缩放因子
        """
        try:
            self._updating_zoom = True
            for callback in self.zoom_callbacks.values():
                if callback:
                    callback(zoom_factor)
        finally:
            self._updating_zoom = False
    
    def get_image_info(self, index: int) -> Optional[ImageInfo]:
        """获取图像信息
        
        Args:
            index: 图像索引 (0或1)
            
        Returns:
            Optional[ImageInfo]: 图像信息
        """
        if index == 0:
            return self.image1_info
        elif index == 1:
            return self.image2_info
        else:
            return None