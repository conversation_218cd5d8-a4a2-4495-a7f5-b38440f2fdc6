#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型详情显示组件

该模块提供模型详情信息的显示组件。
"""

from typing import Optional
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QFrame, QLabel
from PyQt6.QtCore import Qt

from utils.log_utils import LoggerMixin
from config.model_config import ModelConfigManager
from gui.widgets.widget_factory import WidgetFactory


class ModelDetailsWidget(QWidget, LoggerMixin):
    """模型详情显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.widget_factory = WidgetFactory()
        self.model_config_manager = ModelConfigManager()
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # 详情框
        details_frame = QFrame()
        details_frame.setFrameShape(QFrame.Shape.StyledPanel)
        details_frame.setFrameShadow(QFrame.Shadow.Sunken)
        details_layout = QVBoxLayout(details_frame)
        
        self.model_name_label = self.widget_factory.create_label(
            "模型名称", bold=True
        )
        details_layout.addWidget(self.model_name_label)
        
        self.model_description_label = self.widget_factory.create_label(
            "请选择一个模型以查看详情"
        )
        self.model_description_label.setWordWrap(True)
        details_layout.addWidget(self.model_description_label)
        
        self.model_details_label = self.widget_factory.create_label("")
        self.model_details_label.setWordWrap(True)
        details_layout.addWidget(self.model_details_label)
        
        layout.addWidget(details_frame)
    
    def update_model_details(self, model_name: str):
        """更新模型详情
        
        Args:
            model_name: 模型名称
        """
        try:
            model_config = self.model_config_manager.get_config(model_name)
            if model_config:
                self.model_name_label.setText(f"模型: {model_config.name}")
                self.model_description_label.setText(model_config.description)
                
                # 构建详情文本
                details = f"架构: {model_config.architecture}\n"
                details += f"输入尺寸: {model_config.input_size[1]}x{model_config.input_size[2]}\n"
                details += f"特征维度: {model_config.feature_dim}\n"
                
                self.model_details_label.setText(details)
            else:
                self.clear_details()
        except Exception as e:
            self.logger.error(f"更新模型详情失败: {e}")
            self.clear_details()
    
    def clear_details(self):
        """清空详情"""
        self.model_name_label.setText("模型名称")
        self.model_description_label.setText("请选择一个模型以查看详情")
        self.model_details_label.setText("")