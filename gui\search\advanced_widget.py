#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级搜索组件

该模块提供高级搜索功能界面，包括文件属性过滤、日期范围过滤等。
"""

from typing import Dict, Any, Optional, Tuple
from datetime import datetime, date

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QCheckBox,
    QGroupBox, QSpinBox, QDateEdit, QComboBox, QDoubleSpinBox,
    QScrollArea, QFrame, QFormLayout
)
from PyQt6.QtCore import pyqtSignal, Qt, QDate

from utils.log_utils import LoggerMixin
from gui.helpers.message_helper import MessageHelper
from gui.widget_factory import WidgetFactory, ButtonStyle, ButtonSize


class AdvancedSearchWidget(QWidget, LoggerMixin):
    """高级搜索组件"""
    
    # 信号
    searchRequested = pyqtSignal(str, dict)  # 搜索请求
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.widget_factory = WidgetFactory()
        
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 创建滚动区域内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(10)
        
        # 文件属性过滤
        self._setup_file_filters(scroll_layout)
        
        # 日期范围过滤
        self._setup_date_filters(scroll_layout)
        
        # 内容过滤
        self._setup_content_filters(scroll_layout)
        
        # 搜索参数
        self._setup_search_params(scroll_layout)
        
        # 按钮组
        self._setup_buttons(scroll_layout)
        
        scroll_layout.addStretch()
        
        # 设置滚动区域内容并添加到主布局
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)
    
    def _setup_file_filters(self, layout):
        """设置文件属性过滤"""
        file_group = self.widget_factory.create_group_box("文件属性过滤")
        file_layout = QFormLayout(file_group)
        
        # 文件扩展名
        self.file_extension_input = QLineEdit()
        self.file_extension_input.setPlaceholderText("例如: .jpg, .png, .gif")
        file_layout.addRow("文件扩展名:", self.file_extension_input)
        
        # 文件大小范围
        size_layout = QHBoxLayout()
        
        self.min_size_spin = QDoubleSpinBox()
        self.min_size_spin.setRange(0, 999999)
        self.min_size_spin.setSuffix(" MB")
        self.min_size_spin.setDecimals(2)
        size_layout.addWidget(self.min_size_spin)
        
        size_layout.addWidget(self.widget_factory.create_label("到"))
        
        self.max_size_spin = QDoubleSpinBox()
        self.max_size_spin.setRange(0, 999999)
        self.max_size_spin.setSuffix(" MB")
        self.max_size_spin.setDecimals(2)
        self.max_size_spin.setValue(100)  # 默认最大100MB
        size_layout.addWidget(self.max_size_spin)
        
        file_layout.addRow("文件大小 (MB):", size_layout)
        
        # 图像尺寸范围
        dimension_layout = QVBoxLayout()
        
        # 宽度范围
        width_layout = QHBoxLayout()
        self.min_width_spin = QSpinBox()
        self.min_width_spin.setRange(0, 99999)
        self.min_width_spin.setSuffix(" px")
        width_layout.addWidget(self.min_width_spin)
        
        width_layout.addWidget(self.widget_factory.create_label("到"))
        
        self.max_width_spin = QSpinBox()
        self.max_width_spin.setRange(0, 99999)
        self.max_width_spin.setSuffix(" px")
        self.max_width_spin.setValue(4096)  # 默认最大4096px
        width_layout.addWidget(self.max_width_spin)
        
        dimension_layout.addLayout(width_layout)
        
        # 高度范围
        height_layout = QHBoxLayout()
        self.min_height_spin = QSpinBox()
        self.min_height_spin.setRange(0, 99999)
        self.min_height_spin.setSuffix(" px")
        height_layout.addWidget(self.min_height_spin)
        
        height_layout.addWidget(self.widget_factory.create_label("到"))
        
        self.max_height_spin = QSpinBox()
        self.max_height_spin.setRange(0, 99999)
        self.max_height_spin.setSuffix(" px")
        self.max_height_spin.setValue(4096)  # 默认最大4096px
        height_layout.addWidget(self.max_height_spin)
        
        dimension_layout.addLayout(height_layout)
        
        file_layout.addRow("图像尺寸 (宽度/高度):", dimension_layout)
        
        layout.addWidget(file_group)
    
    def _setup_date_filters(self, layout):
        """设置日期范围过滤"""
        date_group = self.widget_factory.create_group_box("日期范围过滤")
        date_layout = QFormLayout(date_group)
        
        # 启用日期过滤
        self.enable_date_filter_cb = self.widget_factory.create_checkbox("启用日期过滤")
        date_layout.addRow("", self.enable_date_filter_cb)
        
        # 创建日期范围
        date_range_layout = QHBoxLayout()
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))  # 默认30天前
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setEnabled(False)
        date_range_layout.addWidget(self.start_date_edit)
        
        date_range_layout.addWidget(self.widget_factory.create_label("到"))
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setEnabled(False)
        date_range_layout.addWidget(self.end_date_edit)
        
        date_layout.addRow("日期范围:", date_range_layout)
        
        # 日期类型选择
        self.date_type_combo = QComboBox()
        self.date_type_combo.addItems(["创建日期", "修改日期", "访问日期"])
        self.date_type_combo.setEnabled(False)
        date_layout.addRow("日期类型:", self.date_type_combo)
        
        layout.addWidget(date_group)
    
    def _setup_content_filters(self, layout):
        """设置内容过滤"""
        content_group = self.widget_factory.create_group_box("内容过滤")
        content_layout = QFormLayout(content_group)
        
        # 关键词过滤
        self.keywords_input = QLineEdit()
        self.keywords_input.setPlaceholderText("用逗号分隔多个关键词")
        content_layout.addRow("关键词:", self.keywords_input)
        
        # 排除关键词
        self.exclude_keywords_input = QLineEdit()
        self.exclude_keywords_input.setPlaceholderText("用逗号分隔要排除的关键词")
        content_layout.addRow("排除关键词:", self.exclude_keywords_input)
        
        # 标签过滤
        self.tags_input = QLineEdit()
        self.tags_input.setPlaceholderText("用逗号分隔多个标签")
        content_layout.addRow("包含标签:", self.tags_input)
        
        # 类别过滤
        self.category_input = QLineEdit()
        self.category_input.setPlaceholderText("指定类别名称")
        content_layout.addRow("类别:", self.category_input)
        
        layout.addWidget(content_group)
    
    def _setup_search_params(self, layout):
        """设置搜索参数"""
        params_group = self.widget_factory.create_group_box("搜索参数")
        params_layout = QFormLayout(params_group)
        
        # 最大结果数
        try:
            from config.config_manager import ConfigManager
            config_manager = ConfigManager()
            default_max_results = config_manager.config.search.top_k
        except Exception:
            default_max_results = 50
            
        self.max_results_spin = self.widget_factory.create_spin_box(
            minimum=1, maximum=1000, value=default_max_results
        )
        params_layout.addRow("最大结果数:", self.max_results_spin)
        
        # 排序方式
        self.sort_combo = QComboBox()
        self.sort_combo.addItems([
            "相关性", "文件名", "文件大小", "创建时间", "修改时间"
        ])
        params_layout.addRow("排序方式:", self.sort_combo)
        
        # 排序顺序
        self.sort_order_combo = QComboBox()
        self.sort_order_combo.addItems(["降序", "升序"])
        params_layout.addRow("排序顺序:", self.sort_order_combo)
        
        layout.addWidget(params_group)
    
    def _setup_buttons(self, layout):
        """设置按钮"""
        button_layout = QHBoxLayout()
        
        # 重置按钮
        self.reset_btn = self.widget_factory.create_button(
            "重置", "refresh", None, ButtonStyle.WARNING, ButtonSize.MEDIUM,
            click_handler=self.reset_filters
        )
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        
        # 搜索按钮
        self.search_btn = self.widget_factory.create_button(
            "开始搜索", "search", None, ButtonStyle.SUCCESS, ButtonSize.LARGE,
            click_handler=self.start_search
        )
        button_layout.addWidget(self.search_btn)
        
        layout.addLayout(button_layout)
    
    def connect_signals(self):
        """连接信号"""
        # 日期过滤启用/禁用
        self.enable_date_filter_cb.stateChanged.connect(self._on_date_filter_toggled)
        
        # 回车搜索
        self.keywords_input.returnPressed.connect(self.start_search)
        self.exclude_keywords_input.returnPressed.connect(self.start_search)
        self.tags_input.returnPressed.connect(self.start_search)
        self.category_input.returnPressed.connect(self.start_search)
    
    def _on_date_filter_toggled(self, state):
        """日期过滤开关切换"""
        enabled = state == Qt.CheckState.Checked.value
        
        self.start_date_edit.setEnabled(enabled)
        self.end_date_edit.setEnabled(enabled)
        self.date_type_combo.setEnabled(enabled)
    
    def reset_filters(self):
        """重置所有过滤器"""
        try:
            # 重置文件属性过滤
            self.file_extension_input.clear()
            self.min_size_spin.setValue(0)
            self.max_size_spin.setValue(100)
            self.min_width_spin.setValue(0)
            self.max_width_spin.setValue(4096)
            self.min_height_spin.setValue(0)
            self.max_height_spin.setValue(4096)
            
            # 重置日期过滤
            self.enable_date_filter_cb.setChecked(False)
            self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
            self.end_date_edit.setDate(QDate.currentDate())
            self.date_type_combo.setCurrentIndex(0)
            
            # 重置内容过滤
            self.keywords_input.clear()
            self.exclude_keywords_input.clear()
            self.tags_input.clear()
            self.category_input.clear()
            
            # 重置搜索参数
            try:
                from config.config_manager import ConfigManager
                config_manager = ConfigManager()
                default_max_results = config_manager.config.search.top_k
            except Exception:
                default_max_results = 50
            
            self.max_results_spin.setValue(default_max_results)
            self.sort_combo.setCurrentIndex(0)
            self.sort_order_combo.setCurrentIndex(0)
            
            self.logger.info("所有过滤器已重置")
            MessageHelper.show_info(self, "提示", "所有过滤器已重置")
            
        except Exception as e:
            self.logger.error(f"重置过滤器失败: {e}")
            MessageHelper.show_error(self, "错误", f"重置过滤器失败: {e}")
    
    def start_search(self):
        """开始搜索"""
        try:
            # 检查是否有任何搜索条件
            if not self._has_search_criteria():
                MessageHelper.show_warning(self, "警告", "请至少设置一个搜索条件")
                return
            
            # 构建搜索参数
            params = self.get_search_params()
            
            # 构建查询字符串
            query = self._build_query_string()
            
            # 发送搜索请求
            self.searchRequested.emit(query, params)
            
        except Exception as e:
            self.logger.error(f"开始高级搜索失败: {e}")
            MessageHelper.show_error(self, "错误", f"开始搜索失败: {e}")
    
    def _has_search_criteria(self) -> bool:
        """检查是否有搜索条件"""
        return any([
            self.file_extension_input.text().strip(),
            self.min_size_spin.value() > 0,
            self.max_size_spin.value() < 999999,
            self.min_width_spin.value() > 0,
            self.max_width_spin.value() < 99999,
            self.min_height_spin.value() > 0,
            self.max_height_spin.value() < 99999,
            self.enable_date_filter_cb.isChecked(),
            self.keywords_input.text().strip(),
            self.exclude_keywords_input.text().strip(),
            self.tags_input.text().strip(),
            self.category_input.text().strip()
        ])
    
    def _build_query_string(self) -> str:
        """构建查询字符串"""
        query_parts = []
        
        # 添加关键词
        keywords = self.keywords_input.text().strip()
        if keywords:
            query_parts.append(keywords)
        
        # 添加标签
        tags = self.tags_input.text().strip()
        if tags:
            query_parts.append(f"tags:{tags}")
        
        # 添加类别
        category = self.category_input.text().strip()
        if category:
            query_parts.append(f"category:{category}")
        
        return " ".join(query_parts) if query_parts else "*"
    
    def get_search_params(self) -> Dict[str, Any]:
        """获取搜索参数"""
        params = {
            "max_results": self.max_results_spin.value(),
            "sort_by": self._get_sort_field(),
            "sort_order": "desc" if self.sort_order_combo.currentIndex() == 0 else "asc"
        }
        
        # 文件属性过滤
        file_filters = {}
        
        # 文件扩展名
        extensions = self.file_extension_input.text().strip()
        if extensions:
            ext_list = [ext.strip().lower() for ext in extensions.split(",") if ext.strip()]
            # 确保扩展名以点开头
            ext_list = [ext if ext.startswith('.') else f'.{ext}' for ext in ext_list]
            file_filters["extensions"] = ext_list
        
        # 文件大小
        min_size = self.min_size_spin.value()
        max_size = self.max_size_spin.value()
        if min_size > 0 or max_size < 999999:
            file_filters["size_range"] = {
                "min_mb": min_size,
                "max_mb": max_size
            }
        
        # 图像尺寸
        min_width = self.min_width_spin.value()
        max_width = self.max_width_spin.value()
        min_height = self.min_height_spin.value()
        max_height = self.max_height_spin.value()
        
        if (min_width > 0 or max_width < 99999 or 
            min_height > 0 or max_height < 99999):
            file_filters["dimensions"] = {
                "width_range": {"min": min_width, "max": max_width},
                "height_range": {"min": min_height, "max": max_height}
            }
        
        if file_filters:
            params["file_filters"] = file_filters
        
        # 日期过滤
        if self.enable_date_filter_cb.isChecked():
            start_date = self.start_date_edit.date().toPython()
            end_date = self.end_date_edit.date().toPython()
            date_type = self._get_date_type()
            
            params["date_filter"] = {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "date_type": date_type
            }
        
        # 内容过滤
        content_filters = {}
        
        # 排除关键词
        exclude_keywords = self.exclude_keywords_input.text().strip()
        if exclude_keywords:
            exclude_list = [kw.strip() for kw in exclude_keywords.split(",") if kw.strip()]
            content_filters["exclude_keywords"] = exclude_list
        
        # 标签过滤
        tags = self.tags_input.text().strip()
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
            content_filters["required_tags"] = tag_list
        
        # 类别过滤
        category = self.category_input.text().strip()
        if category:
            content_filters["category"] = category
        
        if content_filters:
            params["content_filters"] = content_filters
        
        return params
    
    def _get_sort_field(self) -> str:
        """获取排序字段"""
        sort_map = {
            0: "relevance",
            1: "filename",
            2: "file_size",
            3: "created_time",
            4: "modified_time"
        }
        return sort_map.get(self.sort_combo.currentIndex(), "relevance")
    
    def _get_date_type(self) -> str:
        """获取日期类型"""
        date_type_map = {
            0: "created",
            1: "modified",
            2: "accessed"
        }
        return date_type_map.get(self.date_type_combo.currentIndex(), "created")
    
    def get_date_range(self) -> Optional[Tuple[date, date]]:
        """获取日期范围"""
        if not self.enable_date_filter_cb.isChecked():
            return None
        
        start_date = self.start_date_edit.date().toPython()
        end_date = self.end_date_edit.date().toPython()
        
        return start_date, end_date
    
    def set_date_range(self, start_date: date, end_date: date):
        """设置日期范围"""
        self.start_date_edit.setDate(QDate.fromString(start_date.isoformat(), Qt.DateFormat.ISODate))
        self.end_date_edit.setDate(QDate.fromString(end_date.isoformat(), Qt.DateFormat.ISODate))
        self.enable_date_filter_cb.setChecked(True)
        
    def set_searching(self, is_searching: bool):
        """设置搜索状态
        
        Args:
            is_searching: 是否正在搜索
        """
        try:
            # 更新搜索按钮状态
            if hasattr(self, 'search_btn'):
                self.search_btn.setEnabled(not is_searching)
                self.search_btn.setText("停止搜索" if is_searching else "搜索")
            
            # 更新重置按钮状态
            if hasattr(self, 'reset_btn'):
                self.reset_btn.setEnabled(not is_searching)
            
            # 记录日志
            self.logger.debug(f"高级搜索组件状态已更新: {'搜索中' if is_searching else '空闲'}")
            
        except Exception as e:
            self.logger.error(f"设置搜索状态失败: {e}")