"""性能配置模块

定义系统性能相关的配置参数。
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from enum import Enum


class MemoryStrategy(Enum):
    """内存策略枚举"""
    CONSERVATIVE = "conservative"  # 保守策略，低内存使用
    BALANCED = "balanced"  # 平衡策略
    AGGRESSIVE = "aggressive"  # 激进策略，高性能
    CUSTOM = "custom"  # 自定义策略


class ThreadingStrategy(Enum):
    """线程策略枚举"""
    SINGLE = "single"  # 单线程
    MULTI = "multi"  # 多线程
    ADAPTIVE = "adaptive"  # 自适应


class CacheStrategy(Enum):
    """缓存策略枚举"""
    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    FIFO = "fifo"  # 先进先出
    RANDOM = "random"  # 随机淘汰


@dataclass
class MemoryConfig:
    """内存配置"""
    strategy: MemoryStrategy = MemoryStrategy.BALANCED
    
    # 内存限制 (MB)
    max_memory_mb: int = 2048
    feature_cache_mb: int = 512
    model_cache_mb: int = 1024
    result_cache_mb: int = 256
    
    # 内存监控
    enable_memory_monitoring: bool = True
    memory_warning_threshold: float = 0.8  # 80%
    memory_critical_threshold: float = 0.95  # 95%
    
    # 垃圾回收
    enable_gc_optimization: bool = True
    gc_threshold_ratio: float = 0.1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'strategy': self.strategy.value,
            'max_memory_mb': self.max_memory_mb,
            'feature_cache_mb': self.feature_cache_mb,
            'model_cache_mb': self.model_cache_mb,
            'result_cache_mb': self.result_cache_mb,
            'enable_memory_monitoring': self.enable_memory_monitoring,
            'memory_warning_threshold': self.memory_warning_threshold,
            'memory_critical_threshold': self.memory_critical_threshold,
            'enable_gc_optimization': self.enable_gc_optimization,
            'gc_threshold_ratio': self.gc_threshold_ratio
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryConfig':
        """从字典创建"""
        return cls(
            strategy=MemoryStrategy(data.get('strategy', 'balanced')),
            max_memory_mb=data.get('max_memory_mb', 2048),
            feature_cache_mb=data.get('feature_cache_mb', 512),
            model_cache_mb=data.get('model_cache_mb', 1024),
            result_cache_mb=data.get('result_cache_mb', 256),
            enable_memory_monitoring=data.get('enable_memory_monitoring', True),
            memory_warning_threshold=data.get('memory_warning_threshold', 0.8),
            memory_critical_threshold=data.get('memory_critical_threshold', 0.95),
            enable_gc_optimization=data.get('enable_gc_optimization', True),
            gc_threshold_ratio=data.get('gc_threshold_ratio', 0.1)
        )


@dataclass
class ThreadingConfig:
    """线程配置"""
    strategy: ThreadingStrategy = ThreadingStrategy.ADAPTIVE
    
    # 线程池配置
    max_workers: int = 4
    feature_extraction_workers: int = 2
    search_workers: int = 2
    io_workers: int = 2
    
    # 线程优先级
    enable_thread_priority: bool = False
    high_priority_tasks: list = field(default_factory=lambda: ['search', 'feature_extraction'])
    
    # 线程监控
    enable_thread_monitoring: bool = True
    thread_timeout_seconds: int = 300
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'strategy': self.strategy.value,
            'max_workers': self.max_workers,
            'feature_extraction_workers': self.feature_extraction_workers,
            'search_workers': self.search_workers,
            'io_workers': self.io_workers,
            'enable_thread_priority': self.enable_thread_priority,
            'high_priority_tasks': self.high_priority_tasks,
            'enable_thread_monitoring': self.enable_thread_monitoring,
            'thread_timeout_seconds': self.thread_timeout_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ThreadingConfig':
        """从字典创建"""
        return cls(
            strategy=ThreadingStrategy(data.get('strategy', 'adaptive')),
            max_workers=data.get('max_workers', 4),
            feature_extraction_workers=data.get('feature_extraction_workers', 2),
            search_workers=data.get('search_workers', 2),
            io_workers=data.get('io_workers', 2),
            enable_thread_priority=data.get('enable_thread_priority', False),
            high_priority_tasks=data.get('high_priority_tasks', ['search', 'feature_extraction']),
            enable_thread_monitoring=data.get('enable_thread_monitoring', True),
            thread_timeout_seconds=data.get('thread_timeout_seconds', 300)
        )


@dataclass
class CacheConfig:
    """缓存配置"""
    strategy: CacheStrategy = CacheStrategy.LRU
    
    # 特征缓存
    feature_cache_size: int = 10000
    feature_cache_ttl_seconds: int = 3600
    
    # 模型缓存
    model_cache_size: int = 5
    model_cache_ttl_seconds: int = 7200
    
    # 结果缓存
    result_cache_size: int = 1000
    result_cache_ttl_seconds: int = 1800
    
    # 索引缓存
    index_cache_size: int = 10
    index_cache_ttl_seconds: int = 3600
    
    # 缓存预热
    enable_cache_warmup: bool = True
    warmup_batch_size: int = 100
    
    # 缓存统计
    enable_cache_stats: bool = True
    stats_report_interval_seconds: int = 300
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'strategy': self.strategy.value,
            'feature_cache_size': self.feature_cache_size,
            'feature_cache_ttl_seconds': self.feature_cache_ttl_seconds,
            'model_cache_size': self.model_cache_size,
            'model_cache_ttl_seconds': self.model_cache_ttl_seconds,
            'result_cache_size': self.result_cache_size,
            'result_cache_ttl_seconds': self.result_cache_ttl_seconds,
            'index_cache_size': self.index_cache_size,
            'index_cache_ttl_seconds': self.index_cache_ttl_seconds,
            'enable_cache_warmup': self.enable_cache_warmup,
            'warmup_batch_size': self.warmup_batch_size,
            'enable_cache_stats': self.enable_cache_stats,
            'stats_report_interval_seconds': self.stats_report_interval_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheConfig':
        """从字典创建"""
        return cls(
            strategy=CacheStrategy(data.get('strategy', 'lru')),
            feature_cache_size=data.get('feature_cache_size', 10000),
            feature_cache_ttl_seconds=data.get('feature_cache_ttl_seconds', 3600),
            model_cache_size=data.get('model_cache_size', 5),
            model_cache_ttl_seconds=data.get('model_cache_ttl_seconds', 7200),
            result_cache_size=data.get('result_cache_size', 1000),
            result_cache_ttl_seconds=data.get('result_cache_ttl_seconds', 1800),
            index_cache_size=data.get('index_cache_size', 10),
            index_cache_ttl_seconds=data.get('index_cache_ttl_seconds', 3600),
            enable_cache_warmup=data.get('enable_cache_warmup', True),
            warmup_batch_size=data.get('warmup_batch_size', 100),
            enable_cache_stats=data.get('enable_cache_stats', True),
            stats_report_interval_seconds=data.get('stats_report_interval_seconds', 300)
        )


@dataclass
class IOConfig:
    """IO配置"""
    # 批处理配置
    batch_size: int = 32
    max_batch_size: int = 128
    
    # 并发配置
    max_concurrent_reads: int = 10
    max_concurrent_writes: int = 5
    
    # 超时配置
    read_timeout_seconds: int = 30
    write_timeout_seconds: int = 60
    
    # 重试配置
    max_retries: int = 3
    retry_delay_seconds: float = 1.0
    
    # 缓冲配置
    read_buffer_size: int = 8192
    write_buffer_size: int = 8192
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'batch_size': self.batch_size,
            'max_batch_size': self.max_batch_size,
            'max_concurrent_reads': self.max_concurrent_reads,
            'max_concurrent_writes': self.max_concurrent_writes,
            'read_timeout_seconds': self.read_timeout_seconds,
            'write_timeout_seconds': self.write_timeout_seconds,
            'max_retries': self.max_retries,
            'retry_delay_seconds': self.retry_delay_seconds,
            'read_buffer_size': self.read_buffer_size,
            'write_buffer_size': self.write_buffer_size
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'IOConfig':
        """从字典创建"""
        return cls(
            batch_size=data.get('batch_size', 32),
            max_batch_size=data.get('max_batch_size', 128),
            max_concurrent_reads=data.get('max_concurrent_reads', 10),
            max_concurrent_writes=data.get('max_concurrent_writes', 5),
            read_timeout_seconds=data.get('read_timeout_seconds', 30),
            write_timeout_seconds=data.get('write_timeout_seconds', 60),
            max_retries=data.get('max_retries', 3),
            retry_delay_seconds=data.get('retry_delay_seconds', 1.0),
            read_buffer_size=data.get('read_buffer_size', 8192),
            write_buffer_size=data.get('write_buffer_size', 8192)
        )


@dataclass
class PerformanceConfig:
    """性能配置主类"""
    # 子配置
    memory: MemoryConfig = field(default_factory=MemoryConfig)
    threading: ThreadingConfig = field(default_factory=ThreadingConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    io: IOConfig = field(default_factory=IOConfig)
    
    # 性能监控
    enable_performance_monitoring: bool = True
    monitoring_interval_seconds: int = 60
    
    # 性能优化
    enable_auto_optimization: bool = False
    optimization_interval_seconds: int = 300
    
    # 性能报告
    enable_performance_reporting: bool = False
    report_interval_seconds: int = 3600
    report_file_path: Optional[str] = None
    
    # 调试配置
    enable_profiling: bool = False
    profiling_output_dir: str = "./profiling"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'memory': self.memory.to_dict(),
            'threading': self.threading.to_dict(),
            'cache': self.cache.to_dict(),
            'io': self.io.to_dict(),
            'enable_performance_monitoring': self.enable_performance_monitoring,
            'monitoring_interval_seconds': self.monitoring_interval_seconds,
            'enable_auto_optimization': self.enable_auto_optimization,
            'optimization_interval_seconds': self.optimization_interval_seconds,
            'enable_performance_reporting': self.enable_performance_reporting,
            'report_interval_seconds': self.report_interval_seconds,
            'report_file_path': self.report_file_path,
            'enable_profiling': self.enable_profiling,
            'profiling_output_dir': self.profiling_output_dir
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PerformanceConfig':
        """从字典创建"""
        return cls(
            memory=MemoryConfig.from_dict(data.get('memory', {})),
            threading=ThreadingConfig.from_dict(data.get('threading', {})),
            cache=CacheConfig.from_dict(data.get('cache', {})),
            io=IOConfig.from_dict(data.get('io', {})),
            enable_performance_monitoring=data.get('enable_performance_monitoring', True),
            monitoring_interval_seconds=data.get('monitoring_interval_seconds', 60),
            enable_auto_optimization=data.get('enable_auto_optimization', False),
            optimization_interval_seconds=data.get('optimization_interval_seconds', 300),
            enable_performance_reporting=data.get('enable_performance_reporting', False),
            report_interval_seconds=data.get('report_interval_seconds', 3600),
            report_file_path=data.get('report_file_path'),
            enable_profiling=data.get('enable_profiling', False),
            profiling_output_dir=data.get('profiling_output_dir', './profiling')
        )
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            # 检查内存配置
            if self.memory.max_memory_mb <= 0:
                return False
            total_cache = (self.memory.feature_cache_mb + 
                          self.memory.model_cache_mb + 
                          self.memory.result_cache_mb)
            if total_cache > self.memory.max_memory_mb:
                return False
                
            # 检查线程配置
            if self.threading.max_workers <= 0:
                return False
            if (self.threading.feature_extraction_workers + 
                self.threading.search_workers + 
                self.threading.io_workers > self.threading.max_workers * 2):
                return False
                
            # 检查缓存配置
            if any(size <= 0 for size in [
                self.cache.feature_cache_size,
                self.cache.model_cache_size,
                self.cache.result_cache_size,
                self.cache.index_cache_size
            ]):
                return False
                
            # 检查IO配置
            if self.io.batch_size <= 0 or self.io.max_batch_size <= 0:
                return False
            if self.io.batch_size > self.io.max_batch_size:
                return False
                
            # 检查监控间隔
            if self.monitoring_interval_seconds <= 0:
                return False
                
            return True
            
        except Exception:
            return False
    
    def get_memory_limits(self) -> Dict[str, int]:
        """获取内存限制"""
        return {
            'total': self.memory.max_memory_mb,
            'feature_cache': self.memory.feature_cache_mb,
            'model_cache': self.memory.model_cache_mb,
            'result_cache': self.memory.result_cache_mb
        }
    
    def get_thread_limits(self) -> Dict[str, int]:
        """获取线程限制"""
        return {
            'max_workers': self.threading.max_workers,
            'feature_extraction': self.threading.feature_extraction_workers,
            'search': self.threading.search_workers,
            'io': self.threading.io_workers
        }
    
    def optimize_for_system(self, available_memory_mb: int, cpu_cores: int) -> None:
        """根据系统资源优化配置"""
        # 优化内存配置
        self.memory.max_memory_mb = min(self.memory.max_memory_mb, 
                                       int(available_memory_mb * 0.8))
        
        # 重新分配缓存内存
        total_cache = (self.memory.feature_cache_mb + 
                      self.memory.model_cache_mb + 
                      self.memory.result_cache_mb)
        if total_cache > self.memory.max_memory_mb * 0.7:
            ratio = (self.memory.max_memory_mb * 0.7) / total_cache
            self.memory.feature_cache_mb = int(self.memory.feature_cache_mb * ratio)
            self.memory.model_cache_mb = int(self.memory.model_cache_mb * ratio)
            self.memory.result_cache_mb = int(self.memory.result_cache_mb * ratio)
        
        # 优化线程配置
        self.threading.max_workers = min(self.threading.max_workers, cpu_cores)
        self.threading.feature_extraction_workers = min(
            self.threading.feature_extraction_workers, 
            max(1, cpu_cores // 2)
        )
        self.threading.search_workers = min(
            self.threading.search_workers,
            max(1, cpu_cores // 4)
        )
        self.threading.io_workers = min(
            self.threading.io_workers,
            max(1, cpu_cores // 4)
        )