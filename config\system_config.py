#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置管理模块

该模块实现系统配置的加载、保存和验证功能。
"""

import json
import yaml
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
from dataclasses import dataclass, asdict, field
from datetime import datetime

from utils.log_utils import LoggerMixin


@dataclass
class UIConfig:
    """用户界面配置"""
    theme: str = 'light'                    # 主题: light, dark
    language: str = 'zh_CN'                 # 语言: zh_CN, en_US
    window_width: int = 1200                # 窗口宽度
    window_height: int = 800                # 窗口高度
    window_maximized: bool = False          # 是否最大化
    splitter_sizes: List[int] = field(default_factory=lambda: [300, 900])  # 分割器尺寸
    show_toolbar: bool = True               # 是否显示工具栏
    show_statusbar: bool = True             # 是否显示状态栏
    grid_view_columns: int = 4              # 网格视图列数
    thumbnail_size: int = 150               # 缩略图尺寸
    auto_save_settings: bool = True         # 是否自动保存设置
    recent_files_limit: int = 10            # 最近文件数量限制


@dataclass
class SearchConfig:
    """搜索配置"""
    default_search_mode: str = 'similarity'     # 默认搜索模式
    max_results: int = 100                      # 最大结果数量
    similarity_threshold: float = 0.5           # 相似度阈值
    enable_gpu: bool = True                     # 是否启用GPU
    batch_size: int = 32                        # 批处理大小
    num_workers: int = 4                        # 工作线程数
    cache_features: bool = True                 # 是否缓存特征
    cache_size_mb: int = 512                    # 缓存大小(MB)
    auto_update_index: bool = True              # 是否自动更新索引
    search_timeout: int = 30                    # 搜索超时时间(秒)
    
    # 特征权重
    feature_weights: Dict[str, float] = field(default_factory=lambda: {
        'color': 0.3,
        'texture': 0.3,
        'shape': 0.2,
        'deep_features': 0.2
    })


@dataclass
class PerformanceConfig:
    """性能配置"""
    max_memory_usage_mb: int = 4096         # 增加最大内存使用(MB)
    enable_multiprocessing: bool = True     # 是否启用多进程
    max_processes: int = 8                  # 增加最大进程数
    enable_threading: bool = True           # 是否启用多线程
    max_threads: int = 16                   # 增加最大线程数
    io_buffer_size: int = 65536            # 增加IO缓冲区大小(64KB)
    enable_compression: bool = True         # 是否启用压缩
    compression_level: int = 3              # 降低压缩级别以提高速度
    enable_profiling: bool = False          # 是否启用性能分析

    # 新增性能优化配置
    batch_processing: 'BatchPerformanceConfig' = field(default_factory=lambda: BatchPerformanceConfig())
    database: 'DatabasePerformanceConfig' = field(default_factory=lambda: DatabasePerformanceConfig())
    cache: 'CachePerformanceConfig' = field(default_factory=lambda: CachePerformanceConfig())
    io: 'IOPerformanceConfig' = field(default_factory=lambda: IOPerformanceConfig())


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = 'INFO'                     # 日志级别
    format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    file_handler: bool = True               # 是否启用文件处理器
    console_handler: bool = True            # 是否启用控制台处理器
    log_file: str = 'logs/fabric_search.log'  # 日志文件路径
    max_file_size_mb: int = 10              # 最大文件大小(MB)
    backup_count: int = 5                   # 备份文件数量
    rotation: str = 'size'                  # 轮转方式: size, time


@dataclass
class BatchPerformanceConfig:
    """批处理性能配置"""
    batch_size: int = 64                    # 批处理大小
    max_workers: int = 8                    # 最大工作线程数
    chunk_size: int = 500                   # 块大小
    enable_batch_insert: bool = True        # 启用批量插入
    prefetch_factor: int = 4                # 预取因子
    memory_pool_size_mb: int = 1024         # 内存池大小(MB)


@dataclass
class DatabasePerformanceConfig:
    """数据库性能配置"""
    batch_size: int = 100                   # 数据库批量操作大小
    connection_pool_size: int = 20          # 连接池大小
    max_overflow: int = 30                  # 最大溢出连接数
    pool_timeout: int = 30                  # 连接池超时时间
    enable_wal_mode: bool = True            # 启用WAL模式
    cache_size: int = 10000                 # 数据库缓存大小
    synchronous: str = 'NORMAL'             # 同步模式
    journal_mode: str = 'WAL'               # 日志模式


@dataclass
class CachePerformanceConfig:
    """缓存性能配置"""
    feature_cache_size: int = 50000         # 特征缓存大小
    image_cache_size_mb: int = 512          # 图像缓存大小(MB)
    enable_lru_cache: bool = True           # 启用LRU缓存
    cache_ttl_seconds: int = 3600           # 缓存TTL(秒)
    enable_compression: bool = True         # 启用缓存压缩
    compression_level: int = 3              # 缓存压缩级别


@dataclass
class IOPerformanceConfig:
    """I/O性能配置"""
    buffer_size: int = 65536                # I/O缓冲区大小(64KB)
    enable_async_io: bool = True            # 启用异步I/O
    max_concurrent_reads: int = 16          # 最大并发读取数
    enable_memory_mapping: bool = True      # 启用内存映射
    read_ahead_size: int = 131072           # 预读大小(128KB)


@dataclass
class SecurityConfig:
    """安全配置"""
    enable_encryption: bool = False         # 是否启用加密
    encryption_key: Optional[str] = None    # 加密密钥
    enable_backup: bool = True              # 是否启用备份
    backup_interval_hours: int = 24         # 备份间隔(小时)
    max_backup_files: int = 7               # 最大备份文件数
    enable_integrity_check: bool = True     # 是否启用完整性检查
    checksum_algorithm: str = 'sha256'      # 校验和算法


@dataclass
class SystemConfig:
    """系统配置"""
    version: str = '2.0.0'                  # 系统版本
    config_version: str = '1.0'             # 配置版本
    last_updated: str = field(default_factory=lambda: datetime.now().isoformat())
    
    # 子配置
    ui: UIConfig = field(default_factory=UIConfig)
    search: SearchConfig = field(default_factory=SearchConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    
    # 自定义配置
    custom: Dict[str, Any] = field(default_factory=dict)


class ConfigManager(LoggerMixin):
    """配置管理器"""
    
    def __init__(self, config_file: Optional[Union[str, Path]] = None):
        super().__init__()
        
        # 默认配置文件路径
        if config_file is None:
            config_file = Path.cwd() / 'config' / 'system_config.yaml'
        
        self.config_file = Path(config_file)
        self.config = SystemConfig()
        
        # 配置验证规则
        self.validation_rules = {
            'ui.theme': ['light', 'dark'],
            'ui.language': ['zh_CN', 'en_US'],
            'search.default_search_mode': ['similarity', 'color', 'texture', 'shape'],
            'logging.level': ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
            'logging.rotation': ['size', 'time'],
            'security.checksum_algorithm': ['md5', 'sha1', 'sha256', 'sha512']
        }
    
    def load_config(self, config_file: Optional[Union[str, Path]] = None) -> bool:
        """
        加载配置文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            if config_file is not None:
                self.config_file = Path(config_file)
            
            if not self.config_file.exists():
                self.logger.info(f"配置文件不存在，使用默认配置: {self.config_file}")
                return self.save_config()  # 保存默认配置
            
            # 根据文件扩展名选择加载方式
            if self.config_file.suffix.lower() in ['.yaml', '.yml']:
                config_data = self._load_yaml()
            elif self.config_file.suffix.lower() == '.json':
                config_data = self._load_json()
            else:
                raise ValueError(f"不支持的配置文件格式: {self.config_file.suffix}")
            
            # 更新配置
            self._update_config_from_dict(config_data)
            
            # 验证配置
            if not self.validate_config():
                self.logger.warning("配置验证失败，使用默认配置")
                self.config = SystemConfig()
            
            self.logger.info(f"配置加载成功: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载配置失败: {str(e)}")
            return False
    
    def save_config(self, config_file: Optional[Union[str, Path]] = None) -> bool:
        """
        保存配置文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            if config_file is not None:
                self.config_file = Path(config_file)
            
            # 创建目录
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 更新时间戳
            self.config.last_updated = datetime.now().isoformat()
            
            # 根据文件扩展名选择保存方式
            if self.config_file.suffix.lower() in ['.yaml', '.yml']:
                success = self._save_yaml()
            elif self.config_file.suffix.lower() == '.json':
                success = self._save_json()
            else:
                raise ValueError(f"不支持的配置文件格式: {self.config_file.suffix}")
            
            if success:
                self.logger.info(f"配置保存成功: {self.config_file}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {str(e)}")
            return False
    
    def _load_yaml(self) -> Dict[str, Any]:
        """加载YAML配置"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    
    def _load_json(self) -> Dict[str, Any]:
        """加载JSON配置"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _save_yaml(self) -> bool:
        """保存YAML配置"""
        try:
            config_dict = asdict(self.config)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
            return True
        except Exception as e:
            self.logger.error(f"保存YAML配置失败: {str(e)}")
            return False
    
    def _save_json(self) -> bool:
        """保存JSON配置"""
        try:
            config_dict = asdict(self.config)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            self.logger.error(f"保存JSON配置失败: {str(e)}")
            return False
    
    def _update_config_from_dict(self, config_data: Dict[str, Any]) -> None:
        """从字典更新配置"""
        def update_dataclass(obj, data):
            if not isinstance(data, dict):
                return
            
            for key, value in data.items():
                if hasattr(obj, key):
                    attr = getattr(obj, key)
                    if hasattr(attr, '__dataclass_fields__'):  # 嵌套dataclass
                        update_dataclass(attr, value)
                    else:
                        setattr(obj, key, value)
        
        update_dataclass(self.config, config_data)
    
    def validate_config(self) -> bool:
        """
        验证配置
        
        Returns:
            bool: 验证是否通过
        """
        try:
            # 验证枚举值
            for path, valid_values in self.validation_rules.items():
                value = self.get_config_value(path)
                if value is not None and value not in valid_values:
                    self.logger.error(f"配置项 {path} 的值 {value} 不在有效范围内: {valid_values}")
                    return False
            
            # 验证数值范围
            if not (0 < self.config.ui.window_width <= 4096):
                self.logger.error("窗口宽度必须在1-4096之间")
                return False
            
            if not (0 < self.config.ui.window_height <= 2160):
                self.logger.error("窗口高度必须在1-2160之间")
                return False
            
            if not (0.0 <= self.config.search.similarity_threshold <= 1.0):
                self.logger.error("相似度阈值必须在0.0-1.0之间")
                return False
            
            if not (1 <= self.config.search.max_results <= 10000):
                self.logger.error("最大结果数量必须在1-10000之间")
                return False
            
            # 验证特征权重
            weights = self.config.search.feature_weights
            if abs(sum(weights.values()) - 1.0) > 0.01:
                self.logger.error("特征权重总和必须等于1.0")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {str(e)}")
            return False
    
    def get_config_value(self, path: str) -> Any:
        """
        获取配置值
        
        Args:
            path: 配置路径，如 'ui.theme'
            
        Returns:
            Any: 配置值
        """
        try:
            obj = self.config
            for part in path.split('.'):
                obj = getattr(obj, part)
            return obj
        except AttributeError:
            return None
    
    def set_config_value(self, path: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            path: 配置路径
            value: 配置值
            
        Returns:
            bool: 设置是否成功
        """
        try:
            parts = path.split('.')
            obj = self.config
            
            # 导航到父对象
            for part in parts[:-1]:
                obj = getattr(obj, part)
            
            # 设置值
            setattr(obj, parts[-1], value)
            
            # 验证配置
            if self.validate_config():
                return True
            else:
                self.logger.error(f"设置配置值后验证失败: {path} = {value}")
                return False
                
        except Exception as e:
            self.logger.error(f"设置配置值失败: {path} = {value} - {str(e)}")
            return False
    
    def reset_to_defaults(self) -> None:
        """重置为默认配置"""
        self.config = SystemConfig()
        self.logger.info("配置已重置为默认值")
    
    def backup_config(self, backup_dir: Optional[Union[str, Path]] = None) -> bool:
        """
        备份配置文件
        
        Args:
            backup_dir: 备份目录
            
        Returns:
            bool: 备份是否成功
        """
        try:
            if backup_dir is None:
                backup_dir = self.config_file.parent / 'backups'
            
            backup_dir = Path(backup_dir)
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = backup_dir / f"{self.config_file.stem}_{timestamp}{self.config_file.suffix}"
            
            # 复制配置文件
            import shutil
            shutil.copy2(self.config_file, backup_file)
            
            self.logger.info(f"配置备份成功: {backup_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"配置备份失败: {str(e)}")
            return False
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            'config_file': str(self.config_file),
            'version': self.config.version,
            'config_version': self.config.config_version,
            'last_updated': self.config.last_updated,
            'file_exists': self.config_file.exists(),
            'file_size': self.config_file.stat().st_size if self.config_file.exists() else 0
        }


# 全局配置管理器实例
_config_manager = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
        _config_manager.load_config()
    return _config_manager


def get_config() -> SystemConfig:
    """获取系统配置"""
    return get_config_manager().config