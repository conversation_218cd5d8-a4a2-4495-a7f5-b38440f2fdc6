#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像对比工具栏

该模块提供图像对比面板的工具栏组件。
"""

from typing import Optional
from PyQt6.QtWidgets import (
    QWidget, QHBoxLayout, QPushButton, QCheckBox, QFrame, QSpacerItem, QSizePolicy
)
from PyQt6.QtCore import pyqtSignal, Qt
from PyQt6.QtGui import QKeySequence, QShortcut

from gui.widgets import WidgetFactory, ButtonStyle, ButtonSize
from .compare_config import CompareConfig


class CompareToolbar(QWidget):
    """图像对比工具栏"""
    
    # 信号
    zoomInRequested = pyqtSignal()
    zoomOutRequested = pyqtSignal()
    fitToWindowRequested = pyqtSignal()
    actualSizeRequested = pyqtSignal()
    resetViewRequested = pyqtSignal()
    syncZoomToggled = pyqtSignal(bool)
    syncPanToggled = pyqtSignal(bool)
    closeRequested = pyqtSignal()
    
    def __init__(self, config: Optional[CompareConfig] = None, parent=None):
        super().__init__(parent)
        
        self.config = config or CompareConfig()
        self.widget_factory = WidgetFactory()
        
        self.setup_ui()
        self.setup_shortcuts()
        self.setup_connections()
    
    def setup_ui(self):
        """设置界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 缩放控制按钮
        self.zoom_in_button = self.widget_factory.create_button(
            "放大", 
            icon_name="zoom-in",
            style=ButtonStyle.SECONDARY,
            size=ButtonSize.MEDIUM,
            tooltip="放大图像 (Ctrl++)"
        )
        layout.addWidget(self.zoom_in_button)
        
        self.zoom_out_button = self.widget_factory.create_button(
            "缩小", 
            icon_name="zoom-out",
            style=ButtonStyle.SECONDARY,
            size=ButtonSize.MEDIUM,
            tooltip="缩小图像 (Ctrl+-)"
        )
        layout.addWidget(self.zoom_out_button)
        
        self.fit_window_button = self.widget_factory.create_button(
            "适应窗口", 
            icon_name="fit-to-window",
            style=ButtonStyle.SECONDARY,
            size=ButtonSize.MEDIUM,
            tooltip="适应窗口 (Ctrl+0)"
        )
        layout.addWidget(self.fit_window_button)
        
        self.actual_size_button = self.widget_factory.create_button(
            "实际大小", 
            icon_name="actual-size",
            style=ButtonStyle.SECONDARY,
            size=ButtonSize.MEDIUM,
            tooltip="实际大小 (Ctrl+1)"
        )
        layout.addWidget(self.actual_size_button)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
        
        # 同步选项
        self.sync_zoom_checkbox = QCheckBox("同步缩放")
        self.sync_zoom_checkbox.setChecked(self.config.sync_zoom)
        layout.addWidget(self.sync_zoom_checkbox)
        
        self.sync_pan_checkbox = QCheckBox("同步平移")
        self.sync_pan_checkbox.setChecked(self.config.sync_pan)
        layout.addWidget(self.sync_pan_checkbox)
        
        # 弹性空间
        spacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        layout.addItem(spacer)
        
        # 重置和关闭按钮
        self.reset_button = self.widget_factory.create_button(
            "重置视图", 
            icon_name="refresh",
            style=ButtonStyle.PRIMARY,
            size=ButtonSize.MEDIUM,
            tooltip="重置视图 (Ctrl+R)"
        )
        layout.addWidget(self.reset_button)
        
        self.close_button = self.widget_factory.create_button(
            "关闭", 
            icon_name="close",
            style=ButtonStyle.PRIMARY,
            size=ButtonSize.MEDIUM
        )
        layout.addWidget(self.close_button)
    
    def setup_shortcuts(self):
        """设置快捷键"""
        if not self.config.enable_shortcuts:
            return
        
        # 放大快捷键 (Ctrl++)
        self.zoom_in_shortcut = QShortcut(QKeySequence("Ctrl++"), self)
        self.zoom_in_shortcut.activated.connect(self.zoomInRequested.emit)
        
        # 缩小快捷键 (Ctrl+-)
        self.zoom_out_shortcut = QShortcut(QKeySequence("Ctrl+-"), self)
        self.zoom_out_shortcut.activated.connect(self.zoomOutRequested.emit)
        
        # 适应窗口快捷键 (Ctrl+0)
        self.fit_window_shortcut = QShortcut(QKeySequence("Ctrl+0"), self)
        self.fit_window_shortcut.activated.connect(self.fitToWindowRequested.emit)
        
        # 实际大小快捷键 (Ctrl+1)
        self.actual_size_shortcut = QShortcut(QKeySequence("Ctrl+1"), self)
        self.actual_size_shortcut.activated.connect(self.actualSizeRequested.emit)
        
        # 重置视图快捷键 (Ctrl+R)
        self.reset_shortcut = QShortcut(QKeySequence("Ctrl+R"), self)
        self.reset_shortcut.activated.connect(self.resetViewRequested.emit)
    
    def setup_connections(self):
        """设置信号连接"""
        # 按钮信号
        self.zoom_in_button.clicked.connect(self.zoomInRequested.emit)
        self.zoom_out_button.clicked.connect(self.zoomOutRequested.emit)
        self.fit_window_button.clicked.connect(self.fitToWindowRequested.emit)
        self.actual_size_button.clicked.connect(self.actualSizeRequested.emit)
        self.reset_button.clicked.connect(self.resetViewRequested.emit)
        self.close_button.clicked.connect(self.closeRequested.emit)
        
        # 同步选项信号
        self.sync_zoom_checkbox.toggled.connect(self.syncZoomToggled.emit)
        self.sync_pan_checkbox.toggled.connect(self.syncPanToggled.emit)
    
    def set_sync_zoom(self, enabled: bool):
        """设置同步缩放状态
        
        Args:
            enabled: 是否启用
        """
        self.sync_zoom_checkbox.setChecked(enabled)
    
    def set_sync_pan(self, enabled: bool):
        """设置同步平移状态
        
        Args:
            enabled: 是否启用
        """
        self.sync_pan_checkbox.setChecked(enabled)
    
    def is_sync_zoom_enabled(self) -> bool:
        """获取同步缩放状态
        
        Returns:
            bool: 是否启用同步缩放
        """
        return self.sync_zoom_checkbox.isChecked()
    
    def is_sync_pan_enabled(self) -> bool:
        """获取同步平移状态
        
        Returns:
            bool: 是否启用同步平移
        """
        return self.sync_pan_checkbox.isChecked()