"""模型配置模块

定义深度学习模型相关的配置参数。
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from pathlib import Path


class ModelType(Enum):
    """模型类型枚举"""
    RESNET = "resnet"
    VGG = "vgg"
    INCEPTION = "inception"
    MOBILENET = "mobilenet"
    EFFICIENTNET = "efficientnet"
    DENSENET = "densenet"
    CLIP = "clip"
    DINO = "dino"
    CUSTOM = "custom"


class ModelBackend(Enum):
    """模型后端枚举"""
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"
    ONNX = "onnx"
    TENSORRT = "tensorrt"


class OptimizationLevel(Enum):
    """优化级别枚举"""
    NONE = "none"
    BASIC = "basic"
    ADVANCED = "advanced"
    AGGRESSIVE = "aggressive"


@dataclass
class ModelWeights:
    """模型权重配置"""
    source: str = "pretrained"  # pretrained, local, url
    path: Optional[str] = None
    url: Optional[str] = None
    checksum: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'source': self.source,
            'path': self.path,
            'url': self.url,
            'checksum': self.checksum
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelWeights':
        """从字典创建"""
        return cls(
            source=data.get('source', 'pretrained'),
            path=data.get('path'),
            url=data.get('url'),
            checksum=data.get('checksum')
        )


@dataclass
class ModelOptimization:
    """模型优化配置"""
    level: OptimizationLevel = OptimizationLevel.BASIC
    
    # 量化配置
    enable_quantization: bool = False
    quantization_bits: int = 8
    quantization_method: str = "dynamic"  # dynamic, static, qat
    
    # 剪枝配置
    enable_pruning: bool = False
    pruning_ratio: float = 0.1
    
    # 蒸馏配置
    enable_distillation: bool = False
    teacher_model: Optional[str] = None
    
    # TensorRT配置
    enable_tensorrt: bool = False
    tensorrt_precision: str = "fp16"  # fp32, fp16, int8
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'level': self.level.value,
            'enable_quantization': self.enable_quantization,
            'quantization_bits': self.quantization_bits,
            'quantization_method': self.quantization_method,
            'enable_pruning': self.enable_pruning,
            'pruning_ratio': self.pruning_ratio,
            'enable_distillation': self.enable_distillation,
            'teacher_model': self.teacher_model,
            'enable_tensorrt': self.enable_tensorrt,
            'tensorrt_precision': self.tensorrt_precision
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelOptimization':
        """从字典创建"""
        return cls(
            level=OptimizationLevel(data.get('level', 'basic')),
            enable_quantization=data.get('enable_quantization', False),
            quantization_bits=data.get('quantization_bits', 8),
            quantization_method=data.get('quantization_method', 'dynamic'),
            enable_pruning=data.get('enable_pruning', False),
            pruning_ratio=data.get('pruning_ratio', 0.1),
            enable_distillation=data.get('enable_distillation', False),
            teacher_model=data.get('teacher_model'),
            enable_tensorrt=data.get('enable_tensorrt', False),
            tensorrt_precision=data.get('tensorrt_precision', 'fp16')
        )


@dataclass
class ModelConfig:
    """模型配置主类"""
    # 基本模型信息
    model_type: ModelType = ModelType.RESNET
    model_name: str = "resnet50"
    model_variant: Optional[str] = None  # 模型变体，如 resnet50, resnet101
    backend: ModelBackend = ModelBackend.PYTORCH
    
    # 模型权重
    weights: ModelWeights = field(default_factory=ModelWeights)
    
    # 输入配置
    input_size: Tuple[int, int] = (224, 224)
    input_channels: int = 3
    normalize_input: bool = True
    input_mean: List[float] = field(default_factory=lambda: [0.485, 0.456, 0.406])
    input_std: List[float] = field(default_factory=lambda: [0.229, 0.224, 0.225])
    
    # 特征提取配置
    feature_layer: Optional[str] = None  # 特征提取层名称
    feature_dim: Optional[int] = None  # 特征维度
    pool_features: bool = True  # 是否对特征进行池化
    normalize_features: bool = True  # 是否归一化特征
    
    # 批处理配置
    batch_size: int = 32
    max_batch_size: int = 128
    
    # GPU配置
    use_gpu: bool = True
    gpu_id: int = 0
    mixed_precision: bool = False
    
    # 优化配置
    optimization: ModelOptimization = field(default_factory=ModelOptimization)
    
    # 缓存配置
    cache_model: bool = True
    model_cache_dir: str = "./models"
    
    # 预热配置
    warmup_iterations: int = 5
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'model_type': self.model_type.value,
            'model_name': self.model_name,
            'model_variant': self.model_variant,
            'backend': self.backend.value,
            'weights': self.weights.to_dict(),
            'input_size': list(self.input_size),
            'input_channels': self.input_channels,
            'normalize_input': self.normalize_input,
            'input_mean': self.input_mean,
            'input_std': self.input_std,
            'feature_layer': self.feature_layer,
            'feature_dim': self.feature_dim,
            'pool_features': self.pool_features,
            'normalize_features': self.normalize_features,
            'batch_size': self.batch_size,
            'max_batch_size': self.max_batch_size,
            'use_gpu': self.use_gpu,
            'gpu_id': self.gpu_id,
            'mixed_precision': self.mixed_precision,
            'optimization': self.optimization.to_dict(),
            'cache_model': self.cache_model,
            'model_cache_dir': self.model_cache_dir,
            'warmup_iterations': self.warmup_iterations
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelConfig':
        """从字典创建"""
        input_size = data.get('input_size', [224, 224])
        if isinstance(input_size, list):
            input_size = tuple(input_size)
            
        return cls(
            model_type=ModelType(data.get('model_type', 'resnet')),
            model_name=data.get('model_name', 'resnet50'),
            model_variant=data.get('model_variant'),
            backend=ModelBackend(data.get('backend', 'pytorch')),
            weights=ModelWeights.from_dict(data.get('weights', {})),
            input_size=input_size,
            input_channels=data.get('input_channels', 3),
            normalize_input=data.get('normalize_input', True),
            input_mean=data.get('input_mean', [0.485, 0.456, 0.406]),
            input_std=data.get('input_std', [0.229, 0.224, 0.225]),
            feature_layer=data.get('feature_layer'),
            feature_dim=data.get('feature_dim'),
            pool_features=data.get('pool_features', True),
            normalize_features=data.get('normalize_features', True),
            batch_size=data.get('batch_size', 32),
            max_batch_size=data.get('max_batch_size', 128),
            use_gpu=data.get('use_gpu', True),
            gpu_id=data.get('gpu_id', 0),
            mixed_precision=data.get('mixed_precision', False),
            optimization=ModelOptimization.from_dict(data.get('optimization', {})),
            cache_model=data.get('cache_model', True),
            model_cache_dir=data.get('model_cache_dir', './models'),
            warmup_iterations=data.get('warmup_iterations', 5)
        )
    
    def get_model_identifier(self) -> str:
        """获取模型唯一标识符"""
        parts = [self.model_type.value, self.model_name]
        if self.model_variant:
            parts.append(self.model_variant)
        if self.feature_layer:
            parts.append(f"layer_{self.feature_layer}")
        return "_".join(parts)
    
    def get_cache_path(self) -> Path:
        """获取模型缓存路径"""
        cache_dir = Path(self.model_cache_dir)
        model_id = self.get_model_identifier()
        return cache_dir / f"{model_id}.pth"
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            # 检查输入尺寸
            if len(self.input_size) != 2:
                return False
            if any(s <= 0 for s in self.input_size):
                return False
                
            # 检查输入通道数
            if self.input_channels <= 0:
                return False
                
            # 检查批大小
            if self.batch_size <= 0 or self.max_batch_size <= 0:
                return False
            if self.batch_size > self.max_batch_size:
                return False
                
            # 检查GPU配置
            if self.gpu_id < 0:
                return False
                
            # 检查归一化参数
            if self.normalize_input:
                if len(self.input_mean) != self.input_channels:
                    return False
                if len(self.input_std) != self.input_channels:
                    return False
                if any(s <= 0 for s in self.input_std):
                    return False
                    
            # 检查预热迭代次数
            if self.warmup_iterations < 0:
                return False
                
            return True
            
        except Exception:
            return False
    
    def get_effective_batch_size(self, requested_size: Optional[int] = None) -> int:
        """获取有效的批大小"""
        if requested_size is None:
            return self.batch_size
        return min(max(1, requested_size), self.max_batch_size)
    
    def supports_mixed_precision(self) -> bool:
        """检查是否支持混合精度"""
        # 只有在使用GPU且后端支持时才启用混合精度
        return (self.use_gpu and 
                self.mixed_precision and 
                self.backend in [ModelBackend.PYTORCH, ModelBackend.TENSORFLOW])
    
    def get_optimization_config(self) -> Dict[str, Any]:
        """获取优化配置"""
        config = self.optimization.to_dict()
        config.update({
            'mixed_precision': self.supports_mixed_precision(),
            'batch_size': self.batch_size,
            'use_gpu': self.use_gpu
        })
        return config


# 预定义的模型配置
PREDEFINED_MODELS = {
    'resnet50': ModelConfig(
        model_type=ModelType.RESNET,
        model_name='resnet50',
        feature_dim=2048,
        feature_layer='avgpool'
    ),
    'resnet101': ModelConfig(
        model_type=ModelType.RESNET,
        model_name='resnet101',
        feature_dim=2048,
        feature_layer='avgpool'
    ),
    'vgg16': ModelConfig(
        model_type=ModelType.VGG,
        model_name='vgg16',
        feature_dim=4096,
        feature_layer='classifier.3'
    ),
    'efficientnet_b0': ModelConfig(
        model_type=ModelType.EFFICIENTNET,
        model_name='efficientnet_b0',
        input_size=(224, 224),
        feature_dim=1280
    ),
    'clip_vit_b32': ModelConfig(
        model_type=ModelType.CLIP,
        model_name='ViT-B/32',
        input_size=(224, 224),
        feature_dim=512,
        normalize_features=True
    )
}


def get_model_config(model_name: str) -> Optional[ModelConfig]:
    """获取预定义的模型配置
    
    Args:
        model_name: 模型名称
        
    Returns:
        Optional[ModelConfig]: 模型配置，如果不存在则返回None
    """
    return PREDEFINED_MODELS.get(model_name)


def list_available_models() -> List[str]:
    """列出所有可用的预定义模型
    
    Returns:
        List[str]: 模型名称列表
    """
    return list(PREDEFINED_MODELS.keys())