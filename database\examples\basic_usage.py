#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模块基本使用示例

该示例展示了如何使用数据库模块进行基本操作，包括：
1. 获取数据库连接
2. 执行事务
3. 使用布料仓库
"""

import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# 导入数据库模块
from database import (
    get_database_manager, 
    close_database_manager,
    get_fabric_repository,
    close_fabric_repository
)


def example_database_connection():
    """数据库连接示例"""
    logger.info("=== 数据库连接示例 ===")
    db_manager = get_database_manager()
    
    with db_manager.get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version()")
        version = cursor.fetchone()[0]
        logger.info(f"SQLite 版本: {version}")


def example_transaction():
    """事务处理示例"""
    logger.info("\n=== 事务处理示例 ===")
    db_manager = get_database_manager()
    
    # 使用事务执行多个操作
    with db_manager.transaction() as conn:
        cursor = conn.cursor()
        
        # 在事务中执行多个操作
        cursor.execute("SELECT COUNT(*) FROM fabric")
        count_before = cursor.fetchone()[0]
        logger.info(f"当前布料数量: {count_before}")
        
        # 注意：这里只是演示，实际应用中应使用参数化查询防止SQL注入
        logger.info("事务会自动提交或回滚")


def example_fabric_repository():
    """布料仓库示例"""
    logger.info("\n=== 布料仓库示例 ===")
    fabric_repo = get_fabric_repository()
    
    # 获取布料总数
    count = fabric_repo.crud.count()
    logger.info(f"布料总数: {count}")
    
    # 获取部分布料数据
    fabrics = fabric_repo.crud.get_all(limit=3)
    logger.info(f"获取到 {len(fabrics)} 条布料数据")
    
    # 显示布料信息
    for fabric in fabrics:
        logger.info(f"布料ID: {fabric['id']}, 名称: {fabric.get('name', '未命名')}")


def main():
    """主函数"""
    logger.info("开始数据库模块基本使用示例...")
    
    try:
        # 数据库连接示例
        example_database_connection()
        
        # 事务处理示例
        example_transaction()
        
        # 布料仓库示例
        example_fabric_repository()
        
    except Exception as e:
        logger.error(f"示例执行异常: {e}")
    finally:
        # 关闭数据库连接
        close_fabric_repository()
        close_database_manager()
        logger.info("\n示例执行完毕，数据库连接已关闭")


if __name__ == "__main__":
    main()