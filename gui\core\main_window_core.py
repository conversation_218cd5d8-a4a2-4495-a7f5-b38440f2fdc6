#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口核心类

该模块提供主窗口的核心功能，简化主窗口代码。
"""

import os
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QSplitter
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QCloseEvent, QResizeEvent, QMoveEvent

from gui.config.app_config import AppConfig
from gui.managers import (
    MenuManager, ToolbarManager, SearchHandler, FileHandler,
    SettingsManager, EventHandler, WindowStateManager
)


@dataclass
class WindowState:
    """窗口状态"""
    geometry: Optional[bytes] = None
    window_state: Optional[bytes] = None
    splitter_state: Optional[bytes] = None
    is_maximized: bool = False
    is_fullscreen: bool = False


class MainWindowCore:
    """主窗口核心功能类
    
    该类封装了主窗口的核心功能，减少主窗口类的代码复杂度。
    """
    
    def __init__(self, main_window, feature_manager=None):
        self.main_window = main_window
        self.feature_manager = feature_manager or getattr(main_window, 'feature_manager', None)
        
        # 添加调试日志
        import logging
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"MainWindowCore.__init__ - 传入的feature_manager状态: {feature_manager is not None}")
        self.logger.info(f"MainWindowCore.__init__ - 最终的feature_manager状态: {self.feature_manager is not None}")
        if self.feature_manager is not None:
            self.logger.info(f"MainWindowCore.__init__ - feature_manager类型: {type(self.feature_manager)}")
            self.logger.info(f"MainWindowCore.__init__ - feature_manager ID: {id(self.feature_manager)}")
        
        self.app_config = AppConfig()
        
        # 从主窗口获取核心组件
        self.fabric_repository = getattr(main_window, 'fabric_repository', None)
        self.search_repository = getattr(main_window, 'search_repository', None)
        self.search_history_manager = getattr(main_window, 'search_history_manager', None)
        self.statistics_manager = getattr(main_window, 'statistics_manager', None)
        self.gpu_manager = getattr(main_window, 'gpu_manager', None)
        self.model_config_manager = getattr(main_window, 'model_config_manager', None)
        
        # 初始化任务管理器
        from utils.task_manager import TaskManager
        self.task_manager = TaskManager()
        
        # 初始化管理器
        self._init_managers()
        
        # 状态
        self.current_search_results = []
        self.is_searching = False
    
    def _init_managers(self):
        """初始化管理器"""
        # 菜单管理器
        self.menu_manager = MenuManager(self.main_window)
        
        # 工具栏管理器
        self.toolbar_manager = ToolbarManager(self.main_window)
        
        # 搜索处理器
        self.search_handler = SearchHandler(self.main_window)
        
        # 文件处理器
        self.file_handler = FileHandler(self.main_window)
        
        # 设置管理器
        self.settings_manager = SettingsManager(self.main_window)
        
        # 事件处理器
        self.event_handler = EventHandler(self.main_window)
        
        # 窗口状态管理器
        self.window_state_manager = WindowStateManager(self.main_window)
    
    def init_search_engine(self):
        """初始化搜索引擎"""
        # 确保使用已传入的特征管理器，避免重复创建
        if self.feature_manager and hasattr(self.main_window, 'fabric_repository'):
            # 添加调试日志
            self.logger.info(f"MainWindowCore.init_search_engine - feature_manager状态: {self.feature_manager is not None}")
            if self.feature_manager is not None:
                self.logger.info(f"feature_manager类型: {type(self.feature_manager)}")
                self.logger.info(f"feature_manager ID: {id(self.feature_manager)}")
            
            config = {
                'fabric_repository': self.main_window.fabric_repository,
                'feature_manager': self.feature_manager,  # 使用已有的特征管理器
                'database_path': getattr(self.main_window.fabric_repository, 'db_manager', {}).get('db_path', './data/fabric_search.db') if hasattr(self.main_window, 'fabric_repository') else './data/fabric_search.db',
                'data_dir': './data',
                'max_results': 100,
                'similarity_threshold': 0.8,
                'enable_cache': True,
                'cache_size': 1000,
                'search_timeout': 30
            }
            self.search_handler.initialize_search_engine(config)
        else:
            self.logger.warning(f"无法初始化搜索引擎 - feature_manager: {self.feature_manager is not None}, fabric_repository: {hasattr(self.main_window, 'fabric_repository')}")
    
    def setup_ui(self, central_widget: QWidget):
        """设置UI
        
        Args:
            central_widget: 中央部件
        """
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # 创建分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(main_splitter)
        
        return main_splitter
    
    def connect_all_signals(self):
        """连接所有信号"""
        self._connect_menu_signals()
        self._connect_toolbar_signals()
        self._connect_search_signals()
        self._connect_file_signals()
        self._connect_settings_signals()
        self._connect_event_signals()
        self._connect_window_signals()
    
    def _connect_menu_signals(self):
        """连接菜单信号"""
        self.menu_manager.openImageRequested.connect(
            self.file_handler.open_image
        )
        self.menu_manager.openFolderRequested.connect(
            self.file_handler.open_folder
        )
        self.menu_manager.importDatabaseRequested.connect(
            self.file_handler.import_database
        )
        self.menu_manager.exportResultsRequested.connect(
            lambda: self.file_handler.export_results(self.current_search_results)
        )
        self.menu_manager.settingsRequested.connect(
            self._show_settings_dialog
        )
        self.menu_manager.fullscreenToggled.connect(
            self.window_state_manager.toggle_fullscreen
        )
        self.menu_manager.themeChanged.connect(
            self.window_state_manager.change_theme
        )
        self.menu_manager.modelSelectorRequested.connect(
            self._show_model_selector_dialog
        )
        self.menu_manager.taskManagerRequested.connect(
            self.event_handler.show_task_manager_dialog
        )
        self.menu_manager.toolbarToggled.connect(
            self.window_state_manager.toggle_toolbar
        )
        self.menu_manager.rebuildIndexRequested.connect(
            self.search_handler.rebuild_index
        )
        self.menu_manager.clearCacheRequested.connect(
            self.search_handler.clear_cache
        )
        self.menu_manager.aboutRequested.connect(
            self.event_handler.show_about_dialog
        )
    
    def _connect_toolbar_signals(self):
        """连接工具栏信号"""
        self.toolbar_manager.openImageRequested.connect(
            self.file_handler.open_image
        )
        self.toolbar_manager.openFolderRequested.connect(
            self.file_handler.open_folder
        )
        self.toolbar_manager.settingsRequested.connect(
            self._show_settings_dialog
        )
        self.toolbar_manager.modelSelectorRequested.connect(
            self._show_model_selector_dialog
        )
        self.toolbar_manager.taskManagerRequested.connect(
            self.event_handler.show_task_manager_dialog
        )
        self.toolbar_manager.rebuildIndexRequested.connect(
            self.search_handler.rebuild_index
        )
        self.toolbar_manager.clearCacheRequested.connect(
            self.search_handler.clear_cache
        )
    
    def _connect_search_signals(self):
        """连接搜索信号"""
        self.search_handler.searchStarted.connect(
            self._on_search_started
        )
        self.search_handler.searchFinished.connect(
            self._on_search_finished
        )
        self.search_handler.searchError.connect(
            self._on_search_error
        )
        self.search_handler.searchResultsUpdated.connect(
            self._on_search_results_updated
        )
        self.search_handler.searchProgress.connect(
            self._on_search_progress
        )
    
    def _connect_file_signals(self):
        """连接文件信号"""
        self.file_handler.imageOpened.connect(
            self._on_image_opened
        )
        self.file_handler.folderOpened.connect(
            self._on_folder_opened
        )
        self.file_handler.databaseImported.connect(
            self._on_database_imported
        )
        self.file_handler.resultsExported.connect(
            self._on_results_exported
        )
        self.file_handler.operationError.connect(
            self._on_file_operation_error
        )
    
    def _connect_settings_signals(self):
        """连接设置信号"""
        self.settings_manager.settingsLoaded.connect(
            self._on_settings_loaded
        )
        self.settings_manager.settingsSaved.connect(
            self._on_settings_saved
        )
        self.settings_manager.settingsChanged.connect(
            self._on_settings_changed
        )
        self.settings_manager.fontChanged.connect(
            self._on_font_changed
        )
        self.settings_manager.themeChanged.connect(
            self._on_theme_changed
        )
    
    def _connect_event_signals(self):
        """连接事件信号"""
        self.event_handler.itemSelected.connect(
            self._on_item_selected
        )
        self.event_handler.itemDoubleClicked.connect(
            self._on_item_double_clicked
        )
        self.event_handler.settingsApplied.connect(
            self._on_settings_applied
        )
        self.event_handler.modelChanged.connect(
            self._on_model_changed
        )
    
    def _connect_window_signals(self):
        """连接窗口信号"""
        self.window_state_manager.fullscreenChanged.connect(
            self._on_fullscreen_changed
        )
        self.window_state_manager.toolbarVisibilityChanged.connect(
            self._on_toolbar_visibility_changed
        )
        self.window_state_manager.themeChanged.connect(
            self._on_theme_changed
        )
        self.window_state_manager.statusUpdated.connect(
            self._on_status_updated
        )
    
    def load_and_apply_settings(self):
        """加载并应用设置"""
        try:
            self.app_config.load()
            settings = self.app_config.get_config_dict()
            self.settings_manager.apply_settings(settings)
        except Exception as e:
            print(f"加载设置失败: {e}")
    
    def init_search_engine(self):
        """初始化搜索引擎"""
        try:
            config = self._get_search_engine_config()
            self.search_handler.initialize_search_engine(config)
        except Exception as e:
            print(f"初始化搜索引擎失败: {e}")
    
    def _get_search_engine_config(self) -> Dict[str, Any]:
        """获取搜索引擎配置"""
        # 使用已传入的组件，避免重复创建
        fabric_repository = self.fabric_repository
        feature_manager = self.feature_manager
        
        # 验证必要组件是否存在
        if not fabric_repository or not feature_manager:
            error_msg = f"缺少必要组件 - fabric_repository: {fabric_repository is not None}, feature_manager: {feature_manager is not None}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
        
        self.logger.info("使用已传入的组件，避免重复创建")
        self.logger.info(f"feature_manager ID: {id(feature_manager)}")
        
        # 获取数据库路径
        database_path = './data/fabric_search.db'
        if fabric_repository and hasattr(fabric_repository, 'db_manager'):
            database_path = getattr(fabric_repository.db_manager.config, 'db_path', database_path)
        
        return {
            'database_path': database_path,
            'max_results': self.app_config.search.max_results,
            'similarity_threshold': self.app_config.search.similarity_threshold,
            'enable_cache': self.app_config.search.enable_cache,
            'cache_size': self.app_config.search.cache_size,
            'search_timeout': self.app_config.search.search_timeout,
            'fabric_repository': fabric_repository,
            'feature_manager': feature_manager,
            'data_dir': str(self.app_config.data_dir) if hasattr(self.app_config, 'data_dir') else './data'
        }
    
    # 事件处理方法
    def _show_settings_dialog(self):
        """显示设置对话框"""
        current_settings = self.app_config.get_config_dict()
        self.event_handler.show_settings_dialog(current_settings)
    
    def _show_model_selector_dialog(self):
        """显示模型选择对话框"""
        current_model = self.app_config.database.path  # 临时使用
        self.event_handler.show_model_selector_dialog(current_model)
    
    def _on_search_started(self):
        """搜索开始"""
        self.is_searching = True
        self.window_state_manager.update_status("搜索中...")
        self.window_state_manager.show_progress(0, 100, "正在搜索...")
    
    def _on_search_finished(self):
        """搜索完成"""
        self.is_searching = False
        self.window_state_manager.update_status("搜索完成")
        self.window_state_manager.hide_progress()
    
    def _on_search_error(self, error_message: str):
        """搜索错误"""
        self.is_searching = False
        self.window_state_manager.update_status(f"搜索错误: {error_message}")
        self.window_state_manager.hide_progress()
        self.event_handler.show_error_message("搜索错误", error_message)
    
    def _on_search_results_updated(self, results):
        """搜索结果更新"""
        self.current_search_results = results
        
        # 处理不同类型的结果
        from search.models import SearchResult
        
        if isinstance(results, SearchResult):
            # 如果是SearchResult对象，使用total_results属性
            result_count = results.total_results
        elif hasattr(results, '__len__'):
            # 如果是列表或其他可计算长度的对象
            result_count = len(results)
        else:
            # 默认情况
            result_count = 0
            
        self.window_state_manager.update_status(f"找到 {result_count} 个结果")
        
        # 将搜索结果传递给ResultPanel
        if hasattr(self.main_window, 'result_panel') and self.main_window.result_panel:
            try:
                self.logger.info(f"将搜索结果传递给ResultPanel，结果数: {result_count}")
                self.main_window.result_panel.update_results(results)
            except Exception as e:
                self.logger.error(f"传递搜索结果到ResultPanel失败: {e}")
        else:
            self.logger.warning("ResultPanel不可用，无法显示搜索结果")
    
    def _on_search_progress(self, progress: int, message: str):
        """搜索进度更新"""
        self.window_state_manager.update_progress(progress, message)
    
    def _on_image_opened(self, image_path: str):
        """图像打开"""
        self.window_state_manager.update_status(
            f"已打开图像: {os.path.basename(image_path)}"
        )
    
    def _on_folder_opened(self, folder_path: str):
        """文件夹打开"""
        self.window_state_manager.update_status(
            f"已打开文件夹: {os.path.basename(folder_path)}"
        )
        
        # 自动触发特征提取
        try:
            # 直接调用主窗口的特征提取方法
            if hasattr(self.main_window, '_on_folder_path_set_for_extraction'):
                self.main_window._on_folder_path_set_for_extraction(folder_path)
            else:
                self.logger.warning("主窗口没有_on_folder_path_set_for_extraction方法")
        except Exception as e:
            self.logger.error(f"触发特征提取失败: {e}")
            self.window_state_manager.update_status(f"特征提取启动失败: {e}")
    
    def _on_database_imported(self, database_path: str):
        """数据库导入完成"""
        self.app_config.database.path = database_path
        self.app_config.save()
        self.init_search_engine()
        self.window_state_manager.update_status("数据库导入完成")
    
    def _on_results_exported(self, export_path: str):
        """结果导出完成"""
        self.window_state_manager.update_status(f"结果已导出到: {export_path}")
    
    def _on_file_operation_error(self, error_message: str):
        """文件操作错误"""
        self.event_handler.show_error_message("文件操作错误", error_message)
    
    def _on_settings_loaded(self, settings: Dict[str, Any]):
        """设置加载完成"""
        pass
    
    def _on_settings_saved(self):
        """设置保存完成"""
        pass
    
    def _on_settings_changed(self, key: str, value: Any):
        """设置变更"""
        pass
    
    def _on_settings_applied(self, settings: Dict[str, Any]):
        """设置应用"""
        # 将扁平结构的设置字典转换为嵌套结构
        nested_settings = self._nest_settings(settings)
        self.app_config.update_config(nested_settings)
        self.app_config.save()
    
    def _nest_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        将扁平结构的设置字典转换为嵌套结构
        
        Args:
            settings: 扁平结构的设置字典
            
        Returns:
            Dict[str, Any]: 嵌套结构的设置字典
        """
        nested_settings = {
            'window': {},
            'database': {},
            'search': {},
            'ui': {}
        }
        
        # 窗口设置
        if 'toolbar_visible' in settings:
            nested_settings['window']['toolbar_visible'] = settings['toolbar_visible']
        if 'statusbar_visible' in settings:
            nested_settings['window']['statusbar_visible'] = settings['statusbar_visible']
        
        # 数据库设置
        if 'backup_enabled' in settings:
            nested_settings['database']['auto_backup'] = settings['backup_enabled']
        if 'backup_interval' in settings:
            nested_settings['database']['backup_interval'] = settings['backup_interval']
        
        # 搜索设置
        if 'default_similarity_threshold' in settings:
            nested_settings['search']['similarity_threshold'] = settings['default_similarity_threshold']
        if 'max_search_results' in settings:
            nested_settings['search']['max_results'] = settings['max_search_results']
        if 'search_timeout' in settings:
            nested_settings['search']['search_timeout'] = settings['search_timeout']
        # 注释掉不支持的字段
        # if 'enable_search_history' in settings:
        #     nested_settings['search']['enable_history'] = settings['enable_search_history']
        # 注释掉不支持的字段
        # if 'max_history_items' in settings:
        #     nested_settings['search']['max_history_items'] = settings['max_history_items']
        if 'cache_size' in settings:
            nested_settings['search']['cache_size'] = settings['cache_size']
        
        # UI设置
        if 'theme' in settings:
            nested_settings['ui']['theme'] = settings['theme']
        if 'font_family' in settings:
            nested_settings['ui']['font_family'] = settings['font_family']
        if 'font_size' in settings:
            nested_settings['ui']['font_size'] = settings['font_size']
        if 'animation_enabled' in settings:
            nested_settings['ui']['animation_enabled'] = settings['animation_enabled']
        # 注释掉不支持的字段
        # if 'show_tooltips' in settings:
        #     nested_settings['ui']['show_tooltips'] = settings['show_tooltips']
        if 'language' in settings:
            nested_settings['ui']['language'] = settings['language']
        
        # 其他设置
        for key in ['use_gpu', 'max_threads', 'preload_thumbnails', 'log_level', 
                   'debug_mode', 'auto_update', 'telemetry_enabled', 'thumbnail_size']:
            if key in settings:
                nested_settings[key] = settings[key]
        
        return nested_settings
    
    def _on_font_changed(self, font):
        """字体变更"""
        from PyQt6.QtWidgets import QApplication
        QApplication.instance().setFont(font)
    
    def _on_theme_changed(self, theme_type):
        """主题变更"""
        self.menu_manager.update_theme_state(theme_type)
    
    def _on_model_changed(self, model_path: str):
        """模型变更"""
        # 更新配置并重新初始化搜索引擎
        self.init_search_engine()
    
    def _on_item_selected(self, item_path: str):
        """项目选中"""
        pass
    
    def _on_item_double_clicked(self, item_path: str):
        """项目双击"""
        pass
    
    def _on_fullscreen_changed(self, is_fullscreen: bool):
        """全屏状态变更"""
        self.menu_manager.update_fullscreen_state(is_fullscreen)
    
    def _on_toolbar_visibility_changed(self, is_visible: bool):
        """工具栏可见性变更"""
        self.menu_manager.update_toolbar_state(is_visible)
        self.toolbar_manager.set_visible(is_visible)
    
    def _on_status_updated(self, message: str):
        """状态更新"""
        pass
    
    def handle_close_event(self, event: QCloseEvent) -> bool:
        """处理关闭事件
        
        Returns:
            bool: 是否允许关闭
        """
        if self.event_handler.handle_close_event(event):
            self.app_config.save()
            self.search_handler.stop_search()
            self._cleanup()
            return True
        return False
    
    def handle_resize_event(self, event: QResizeEvent):
        """处理窗口大小改变事件"""
        self.event_handler.handle_resize_event(event)
    
    def handle_move_event(self, event: QMoveEvent):
        """处理窗口移动事件"""
        self.event_handler.handle_move_event(event)
    
    def _cleanup(self):
        """清理资源"""
        try:
            self.search_handler.cleanup()
            self.file_handler.cleanup()
            self.event_handler.cleanup()
            self.window_state_manager.cleanup()
        except Exception as e:
            print(f"清理资源失败: {e}")