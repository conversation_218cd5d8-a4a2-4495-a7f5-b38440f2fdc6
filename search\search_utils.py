# -*- coding: utf-8 -*-
"""
搜索工具模块

该模块提供搜索相关的工具函数和辅助类。
"""

import re
import json
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from pathlib import Path

from utils.log_utils import LoggerMixin
from .search_strategies import (
    SearchStrategy, SearchStrategyType, SearchStrategyFactory,
    ExpansionSearchStrategy, SingleFeatureSearchStrategy,
    FeatureWeights, SearchResult
)
from features.search.similarity_search import SimilarityCalculator
from database.models import FabricImage


@dataclass
class SearchConfiguration:
    """搜索配置类"""
    strategy_type: SearchStrategyType = SearchStrategyType.WEIGHTED
    feature_weights: Optional[FeatureWeights] = None
    similarity_threshold: float = 0.7
    max_results: int = 100
    enable_caching: bool = True
    cache_ttl: int = 300  # 缓存时间（秒）
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'strategy_type': self.strategy_type.value,
            'feature_weights': self.feature_weights.to_dict() if self.feature_weights else None,
            'similarity_threshold': self.similarity_threshold,
            'max_results': self.max_results,
            'enable_caching': self.enable_caching,
            'cache_ttl': self.cache_ttl
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchConfiguration':
        """从字典创建配置"""
        feature_weights = None
        if data.get('feature_weights'):
            weights_data = data['feature_weights']
            feature_weights = FeatureWeights(
                color_weight=weights_data.get('color', 0.25),
                texture_weight=weights_data.get('texture', 0.25),
                shape_weight=weights_data.get('shape', 0.25),
                deep_learning_weight=weights_data.get('deep_learning', 0.25)
            )
        
        return cls(
            strategy_type=SearchStrategyType(data.get('strategy_type', 'weighted')),
            feature_weights=feature_weights,
            similarity_threshold=data.get('similarity_threshold', 0.7),
            max_results=data.get('max_results', 100),
            enable_caching=data.get('enable_caching', True),
            cache_ttl=data.get('cache_ttl', 300)
        )


class TextQueryProcessor(LoggerMixin):
    """文本查询处理器"""
    
    def __init__(self):
        super().__init__()
        # 停用词列表
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'
        }
    
    def preprocess_query(self, query: str) -> Dict[str, Any]:
        """预处理查询文本
        
        Args:
            query: 原始查询文本
            
        Returns:
            Dict[str, Any]: 处理后的查询信息
        """
        try:
            # 清理文本
            cleaned_query = self._clean_text(query)
            
            # 分词
            terms = self._tokenize(cleaned_query)
            
            # 去除停用词
            filtered_terms = self._remove_stop_words(terms)
            
            # 提取关键词
            keywords = self._extract_keywords(filtered_terms)
            
            # 检测查询类型
            query_type = self._detect_query_type(cleaned_query)
            
            return {
                'original_query': query,
                'cleaned_query': cleaned_query,
                'terms': terms,
                'filtered_terms': filtered_terms,
                'keywords': keywords,
                'query_type': query_type,
                'term_count': len(filtered_terms)
            }
            
        except Exception as e:
            self.logger.error(f"预处理查询文本失败: {e}")
            return {
                'original_query': query,
                'cleaned_query': query,
                'terms': [query],
                'filtered_terms': [query],
                'keywords': [query],
                'query_type': 'simple',
                'term_count': 1
            }
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 转换为小写
        text = text.lower().strip()
        
        # 移除特殊字符，保留中文、英文、数字和空格
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
        
        # 合并多个空格
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _tokenize(self, text: str) -> List[str]:
        """分词"""
        # 简单的空格分词（实际项目中可以使用更复杂的分词器）
        return [term.strip() for term in text.split() if term.strip()]
    
    def _remove_stop_words(self, terms: List[str]) -> List[str]:
        """去除停用词"""
        return [term for term in terms if term not in self.stop_words]
    
    def _extract_keywords(self, terms: List[str]) -> List[str]:
        """提取关键词"""
        # 简单实现：返回长度大于1的词
        return [term for term in terms if len(term) > 1]
    
    def _detect_query_type(self, query: str) -> str:
        """检测查询类型"""
        if '颜色' in query or 'color' in query:
            return 'color'
        elif '纹理' in query or 'texture' in query:
            return 'texture'
        elif '形状' in query or 'shape' in query:
            return 'shape'
        elif '类别' in query or 'category' in query:
            return 'category'
        else:
            return 'general'


class SearchResultAnalyzer(LoggerMixin):
    """搜索结果分析器"""
    
    def analyze_results(self, results: List[SearchResult]) -> Dict[str, Any]:
        """分析搜索结果
        
        Args:
            results: 搜索结果列表
            
        Returns:
            Dict[str, Any]: 分析报告
        """
        try:
            if not results:
                return {
                    'total_results': 0,
                    'analysis': 'No results to analyze'
                }
            
            # 基本统计
            total_results = len(results)
            avg_score = sum(result.similarity_score for result in results) / total_results
            max_score = max(result.similarity_score for result in results)
            min_score = min(result.similarity_score for result in results)
            
            # 分数分布
            score_distribution = self._analyze_score_distribution(results)
            
            # 策略使用统计
            strategy_stats = self._analyze_strategy_usage(results)
            
            # 特征分析
            feature_analysis = self._analyze_feature_scores(results)
            
            return {
                'total_results': total_results,
                'score_statistics': {
                    'average': avg_score,
                    'maximum': max_score,
                    'minimum': min_score
                },
                'score_distribution': score_distribution,
                'strategy_statistics': strategy_stats,
                'feature_analysis': feature_analysis
            }
            
        except Exception as e:
            self.logger.error(f"分析搜索结果失败: {e}")
            return {'error': str(e)}
    
    def _analyze_score_distribution(self, results: List[SearchResult]) -> Dict[str, int]:
        """分析分数分布"""
        distribution = {
            'excellent': 0,  # 0.9-1.0
            'good': 0,       # 0.7-0.9
            'fair': 0,       # 0.5-0.7
            'poor': 0        # 0.0-0.5
        }
        
        for result in results:
            score = result.similarity_score
            if score >= 0.9:
                distribution['excellent'] += 1
            elif score >= 0.7:
                distribution['good'] += 1
            elif score >= 0.5:
                distribution['fair'] += 1
            else:
                distribution['poor'] += 1
        
        return distribution
    
    def _analyze_strategy_usage(self, results: List[SearchResult]) -> Dict[str, int]:
        """分析策略使用统计"""
        strategy_counts = {}
        
        for result in results:
            strategy = result.strategy_used
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        
        return strategy_counts
    
    def _analyze_feature_scores(self, results: List[SearchResult]) -> Dict[str, Dict[str, float]]:
        """分析特征分数"""
        feature_stats = {}
        
        # 收集所有特征类型
        all_features = set()
        for result in results:
            all_features.update(result.feature_scores.keys())
        
        # 计算每个特征的统计信息
        for feature in all_features:
            scores = []
            for result in results:
                if feature in result.feature_scores:
                    scores.append(result.feature_scores[feature])
            
            if scores:
                feature_stats[feature] = {
                    'average': sum(scores) / len(scores),
                    'maximum': max(scores),
                    'minimum': min(scores),
                    'count': len(scores)
                }
        
        return feature_stats


class SearchStrategyRecommender(LoggerMixin):
    """搜索策略推荐器"""
    
    def recommend_strategy(self, 
                         query_info: Dict[str, Any],
                         dataset_size: int,
                         performance_requirements: Dict[str, Any]) -> SearchStrategyType:
        """推荐搜索策略
        
        Args:
            query_info: 查询信息
            dataset_size: 数据集大小
            performance_requirements: 性能要求
            
        Returns:
            SearchStrategyType: 推荐的策略类型
        """
        try:
            # 根据查询类型推荐
            query_type = query_info.get('query_type', 'general')
            term_count = query_info.get('term_count', 1)
            
            # 性能要求
            speed_priority = performance_requirements.get('speed_priority', False)
            accuracy_priority = performance_requirements.get('accuracy_priority', True)
            
            # 推荐逻辑
            if speed_priority and dataset_size > 10000:
                # 大数据集且要求速度，使用单特征搜索
                if query_type in ['color', 'texture', 'shape']:
                    return SearchStrategyType.SINGLE_FEATURE
                else:
                    return SearchStrategyType.WEIGHTED
            
            elif accuracy_priority:
                # 要求准确性
                if term_count > 3:
                    # 复杂查询，使用查询扩展
                    return SearchStrategyType.QUERY_EXPANSION
                elif dataset_size < 1000:
                    # 小数据集，使用混合策略
                    return SearchStrategyType.HYBRID
                else:
                    # 使用自适应策略
                    return SearchStrategyType.ADAPTIVE
            
            else:
                # 平衡性能和准确性
                return SearchStrategyType.WEIGHTED
            
        except Exception as e:
            self.logger.error(f"推荐搜索策略失败: {e}")
            return SearchStrategyType.WEIGHTED
    
    def get_optimal_weights(self, query_type: str) -> FeatureWeights:
        """获取最优特征权重
        
        Args:
            query_type: 查询类型
            
        Returns:
            FeatureWeights: 特征权重
        """
        weight_presets = {
            'color': FeatureWeights(0.5, 0.2, 0.1, 0.2),
            'texture': FeatureWeights(0.1, 0.5, 0.2, 0.2),
            'shape': FeatureWeights(0.1, 0.2, 0.5, 0.2),
            'general': FeatureWeights(0.25, 0.25, 0.25, 0.25),
            'category': FeatureWeights(0.2, 0.2, 0.2, 0.4)
        }
        
        return weight_presets.get(query_type, FeatureWeights())


def create_search_strategy_from_config(config: SearchConfiguration,
                                     similarity_calculator: SimilarityCalculator) -> SearchStrategy:
    """根据配置创建搜索策略
    
    Args:
        config: 搜索配置
        similarity_calculator: 相似度计算器
        
    Returns:
        SearchStrategy: 搜索策略实例
    """
    if config.strategy_type == SearchStrategyType.QUERY_EXPANSION:
        # 使用新的ExpansionSearchStrategy
        return ExpansionSearchStrategy(similarity_calculator)
    else:
        # 使用工厂方法创建其他策略
        return SearchStrategyFactory.create_strategy(
            config.strategy_type,
            similarity_calculator,
            feature_weights=config.feature_weights
        )


def validate_search_parameters(query: str, 
                             max_results: int,
                             similarity_threshold: float) -> Tuple[bool, str]:
    """验证搜索参数
    
    Args:
        query: 查询字符串
        max_results: 最大结果数
        similarity_threshold: 相似度阈值
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    if not query or not query.strip():
        return False, "查询不能为空"
    
    if max_results <= 0 or max_results > 10000:
        return False, "最大结果数必须在1-10000之间"
    
    if similarity_threshold < 0.0 or similarity_threshold > 1.0:
        return False, "相似度阈值必须在0.0-1.0之间"
    
    return True, ""